import { Hono } from 'hono';

import { connectionTracker } from '../lib/connection-tracker';
import { createErrorResponse, createResponse } from '../lib/utils';

const app = new Hono();

/**
 * GET /diagnostics - 获取连接诊断信息
 */
app.get('/diagnostics', async (c) => {
  try {
    const overview = connectionTracker.getDiagnostics();
    return c.json(createResponse(overview, '连接诊断信息'));
  } catch (error) {
    return c.json(createErrorResponse('获取诊断信息失败'), 500);
  }
});

/**
 * GET /diagnostics/:invitationId - 获取特定邀请的连接状态
 */
app.get('/diagnostics/:invitationId', async (c) => {
  try {
    const invitationId = c.req.param('invitationId');
    const diagnostics = connectionTracker.getDiagnostics(invitationId);

    if (!diagnostics) {
      return c.json(createErrorResponse('未找到该邀请的连接记录'), 404);
    }

    return c.json(createResponse(diagnostics, '连接诊断详情'));
  } catch (error) {
    return c.json(createErrorResponse('获取诊断详情失败'), 500);
  }
});

export default app;
