// 团队相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type {
  AddTeamMemberRequest,
  ApiResponse,
  CreateTeamRequest,
  RemoveTeamMemberRequest,
  Team,
  TeamDetail,
  TeamFilters,
  TeamMember,
  TeamMemberFilters,
  TeamMembersListResponse,
  TeamSelectOption,
  TeamsListResponse,
  UpdateTeamMemberRequest,
  UpdateTeamRequest,
} from '../types';

// 团队列表查询选项
export const teamsQueryOptions = (filters: TeamFilters = {}) => ({
  queryKey: queryKeys.teams(filters),
  queryFn: async () => {
    const response = await apiClient.get<TeamsListResponse>('teams', filters as Record<string, string | number | boolean | string[] | number[] | null | undefined>);
    return response;
  },
});

// 单个团队详情查询选项
export const teamQueryOptions = (id: string) => ({
  queryKey: queryKeys.team(id),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<TeamDetail>>(`teams/${id}`);
    return response.data;
  },
});

// 团队成员列表查询选项
export const teamMembersQueryOptions = (teamId: string, filters: TeamMemberFilters = {}) => ({
  queryKey: queryKeys.teamMembers(teamId, filters),
  queryFn: async () => {
    const response = await apiClient.get<TeamMembersListResponse>(`teams/${teamId}/members`, filters as Record<string, string | number | boolean | string[] | number[] | null | undefined>);
    return response;
  },
});

// 团队选择选项查询（用于下拉选择）
export const teamsForSelectQueryOptions = (organizationId?: string) => ({
  queryKey: queryKeys.teams({ limit: 100, organizationId }),
  queryFn: async () => {
    const response = await apiClient.get<TeamsListResponse>('teams', { limit: 100 });
    return response.data.map(
      (team: Team): TeamSelectOption => ({
        id: team.id,
        name: team.name,
      }),
    );
  },
});

// 创建团队 Mutation
export const createTeamMutation = {
  mutationFn: async (data: CreateTeamRequest) => {
    const response = await apiClient.post<ApiResponse<Team>>('teams', data);
    return response.data;
  },
};

// 更新团队 Mutation
export const updateTeamMutation = (teamId: string) => ({
  mutationFn: async (data: UpdateTeamRequest) => {
    const response = await apiClient.put<ApiResponse<Team>>(`teams/${teamId}`, data);
    return response.data;
  },
});

// 删除团队 Mutation
export const deleteTeamMutation = (teamId: string) => ({
  mutationFn: async () => {
    const response = await apiClient.delete<ApiResponse<void>>(`teams/${teamId}`);
    return response.data;
  },
});

// 添加团队成员 Mutation
export const addTeamMemberMutation = (teamId: string) => ({
  mutationFn: async (data: AddTeamMemberRequest) => {
    const response = await apiClient.post<ApiResponse<TeamMember>>(`teams/${teamId}/members`, data);
    return response.data;
  },
});

// 移除团队成员 Mutation
export const removeTeamMemberMutation = (teamId: string) => ({
  mutationFn: async (data: RemoveTeamMemberRequest) => {
    const response = await apiClient.delete<ApiResponse<void>>(`teams/${teamId}/members/${data.userId}`);
    return response.data;
  },
});

// 更新团队成员 Mutation
export const updateTeamMemberMutation = (teamId: string, memberId: string) => ({
  mutationFn: async (data: UpdateTeamMemberRequest) => {
    const response = await apiClient.put<ApiResponse<TeamMember>>(`teams/${teamId}/members/${memberId}`, data);
    return response.data;
  },
});
