import Bun from 'bun';
import type { Context } from 'hono';

import redis from '../client/redis.ts';
import { ErrorCode } from '../constants/errorCodes.ts';
import { AppError } from '../errors/custom.error.ts';
import { RagflowUploaderService } from '../services/questionAndAnswerRagflowServices.ts';
import { QuestionAndAnswerService } from '../services/questionAndAnswerService';
import { questionAndAnswerSearchSchema } from '../types/request/questionAndAnswer';
import {
  bulkDeleteSchema,
  categoryCodeParamSchema,
  paramIdSchema,
  questionAndAnswerQuerySchema,
  statsQuerySchema,
  updateOrderStatusSchema,
  upsertQuestionAndAnswerSchema,
} from '../types/validators/questionAndAnswerValidator';
import { removeScanKeys } from '../utils/cache.ts';
import { R } from '../utils/Response.ts';

const questionAndAnswerService = new QuestionAndAnswerService();

export class QuestionAndAnswerController {
  private ragflowUploaderService: RagflowUploaderService;

  constructor() {
    this.ragflowUploaderService = new RagflowUploaderService();
  }
  /** 创建或更新问答 */
  public async upsert(c: Context) {
    const body = await c.req.json();
    const validation = upsertQuestionAndAnswerSchema.safeParse(body);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }
    const result = await questionAndAnswerService.upsert(validation.data);

    // 清除相关缓存
    await removeScanKeys(redis, 'qa:list:');
    await removeScanKeys(redis, 'qa:stats:');
    await removeScanKeys(redis, 'qa:types:');
    await removeScanKeys(redis, 'qa:category:');
    if (validation.data.id) {
      await redis.del(`qa:detail:${validation.data.id}`);
    }

    return R.success(c, result);
  }

  /** 根据ID获取问答详情 */
  public async findById(c: Context) {
    const params = c.req.param();
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }

    // 构建缓存键
    const rdsKey = `qa:detail:${validation.data.id}`;
    const cacheString = await redis.get(rdsKey);

    // 如果有缓存先在缓存中查询
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerService.findById(validation.data.id);
    if (!result) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '问答记录不存在',
      });
    }

    // 加入缓存
    await redis.set(rdsKey, JSON.stringify(result), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

    return R.success(c, result);
  }

  /** 获取问答列表 */
  public async findMany(c: Context) {
    const query = c.req.query();
    const validation = questionAndAnswerQuerySchema.safeParse(query);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }

    // 构建缓存键（基于查询参数）
    const queryString = JSON.stringify(validation.data);
    const rdsKey = `qa:list:${Buffer.from(queryString).toString('base64')}`;
    const cacheString = await redis.get(rdsKey);

    // 如果有缓存先在缓存中查询
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerService.findMany(validation.data);

    // 加入缓存
    await redis.set(rdsKey, JSON.stringify(result), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

    return R.success(c, result);
  }

  /** 根据问题类型查找问答 */
  public async findByQuestionType(c: Context) {
    const { questionType } = c.req.param();
    if (!questionType) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        message: '问题类型参数必须提供',
      });
    }

    const decodedQuestionType = decodeURIComponent(questionType);

    // 构建缓存键
    const rdsKey = `qa:bytype:${decodedQuestionType}`;
    const cacheString = await redis.get(rdsKey);

    // 如果有缓存先在缓存中查询
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerService.findByQuestionType(decodedQuestionType);

    // 加入缓存
    await redis.set(rdsKey, JSON.stringify(result), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

    return R.success(c, result);
  }

  /** 根据分类编码查找问答 */
  public async findByCategoryCode(c: Context) {
    const params = c.req.param();
    const validation = categoryCodeParamSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }

    const { categoryCode } = validation.data;

    // 构建缓存键
    const rdsKey = `qa:category:${categoryCode}`;
    const cacheString = await redis.get(rdsKey);

    // 如果有缓存先在缓存中查询
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerService.findByCategoryCode(categoryCode);

    // 加入缓存
    await redis.set(rdsKey, JSON.stringify(result), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

    return R.success(c, result);
  }

  /** 根据订单状态查找问答 */
  public async findByOrderStatus(c: Context) {
    const { orderStatus } = c.req.param();
    if (!orderStatus) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        message: '订单状态参数必须提供',
      });
    }

    // 构建缓存键
    const rdsKey = `qa:byorder:${orderStatus}`;
    const cacheString = await redis.get(rdsKey);

    // 如果有缓存先在缓存中查询
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerService.findByOrderStatus(orderStatus);

    // 加入缓存
    await redis.set(rdsKey, JSON.stringify(result), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

    return R.success(c, result);
  }

  /** 更新订单状态 */
  public async updateOrderStatus(c: Context) {
    const params = c.req.param();
    const body = await c.req.json();

    const paramValidation = paramIdSchema.safeParse(params);
    const bodyValidation = updateOrderStatusSchema.safeParse(body);

    if (!paramValidation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: paramValidation.error.issues,
      });
    }

    if (!bodyValidation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: bodyValidation.error.issues,
      });
    }

    const result = await questionAndAnswerService.updateOrderStatus(paramValidation.data.id, bodyValidation.data.orderStatus);

    // 清除相关缓存
    await redis.del(`qa:detail:${paramValidation.data.id}`);
    await removeScanKeys(redis, 'qa:list:');
    await removeScanKeys(redis, 'qa:stats:');
    await removeScanKeys(redis, 'qa:byorder:');

    return R.success(c, result);
  }

  /** 获取统计信息 */
  public async getStats(c: Context) {
    const query = c.req.query();
    const validation = statsQuerySchema.safeParse(query);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }

    // 构建缓存键
    const queryString = JSON.stringify(validation.data);
    const rdsKey = `qa:stats:${Buffer.from(queryString).toString('base64')}`;
    const cacheString = await redis.get(rdsKey);

    // 如果有缓存先在缓存中查询
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerService.getStats(validation.data.startDate, validation.data.endDate, validation.data.groupBy);

    // 加入缓存
    await redis.set(rdsKey, JSON.stringify(result), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

    return R.success(c, result);
  }

  /** 软删除单个问答 */
  public async delete(c: Context) {
    const params = c.req.param();
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }
    const result = await questionAndAnswerService.softDelete(validation.data.id);

    // 清除相关缓存
    await redis.del(`qa:detail:${validation.data.id}`);
    await removeScanKeys(redis, 'qa:list:');
    await removeScanKeys(redis, 'qa:stats:');
    await removeScanKeys(redis, 'qa:bytype:');
    await removeScanKeys(redis, 'qa:byorder:');
    await removeScanKeys(redis, 'qa:category:');

    return R.success(c, result);
  }

  /** 批量软删除问答 */
  public async bulkDelete(c: Context) {
    const body = await c.req.json();
    const validation = bulkDeleteSchema.safeParse(body);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }
    const result = await questionAndAnswerService.bulkDelete(validation.data.ids);

    // 清除相关缓存
    for (const id of validation.data.ids) {
      await redis.del(`qa:detail:${id}`);
    }
    await removeScanKeys(redis, 'qa:list:');
    await removeScanKeys(redis, 'qa:stats:');
    await removeScanKeys(redis, 'qa:bytype:');
    await removeScanKeys(redis, 'qa:byorder:');
    await removeScanKeys(redis, 'qa:category:');

    return R.success(c, result);
  }

  /** 获取所有唯一的问题类型 */
  public async getUniqueQuestionTypes(c: Context) {
    // 构建缓存键
    const rdsKey = 'qa:types:unique';
    const cacheString = await redis.get(rdsKey);

    // 如果有缓存先在缓存中查询
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerService.getUniqueQuestionTypes();

    // 加入缓存
    await redis.set(rdsKey, JSON.stringify(result), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

    return R.success(c, result);
  }

  /** 根据查询字符串搜索问答知识库 */
  public async searchByQuery(c: Context) {
    try {
      const query = c.req.query('query');
      const validation = questionAndAnswerSearchSchema.safeParse({ query });

      if (!validation.success) {
        return c.json(
          {
            code: 400,
            message: '参数验证失败',
            data: [],
            errors: validation.error.issues,
          },
          400,
        );
      }

      const results = await questionAndAnswerService.searchByQuery(validation.data.query.trim());

      return c.json({
        code: 0,
        message: 'success',
        data: results,
      });
    } catch (error) {
      console.error('搜索问答知识库失败:', error);
      return c.json(
        {
          code: 500,
          message: 'fail',
          data: [],
        },
        500,
      );
    }
  }

  public syncRagflow = async (c: Context) => {
    try {
      const result = await this.ragflowUploaderService.syncToRagflow();
      return c.json({ code: 0, data: result });
    } catch (err: unknown) {
      const message = err instanceof Error ? `同步失败: ${err.message}` : '同步失败: 未知错误';
      return c.json({ code: 500, message }, 500);
    }
  };
}
