import { z } from 'zod';

export const frontendLogRequestSchema = z.object({
  level: z.enum(['info', 'warn', 'error', 'debug']),
  message: z.string().min(1),
  timestamp: z.number(),
  url: z.string().optional(),
  userAgent: z.string().optional(),
  userId: z.string().optional(),
  sessionId: z.string().optional(),
  module: z.string().min(1),
  details: z
    .object({
      method: z.string().optional(),
      requestUrl: z.string().optional(),
      requestData: z.any().optional(),
      responseStatus: z.number().optional(),
      responseData: z.any().optional(),
      duration: z.number().optional(),
      stack: z.string().optional(),
      errorType: z.string().optional(),
    })
    .optional(),
});

export type FrontendLogRequestSchema = z.infer<typeof frontendLogRequestSchema>;
