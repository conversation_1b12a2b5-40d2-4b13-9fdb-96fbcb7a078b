#!/usr/bin/env python3
"""
推荐接口性能测试
"""
import asyncio
import time
import json
import aiohttp
import statistics
from typing import List, Dict, Any


class RecommendationPerformanceTest:
    """推荐接口性能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_conversations = [
            # 测试对话1：产品咨询
            [
                {"role": "customer", "content": "你们有什么AI产品？"},
                {"role": "agent", "content": "我们有AI PPT制作、AI视频创作和AI写作助手三款产品。"},
                {"role": "customer", "content": "AI PPT制作怎么样？"}
            ],
            # 测试对话2：价格咨询
            [
                {"role": "customer", "content": "AI写作助手多少钱？"},
                {"role": "agent", "content": "AI写作助手的价格是99元/月。"},
                {"role": "customer", "content": "有点贵，有优惠吗？"}
            ],
            # 测试对话3：订单查询
            [
                {"role": "customer", "content": "我想查询一下我的订单"},
                {"role": "agent", "content": "好的，我来帮您查询订单状态。"},
                {"role": "customer", "content": "订单什么时候能到？"}
            ],
            # 测试对话4：购买意向
            [
                {"role": "customer", "content": "我想买AI视频创作"},
                {"role": "agent", "content": "AI视频创作是很受欢迎的产品，功能很强大。"},
                {"role": "customer", "content": "怎么购买？"}
            ],
            # 测试对话5：空对话
            [],
            # 测试对话6：单条消息
            [
                {"role": "customer", "content": "你好"}
            ]
        ]
    
    async def test_single_request(self, session: aiohttp.ClientSession, conversation_history: List[Dict[str, Any]], conversation_id: str) -> Dict[str, Any]:
        """测试单个推荐请求"""
        start_time = time.time()
        
        try:
            payload = {
                "conversation_history": conversation_history,
                "conversation_id": conversation_id
            }
            
            async with session.post(
                f"{self.base_url}/recommendations",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=15)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    elapsed_time = time.time() - start_time
                    
                    return {
                        "success": True,
                        "elapsed_time": elapsed_time,
                        "recommendations_count": len(result.get("recommendations", [])),
                        "conversation_id": result.get("conversation_id"),
                        "status_code": response.status
                    }
                else:
                    elapsed_time = time.time() - start_time
                    error_text = await response.text()
                    
                    return {
                        "success": False,
                        "elapsed_time": elapsed_time,
                        "error": f"HTTP {response.status}: {error_text}",
                        "status_code": response.status
                    }
                    
        except Exception as e:
            elapsed_time = time.time() - start_time
            return {
                "success": False,
                "elapsed_time": elapsed_time,
                "error": str(e),
                "status_code": 0
            }
    
    async def test_concurrent_requests(self, concurrent_count: int = 10) -> Dict[str, Any]:
        """测试并发请求"""
        print(f"🚀 开始并发测试，并发数: {concurrent_count}")
        
        async with aiohttp.ClientSession() as session:
            tasks = []
            
            for i in range(concurrent_count):
                conversation_idx = i % len(self.test_conversations)
                conversation_history = self.test_conversations[conversation_idx]
                conversation_id = f"test_concurrent_{i}"
                
                task = self.test_single_request(session, conversation_history, conversation_id)
                tasks.append(task)
            
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
            
            # 统计结果
            successful_results = []
            failed_results = []
            
            for result in results:
                if isinstance(result, Exception):
                    failed_results.append({"error": str(result), "elapsed_time": 0})
                elif result.get("success"):
                    successful_results.append(result)
                else:
                    failed_results.append(result)
            
            # 计算统计信息
            if successful_results:
                response_times = [r["elapsed_time"] for r in successful_results]
                avg_response_time = statistics.mean(response_times)
                median_response_time = statistics.median(response_times)
                min_response_time = min(response_times)
                max_response_time = max(response_times)
                p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 1 else response_times[0]
            else:
                avg_response_time = median_response_time = min_response_time = max_response_time = p95_response_time = 0
            
            return {
                "concurrent_count": concurrent_count,
                "total_time": total_time,
                "successful_count": len(successful_results),
                "failed_count": len(failed_results),
                "success_rate": len(successful_results) / concurrent_count * 100,
                "avg_response_time": avg_response_time,
                "median_response_time": median_response_time,
                "min_response_time": min_response_time,
                "max_response_time": max_response_time,
                "p95_response_time": p95_response_time,
                "requests_per_second": concurrent_count / total_time if total_time > 0 else 0,
                "successful_results": successful_results[:3],  # 只显示前3个成功结果
                "failed_results": failed_results[:3]  # 只显示前3个失败结果
            }
    
    async def test_cache_effectiveness(self) -> Dict[str, Any]:
        """测试缓存效果"""
        print("🎯 测试缓存效果...")
        
        conversation_history = self.test_conversations[0]  # 使用第一个测试对话
        conversation_id = "cache_test"
        
        async with aiohttp.ClientSession() as session:
            # 第一次请求（无缓存）
            result1 = await self.test_single_request(session, conversation_history, conversation_id)
            
            # 等待一小段时间
            await asyncio.sleep(0.1)
            
            # 第二次请求（应该命中缓存）
            result2 = await self.test_single_request(session, conversation_history, conversation_id)
            
            # 第三次请求（应该命中缓存）
            result3 = await self.test_single_request(session, conversation_history, conversation_id)
            
            return {
                "first_request": result1,
                "second_request": result2,
                "third_request": result3,
                "cache_speedup": result1["elapsed_time"] / result2["elapsed_time"] if result2["elapsed_time"] > 0 else 0
            }
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/cache/stats") as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"HTTP {response.status}"}
        except Exception as e:
            return {"error": str(e)}
    
    async def run_full_test(self):
        """运行完整的性能测试"""
        print("🔧 推荐接口性能测试")
        print("=" * 50)
        
        # 1. 测试不同并发级别
        concurrent_levels = [1, 5, 10, 20]
        
        for concurrent_count in concurrent_levels:
            print(f"\n📊 并发测试 - 并发数: {concurrent_count}")
            result = await self.test_concurrent_requests(concurrent_count)
            
            print(f"   成功率: {result['success_rate']:.1f}%")
            print(f"   平均响应时间: {result['avg_response_time']:.3f}s")
            print(f"   中位数响应时间: {result['median_response_time']:.3f}s")
            print(f"   P95响应时间: {result['p95_response_time']:.3f}s")
            print(f"   最大响应时间: {result['max_response_time']:.3f}s")
            print(f"   QPS: {result['requests_per_second']:.1f}")
            
            if result['failed_count'] > 0:
                print(f"   ❌ 失败数: {result['failed_count']}")
        
        # 2. 测试缓存效果
        print(f"\n🎯 缓存效果测试")
        cache_result = await self.test_cache_effectiveness()
        
        if cache_result["first_request"]["success"] and cache_result["second_request"]["success"]:
            print(f"   首次请求: {cache_result['first_request']['elapsed_time']:.3f}s")
            print(f"   缓存请求: {cache_result['second_request']['elapsed_time']:.3f}s")
            print(f"   缓存加速比: {cache_result['cache_speedup']:.1f}x")
        else:
            print("   ❌ 缓存测试失败")
        
        # 3. 获取缓存统计
        print(f"\n📈 缓存统计信息")
        cache_stats = await self.get_cache_stats()
        
        if "cache_stats" in cache_stats:
            stats = cache_stats["cache_stats"]
            print(f"   总缓存条目: {stats.get('total_entries', 0)}")
            print(f"   有效条目: {stats.get('valid_entries', 0)}")
            print(f"   过期条目: {stats.get('expired_entries', 0)}")
            print(f"   缓存TTL: {stats.get('cache_ttl', 0)}s")
        else:
            print(f"   ❌ 无法获取缓存统计: {cache_stats.get('error', '未知错误')}")
        
        print(f"\n✅ 性能测试完成！")


async def main():
    """主函数"""
    tester = RecommendationPerformanceTest()
    await tester.run_full_test()


if __name__ == "__main__":
    asyncio.run(main())
