import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2, Wifi, WifiOff } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useWebSocket, WSStatus } from '@/hooks/useWebSocket';
import { useSession } from '@/lib/auth-client';

interface WebSocketStatusProps {
  className?: string;
}

export function WebSocketStatus({ className }: WebSocketStatusProps) {
  const { data: session } = useSession();
  const user = session?.user;

  const wsStatus = useWebSocket({
    userId: user?.id,
    organizationId: user?.organizationId,
    enabled: !!user?.id && !!user?.organizationId,
  });

  const getStatusConfig = (status: WSStatus) => {
    switch (status) {
      case 'idle':
        return {
          icon: WifiOff,
          label: '未连接',
          variant: 'secondary' as const,
          description: 'WebSocket未初始化',
        };
      case 'connecting':
        return {
          icon: Loader2,
          label: '连接中',
          variant: 'default' as const,
          description: '正在建立WebSocket连接...',
          animate: true,
        };
      case 'open':
        return {
          icon: Wifi,
          label: '已连接',
          variant: 'default' as const,
          description: 'WebSocket连接正常，实时数据同步中',
        };
      case 'closed':
        return {
          icon: WifiOff,
          label: '已断开',
          variant: 'secondary' as const,
          description: 'WebSocket连接已断开，将自动重连',
        };
      case 'error':
        return {
          icon: AlertCircle,
          label: '连接错误',
          variant: 'destructive' as const,
          description: 'WebSocket连接出现错误，请检查网络或刷新页面',
        };
      default:
        return {
          icon: WifiOff,
          label: '未知状态',
          variant: 'secondary' as const,
          description: '未知的WebSocket状态',
        };
    }
  };

  const config = getStatusConfig(wsStatus);
  const Icon = config.icon;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge variant={config.variant} className={`flex items-center gap-1.5 ${className}`}>
            <Icon className={`h-3 w-3 ${config.animate ? 'animate-spin' : ''}`} />
            <span className="text-xs">{config.label}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <p className="font-medium">WebSocket状态</p>
            <p className="text-sm text-muted-foreground">{config.description}</p>
            {user && (
              <div className="text-xs text-muted-foreground mt-2 space-y-1">
                <p>用户ID: {user.id}</p>
                <p>组织ID: {user.organizationId || '未设置'}</p>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
