/**
 * 时效配置 DTO
 */
export interface TimeValidityDto {
  /** 时效配置ID */
  id: string;
  /** 时效标签名称 */
  label: string;
  /** 时效类型：fixed（固定时段）、daily（每日重复）、weekly（每周重复）、custom（自定义） */
  validityType: string;
  /** 固定时段起始时间（包含年月日时分秒） */
  startDateTime?: Date | null;
  /** 固定时段结束时间（包含年月日时分秒） */
  endDateTime?: Date | null;
  /** 每日/每周起始时分秒 */
  startTimeHHMMSS?: string | null;
  /** 每日/每周结束时分秒 */
  endTimeHHMMSS?: string | null;
  /** 每周重复时的配置：例如 ["周一", "周二"] */
  repeatWeekdays: string[];
  /** 自定义开始日期 */
  customStartDate?: Date | null;
  /** 自定义结束日期 */
  customEndDate?: Date | null;
  /** 每天的起始时间（时分秒） */
  customDailyStartTime?: string | null;
  /** 每天的结束时间（时分秒） */
  customDailyEndTime?: string | null;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 创建时效配置请求 DTO
 */
export interface CreateTimeValidityDto {
  /** 时效标签名称 */
  label: string;
  /** 时效类型：fixed（固定时段）、daily（每日重复）、weekly（每周重复）、custom（自定义） */
  validityType: string;
  /** 固定时段起始时间（包含年月日时分秒） */
  startDateTime?: Date;
  /** 固定时段结束时间（包含年月日时分秒） */
  endDateTime?: Date;
  /** 每日/每周起始时分秒 */
  startTimeHHMMSS?: string;
  /** 每日/每周结束时分秒 */
  endTimeHHMMSS?: string;
  /** 每周重复时的配置：例如 ["周一", "周二"] */
  repeatWeekdays?: string[];
  /** 自定义开始日期 */
  customStartDate?: Date;
  /** 自定义结束日期 */
  customEndDate?: Date;
  /** 每天的起始时间（时分秒） */
  customDailyStartTime?: string;
  /** 每天的结束时间（时分秒） */
  customDailyEndTime?: string;
}

/**
 * 更新时效配置请求 DTO
 */
export interface UpdateTimeValidityDto extends Partial<CreateTimeValidityDto> {
  /** 时效配置ID */
  id: string;
}
