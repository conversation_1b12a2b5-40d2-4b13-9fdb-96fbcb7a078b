/**
 * 问答知识库 DTO - 基本信息
 * 用于列表、详情等场景，包含问答的主字段和统计信息
 */
export interface QuestionAndAnswerDto {
  /** 问答ID */
  id: string;
  /** 问题类型 */
  questionType: string;
  /** 回答数组（必须字段）使用数组存储多个样本 */
  answers: string[];
  /** 分类编码，关联到KnowledgeCategory的code字段 */
  categoryCode: string;
  /** 订单状态，使用四位二进制字符串表示 */
  orderStatus: string;
  /** 常见问法样本数组 */
  commonQuestionSamples: string[];
  /** 店铺名 */
  shopName?: string | null;
  /** 店铺id */
  shopId?: string | null;
  /** 商品名称 */
  productName?: string | null;
  /** 商品链接 */
  productUrl?: string | null;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 是否已删除 */
  isDeleted: number;
}

/**
 * 问答知识库列表 DTO
 * 用于分页返回问答列表
 */
export interface QuestionAndAnswerListDto {
  /** 问答列表 */
  items: QuestionAndAnswerDto[];
  /** 总数 */
  total: number;
}

/**
 * 订单状态解析结果
 */
export interface OrderStatusDto {
  /** 原始状态码 */
  raw: string;
  /** 售前状态 */
  preSale: boolean;
  /** 发货前状态 */
  beforeShip: boolean;
  /** 发货后状态 */
  afterShip: boolean;
  /** 售后状态 */
  afterSale: boolean;
  /** 可读描述 */
  description: string;
}

/**
 * 问答知识库详细信息 DTO（包含关联的分类信息）
 */
export interface QuestionAndAnswerDetailDto extends QuestionAndAnswerDto {
  /** 分类详情 */
  category: {
    code: string;
    id: string;
    name: string;
    level: number;
    description: string | null;
  };
}

/**
 * 问答统计 DTO
 * 用于统计分析
 */
export interface QuestionAndAnswerStatsDto {
  /** 总问答数量 */
  total: number;
  /** 各问题类型统计 */
  typeStats: Array<{
    questionType: string;
    count: number;
  }>;
  /** 各分类统计 */
  categoryStats: Array<{
    categoryCode: string;
    categoryName: string;
    count: number;
  }>;
}

/**
 * 批量删除结果 DTO
 */
export interface BulkDeleteResultDto {
  /** 删除成功的数量 */
  deletedCount: number;
  /** 删除失败的ID列表 */
  failedIds: string[];
}

/**
 * 问答知识库搜索响应 DTO
 * 用于返回搜索结果
 */
export interface QuestionAndAnswerSearchResultDto {
  /** 匹配的回答数组 */
  answers: string[];
}
