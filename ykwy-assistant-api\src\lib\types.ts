import type { Prisma } from '@prisma/client';

// 通用响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  code?: string;
  details?: unknown;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export type ConversationWithDetails = Prisma.ConversationGetPayload<{
  include: {
    customer: true;
    platform: true;
    assignedUser: true;
    messages: {
      orderBy: {
        sentAt: 'asc';
      };
      take: 50;
    };
  };
}>;

export type MessageWithSender = Prisma.MessageGetPayload<{
  include: {
    sender: true;
    parentMessage: true;
    replies: true;
  };
}>;

export type CustomerWithQianniuAccount = Prisma.CustomerGetPayload<{
  include: {
    qianniuAccount: true;
  };
}>;
