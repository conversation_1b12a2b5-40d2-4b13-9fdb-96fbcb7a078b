import Bun from 'bun';
import { type Context, Hono } from 'hono';
import { cors } from 'hono/cors';
import { etag } from 'hono/etag';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import { timeout } from 'hono/timeout';

import { auth } from './middlewares/auth';
import { errorHandler } from './middlewares/errorHandler.ts';
import apiRouter from './router/router';
import { SchedulerService } from './services/schedulerService';

const app = new Hono();

app.use('*', etag(), logger());
app.use('*', prettyJSON());
app.use('*', cors());

// 针对文件上传路由使用更长的超时时间，其他路由使用全局20秒超时
app.use('*', async (c, next) => {
  const path = c.req.path;
  const isFileUploadRoute = path.endsWith('/export') || path.endsWith('/import') || path.endsWith('/batch-import');

  if (isFileUploadRoute) {
    // 文件上传路由使用更长超时（2分钟）
    return timeout(120 * 1000)(c, next);
  } else {
    // 其他路由使用20秒超时
    return timeout(20 * 1000)(c, next);
  }
});

app.use(secureHeaders());

// 全局认证中间件 - 排除登录和注册路由
app.use('*', async (c, next) => {
  const path = c.req.path;
  const shouldSkipAuth = path === '/' || path === '/api/v1/login' || path === '/api/v1/register' || path.startsWith('/api/v2/');
  if (shouldSkipAuth) {
    return next();
  }
  return auth()(c, next);
});

app.get('/', (c) => {
  return c.json({ msg: 'Welcome To Bun.js API!' });
});

// apiRouter.onError(errorHandler);
app.route('/api', apiRouter);
app.onError(errorHandler);
app.notFound((c: Context) => c.json({ msg: 'Not Found' }, 404));

// 启动定时任务服务
const schedulerService = new SchedulerService();
schedulerService.startAllTasks();

export default { port: Bun.env['PORT'] || 3000, fetch: app.fetch };
