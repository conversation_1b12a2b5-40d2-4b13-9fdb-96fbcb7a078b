// 千牛客户端相关 React Query Hooks

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { useOrganization } from '../../hooks/useOrganization';
import { authClient } from '../../lib/auth-client';
import { queryKeys } from '../../lib/query-keys';
import {
  deleteQianniuClientMutation,
  organizationQianniuClientsQueryOptions,
  qianniuClientQueryOptions,
  qianniuClientsQueryOptions,
  registerQianniuClientMutation,
  updateQianniuClientStatusMutation,
} from '../api/qianniuClients';
import type { QianniuClientFilters } from '../types';

// 获取千牛客户端列表
export const useQianniuClients = (filters: QianniuClientFilters = {}) => {
  const { data: session } = authClient.useSession();
  const { organizationId } = useOrganization();
  const user = session?.user as { id: string } | undefined;

  return useQuery({
    ...qianniuClientsQueryOptions({ ...filters, organizationId }),
    enabled: !!user?.id && !!organizationId,
  });
};

// 获取单个千牛客户端详情
export const useQianniuClient = (clientId?: string) => {
  return useQuery({
    ...qianniuClientQueryOptions(clientId || ''),
    enabled: !!clientId,
  });
};

// 获取组织的千牛客户端
export const useOrganizationQianniuClients = () => {
  const { data: session } = authClient.useSession();
  const { organizationId } = useOrganization();
  const user = session?.user as { id: string } | undefined;

  return useQuery({
    ...organizationQianniuClientsQueryOptions(organizationId || ''),
    enabled: !!user?.id && !!organizationId,
  });
};

// 注册千牛客户端
export const useRegisterQianniuClient = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...registerQianniuClientMutation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.qianniuClients() });
    },
  });
};

// 更新千牛客户端状态
export const useUpdateQianniuClientStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...updateQianniuClientStatusMutation,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.qianniuClients() });
      // 如果有具体的客户端ID，也invalidate该客户端的详情查询
      if (variables.clientId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.qianniuClient(variables.clientId) });
      }
    },
  });
};

// 删除千牛客户端
export const useDeleteQianniuClient = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...deleteQianniuClientMutation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.qianniuClients() });
    },
  });
};
