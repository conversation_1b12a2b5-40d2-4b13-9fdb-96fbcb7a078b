import { QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Card, Input, Table, Tabs, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useState } from 'react';

const { TabPane } = Tabs;

// 问法模糊问题数据接口
interface VagueQuestionItem {
  key: string;
  questions: string[];
  suggestedQuestion: string;
  originalVoice: string[];
  trainingCount: number;
}

// 模拟数据
const vagueQuestionItems: VagueQuestionItem[] = [
  {
    key: '1',
    questions: ['可以加急发货一下吗'],
    suggestedQuestion: '【行业场景】催发货',
    originalVoice: ['1. 可以着急发货一下吗', '2. 加急发快一点吗,我急用'],
    trainingCount: 3,
  },
  {
    key: '2',
    questions: ['这个颜色什么时候能到货吗？'],
    suggestedQuestion: '【行业场景】什么时候有货',
    originalVoice: ['1. 你好 紫色什么时候到货', '2. 这款什么时候有现货'],
    trainingCount: 3,
  },
];

const VagueQuestions: React.FC = () => {
  const [activeTab, setActiveTab] = useState('pending');
  const [searchValue, setSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 待审核表格列定义
  const pendingColumns: ColumnsType<VagueQuestionItem> = [
    {
      title: '问法模糊的高频问题',
      dataIndex: 'questions',
      key: 'questions',
      width: '25%',
      render: (questions: string[]) => (
        <div className="space-y-1" data-oid="85fswu-">
          {questions.map((question, index) => (
            <div
              key={index}
              className="text-sm text-gray-700"
              data-oid="1fcdqmd"
            >
              {question}
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '当前识别到的场景',
      dataIndex: 'suggestedQuestion',
      key: 'suggestedQuestion',
      width: '20%',
      render: (text: string) => (
        <span className="text-sm text-blue-600" data-oid="9_a6e_z">
          {text}
        </span>
      ),
    },
    {
      title: '买家原声',
      dataIndex: 'originalVoice',
      key: 'originalVoice',
      width: '30%',
      render: (voices: string[]) => (
        <div className="space-y-1" data-oid="g.y-dve">
          {voices.map((voice, index) => (
            <div
              key={index}
              className="text-sm text-gray-700"
              data-oid=":zc1nbu"
            >
              {voice}
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '咨询热度',
      dataIndex: 'trainingCount',
      key: 'trainingCount',
      width: '15%',
      render: (count: number) => (
        <span className="text-sm text-gray-600" data-oid="twn6fft">
          {count}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: '10%',
      align: 'center',
      render: () => (
        <div className="flex gap-2" data-oid="vo18ge4">
          <Button
            type="link"
            className="text-blue-500 p-0 text-sm"
            data-oid="3jws1u6"
          >
            通过
          </Button>
          <Button
            type="link"
            className="text-blue-500 p-0 text-sm"
            data-oid="a4g2orx"
          >
            识别效果调整
          </Button>
          <Button
            type="link"
            className="text-blue-500 p-0 text-sm"
            data-oid="_oclari"
          >
            忽略
          </Button>
        </div>
      ),
    },
  ];

  // 已通过表格列定义（去掉操作列）
  const approvedColumns: ColumnsType<VagueQuestionItem> = [
    {
      title: '问法模糊的高频问题',
      dataIndex: 'questions',
      key: 'questions',
      width: '30%',
      render: (questions: string[]) => (
        <div className="space-y-1" data-oid="5fc.713">
          {questions.map((question, index) => (
            <div
              key={index}
              className="text-sm text-gray-700"
              data-oid="i76n2c1"
            >
              {question}
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '当前识别到的场景',
      dataIndex: 'suggestedQuestion',
      key: 'suggestedQuestion',
      width: '25%',
      render: (text: string) => (
        <span className="text-sm text-blue-600" data-oid="1fcu-su">
          {text}
        </span>
      ),
    },
    {
      title: '买家原声',
      dataIndex: 'originalVoice',
      key: 'originalVoice',
      width: '30%',
      render: (voices: string[]) => (
        <div className="space-y-1" data-oid="rj7gbxd">
          {voices.map((voice, index) => (
            <div
              key={index}
              className="text-sm text-gray-700"
              data-oid="looz_hx"
            >
              {voice}
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '咨询热度',
      dataIndex: 'trainingCount',
      key: 'trainingCount',
      width: '11%',
      render: (count: number) => (
        <span className="text-sm text-gray-600" data-oid="k883rmh">
          {count}
        </span>
      ),
    },
  ];

  // 根据当前标签页获取对应的表格配置
  const getTableConfig = () => {
    switch (activeTab) {
      case 'pending':
        return {
          columns: pendingColumns,
          dataSource: vagueQuestionItems,
        };
      case 'approved':
        return {
          columns: approvedColumns,
          dataSource: vagueQuestionItems, // 这里可以用不同的数据源
        };
      default:
        return {
          columns: pendingColumns,
          dataSource: vagueQuestionItems,
        };
    }
  };

  return (
    <div className="px-4 md:px-6 pb-6" data-oid=":ek0qxx">
      <Card data-oid="rtdzpd9">
        {/* 标签页和搜索，包含右上角提示图标 */}
        <div
          className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-4"
          data-oid="u_i8_lv"
        >
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            className="[&_.ant-tabs-tab]:px-4 [&_.ant-tabs-tab]:py-2 [&_.ant-tabs-tab]:text-sm [&_.ant-tabs-tab-active]:text-blue-500 [&_.ant-tabs-ink-bar]:bg-blue-500 [&_.ant-tabs-ink-bar]:h-0.5"
            data-oid="nzopsud"
          >
            <TabPane tab="待处理" key="pending" data-oid="rg6t-th" />
            <TabPane tab="已通过" key="approved" data-oid="xlnn437" />
          </Tabs>

          <div className="flex items-center gap-3" data-oid=".:74h9v">
            <Input
              placeholder="输入人员问题"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              prefix={
                <SearchOutlined className="text-gray-400" data-oid=".cr52.g" />
              }
              className="w-full md:w-64"
              allowClear
              data-oid="lknkg-a"
            />

            <Tooltip
              title="待审核和已通过列表仅保留近 3 个月的数据"
              data-oid="i.pgemr"
            >
              <QuestionCircleOutlined
                className="text-gray-400 text-base cursor-help"
                data-oid="6avqke4"
              />
            </Tooltip>
          </div>
        </div>

        {/* 问题表格 */}
        <div data-oid="d.ff:i:">
          <Table
            columns={getTableConfig().columns as any}
            dataSource={getTableConfig().dataSource}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: getTableConfig().dataSource.length,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size || 10);
              },
              onShowSizeChange: (current, size) => {
                setCurrentPage(1);
                setPageSize(size);
              },
              pageSizeOptions: ['10', '20', '50', '100'],
              responsive: true,
            }}
            className="[&_.ant-table-thead>tr>th]:bg-gray-50 [&_.ant-table-thead>tr>th]:font-medium [&_.ant-table-thead>tr>th]:text-gray-700 [&_.ant-table-tbody>tr:hover>td]:bg-blue-50"
            size="middle"
            bordered={false}
            scroll={{ x: 'max-content', y: 'calc(100vh - 500px)' }}
            data-oid="5mj8k7w"
          />
        </div>
      </Card>
    </div>
  );
};

export default VagueQuestions;
