// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/","redirect":"/home","parentId":"ant-design-pro-layout","id":"1"},"2":{"name":"登录","path":"/login","layout":false,"id":"2"},"3":{"name":"首页","path":"","layout":false,"parentId":"4","id":"3","originPath":"/home"},"4":{"path":"/home","isWrapper":true,"layout":false,"id":"4"},"5":{"name":"店铺首页","path":"","parentId":"6","id":"5","originPath":"/shop/shop-home"},"6":{"path":"/shop/shop-home","isWrapper":true,"parentId":"ant-design-pro-layout","id":"6"},"7":{"name":"问答","path":"","parentId":"8","id":"7","originPath":"/shop/question"},"8":{"path":"/shop/question","isWrapper":true,"parentId":"ant-design-pro-layout","id":"8"},"9":{"name":"问答知识库","path":"/shop/question/knowledge","parentId":"7","id":"9"},"10":{"name":"自动学习","path":"/shop/question/auto-learning","parentId":"7","id":"10"},"11":{"name":"精准意图","path":"/shop/question/intent","parentId":"7","id":"11"},"12":{"name":"活动管理","path":"/shop/question/activity","parentId":"7","id":"12"},"13":{"name":"商品知识库","path":"","parentId":"14","id":"13","originPath":"/shop/product"},"14":{"path":"/shop/product","isWrapper":true,"parentId":"ant-design-pro-layout","id":"14"},"15":{"name":"商品列表","path":"/shop/product/list-simple","parentId":"13","id":"15"},"16":{"name":"尺码表","path":"/shop/product/size-table-simple","parentId":"13","id":"16"},"17":{"name":"智能跟单","path":"","parentId":"18","id":"17","originPath":"/shop/smart-orders"},"18":{"path":"/shop/smart-orders","isWrapper":true,"parentId":"ant-design-pro-layout","id":"18"},"19":{"name":"跟单任务管理","path":"/shop/smart-orders/order-management","parentId":"17","id":"19"},"20":{"name":"发货受限地址","path":"/shop/smart-orders/shipping-restricted-addresses","parentId":"17","id":"20"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__Login__index" */'@/pages/Login/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__Home__index" */'@/pages/Home/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "components__ProtectedRoute" */'@/components/ProtectedRoute.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__Shop__ShopHome" */'@/pages/Shop/ShopHome.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "components__ProtectedRoute" */'@/components/ProtectedRoute.tsx')),
'7': React.lazy(() => import('./EmptyRoute')),
'8': React.lazy(() => import(/* webpackChunkName: "components__ProtectedRoute" */'@/components/ProtectedRoute.tsx')),
'9': React.lazy(() => import(/* webpackChunkName: "p__Shop__Question__Knowledge" */'@/pages/Shop/Question/Knowledge.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__Shop__Question__AutoLearning" */'@/pages/Shop/Question/AutoLearning.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__Shop__Question__Intent" */'@/pages/Shop/Question/Intent.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__Shop__Question__Activity" */'@/pages/Shop/Question/Activity.tsx')),
'13': React.lazy(() => import('./EmptyRoute')),
'14': React.lazy(() => import(/* webpackChunkName: "components__ProtectedRoute" */'@/components/ProtectedRoute.tsx')),
'15': React.lazy(() => import(/* webpackChunkName: "p__Shop__Product__ListSimple" */'@/pages/Shop/Product/ListSimple.tsx')),
'16': React.lazy(() => import(/* webpackChunkName: "p__Shop__Product__SizeTableSimple" */'@/pages/Shop/Product/SizeTableSimple.tsx')),
'17': React.lazy(() => import('./EmptyRoute')),
'18': React.lazy(() => import(/* webpackChunkName: "components__ProtectedRoute" */'@/components/ProtectedRoute.tsx')),
'19': React.lazy(() => import(/* webpackChunkName: "p__Shop__SmartOrders__OrderManagement" */'@/pages/Shop/SmartOrders/OrderManagement.tsx')),
'20': React.lazy(() => import(/* webpackChunkName: "p__Shop__SmartOrders__ShippingRestrictedAddresses" */'@/pages/Shop/SmartOrders/ShippingRestrictedAddresses.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "umi__plugin-layout__Layout" */'C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/.umi/plugin-layout/Layout.tsx')),
},
  };
}
