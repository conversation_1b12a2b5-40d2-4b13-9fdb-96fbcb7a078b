version: '3.8'

services:
  # 数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: ykwy-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=ykwy-assistant
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./logs/postgres:/var/log/postgresql
    ports:
      - "5432:5432"
    networks:
      - ykwy-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  ykwy-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
