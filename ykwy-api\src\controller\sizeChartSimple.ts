import { PrismaClient } from '@prisma/client';
import Bun from 'bun';
import type { Context } from 'hono';

import redis from '../client/redis.js';
import { SizeChartSimpleService } from '../services/sizeChartSimpleService.js';
import {
  createSizeChartSimpleSchema,
  deleteSizeChartSimpleSchema,
  getSizeChartSimpleByIdSchema,
  getSizeChartSimpleListSchema,
  updateSizeChartSimpleSchema,
} from '../types/validators/sizeChartSimpleValidator.js';
import { removeScanKeys } from '../utils/cache.js';
import { R } from '../utils/Response.js';

const prisma = new PrismaClient();
const sizeChartSimpleService = new SizeChartSimpleService(prisma);

/**
 * SizeChartSimple 控制器
 */
export class SizeChartSimpleController {
  /**
   * 检查名称是否已存在
   */
  static async checkNameUnique(c: Context) {
    try {
      const query = c.req.query();
      const { name, excludeId } = query;

      if (!name || name.trim() === '') {
        return R.fail(c, '名称不能为空', 400);
      }

      const exists = await sizeChartSimpleService.isNameExists(name, excludeId);

      return R.success(c, { exists, available: !exists }, exists ? '名称已存在' : '名称可用');
    } catch (error: unknown) {
      console.error('检查名称唯一性失败:', error);
      return R.fail(c, '检查名称唯一性失败', 500);
    }
  }

  /**
   * 创建简单尺码表（支持upsert）
   */
  static async createSizeChartSimple(c: Context) {
    try {
      const body = await c.req.json();
      const validatedData = createSizeChartSimpleSchema.parse(body);

      // 先检查是否已存在相同名称的记录
      const existingRecord = await sizeChartSimpleService.isNameExists(validatedData.name);
      const isUpdate = existingRecord;

      const sizeChartSimple = await sizeChartSimpleService.createSizeChartSimple(validatedData);

      // 清除相关缓存
      await removeScanKeys(redis, 'sizechart-simple:list:');
      if (isUpdate) {
        // 如果是更新，也清除详情缓存
        await redis.del(`sizechart-simple:detail:${sizeChartSimple.id}`);
      }

      const message = isUpdate ? '更新简单尺码表成功' : '创建简单尺码表成功';
      return R.success(c, sizeChartSimple, message);
    } catch (error: unknown) {
      console.error('创建/更新简单尺码表失败:', error);

      // 处理特定的业务错误
      if (error instanceof Error) {
        if (error.message.includes('validation')) {
          return R.fail(c, '输入数据验证失败', 400);
        }
      }

      return R.fail(c, '创建简单尺码表失败', 500);
    }
  }

  /**
   * 根据ID获取简单尺码表
   */
  static async getSizeChartSimpleById(c: Context) {
    try {
      const id = c.req.param('id');
      const { id: validatedId } = getSizeChartSimpleByIdSchema.parse({ id });

      // 构建缓存键
      const rdsKey = `sizechart-simple:detail:${validatedId}`;
      const cacheString = await redis.get(rdsKey);

      // 如果有缓存先在缓存中查询
      if (cacheString) {
        const result = JSON.parse(cacheString);
        return R.success(c, result, '获取简单尺码表成功');
      }

      const sizeChartSimple = await sizeChartSimpleService.getSizeChartSimpleById(validatedId);

      if (!sizeChartSimple) {
        return R.notFound(c, '简单尺码表不存在');
      }

      // 加入缓存
      await redis.set(rdsKey, JSON.stringify(sizeChartSimple), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

      return R.success(c, sizeChartSimple, '获取简单尺码表成功');
    } catch (error: unknown) {
      console.error('获取简单尺码表失败:', error);
      return R.fail(c, '获取简单尺码表失败', 500);
    }
  }

  /**
   * 获取简单尺码表列表
   */
  static async getSizeChartSimpleList(c: Context) {
    try {
      const query = c.req.query();
      const validatedQuery = getSizeChartSimpleListSchema.parse(query);

      // 构建缓存键
      const { page, limit, name, sizeRange, sizeValue } = validatedQuery;
      const rdsKey = `sizechart-simple:list:${page || 1}-${limit || 10}-${name || ''}-${sizeRange || ''}-${sizeValue || ''}`;
      const cacheString = await redis.get(rdsKey);

      // 如果有缓存先在缓存中查询
      if (cacheString) {
        const result = JSON.parse(cacheString);
        return R.success(c, result, '获取简单尺码表列表成功');
      }

      const result = await sizeChartSimpleService.getSizeChartSimpleList(validatedQuery);

      // 加入缓存
      await redis.set(rdsKey, JSON.stringify(result), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

      return R.success(c, result, '获取简单尺码表列表成功');
    } catch (error: unknown) {
      console.error('获取简单尺码表列表失败:', error);
      return R.fail(c, '获取简单尺码表列表失败', 500);
    }
  }

  /**
   * 更新简单尺码表
   */
  static async updateSizeChartSimple(c: Context) {
    try {
      const id = c.req.param('id');
      const { id: validatedId } = getSizeChartSimpleByIdSchema.parse({ id });
      const body = await c.req.json();
      const validatedData = updateSizeChartSimpleSchema.parse(body);

      const sizeChartSimple = await sizeChartSimpleService.updateSizeChartSimple(validatedId, validatedData);

      // 清除相关缓存
      await redis.del(`sizechart-simple:detail:${validatedId}`);
      await removeScanKeys(redis, 'sizechart-simple:list:');

      return R.success(c, sizeChartSimple, '更新简单尺码表成功');
    } catch (error: unknown) {
      console.error('更新简单尺码表失败:', error);

      // 处理特定的业务错误
      if (error instanceof Error) {
        if (error.message.includes('名称已存在')) {
          return R.fail(c, error.message, 400);
        }
        if (error.message.includes('不存在')) {
          return R.notFound(c, error.message);
        }
        if (error.message.includes('validation')) {
          return R.fail(c, '输入数据验证失败', 400);
        }
      }

      return R.fail(c, '更新简单尺码表失败', 500);
    }
  }

  /**
   * 删除简单尺码表
   */
  static async deleteSizeChartSimple(c: Context) {
    try {
      const id = c.req.param('id');
      const { id: validatedId } = deleteSizeChartSimpleSchema.parse({ id });

      await sizeChartSimpleService.deleteSizeChartSimple(validatedId);

      // 清除相关缓存
      await redis.del(`sizechart-simple:detail:${validatedId}`);
      await removeScanKeys(redis, 'sizechart-simple:list:');

      return R.success(c, null, '删除简单尺码表成功');
    } catch (error: unknown) {
      console.error('删除简单尺码表失败:', error);
      return R.fail(c, '删除简单尺码表失败', 500);
    }
  }
}
