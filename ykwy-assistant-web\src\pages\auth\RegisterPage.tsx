import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import AuthCard from '../../components/auth/AuthCard';
import ErrorAlert from '../../components/auth/ErrorAlert';
import FormField from '../../components/auth/FormField';
import SuccessAlert from '../../components/auth/SuccessAlert';
import { authClient } from '../../lib/auth-client';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<{ [key: string]: string }>({});

  const navigate = useNavigate();

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      errors.name = '请输入您的姓名';
    } else if (formData.name.trim().length < 2) {
      errors.name = '姓名至少需要2个字符';
    }

    if (!formData.email) {
      errors.email = '请输入邮箱地址';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    if (!formData.password) {
      errors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      errors.password = '密码至少需要6个字符';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])/.test(formData.password)) {
      errors.password = '密码需要包含大小写字母';
    }

    if (!formData.confirmPassword) {
      errors.confirmPassword = '请确认密码';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = '两次输入的密码不一致';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (fieldErrors[field]) {
      setFieldErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const result = await authClient.signUp.email({
        email: formData.email,
        password: formData.password,
        name: formData.name.trim(),
      });

      if (result.data?.accessToken) {
        setSuccess(true);
      } else {
        setError(result.error?.message || '注册失败，请稍后重试');
      }
    } catch (err: unknown) {
      console.error('Register error:', err);
      setError('注册失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleGoToLogin = () => {
    navigate('/auth/login', {
      state: { email: formData.email },
      replace: true,
    });
  };

  if (success) {
    return (
      <AuthCard title="注册成功" subtitle="欢迎加入我们的平台">
        <SuccessAlert
          title="账户创建成功！"
          message="您现在可以使用刚才注册的邮箱和密码登录您的账户。"
          action={{
            text: '前往登录',
            onClick: handleGoToLogin,
          }}
        />

        <div className="mt-6 text-center">
          <span className="text-sm text-gray-600">
            或者{' '}
            <Link to="/" className="font-medium text-indigo-600 hover:text-indigo-500">
              返回首页
            </Link>
          </span>
        </div>
      </AuthCard>
    );
  }

  return (
    <AuthCard title="创建账户" subtitle="加入我们，开始您的旅程">
      <form onSubmit={handleSubmit} className="space-y-6">
        {error && <ErrorAlert error={error} onDismiss={() => setError('')} />}

        <FormField
          id="name"
          label="姓名"
          type="text"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          placeholder="输入您的姓名"
          error={fieldErrors.name}
          autoComplete="name"
          autoFocus
        />

        <FormField
          id="email"
          label="邮箱地址"
          type="email"
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          placeholder="输入您的邮箱地址"
          error={fieldErrors.email}
          autoComplete="email"
        />

        <FormField
          id="password"
          label="密码"
          type="password"
          value={formData.password}
          onChange={(e) => handleInputChange('password', e.target.value)}
          placeholder="至少6个字符，包含大小写字母"
          error={fieldErrors.password}
          autoComplete="new-password"
        />

        <FormField
          id="confirmPassword"
          label="确认密码"
          type="password"
          value={formData.confirmPassword}
          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
          placeholder="再次输入密码"
          error={fieldErrors.confirmPassword}
          autoComplete="new-password"
        />

        <button
          type="submit"
          disabled={loading}
          className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          {loading ? (
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              注册中...
            </div>
          ) : (
            '创建账户'
          )}
        </button>

        <div className="text-center">
          <span className="text-sm text-gray-600">
            已有账户？{' '}
            <Link to="/auth/login" className="font-medium text-indigo-600 hover:text-indigo-500">
              立即登录
            </Link>
          </span>
        </div>
      </form>
    </AuthCard>
  );
}
