import { Prisma } from '@prisma/client';

import prisma from '../client/prisma.ts';
import { ErrorCode } from '../constants/errorCodes.ts';
import { AppError } from '../errors/custom.error.ts';
import type { BulkCreateResultDto, BulkDeleteResultDto, ShippingRestrictedAreaDto, ShippingRestrictedAreaListDto } from '../types/dto/shippingRestrictedArea';
import type { ShippingRestrictedAreaInput, ShippingRestrictedAreaQuery } from '../types/request/shippingRestrictedArea';

// 添加批量导入结果接口
export interface BatchImportResult {
  successCount: number;
  failCount: number;
  newCount: number;
  updatedCount: number;
  errors?: string[];
}

/**
 * 发货受限地址服务层，负责与数据库的交互
 */
export class ShippingRestrictedAreaService {
  /**
   * 创建或更新发货受限地址
   * @param data 发货受限地址输入参数
   */
  async upsert(data: ShippingRestrictedAreaInput): Promise<ShippingRestrictedAreaDto> {
    const { province, city, district, isActive } = data;

    // 构建更新/创建数据对象
    const areaData = {
      province,
      city,
      district,
      isActive,
    };

    try {
      // 使用province、city、district组合唯一键进行upsert
      return await prisma.shippingRestrictedArea.upsert({
        where: {
          province_city_district: {
            province,
            city,
            district,
          },
        },
        update: { isActive },
        create: areaData,
      });
    } catch (error) {
      // 捕获Prisma唯一约束错误
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        const target = error.meta?.['target'];
        if (Array.isArray(target) && target.includes('province') && target.includes('city') && target.includes('district')) {
          throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
            message: `该地址已存在: ${province}-${city}-${district}，请勿重复添加`,
          });
        }
      }
      throw error;
    }
  }

  /**
   * 根据ID获取发货受限地址
   * @param id 发货受限地址ID
   */
  async findById(id: string): Promise<ShippingRestrictedAreaDto | null> {
    return await prisma.shippingRestrictedArea.findUnique({
      where: { id },
    });
  }

  /**
   * 获取发货受限地址列表（支持分页、过滤）
   * @param params 查询参数
   */
  async findMany(params: ShippingRestrictedAreaQuery): Promise<ShippingRestrictedAreaListDto> {
    const { skip, take, province, city, district, searchText, isActive } = params;

    // 构建查询条件
    const where: Prisma.ShippingRestrictedAreaWhereInput = {};

    // 如果有searchText，则同时搜索省/市/县区
    if (searchText && searchText.trim()) {
      where.OR = [{ province: { contains: searchText, mode: 'insensitive' } }, { city: { contains: searchText, mode: 'insensitive' } }, { district: { contains: searchText, mode: 'insensitive' } }];
    } else {
      // 否则使用单独的字段搜索
      if (province) {
        where.province = { contains: province, mode: 'insensitive' };
      }

      if (city) {
        where.city = { contains: city, mode: 'insensitive' };
      }

      if (district) {
        where.district = { contains: district, mode: 'insensitive' };
      }
    }

    if (typeof isActive === 'boolean') {
      where.isActive = isActive;
    }

    // 执行查询
    const [items, total] = await Promise.all([
      prisma.shippingRestrictedArea.findMany({
        where,
        skip,
        take,
        orderBy: [{ province: 'asc' }, { city: 'asc' }, { district: 'asc' }],
      }),
      prisma.shippingRestrictedArea.count({ where }),
    ]);

    return { items, total };
  }

  /**
   * 批量创建发货受限地址
   * @param data 发货受限地址数组
   */
  async bulkCreate(data: ShippingRestrictedAreaInput[]): Promise<BulkCreateResultDto> {
    try {
      const createManyInput = data.map((item) => ({
        province: item.province,
        city: item.city,
        district: item.district,
        isActive: item.isActive || false,
      }));

      const result = await prisma.shippingRestrictedArea.createMany({
        data: createManyInput,
        skipDuplicates: true,
      });

      return {
        successCount: result.count,
        failCount: data.length - result.count,
      };
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '批量创建发货受限地址失败',
        detail: error instanceof Error ? error.message : '未知错误',
      });
    }
  }

  /**
   * 更新发货受限地址状态
   * @param id 发货受限地址ID
   * @param isActive 是否受限
   */
  async updateStatus(id: string, isActive: boolean): Promise<ShippingRestrictedAreaDto> {
    try {
      return await prisma.shippingRestrictedArea.update({
        where: { id },
        data: { isActive },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
          message: '发货受限地址不存在或已被删除',
        });
      }
      throw error;
    }
  }

  /**
   * 批量更新发货受限地址状态
   * @param ids 发货受限地址ID数组
   * @param isActive 是否受限
   */
  async bulkUpdateStatus(ids: string[], isActive: boolean): Promise<{ count: number }> {
    try {
      const result = await prisma.shippingRestrictedArea.updateMany({
        where: {
          id: { in: ids },
        },
        data: { isActive },
      });

      return { count: result.count };
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '批量更新发货受限地址状态失败',
        detail: error instanceof Error ? error.message : '未知错误',
      });
    }
  }

  /**
   * 删除单个发货受限地址
   * @param id 发货受限地址ID
   */
  async delete(id: string): Promise<ShippingRestrictedAreaDto> {
    try {
      return await prisma.shippingRestrictedArea.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
          message: '发货受限地址不存在或已被删除',
        });
      }
      throw error;
    }
  }

  /**
   * 批量删除发货受限地址
   * @param ids 发货受限地址ID数组
   */
  async bulkDelete(ids: string[]): Promise<BulkDeleteResultDto> {
    try {
      const result = await prisma.shippingRestrictedArea.deleteMany({
        where: {
          id: { in: ids },
        },
      });

      return {
        count: result.count,
      };
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '批量删除发货受限地址失败',
        detail: error instanceof Error ? error.message : '未知错误',
      });
    }
  }

  /**
   * 检查地址是否受限
   * @param province 省份
   * @param city 城市
   * @param district 县/区
   */
  async checkRestriction(province: string, city: string, district: string): Promise<boolean> {
    const area = await prisma.shippingRestrictedArea.findFirst({
      where: {
        province,
        city,
        district,
        isActive: true,
      },
    });

    return !!area;
  }

  /**
   * 清空所有发货受限地址
   */
  async truncate(): Promise<boolean> {
    try {
      await prisma.shippingRestrictedArea.deleteMany({});
      return true;
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '清空发货受限地址失败',
        detail: error instanceof Error ? error.message : '未知错误',
      });
    }
  }

  /**
   * 循环分页查询所有发货受限地址数据（用于RagFlow同步）
   */
  async findAllFromShippingRestrictedArea(): Promise<ShippingRestrictedAreaDto[]> {
    const allData: ShippingRestrictedAreaDto[] = [];
    const pageSize = 1000; // 每次查询1000条
    let skip = 0;
    let hasMore = true;

    while (hasMore) {
      const batch = await prisma.shippingRestrictedArea.findMany({
        skip,
        take: pageSize,
        orderBy: [{ province: 'asc' }, { city: 'asc' }, { district: 'asc' }],
      });

      allData.push(...batch);

      if (batch.length < pageSize) {
        hasMore = false;
      } else {
        skip += pageSize;
      }
    }

    return allData;
  }

  /**
   * 批量导入发货受限地址
   * @param importList 导入数据数组
   */
  async batchImport(importList: ShippingRestrictedAreaInput[]): Promise<BatchImportResult> {
    const errors: string[] = [];
    let successCount = 0;
    const failCount = 0;
    let newCount = 0;
    let updatedCount = 0;

    try {
      // 获取现有记录
      const existingRecords = await prisma.shippingRestrictedArea.findMany({
        where: {
          OR: importList.map((item) => ({
            province: item.province,
            city: item.city,
            district: item.district,
          })),
        },
      });

      const existingMap = new Map(existingRecords.map((record) => [`${record.province}-${record.city}-${record.district}`, record]));

      // 分离新增和更新的记录
      const createOperations: Prisma.ShippingRestrictedAreaCreateManyInput[] = [];
      const updateOperations: Prisma.Prisma__ShippingRestrictedAreaClient<ShippingRestrictedAreaDto>[] = [];

      for (const item of importList) {
        const key = `${item.province}-${item.city}-${item.district}`;
        const existingRecord = existingMap.get(key);

        if (existingRecord) {
          // 更新现有记录
          if (existingRecord.isActive !== (item.isActive || false)) {
            updateOperations.push(
              prisma.shippingRestrictedArea.update({
                where: { id: existingRecord.id },
                data: { isActive: item.isActive || false },
              }),
            );
            updatedCount++;
          }
        } else {
          // 创建新记录
          createOperations.push({
            province: item.province,
            city: item.city,
            district: item.district,
            isActive: item.isActive || false,
          });
          newCount++;
        }
      }

      // 执行批量操作
      if (createOperations.length > 0) {
        await prisma.shippingRestrictedArea.createMany({
          data: createOperations,
          skipDuplicates: true,
        });
      }

      if (updateOperations.length > 0) {
        await Promise.all(updateOperations);
      }

      successCount = newCount + updatedCount;

      return {
        successCount,
        failCount,
        newCount,
        updatedCount,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      console.error('批量导入失败:', error);
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '批量导入发货受限地址失败',
        detail: error instanceof Error ? error.message : '未知错误',
      });
    }
  }
}
