generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

/// 核心模型：存储商品的核心信息
model Product {
  /// 主键，商品ID，使用 UUID 保证全局唯一 (必须字段)
  id                     String                    @id @default(uuid())
  /// 商品名称 (必须字段)
  title                  String
  /// 商品链接或id (必须字段)，例如1688链接或其他平台的商品标识
  sourceUrl              String                    @map("source_url")
  /// 商品状态 (必选字段): true=已上架, false=未上架, null=未设置状态
  status                 Boolean                   @map("status")
  /// 商品分类 (可选)
  category               String?                   @map("category")
  /// 型号 (可选)
  model                  String?                   @map("model")
  /// 商品标签，使用 JSON 数组存储 (可选)
  tags                   String[]                  @map("tags")
  /// 100146-平台
  platform100146         String?                   @map("platform_100146")
  /// 162887-平台
  platform162887         String?                   @map("platform_162887")
  /// 180243-平台
  platform180243         String?                   @map("platform_180243")
  /// 上市时间-平台
  platformLaunchTime     String?                   @map("platform_launch_time")
  /// 功能-平台
  platformFunction       String?                   @map("platform_function")
  /// 厚度-平台
  platformThickness      String?                   @map("platform_thickness")
  /// 图案-平台
  platformPattern        String?                   @map("platform_pattern")
  /// 插片-平台
  platformInsert         String?                   @map("platform_insert")
  /// 材质-平台
  platformMaterial       String?                   @map("platform_material")
  /// 款式-平台
  platformStyle          String?                   @map("platform_style")
  /// 流行元素-平台
  platformTrendElement   String?                   @map("platform_trend_element")
  /// 版型-平台
  platformFit            String?                   @map("platform_fit")
  /// 穿着方式-平台
  platformWearingMethod  String?                   @map("platform_wearing_method")
  /// 类别-平台
  platformCategory       String?                   @map("platform_category")
  /// 罩杯款式-平台
  platformCupStyle       String?                   @map("platform_cup_style")
  /// 肩带-平台
  platformShoulder       String?                   @map("platform_shoulder")
  /// 衣长-平台
  platformClothingLength String?                   @map("platform_clothing_length")
  /// 衣门襟-平台
  platformClothingFront  String?                   @map("platform_clothing_front")
  /// 袖长-平台
  platformSleeveLength   String?                   @map("platform_sleeve_length")
  /// 裤长-平台
  platformPantsLength    String?                   @map("platform_pants_length")
  /// 裤门襟-平台
  platformPantsFront     String?                   @map("platform_pants_front")
  /// 适用人群-平台
  platformTargetGroup    String?                   @map("platform_target_group")
  /// 适用季节-平台
  platformSeason         String?                   @map("platform_season")
  /// 适用性别-平台
  platformGender         String?                   @map("platform_gender")
  /// 适用运动-平台
  platformSport          String?                   @map("platform_sport")
  /// 里料材质-平台
  platformLiningMaterial String?                   @map("platform_lining_material")
  /// 面料-平台
  platformFabric         String?                   @map("platform_fabric")
  /// 领型-平台
  platformCollarType     String?                   @map("platform_collar_type")
  /// 记录创建时间
  createdAt              DateTime                  @default(now()) @map("created_at")
  /// 记录最后更新时间，由 Prisma 自动管理
  updatedAt              DateTime                  @updatedAt @map("updated_at")
  /// 逻辑删除标志 (0: 未删除, 1: 已删除)
  isDeleted              Int                       @default(0) @map("is_deleted")
  identifiers            ProductIdentifier[]
  sizeChartBindings      ProductSizeChartBinding[]

  @@map("products")
}

/// 辅助模型：存储商品的多种外部或内部编码
model ProductIdentifier {
  /// 主键，使用自增 BigInt
  id             BigInt  @id @default(autoincrement())
  /// 编码的类型，例如 'SUPPLIER_SKU', 'INTERNAL_CODE', 'BARCODE'
  identifierType String  @map("identifier_type")
  /// 编码的具体值
  value          String
  /// 外键，关联到 Product 模型
  productId      String  @map("product_id")
  product        Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, identifierType])
  @@index([identifierType, value])
  @@map("product_identifiers")
}

/// 关联模型：用于实现 Product 和 SizeChart 之间的多对多关系（显式定义）
model ProductSizeChartBinding {
  /// 外键，关联到 Product 模型
  productId   String    @map("product_id")
  /// 外键，关联到 SizeChart 模型
  sizeChartId String    @map("size_chart_id")
  /// (可选) 您可以在这个关系上添加额外的字段，例如记录分配时间
  assignedAt  DateTime  @default(now()) @map("assigned_at")
  product     Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
  sizeChart   SizeChart @relation(fields: [sizeChartId], references: [id], onDelete: Cascade)

  @@id([productId, sizeChartId])
  @@map("product_size_chart_bindings")
}

model SizeChartType {
  id           String        @id @default(uuid())
  name         String
  xAxis        String
  yAxis        String?
  desc         String?
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")
  isDeleted    Int           @default(0) @map("is_deleted")
  sizeCharts   SizeChart[]
  subTableDefs SubTableDef[]

  @@map("size_chart_types")
}

model SubTableDef {
  id        String        @id @default(uuid())
  typeId    String        @map("type_id")
  name      String
  key       String
  schema    Json
  createdAt DateTime      @default(now()) @map("created_at")
  updatedAt DateTime      @updatedAt @map("updated_at")
  isDeleted Int           @default(0) @map("is_deleted")
  type      SizeChartType @relation(fields: [typeId], references: [id], onDelete: Cascade)

  @@unique([typeId, key])
  @@map("sub_table_defs")
}

model SizeChart {
  id                      String                    @id @default(uuid())
  name                    String
  typeId                  String                    @map("type_id")
  isComposite             Boolean                   @default(false)
  thresholdRecommendation String
  parentId                String?                   @map("parent_id")
  groupName               String?                   @map("group_name")
  sortOrder               Int                       @default(0) @map("sort_order")
  createdAt               DateTime                  @default(now()) @map("created_at")
  updatedAt               DateTime                  @updatedAt @map("updated_at")
  isDeleted               Int                       @default(0) @map("is_deleted")
  ProductSizeChartBinding ProductSizeChartBinding[]
  entries                 SizeChartEntry[]
  subEntries              SizeChartSubEntry[]
  parent                  SizeChart?                @relation("CompositeChart", fields: [parentId], references: [id])
  children                SizeChart[]               @relation("CompositeChart")
  type                    SizeChartType             @relation(fields: [typeId], references: [id], onDelete: Cascade)

  @@index([typeId])
  @@index([parentId])
  @@map("size_charts_new")
}

model SizeChartEntry {
  id        String    @id @default(uuid())
  chartId   String    @map("chart_id")
  xValue    String    @map("x_value")
  yValue    String?   @map("y_value")
  size      String
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  chart     SizeChart @relation(fields: [chartId], references: [id], onDelete: Cascade)

  @@unique([chartId, xValue, yValue])
  @@index([chartId])
  @@map("size_chart_entries")
}

model SizeChartSubEntry {
  id          String    @id @default(uuid())
  chartId     String    @map("chart_id")
  subTableKey String    @map("sub_table_key")
  data        Json
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  chart       SizeChart @relation(fields: [chartId], references: [id], onDelete: Cascade)

  @@index([chartId, subTableKey])
  @@map("size_chart_sub_entries")
}

model KnowledgeCategory {
  /// 主键，分类ID
  id                              String                           @id @default(uuid())
  /// 分类名称，例如："开始语"、"图片识别"等
  name                            String
  /// 分类编码，使用点号分隔的层级编码，例如："1"、"1.1"、"1.1.1"
  code                            String                           @unique
  /// 分类级别：1=大分类，2=子分类，3=子子分类
  level                           Int
  /// 排序号，用于控制同级分类的显示顺序
  sortOrder                       Int                              @default(0) @map("sort_order")
  /// 分类描述
  description                     String?
  /// 是否启用：true=启用，false=禁用
  isActive                        Boolean                          @default(true) @map("is_active")
  /// 父分类ID，大分类为null
  parentId                        String?                          @map("parent_id")
  /// 创建时间
  createdAt                       DateTime                         @default(now()) @map("created_at")
  /// 更新时间
  updatedAt                       DateTime                         @updatedAt @map("updated_at")
  /// 逻辑删除标志：0=未删除，1=已删除
  isDeleted                       Int                              @default(0) @map("is_deleted")
  parent                          KnowledgeCategory?               @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children                        KnowledgeCategory[]              @relation("CategoryHierarchy")
  questionAndAnswers              QuestionAndAnswer[]
  questionAndAnswerKnowledgeBases QuestionAndAnswerKnowledgeBase[]

  @@index([level, sortOrder])
  @@index([parentId])
  @@index([code])
  @@map("knowledge_categories")
}

model TimeValidity {
  id           String @id @default(uuid()) // 主键，唯一ID
  label        String @map("label") // 时效标签名称
  validityType String @map("validity_type") // 时效类型：fixed（固定时段）、daily（每日重复）、weekly（每周重复）、custom（自定义）

  // 固定时段的配置
  startDateTime DateTime? @map("start_date_time") // 起始时间yy-mm-dd hh:mm:ss
  endDateTime   DateTime? @map("end_date_time") // 结束时间yy-mm-dd hh:mm:ss

  // 对于每日和每周的配置，时分秒
  startTimeHHMMSS String? @map("start_time_hhmmss") // 每日/每周起始时分秒
  endTimeHHMMSS   String? @map("end_time_hhmmss") // 每日/每周结束时分秒

  // 每周重复时的配置
  repeatWeekdays String[] @map("repeat_weekdays") // 例如 ["周一", "周二"]

  // 自定义重复时段的配置：开始日期、结束日期，和日期范围内的每天起止时间
  customStartDate      DateTime? @map("custom_start_date") // 自定义开始日期
  customEndDate        DateTime? @map("custom_end_date") // 自定义结束日期
  customDailyStartTime String?   @map("custom_daily_start_time") // 每天的起始时间（时分秒）
  customDailyEndTime   String?   @map("custom_daily_end_time") // 每天的结束时间（时分秒）

  // 关联到 QuestionAndAnswerKnowledgeBase 表
  questionAndAnswer QuestionAndAnswerKnowledgeBase[] @relation("TimeValidity_QuestionAndAnswer") // 一对多关联

  createdAt DateTime @default(now()) @map("created_at") // 创建时间
  updatedAt DateTime @updatedAt @map("updated_at") // 更新时间

  @@index([validityType]) // 为时效类型字段创建索引，提高查询性能
  @@map("time_validity")
}

model QuestionAndAnswerKnowledgeBase {
  id           String @id @default(uuid())
  questionType String @map("question_type")
  orderStatus  String @map("order_status") @db.Char(4)
  categoryCode String @map("category_code")

  /// 常见问法样本（可以为空）
  commonQuestionSamples String[] @map("common_question_samples")

  /// 是否为自定义问答（true=用户添加，false=行业标准）
  isCustom Boolean @default(false) @map("is_custom")

  /// 匹配方式：full=整句，keyword=关键词，regex=正则
  matchMode String @default("full") @map("match_mode")

  /// 是否永久有效（true 时忽略时间限制）
  isPermanent Boolean @default(true) @map("is_permanent")

  /// 商品相关
  productId String? @map("product_id")

  /// 店铺ID（必填）
  shopId String? @map("shop_id") // 必填字段

  /// 系统字段
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  isDeleted Int      @default(0) @map("is_deleted")

  /// 分类关系
  category KnowledgeCategory @relation(fields: [categoryCode], references: [code])

  /// 多条回答
  answers Answer[]

  /// 时效配置（必须）
  timeValidity   TimeValidity @relation("TimeValidity_QuestionAndAnswer", fields: [timeValidityId], references: [id])
  timeValidityId String       @map("time_validity_id") // 外键字段

  Shop    Shop?        @relation(fields: [shopId], references: [id])
  product TempProduct? @relation(fields: [productId], references: [id])

  @@unique([questionType, orderStatus, categoryCode])
  @@index([categoryCode])
  @@index([productId])
  @@index([isPermanent])
  @@map("question_and_answer_knowledge_base")
}

model Answer {
  id String @id @default(uuid())

  /// 回答内容
  content String

  /// 第几次回复（1=首次，2=第二次…）
  replyOrder Int @default(1)

  /// 所属问答ID
  questionAndAnswerId String @map("qa_id")

  /// 所属问答关联
  questionAndAnswer QuestionAndAnswerKnowledgeBase @relation(fields: [questionAndAnswerId], references: [id])

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([questionAndAnswerId, replyOrder])
  @@index([questionAndAnswerId])
  @@map("answers")
}

model Shop {
  id        String   @id @default(uuid()) // 主键，唯一ID
  name      String // 店铺名称
  createdAt DateTime @default(now()) @map("created_at") // 创建时间
  updatedAt DateTime @updatedAt @map("updated_at") // 更新时间
  isActive  Boolean  @default(true) @map("is_active") // 是否启用
  isDeleted Int      @default(0) @map("is_deleted")

  // tempProducts                    TempProduct[] // 关联到多个商品
  questionAndAnswerKnowledgeBases QuestionAndAnswerKnowledgeBase[]

  @@map("shops")
}

/// 临时商品模型：用于存储临时导入的商品信息
model TempProduct {
  /// 主键，商品ID，使用 UUID 保证全局唯一 (必须字段)
  id                             String                           @id @default(uuid())
  /// 商品名称 (必须字段)
  name                           String                           @map("name")
  /// 商品链接或id (必须字段)，例如淘宝链接或其他平台的商品标识
  linkOrId                       String                           @map("link_or_id")
  /// 商品ID (必须字段)，原始的商品ID，设置为唯一约束
  productId                      String                           @unique @map("product_id")
  /// 商品状态 (必须字段)，例如：已上架、未上架等
  status                         String                           @map("status")
  /// 商品图片链接 (可选字段)
  imageUrl                       String?                          @map("image_url")
  /// 记录创建时间
  createdAt                      DateTime                         @default(now()) @map("created_at")
  /// 记录最后更新时间，由 Prisma 自动管理
  updatedAt                      DateTime                         @updatedAt @map("updated_at")
  /// 逻辑删除标志 (0: 未删除, 1: 已删除)
  isDeleted                      Int                              @default(0) @map("is_deleted")
  /// 货号/款号 (可选字段)
  styleNumber                    String?                          @map("style_number")
  /// 描述 (可选字段)
  description                    Json?                            @map("description")
  /// 店铺ID
  shopId                         String?                          @map("shop_id")
  // Shop                           Shop                             @relation(fields: [shopId], references: [id])
  QuestionAndAnswerKnowledgeBase QuestionAndAnswerKnowledgeBase[]

  @@map("temp_products")
}

/// 晓多问答知识库模型：存储问答对信息
model QuestionAndAnswer {
  /// 主键，使用 UUID 保证全局唯一
  id                    String   @id @default(uuid())
  /// 问题类型
  questionType          String   @map("question_type")
  /// 订单状态
  orderStatus           String   @map("order_status") @db.Char(4)
  /// 分类编码，关联到KnowledgeCategory的code字段
  categoryCode          String   @map("category_code")
  /// 常见问法样本，使用数组存储多个样本
  commonQuestionSamples String[] @map("common_question_samples")
  /// 系统字段
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  isDeleted             Int      @default(0) @map("is_deleted")
  /// 回答（必须字段）使用数组存储多个样本
  answers               String[] @map("answers")

  /// 商品名称
  productName String? @map("product_name")
  /// 商品链接
  productUrl  String? @map("product_url")
  /// 店铺id
  shopId      String? @map("shop_id")
  /// 预留字段
  /// 店铺名
  shopName    String? @map("shop_name")

  category KnowledgeCategory @relation(fields: [categoryCode], references: [code])

  @@unique([questionType, orderStatus, categoryCode])
  @@index([categoryCode])
  @@map("question_and_answers")
}

/// 简单尺码表模型：存储基础尺码信息
model SizeChartSimple {
  /// 主键，使用 UUID 保证全局唯一
  id        String   @id @default(uuid())
  /// 名称
  name      String   @unique @map("name")
  /// 尺码范围
  sizeRange String   @map("size_range")
  /// 尺码值
  sizeValue String   @map("size_value")
  /// 记录创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// 记录最后更新时间，由 Prisma 自动管理
  updatedAt DateTime @updatedAt @map("updated_at")
  /// 逻辑删除标志 (0: 未删除, 1: 已删除)
  isDeleted Int      @default(0) @map("is_deleted")

  @@map("size_chart_simple")
}

/// 复合简单尺码表模型：在简单尺码表基础上增加类型字段
model CompositeSizeChartSimple {
  /// 主键，使用 UUID 保证全局唯一
  id        String   @id @default(uuid())
  /// 名称
  name      String   @unique @map("name")
  /// 类型
  type      String   @map("type")
  /// 尺码范围
  sizeRange String   @map("size_range")
  /// 尺码值
  sizeValue String   @map("size_value")
  /// 记录创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// 记录最后更新时间，由 Prisma 自动管理
  updatedAt DateTime @updatedAt @map("updated_at")
  /// 逻辑删除标志 (0: 未删除, 1: 已删除)
  isDeleted Int      @default(0) @map("is_deleted")

  @@map("composite_size_chart_simple")
}

/// 导入导出任务模型：用于记录商品数据的导入导出任务
model ImportExportTask {
  /// 主键，使用 UUID 保证全局唯一
  id        String   @id @default(uuid())
  /// 任务名称 (必须字段)
  taskName  String   @map("task_name")
  /// 任务类型 (必须字段): import=导入, export=导出
  taskType  String   @map("task_type")
  /// 任务时间 (必须字段)
  taskTime  DateTime @map("task_time")
  /// 文件名 (必须字段)
  fileName  String   @map("file_name")
  /// 任务状态 (必须字段): pending=进行中, success=成功, failed=失败
  status    String   @default("pending") @map("status")
  /// 任务结果 (可选字段)，JSON格式存储结果详情
  result    Json?    @map("result")
  /// 文件路径 (可选字段)
  filePath  String?  @map("file_path")
  /// 记录创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// 记录最后更新时间，由 Prisma 自动管理
  updatedAt DateTime @updatedAt @map("updated_at")
  /// 逻辑删除标志 (0: 未删除, 1: 已删除)
  isDeleted Int      @default(0) @map("is_deleted")

  @@map("import_export_tasks")
}

/// 用户模型：存储用户基本信息
model User {
  /// 主键，自增ID
  id        String   @id @default(uuid())
  /// 用户邮箱，唯一
  email     String   @unique
  /// 哈希加密后的密码
  password  String
  /// 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("users")
}

/// 发货受限地址：存储发货受限的地址信息
model ShippingRestrictedArea {
  /// 主键，使用 UUID 保证全局唯一
  id        String   @id @default(uuid())
  /// 省份/直辖市
  province  String   @map("province")
  /// 城市
  city      String   @map("city")
  /// 县/区
  district  String   @map("district")
  /// 是否受限：true=受限，false=不受限
  isActive  Boolean  @default(false) @map("is_active")
  /// 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([province, city, district])
  @@map("shipping_restricted_areas")
}

/// 场景Q&A
model SceneQA {
  /// 主键，使用 UUID 保证全局唯一
  id         String   @id @default(uuid())
  /// 问题
  question   String   @map("question")
  /// 答案
  answer     String   @map("answer")
  /// 关键词
  keywords   String[] @map("keywords")
  /// 意图
  intent     String   @map("intent")
  /// 商品链接
  productUrl String?  @map("product_url")
  /// 店铺id
  shopId     String?  @map("shop_id")
  /// 创建时间
  createdAt  DateTime @default(now()) @map("created_at")
  /// 更新时间
  updatedAt  DateTime @updatedAt @map("updated_at")

  @@unique([question, intent])
  @@map("scene_qa")
}
