import { PrismaClient } from '@prisma/client';
import Bun from 'bun';
import type { Context } from 'hono';

import redis from '../client/redis.js';
import { CompositeSizeChartSimpleService } from '../services/compositeSizeChartSimpleService.js';
import {
  createCompositeSizeChartSimpleSchema,
  deleteCompositeSizeChartSimpleSchema,
  getCompositeSizeChartSimpleByIdSchema,
  getCompositeSizeChartSimpleListSchema,
  updateCompositeSizeChartSimpleSchema,
} from '../types/validators/compositeSizeChartSimpleValidator.js';
import { removeScanKeys } from '../utils/cache.js';
import { R } from '../utils/Response.js';

const prisma = new PrismaClient();
const compositeSizeChartSimpleService = new CompositeSizeChartSimpleService(prisma);

/**
 * CompositeSizeChartSimple 控制器
 */
export class CompositeSizeChartSimpleController {
  /**
   * 检查名称是否已存在
   */
  static async checkNameUnique(c: Context) {
    try {
      const query = c.req.query();
      const { name, excludeId } = query;

      if (!name || name.trim() === '') {
        return R.fail(c, '名称不能为空', 400);
      }

      const exists = await compositeSizeChartSimpleService.isNameExists(name, excludeId);

      return R.success(c, { exists, available: !exists }, exists ? '名称已存在' : '名称可用');
    } catch (error: unknown) {
      console.error('检查名称唯一性失败:', error);
      return R.fail(c, '检查名称唯一性失败', 500);
    }
  }

  /**
   * 创建复合简单尺码表（支持upsert）
   */
  static async createCompositeSizeChartSimple(c: Context) {
    try {
      const body = await c.req.json();
      const validatedData = createCompositeSizeChartSimpleSchema.parse(body);

      // 先检查是否已存在相同名称的记录
      const existingRecord = await compositeSizeChartSimpleService.isNameExists(validatedData.name);
      const isUpdate = existingRecord;

      const compositeSizeChartSimple = await compositeSizeChartSimpleService.createCompositeSizeChartSimple(validatedData);

      // 清除相关缓存
      await removeScanKeys(redis, 'composite-sizechart-simple:list:');
      if (isUpdate) {
        // 如果是更新，也清除详情缓存
        await redis.del(`composite-sizechart-simple:detail:${compositeSizeChartSimple.id}`);
      }

      const message = isUpdate ? '更新复合简单尺码表成功' : '创建复合简单尺码表成功';
      return R.success(c, compositeSizeChartSimple, message);
    } catch (error: unknown) {
      console.error('创建/更新复合简单尺码表失败:', error);

      // 处理特定的业务错误
      if (error instanceof Error) {
        if (error.message.includes('validation')) {
          return R.fail(c, '输入数据验证失败', 400);
        }
      }

      return R.fail(c, '创建复合简单尺码表失败', 500);
    }
  }

  /**
   * 根据ID获取复合简单尺码表
   */
  static async getCompositeSizeChartSimpleById(c: Context) {
    try {
      const id = c.req.param('id');
      const { id: validatedId } = getCompositeSizeChartSimpleByIdSchema.parse({ id });

      // 构建缓存键
      const rdsKey = `composite-sizechart-simple:detail:${validatedId}`;
      const cacheString = await redis.get(rdsKey);

      // 如果有缓存先在缓存中查询
      if (cacheString) {
        const result = JSON.parse(cacheString);
        return R.success(c, result, '获取复合简单尺码表成功');
      }

      const compositeSizeChartSimple = await compositeSizeChartSimpleService.getCompositeSizeChartSimpleById(validatedId);

      if (!compositeSizeChartSimple) {
        return R.notFound(c, '复合简单尺码表不存在');
      }

      // 加入缓存
      await redis.set(rdsKey, JSON.stringify(compositeSizeChartSimple), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

      return R.success(c, compositeSizeChartSimple, '获取复合简单尺码表成功');
    } catch (error: unknown) {
      console.error('获取复合简单尺码表失败:', error);
      return R.fail(c, '获取复合简单尺码表失败', 500);
    }
  }

  /**
   * 获取复合简单尺码表列表
   */
  static async getCompositeSizeChartSimpleList(c: Context) {
    try {
      const query = c.req.query();
      const validatedQuery = getCompositeSizeChartSimpleListSchema.parse(query);

      // 构建缓存键
      const { page, limit, name, type, sizeRange, sizeValue } = validatedQuery;
      const rdsKey = `composite-sizechart-simple:list:${page || 1}-${limit || 10}-${name || ''}-${type || ''}-${sizeRange || ''}-${sizeValue || ''}`;
      const cacheString = await redis.get(rdsKey);

      // 如果有缓存先在缓存中查询
      if (cacheString) {
        const result = JSON.parse(cacheString);
        return R.success(c, result, '获取复合简单尺码表列表成功');
      }

      const result = await compositeSizeChartSimpleService.getCompositeSizeChartSimpleList(validatedQuery);

      // 加入缓存
      await redis.set(rdsKey, JSON.stringify(result), 'EX', Bun.env['REDIS_CACHE_EXPIRE']!);

      return R.success(c, result, '获取复合简单尺码表列表成功');
    } catch (error: unknown) {
      console.error('获取复合简单尺码表列表失败:', error);
      return R.fail(c, '获取复合简单尺码表列表失败', 500);
    }
  }

  /**
   * 更新复合简单尺码表
   */
  static async updateCompositeSizeChartSimple(c: Context) {
    try {
      const id = c.req.param('id');
      const { id: validatedId } = getCompositeSizeChartSimpleByIdSchema.parse({ id });
      const body = await c.req.json();
      const validatedData = updateCompositeSizeChartSimpleSchema.parse(body);

      const compositeSizeChartSimple = await compositeSizeChartSimpleService.updateCompositeSizeChartSimple(validatedId, validatedData);

      // 清除相关缓存
      await redis.del(`composite-sizechart-simple:detail:${validatedId}`);
      await removeScanKeys(redis, 'composite-sizechart-simple:list:');

      return R.success(c, compositeSizeChartSimple, '更新复合简单尺码表成功');
    } catch (error: unknown) {
      console.error('更新复合简单尺码表失败:', error);

      // 处理特定的业务错误
      if (error instanceof Error) {
        if (error.message.includes('名称已存在')) {
          return R.fail(c, error.message, 400);
        }
        if (error.message.includes('不存在')) {
          return R.notFound(c, error.message);
        }
        if (error.message.includes('validation')) {
          return R.fail(c, '输入数据验证失败', 400);
        }
      }

      return R.fail(c, '更新复合简单尺码表失败', 500);
    }
  }

  /**
   * 删除复合简单尺码表
   */
  static async deleteCompositeSizeChartSimple(c: Context) {
    try {
      const id = c.req.param('id');
      const { id: validatedId } = deleteCompositeSizeChartSimpleSchema.parse({ id });

      await compositeSizeChartSimpleService.deleteCompositeSizeChartSimple(validatedId);

      // 清除相关缓存
      await redis.del(`composite-sizechart-simple:detail:${validatedId}`);
      await removeScanKeys(redis, 'composite-sizechart-simple:list:');

      return R.success(c, null, '删除复合简单尺码表成功');
    } catch (error: unknown) {
      console.error('删除复合简单尺码表失败:', error);
      return R.fail(c, '删除复合简单尺码表失败', 500);
    }
  }
}
