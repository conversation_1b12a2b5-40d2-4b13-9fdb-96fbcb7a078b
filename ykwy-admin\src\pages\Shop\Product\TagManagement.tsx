import AddTagModal from '@/components/Product/AddTagModal';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Card, Checkbox, Input, Table } from 'antd';
import React, { useState } from 'react';

interface TagManagementProps {
  onBack: () => void;
}

const columns = [
  {
    title: <Checkbox className="ml-2" data-oid="7f8:io1" />, // 全选
    dataIndex: 'checkbox',
    width: 60,
    render: () => <Checkbox data-oid="ap7_prs" />,
  },
  {
    title: '标签名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '已关联商品',
    dataIndex: 'related',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    render: () => null,
  },
];

const data: any[] = [];

const TagManagement: React.FC<TagManagementProps> = ({ onBack }) => {
  const [addModalVisible, setAddModalVisible] = useState(false);
  return (
    <div className="bg-[#f6f8fa] min-h-screen p-0" data-oid="-zos354">
      <AddTagModal
        visible={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onSave={(tagName) => {
          console.log('保存标签:', tagName);
          setAddModalVisible(false);
        }}
        data-oid="3dfp00s"
      />

      {/* 顶部返回和标题 */}
      <div className="flex items-center pt-6 pl-8" data-oid="u:jh3z9">
        <ArrowLeftOutlined
          className="text-xl mr-3 cursor-pointer"
          onClick={onBack}
          data-oid="humxclq"
        />

        <span className="text-xl font-semibold" data-oid="30e93fg">
          商品标签管理
        </span>
        <div className="flex-1" data-oid="j._yth_" />
        <Button
          className="mr-4 bg-[#f5f5f5] text-[#bfbfbf] border-none"
          disabled
          data-oid="jdnj83k"
        >
          批量删除
        </Button>
        <Button
          type="primary"
          className="mr-8"
          onClick={() => setAddModalVisible(true)}
          data-oid="9b_tql9"
        >
          添加商品标签
        </Button>
      </div>
      {/* 说明卡片 */}
      <Card
        className="mx-6 mt-6 bg-[#fff7e6] border-none rounded-lg"
        bodyStyle={{
          padding: 24,
          display: 'flex',
          alignItems: 'flex-start',
          position: 'relative',
        }}
        data-oid="ec38q43"
      >
        <img
          src="http://cdn.pdd.myjjing.com/static/sizeRule/size_rule_tip.png"
          alt="icon"
          className="w-12 h-12 mr-5"
          data-oid="9afp4bz"
        />

        <div data-oid="y7xmcf6">
          <div className="font-semibold text-base mb-1" data-oid="ou1oy_f">
            商品标签是什么？有什么作用？
          </div>
          <div
            className="text-[#222] text-sm leading-[22px]"
            data-oid="o9kh:9n"
          >
            商品标签可以用来给商品打上标记，在配置机器人回复的时候就可以根据标签去快速配置话术。
            <br data-oid="x82rg2p" />
            如对于所有包邮的商品，都可以打上【包邮】的标签，在配置是否包邮的答案时，则可以直接对所有带【包邮】标签的商品设置统一的话术，提高效率
          </div>
        </div>
        <div
          className="absolute top-6 right-6 text-xl text-[#bfbfbf] cursor-pointer"
          data-oid="7t5zj08"
        >
          ×
        </div>
      </Card>
      {/* 搜索和表格区 */}
      <div className="bg-white mx-6 mt-6 rounded-lg p-0" data-oid="ze:s:cj">
        <div className="px-6 pb-4" data-oid="brv.3mz">
          <Input
            placeholder="请输入关键字搜索"
            className="w-[260px]"
            data-oid="9rzau8r"
          />
        </div>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          locale={{
            emptyText: (
              <div className="text-center py-10" data-oid="m:gsnk0">
                <img
                  src="https://img.alicdn.com/imgextra/i2/O1CN01Qn1ytF1Ck8Q4lYt6w_!!6000000000422-2-tps-120-96.png"
                  alt="暂无数据"
                  className="w-20 mb-2"
                  data-oid="7qf7k55"
                />

                <div className="text-[#888] text-sm" data-oid="v.ht4m4">
                  暂无数据
                </div>
              </div>
            ),
          }}
          className="min-h-60 rounded-lg"
          data-oid="eutq:tq"
        />
      </div>
    </div>
  );
};

export default TagManagement;
