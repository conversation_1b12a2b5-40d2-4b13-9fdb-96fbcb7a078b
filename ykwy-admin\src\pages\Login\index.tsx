import background from '@/assets/image/jpg/bg.jpg';
import LoginForm from '@/components/Login/LoginForm';
import { login, register } from '@/services/user';
import { useNavigate } from '@umijs/max';
import { Tabs, message } from 'antd';
import Cookies from 'js-cookie';
import React, { useEffect, useState } from 'react';
import type { UserStore } from '../../store/userStore';
import useStore from '../../store/userStore';
import logger from '../../utils/logger';
import './login.css';

export const layout = false;
const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [tab, setTab] = useState('login');
  const [initialValues, setInitialValues] = useState<{
    email: string;
    password: string;
    remember: boolean;
  }>({ email: '', password: '', remember: false });
  const navigate = useNavigate();
  const setUserInfo = useStore((s: UserStore) => s.setUserInfo);

  // 页面加载时从Cookie读取保存的账号
  useEffect(() => {
    const savedEmail = Cookies.get('saved_email') || '';
    const remember = !!savedEmail;
    setInitialValues({ email: savedEmail, password: '', remember });
  }, []);

  const onFinish = async (values: {
    email: string;
    password: string;
    remember: boolean;
  }) => {
    setLoading(true);
    try {
      if (tab === 'login') {
        const res = await login(values);

        setUserInfo(res.data.user);
        Cookies.set('access_token', res.data.accessToken, {
          expires: 7,
          secure: process.env.NODE_ENV === 'production',
        });

        // 记住我状态下只保存邮箱到Cookie，不保存密码
        if (values.remember) {
          Cookies.set('saved_email', values.email, { expires: 3 });
        } else {
          // 未勾选时清除保存的账号
          Cookies.remove('saved_email');
        }
        // 设置用户ID到日志系统
        logger.setUserId(res.data.user.id.toString());

        message.success('登录成功');
        navigate('/home');
      } else {
        await register(values);
        message.success('注册成功，请登录');
        setTab('login');
      }
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : '操作失败';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const items = [
    {
      key: 'login',
      label: '登录',
      children: (
        <LoginForm
          onFinish={onFinish}
          buttonText="登录"
          loading={loading}
          initialValues={initialValues}
        />
      ),
    },
    {
      key: 'register',
      label: '注册',
      children: (
        <LoginForm
          onFinish={onFinish}
          buttonText="注册"
          loading={loading}
          initialValues={{ email: '', password: '', remember: false }}
        />
      ),
    },
  ];

  return (
    <div className="relative h-screen w-full overflow-hidden">
      <div
        className="relative h-full bg-cover bg-center"
        style={{ backgroundImage: `url(${background})` }}
      >
        <div className="absolute inset-0 flex items-center p-4">
          <div className="login-container">
            <div className="bg-white rounded-lg shadow-xl p-[clamp(1rem,3vw,2rem)] max-w-md min-h-[clamp(300px,50vh,500px)] transition-all duration-300">
              <h1 className="text-black text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-center mb-8">
                易康无忧智能客服
              </h1>
              <Tabs activeKey={tab} onChange={setTab} centered items={items} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
