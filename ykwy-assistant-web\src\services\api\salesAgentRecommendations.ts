// 销售智能体推荐 API 服务

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';

// AI推荐查询选项
export const aiRecommendationsQueryOptions = (conversationId: string, enabled = true) => ({
  queryKey: queryKeys.aiRecommendations(conversationId), // 只使用conversationId，保持稳定
  queryFn: async () => {
    try {
<<<<<<< HEAD
      const response = await apiClient.get<{ recommendations: string[] }>(`salesAgent/recommendations/conversation/${conversationId}`, {
        timeout: 1000 * 60 * 5, // 5分钟超时，给AI足够时间
      });
      return response.recommendations || [];
    } catch (error) {
      console.error('AI推荐API调用失败:', error);
      // 返回默认推荐而不是抛出错误
      return [
        '感谢您的咨询，我们会尽快为您处理。如有其他问题，请随时联系我们。',
        '您好，很高兴为您服务。请问还有什么我可以帮助您的吗？',
        '我们的专业团队会为您提供最优质的服务，请问您对我们的产品有什么具体的问题吗？',
      ];
    }
  },
  enabled: !!conversationId && enabled,
  staleTime: 1000 * 60 * 2, // 2分钟缓存
  gcTime: 1000 * 60 * 5, // 5分钟垃圾回收
  refetchOnMount: false,
  refetchOnWindowFocus: false,
  retry: 1, // 只重试1次
  retryDelay: 2000, // 重试延迟2秒
=======
      const response = await apiClient.get<{ recommendations: string[] }>(`salesAgent/recommendations/conversation/${conversationId}`);
      return response.recommendations;
    } catch (error) {
      console.warn('推荐请求失败，返回默认推荐:', error);
      // 返回默认推荐而不是抛出错误，避免UI显示错误状态
      return ['感谢您的咨询，我来为您详细解答这个问题。', '我理解您的需求，让我为您提供专业的建议。', '根据您的情况，我来为您推荐最合适的解决方案。'];
    }
  },
  enabled: !!conversationId && enabled,
  staleTime: 1000 * 60 * 5, // 延长到5分钟，减少频繁请求
  gcTime: 1000 * 60 * 10, // 延长到10分钟
  refetchOnMount: false,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false, // 网络重连时不自动刷新
  retry: 1, // 只重试1次，避免长时间等待
  retryDelay: 1000, // 重试延迟1秒
  // 添加查询超时，避免长时间等待
  meta: {
    timeout: 10000, // 10秒超时
  },
>>>>>>> 67d5f2db4e01e6974c82f7db25d7d5ad928b5722
});
