import { request } from '@umijs/max';

const API_BASE_URL =
  (process.env.UMI_APP_API_URL || 'http://localhost:3009') + '/api/v1';

interface UserParams {
  email: string;
  password: string;
}

// 用户登录
export async function login(
  params: UserParams,
  options?: { [key: string]: any },
) {
  return request(`${API_BASE_URL}/login`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

// 用户注册
export async function register(
  params: UserParams,
  options?: { [key: string]: any },
) {
  return request(`${API_BASE_URL}/register`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

// 获取用户信息
export async function getUser(options?: { [key: string]: any }) {
  return request(`${API_BASE_URL}/info`, {
    method: 'GET',
    ...(options || {}),
  });
}
