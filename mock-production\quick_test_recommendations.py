#!/usr/bin/env python3
"""
快速测试推荐接口
"""
import requests
import json

def test_recommendations():
    """测试推荐接口"""
    url = "http://localhost:8000/recommendations"
    
    payload = {
        "conversation_history": [
            {"role": "customer", "content": "你们有什么产品？"},
            {"role": "customer", "content": "价格怎么样？"}
        ],
        "brand_id": "ykwy",
        "conversation_id": "test_123"
    }
    
    try:
        print("🔧 测试推荐接口...")
        print(f"URL: {url}")
        print(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, json=payload, timeout=10)
        
        print(f"\n📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功！")
            print(f"推荐数量: {len(result.get('recommendations', []))}")
            
            for i, rec in enumerate(result.get('recommendations', []), 1):
                print(f"{i}. {rec}")
        else:
            print("❌ 请求失败！")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_recommendations()
