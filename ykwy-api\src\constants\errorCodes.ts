import type { ContentfulStatusCode } from 'hono/utils/http-status';

export interface ErrorCodeType {
  code: string;
  message: string;
  status: ContentfulStatusCode;
}

export const ErrorCode = {
  // --- 通用错误 G: General ---
  G001_UNEXPECTED_ERROR: {
    code: 'G001',
    message: '系统发生意外错误',
    status: 500,
  },
  G002_VALIDATION_ERROR: { code: 'G002', message: '参数验证失败', status: 400 },

  // --- 认证相关错误 AUTH: Authentication ---
  AUTH_NO_CREDENTIALS: {
    code: 'AUTH001',
    message: '未提供认证信息',
    status: 401,
  },
  AUTH_JWT_FORMAT_ERROR: {
    code: 'AUTH002',
    message: 'JWT令牌格式错误',
    status: 401,
  },
  AUTH_JWT_INVALID: { code: 'AUTH003', message: '无效的JWT令牌', status: 401 },
  AUTH_JWT_EXPIRED: { code: 'AUTH004', message: 'JWT令牌已过期', status: 401 },
  AUTH_JWT_VERIFY_FAILED: {
    code: 'AUTH005',
    message: 'JWT令牌验证失败',
    status: 401,
  },
  AUTH_JWT_GENERATE_FAILED: {
    code: 'AUTH006',
    message: 'JWT令牌生成失败',
    status: 500,
  },
  AUTH_BASIC_FORMAT_ERROR: {
    code: 'AUTH007',
    message: 'Basic认证格式错误',
    status: 400,
  },
  AUTH_USER_NOT_FOUND: { code: 'AUTH008', message: '用户不存在', status: 401 },
  AUTH_PASSWORD_INCORRECT: {
    code: 'AUTH009',
    message: '密码错误',
    status: 401,
  },
  AUTH_UNSUPPORTED_STRATEGY: {
    code: 'AUTH010',
    message: '不支持的认证方式',
    status: 401,
  },
  AUTH_USER_ALREADY_EXISTS: {
    code: 'AUTH011',
    message: '用户已存在',
    status: 409,
  },
  AUTH_CONFIGURATION_ERROR: {
    code: 'AUTH012',
    message: '认证配置错误',
    status: 500,
  },
  AUTH_INVALID_TOKEN: {
    code: 'AUTH013',
    message: '无效的访问令牌',
    status: 401,
  },

  // --- 尺码表相关错误 SC: Size Chart ---
  SC001_IN_USE: {
    code: 'SC001',
    message: '无法删除：该尺码表仍被一个或多个商品使用',
    status: 409,
  },
  SC002_NOT_FOUND: {
    code: 'SC002',
    message: '指定的尺码表不存在或已被删除',
    status: 404,
  },
  SC003_CLONE_SOURCE_NOT_FOUND: {
    code: 'SC003',
    message: '用于复制的源尺码表不存在',
    status: 404,
  },
} as const;
