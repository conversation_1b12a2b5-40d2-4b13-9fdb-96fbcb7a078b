import { ChevronLeft, ChevronRight, Copy, Eye, MoreHorizontal, RefreshCw, Trash2 } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

import { type ConnectionInvitation, useConnectionInvitation, useConnectionInvitations, useRegenerateConnectionInvitation, useRevokeConnectionInvitation } from '../../services';
import { DeploymentInstructionsModal } from './DeploymentInstructionsModal';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const statusConfig = {
  PENDING: {
    label: '待激活',
    variant: 'secondary' as const,
    color: 'bg-yellow-100 text-yellow-800',
  },
  ACTIVATED: {
    label: '已激活',
    variant: 'default' as const,
    color: 'bg-green-100 text-green-800',
  },
  EXPIRED: {
    label: '已过期',
    variant: 'destructive' as const,
    color: 'bg-red-100 text-red-800',
  },
  REVOKED: {
    label: '已撤销',
    variant: 'outline' as const,
    color: 'bg-gray-100 text-gray-800',
  },
};

export function ConnectionInvitationList() {
  const [selectedInvitation, setSelectedInvitation] = useState<{
    invitation: { id: string; name: string };
    cdnUrl: string;
    deploymentInstructions: {
      method1: { title: string; command: string; description: string };
      method2: { title: string; steps: string[] };
      notes: string[];
    };
    team?: { name: string };
    status?: string;
  } | null>(null);
  const [showInstructions, setShowInstructions] = useState(false);
  const [selectedInvitationId, setSelectedInvitationId] = useState<string | null>(null);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 获取邀请列表
  const { data: invitations, isLoading } = useConnectionInvitations();

  // 获取选中邀请的详细信息
  const { data: invitationDetail } = useConnectionInvitation(selectedInvitationId || '');

  // 撤销邀请
  const revokeMutation = useRevokeConnectionInvitation();

  // 重新生成邀请
  const regenerateMutation = useRegenerateConnectionInvitation();

  // 处理撤销邀请
  const handleRevokeInvitation = (id: string) => {
    revokeMutation.mutate(id, {
      onSuccess: () => {
        toast.success('邀请已撤销');
      },
      onError: () => {
        toast.error('撤销邀请失败');
      },
    });
  };

  // 处理重新生成邀请
  const handleRegenerateInvitation = (id: string, data: { expiresInDays?: number } = {}, status?: string) => {
    const isActivated = status === 'ACTIVATED';
    const actionText = isActivated ? '脚本更新' : '邀请重新生成';

    regenerateMutation.mutate(
      { id, data },
      {
        onSuccess: () => {
          if (isActivated) {
            toast.success('脚本已更新！请重新执行部署命令来应用最新的脚本更新');
          } else {
            toast.success('邀请已重新生成');
          }
        },
        onError: () => {
          toast.error(`${actionText}失败`);
        },
      },
    );
  };

  const handleViewInstructions = (invitation: ConnectionInvitation) => {
    // 设置选中的邀请ID，触发详情查询
    setSelectedInvitationId(invitation.id);

    // 当详情数据加载完成后，设置选中的邀请并显示说明
    if (invitationDetail) {
      setSelectedInvitation(invitationDetail);
      setShowInstructions(true);
    }
  };

  // 当详情数据变化时，更新选中的邀请
  useEffect(() => {
    if (invitationDetail && selectedInvitationId) {
      setSelectedInvitation(invitationDetail);
      setShowInstructions(true);
    }
  }, [invitationDetail, selectedInvitationId]);

  const handleCopyId = (id: string) => {
    navigator.clipboard.writeText(id);
    toast.success('连接ID已复制');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 计算分页数据
  const paginatedData = useMemo(() => {
    if (!invitations?.data) return [];
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return invitations.data.slice(startIndex, endIndex);
  }, [invitations?.data, currentPage, pageSize]);

  const totalPages = Math.ceil((invitations?.data?.length || 0) / pageSize);
  const totalItems = invitations?.data?.length || 0;

  // 分页控制函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: string) => {
    setPageSize(Number(newPageSize));
    setCurrentPage(1); // 重置到第一页
  };

  if (isLoading) {
    return <div className="text-center py-4">加载中...</div>;
  }

  if (!invitations?.data?.length) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>暂无连接邀请</p>
        <p className="text-sm mt-1">点击上方按钮创建第一个连接邀请</p>
      </div>
    );
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>连接名称</TableHead>
            <TableHead>团队</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>创建时间</TableHead>
            <TableHead>过期时间</TableHead>
            <TableHead>操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {paginatedData.map((invitation) => (
            <TableRow key={invitation.id}>
              <TableCell>
                <div>
                  <div className="font-medium">{invitation.name}</div>
                  {invitation.description && <div className="text-sm text-gray-500">{invitation.description}</div>}
                </div>
              </TableCell>
              <TableCell>{invitation.team.name}</TableCell>
              <TableCell>
                <Badge variant={statusConfig[invitation.status].variant} className={statusConfig[invitation.status].color}>
                  {statusConfig[invitation.status].label}
                </Badge>
              </TableCell>
              <TableCell>{formatDate(invitation.createdAt)}</TableCell>
              <TableCell>{formatDate(invitation.expiresAt)}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewInstructions(invitation)}>
                      <Eye className="w-4 h-4 mr-2" />
                      查看部署说明
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleCopyId(invitation.id)}>
                      <Copy className="w-4 h-4 mr-2" />
                      复制连接ID
                    </DropdownMenuItem>
                    {(invitation.status === 'PENDING' || invitation.status === 'ACTIVATED') && (
                      <DropdownMenuItem onClick={() => handleRegenerateInvitation(invitation.id, {}, invitation.status)}>
                        <RefreshCw className="w-4 h-4 mr-2" />
                        {invitation.status === 'ACTIVATED' ? '更新脚本' : '重新生成'}
                      </DropdownMenuItem>
                    )}
                    {invitation.status === 'PENDING' && (
                      <DropdownMenuItem onClick={() => handleRevokeInvitation(invitation.id)} className="text-red-600">
                        <Trash2 className="w-4 h-4 mr-2" />
                        撤销邀请
                      </DropdownMenuItem>
                    )}
                    {invitation.status === 'ACTIVATED' && (
                      <DropdownMenuItem onClick={() => handleRevokeInvitation(invitation.id)} className="text-orange-600">
                        <Trash2 className="w-4 h-4 mr-2" />
                        断开连接
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* 分页组件 */}
      {totalItems > 0 && (
        <div className="flex items-center justify-between px-2 py-4">
          <div className="flex items-center space-x-2">
            <p className="text-sm text-gray-700">
              显示第 {(currentPage - 1) * pageSize + 1} 到 {Math.min(currentPage * pageSize, totalItems)} 条，共 {totalItems} 条记录
            </p>
          </div>

          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-700">每页显示</p>
              <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent side="top">
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-700">条</p>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage <= 1} className="h-8 w-8 p-0">
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter((page) => {
                    // 显示当前页前后2页
                    return Math.abs(page - currentPage) <= 2 || page === 1 || page === totalPages;
                  })
                  .map((page, index, array) => {
                    // 添加省略号
                    const showEllipsis = index > 0 && page - array[index - 1] > 1;
                    return (
                      <div key={page} className="flex items-center">
                        {showEllipsis && <span className="px-2 text-gray-500">...</span>}
                        <Button variant={currentPage === page ? 'default' : 'outline'} size="sm" onClick={() => handlePageChange(page)} className="h-8 w-8 p-0">
                          {page}
                        </Button>
                      </div>
                    );
                  })}
              </div>

              <Button variant="outline" size="sm" onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage >= totalPages} className="h-8 w-8 p-0">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 部署说明模态框 */}
      {selectedInvitation && <DeploymentInstructionsModal invitation={selectedInvitation} open={showInstructions} onOpenChange={setShowInstructions} />}
    </>
  );
}
