import type { Context, Next } from 'hono';

import { prisma } from '../lib/db';
import { extractTokenFromHeader, type UserJWTPayload, verifyToken } from '../lib/jwt';
import { logger } from '../lib/logger';

// 扩展 Hono 的 Context 类型
declare module 'hono' {
  interface ContextVariableMap {
    user: {
      id: string;
      email: string;
      role: string;
      organizationId?: string;
      teamId?: string;
    };
    jwtPayload: UserJWTPayload;
  }
}

/**
 * JWT 认证中间件
 */
export async function jwtAuth(c: Context, next: Next): Promise<Response | void> {
  try {
    const authHeader = c.req.header('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return c.json({ error: 'No token provided' }, 401);
    }

    // 验证 JWT 令牌
    const payload = await verifyToken(token);
    if (!payload || payload.type !== 'access') {
      return c.json({ error: 'Invalid or expired token' }, 401);
    }

    // 检查会话是否存在且有效
    const session = await prisma.session.findFirst({
      where: {
        token,
        userId: payload.userId,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!session) {
      return c.json({ error: 'Session expired or invalid' }, 401);
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        members: {
          include: {
            organization: {
              select: { id: true, name: true },
            },
          },
        },
        team: {
          select: { id: true, name: true },
        },
      },
    });

    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    // 设置用户信息到上下文
    c.set('user', {
      id: user.id,
      email: user.email,
      role: user.role,
      organizationId: user.members[0]?.organization?.id,
      teamId: user.teamId || undefined,
    });

    c.set('jwtPayload', payload);

    await next();
  } catch (error) {
    logger.error('JWT认证中间件错误', {}, error instanceof Error ? error : new Error(String(error)));
    return c.json({ error: 'Authentication failed' }, 401);
  }
}

/**
 * 可选的 JWT 认证中间件（不强制要求认证）
 */
export async function optionalJwtAuth(c: Context, next: Next) {
  try {
    const authHeader = c.req.header('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (token) {
      const payload = await verifyToken(token);
      if (payload && payload.type === 'access') {
        // 获取用户信息
        const user = await prisma.user.findUnique({
          where: { id: payload.userId },
          include: {
            members: {
              include: {
                organization: {
                  select: { id: true, name: true },
                },
              },
            },
            team: {
              select: { id: true, name: true },
            },
          },
        });

        if (user) {
          c.set('user', {
            id: user.id,
            email: user.email,
            role: user.role,
            organizationId: user.members[0]?.organization?.id,
            teamId: user.teamId || undefined,
          });
          c.set('jwtPayload', payload);
        }
      }
    }

    await next();
  } catch (error) {
    logger.error('可选JWT认证中间件错误', {}, error instanceof Error ? error : new Error(String(error)));
    // 可选认证失败时不返回错误，继续执行
    await next();
  }
}

/**
 * 角色权限检查中间件
 */
export function requireRole(...roles: string[]) {
  return async (c: Context, next: Next): Promise<Response | void> => {
    const user = c.get('user');

    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    if (!roles.includes(user.role)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    await next();
  };
}

/**
 * 组织权限检查中间件
 */
export function requireOrganization() {
  return async (c: Context, next: Next): Promise<Response | void> => {
    const user = c.get('user');

    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    if (!user.organizationId) {
      return c.json({ error: 'Organization membership required' }, 403);
    }

    await next();
  };
}

/**
 * 团队权限检查中间件
 */
export function requireTeam() {
  return async (c: Context, next: Next): Promise<Response | void> => {
    const user = c.get('user');

    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    if (!user.teamId) {
      return c.json({ error: 'Team membership required' }, 403);
    }

    await next();
  };
}
