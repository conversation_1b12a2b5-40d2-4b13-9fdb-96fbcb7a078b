import { Hono } from 'hono';

import { UserController } from '../controller/user';

const router = new Hono();
const userController = new UserController();

// 用户注册
router.post('/register', (c) => userController.register(c));

// 用户登录
router.post('/login', (c) => userController.login(c));

// 获取当前用户信息
router.get('/info', (c) => userController.getCurrentUser(c));

// 刷新访问令牌
router.post('/refresh-token', (c) => userController.refreshToken(c));

// 修改用户信息
router.put('/info', (c) => userController.updateUserInfo(c));

// 修改密码
router.post('/change-password', (c) => userController.changePassword(c));

export default router;
