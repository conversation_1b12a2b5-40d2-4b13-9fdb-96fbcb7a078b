import type {
  ApiResponse,
  CompositeSizeChartSimple,
  CompositeSizeChartSimpleListResponse,
  CompositeSizeChartSimpleQueryParams,
  CreateCompositeSizeChartSimpleInput,
  CreateSizeChartSimpleInput,
  SizeChartSimple,
  SizeChartSimpleListResponse,
  SizeChartSimpleQueryParams,
  UpdateCompositeSizeChartSimpleInput,
  UpdateSizeChartSimpleInput,
} from '@/models/sizeChart';
import { request } from '@umijs/max';

const API_BASE_URL =
  (process.env.UMI_APP_API_URL || 'http://localhost:3009') + '/api/v1';

// ================== 简单尺码表相关 API ==================

/**
 * 获取简单尺码表列表
 */
export async function getSizeChartSimpleList(
  params?: SizeChartSimpleQueryParams,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<SizeChartSimpleListResponse>>(
    `${API_BASE_URL}/size-chart-simple`,
    {
      method: 'GET',
      params,
      ...(options || {}),
    },
  );
}

/**
 * 获取单个简单尺码表详情
 */
export async function getSizeChartSimpleDetail(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<SizeChartSimple>>(
    `${API_BASE_URL}/size-chart-simple/${id}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 创建简单尺码表
 */
export async function createSizeChartSimple(
  data: CreateSizeChartSimpleInput,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<SizeChartSimple>>(
    `${API_BASE_URL}/size-chart-simple`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
      ...(options || {}),
    },
  );
}

/**
 * 更新简单尺码表
 */
export async function updateSizeChartSimple(
  id: string,
  data: UpdateSizeChartSimpleInput,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<SizeChartSimple>>(
    `${API_BASE_URL}/size-chart-simple/${id}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
      ...(options || {}),
    },
  );
}

/**
 * 删除简单尺码表
 */
export async function deleteSizeChartSimple(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<null>>(`${API_BASE_URL}/size-chart-simple/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

// ================== 复合尺码表相关 API ==================

/**
 * 获取复合尺码表列表
 */
export async function getCompositeSizeChartSimpleList(
  params?: CompositeSizeChartSimpleQueryParams,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<CompositeSizeChartSimpleListResponse>>(
    `${API_BASE_URL}/composite-size-chart-simple`,
    {
      method: 'GET',
      params,
      ...(options || {}),
    },
  );
}

/**
 * 获取单个复合尺码表详情
 */
export async function getCompositeSizeChartSimpleDetail(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<CompositeSizeChartSimple>>(
    `${API_BASE_URL}/composite-size-chart-simple/${id}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 创建复合尺码表
 */
export async function createCompositeSizeChartSimple(
  data: CreateCompositeSizeChartSimpleInput,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<CompositeSizeChartSimple>>(
    `${API_BASE_URL}/composite-size-chart-simple`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
      ...(options || {}),
    },
  );
}

/**
 * 更新复合尺码表
 */
export async function updateCompositeSizeChartSimple(
  id: string,
  data: UpdateCompositeSizeChartSimpleInput,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<CompositeSizeChartSimple>>(
    `${API_BASE_URL}/composite-size-chart-simple/${id}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
      ...(options || {}),
    },
  );
}

/**
 * 删除复合尺码表
 */
export async function deleteCompositeSizeChartSimple(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<null>>(
    `${API_BASE_URL}/composite-size-chart-simple/${id}`,
    {
      method: 'DELETE',
      ...(options || {}),
    },
  );
}
