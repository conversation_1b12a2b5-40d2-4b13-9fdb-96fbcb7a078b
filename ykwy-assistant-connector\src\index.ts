﻿/**
 * 千牛连接一键启动脚本
 * 自动化千牛消息接收和发送的完整流程
 *
 * 功能：
 * 1. 自动调用后端API创建连接邀请获取脚本地址
 * 2. 智能检测千牛状态并引导用户操作
 * 3. 自动注入脚本到千牛客户端
 * 4. 启动TCP代理和千牛助手服务
 *
 * 使用方法：双击运行或 bun run index.ts
 */

import QianniuConnector from './core/qianniu-connector.js';
import OneClickService from './services/one-click-service.js';
import { Help } from './utils/help.js';

// 全局错误处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error.message);
  console.error('详细错误:', error.stack);
  console.log('\n💡 请检查配置文件和网络连接，或联系技术支持');
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  console.log('\n💡 请检查配置文件和网络连接，或联系技术支持');
  process.exit(1);
});

// 主函数
async function main() {
  const connector = new QianniuConnector();
  const oneClickService = new OneClickService(connector);
  const args = process.argv.slice(2);

  // 优雅关闭处理
  process.on('SIGINT', () => {
    console.log('\n🛑 收到停止信号...');
    connector.stopService();
    process.exit(0);
  });

  // 默认执行一键启动，除非指定了特定命令
  if (args.length === 0) {
    try {
      // 一键启动模式
      const success = await oneClickService.start();
      if (success) {
        // 保持进程运行
        process.stdin.resume();
      } else {
        console.error('❌ 一键启动失败');
        process.exit(1);
      }
    } catch (error) {
      console.error('❌ 一键启动过程中发生错误:', (error as Error).message);
      console.error('详细错误:', (error as Error).stack);
      process.exit(1);
    }
    return;
  }

  const command = args[0];

  // 处理特定命令
  try {
    switch (command) {
      case 'help':
      case '--help':
      case '-h':
        Help.show();
        break;

      case 'config':
        // 重新配置
        await connector.getConfigManager().configWizard();
        console.log('✅ 配置完成，现在可以运行一键启动');
        break;

      default:
        console.error(`❌ 未知命令: ${command}`);
        console.log('💡 直接运行 node index.js 进行一键启动');
        Help.show();
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ 执行失败:', (error as Error).message);
    process.exit(1);
  }
}

// 直接运行主函数
main().catch(console.error);
