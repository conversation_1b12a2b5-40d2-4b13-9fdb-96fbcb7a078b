#!/usr/bin/env node

/**
 * 千牛消息快速测试脚本
 * 用于快速测试单个消息发送功能
 */

const net = require('net');
const path = require('path');

// 配置
const CONFIG = {
  host: '127.0.0.1',
  port: 9997, // 直接连接到ykwy-assistant-api的TCP服务器
  timeout: 5000,
  // 测试用的千牛客户ID（这是千牛TCP协议需要的格式，不是数据库的UUID）
  qianniuCustomerId: "2807792688.1-1597305140.1#11001@cntaobao"
};

/**
 * 模拟千牛客户端连接并发送命令
 */
function sendCommand(payload) {
  return new Promise((resolve, reject) => {
    const client = new net.Socket();
    let responseReceived = false;
    let connectionEstablished = false;

    // 设置超时
    const timeout = setTimeout(() => {
      if (!responseReceived) {
        client.destroy();
        reject(new Error('请求超时'));
      }
    }, CONFIG.timeout);

    client.connect(CONFIG.port, CONFIG.host, () => {
      console.log(`✅ 已连接到 ${CONFIG.host}:${CONFIG.port}`);
      connectionEstablished = true;
    });

    client.on('data', (data) => {
      const response = data.toString().trim();
      console.log(`📨 收到数据: ${response}`);

      // 服务器连接后会立即发送getusername请求
      if (response.includes('"method":"getusername"')) {
        console.log('📋 收到服务器的getusername请求，发送响应...');

        // 模拟千牛客户端响应用户名
        const usernameResponse = JSON.stringify({
          method: "getusername",
          success: true,
          username: "test_qianniu_client"
        }) + "\r\n\r\n";

        client.write(usernameResponse);
        console.log('📤 已响应用户名，等待建立连接映射...');

        // 标记连接已建立，等待一下再发送测试命令
        connectionEstablished = true;
        setTimeout(() => {
          const message = JSON.stringify(payload) + "\r\n\r\n";
          console.log(`📤 发送测试命令: ${JSON.stringify(payload, null, 2)}`);
          client.write(message);
        }, 1000); // 给服务器更多时间建立映射
      } else if (connectionEstablished) {
        // 这是对我们测试命令的响应
        responseReceived = true;
        clearTimeout(timeout);
        client.destroy();
        resolve(response);
      }
    });

    client.on('error', (err) => {
      clearTimeout(timeout);
      console.error('❌ 连接错误:', err.message);
      reject(err);
    });

    client.on('close', () => {
      console.log('🔌 连接已关闭');
    });
  });
}

/**
 * 测试发送文本消息
 */
async function testSendText(text, qianniuCustomerId = CONFIG.qianniuCustomerId) {
  console.log('\n📝 测试发送文本消息');
  console.log('=' .repeat(40));

  const payload = {
    method: "sendtext",
    id: qianniuCustomerId,
    text: text,
    keep: "on"
  };

  try {
    const response = await sendCommand(payload);
    console.log('✅ 文本消息发送成功');
    return response;
  } catch (error) {
    console.error('❌ 文本消息发送失败:', error.message);
    throw error;
  }
}

/**
 * 测试发送图片消息
 */
async function testSendImage(imagePath, qianniuCustomerId = CONFIG.qianniuCustomerId) {
  console.log('\n🖼️  测试发送图片消息');
  console.log('=' .repeat(40));

  const payload = {
    method: "sendimage",
    id: qianniuCustomerId,
    image: path.resolve(imagePath),
    keep: "off"
  };

  try {
    const response = await sendCommand(payload);
    console.log('✅ 图片消息发送成功');
    return response;
  } catch (error) {
    console.error('❌ 图片消息发送失败:', error.message);
    throw error;
  }
}

/**
 * 测试发送视频消息
 */
async function testSendVideo(videoPath, qianniuCustomerId = CONFIG.qianniuCustomerId) {
  console.log('\n🎥 测试发送视频消息');
  console.log('=' .repeat(40));

  const payload = {
    method: "sendvideo",
    id: qianniuCustomerId,
    video: path.resolve(videoPath),
    keep: "off"
  };

  try {
    const response = await sendCommand(payload);
    console.log('✅ 视频消息发送成功');
    return response;
  } catch (error) {
    console.error('❌ 视频消息发送失败:', error.message);
    throw error;
  }
}

/**
 * 测试获取用户名
 */
async function testGetUsername() {
  console.log('\n👤 测试获取用户名');
  console.log('=' .repeat(40));

  const payload = {
    method: "getusername"
  };

  try {
    const response = await sendCommand(payload);
    console.log('✅ 获取用户名成功');
    return response;
  } catch (error) {
    console.error('❌ 获取用户名失败:', error.message);
    throw error;
  }
}

/**
 * 测试设置高亮
 */
async function testSetHighlight(flash = "open", qianniuCustomerId = CONFIG.qianniuCustomerId) {
  console.log('\n💡 测试设置高亮');
  console.log('=' .repeat(40));

  const payload = {
    method: "highlight",
    id: qianniuCustomerId,
    flash: flash
  };

  try {
    const response = await sendCommand(payload);
    console.log('✅ 设置高亮成功');
    return response;
  } catch (error) {
    console.error('❌ 设置高亮失败:', error.message);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes('--help')) {
    console.log(`
🎯 千牛消息快速测试脚本

用法: node quick-test-messages.js <命令> [参数]

命令:
  text <message>          发送文本消息
  image <path>            发送图片消息
  video <path>            发送视频消息
  username                获取用户名
  highlight [open|close]  设置高亮 (默认: open)

全局选项:
  --host <host>           服务器地址 (默认: ${CONFIG.host})
  --port <port>           服务器端口 (默认: 9996)
  --id <qianniuCustomerId>  千牛客户ID

示例:
  node quick-test-messages.js text "Hello World"
  node quick-test-messages.js image ./test.jpg
  node quick-test-messages.js video ./test.mp4
  node quick-test-messages.js username
  node quick-test-messages.js highlight open
  node quick-test-messages.js text "Hello" --host ************* --port 9996
    `);
    return;
  }

  // 解析全局选项
  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--host') {
      CONFIG.host = args[++i];
    } else if (args[i] === '--port') {
      CONFIG.port = parseInt(args[++i]);
    } else if (args[i] === '--id') {
      CONFIG.qianniuCustomerId = args[++i];
    }
  }

  const command = args[0];

  console.log('🎯 千牛消息快速测试');
  console.log(`🔗 服务器: ${CONFIG.host}:${CONFIG.port}`);
  console.log(`🆔 千牛客户ID: ${CONFIG.qianniuCustomerId}`);

  try {
    switch (command) {
      case 'text':
        if (!args[1]) {
          console.error('❌ 请提供要发送的文本消息');
          process.exit(1);
        }
        await testSendText(args[1]);
        break;

      case 'image':
        if (!args[1]) {
          console.error('❌ 请提供图片文件路径');
          process.exit(1);
        }
        await testSendImage(args[1]);
        break;

      case 'video':
        if (!args[1]) {
          console.error('❌ 请提供视频文件路径');
          process.exit(1);
        }
        await testSendVideo(args[1]);
        break;

      case 'username':
        await testGetUsername();
        break;

      case 'highlight':
        const flash = args[1] || 'open';
        await testSetHighlight(flash);
        break;

      default:
        console.error(`❌ 未知命令: ${command}`);
        console.log('使用 --help 查看帮助信息');
        process.exit(1);
    }

    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testSendText,
  testSendImage,
  testSendVideo,
  testGetUsername,
  testSetHighlight,
  sendCommand
};
