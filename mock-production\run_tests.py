#!/usr/bin/env python3
"""
测试运行器 - 选择运行不同的测试
"""
import sys
import subprocess
import os

def run_quick_test():
    """运行快速测试"""
    print("🚀 运行快速端到端测试...")
    try:
        result = subprocess.run([sys.executable, "quick_e2e_test.py"], 
                              cwd=os.path.dirname(__file__), 
                              capture_output=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行快速测试失败: {e}")
        return False

def run_full_test():
    """运行完整测试"""
    print("🚀 运行完整端到端测试...")
    try:
        result = subprocess.run([sys.executable, "e2e_test_recommendations.py"], 
                              cwd=os.path.dirname(__file__), 
                              capture_output=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行完整测试失败: {e}")
        return False

def run_connectivity_test():
    """运行连通性测试"""
    print("🚀 运行连通性测试...")
    try:
        result = subprocess.run([sys.executable, "test_connectivity.py"], 
                              cwd=os.path.dirname(__file__), 
                              capture_output=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行连通性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 推荐接口测试运行器")
    print("=" * 40)
    print("请选择要运行的测试:")
    print("1. 快速测试 (推荐)")
    print("2. 完整测试 (包含性能和并发)")
    print("3. 连通性测试")
    print("4. 运行所有测试")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (0-4): ").strip()
            
            if choice == "0":
                print("👋 退出测试")
                break
            elif choice == "1":
                run_quick_test()
                break
            elif choice == "2":
                run_full_test()
                break
            elif choice == "3":
                run_connectivity_test()
                break
            elif choice == "4":
                print("🚀 运行所有测试...")
                print("\n" + "="*50)
                print("1/3 连通性测试")
                print("="*50)
                run_connectivity_test()
                
                print("\n" + "="*50)
                print("2/3 快速功能测试")
                print("="*50)
                run_quick_test()
                
                print("\n" + "="*50)
                print("3/3 完整端到端测试")
                print("="*50)
                run_full_test()
                
                print("\n🎉 所有测试完成！")
                break
            else:
                print("❌ 无效选择，请输入 0-4")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消测试")
            break
        except Exception as e:
            print(f"❌ 输入错误: {e}")

if __name__ == "__main__":
    main()
