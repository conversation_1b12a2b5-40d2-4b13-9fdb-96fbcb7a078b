import { z } from 'zod';

/**
 * 订单状态验证 - 四位二进制字符串
 * 第1位：售前，第2位：发货前，第3位：发货后，第4位：售后
 */
const orderStatusPattern = /^[01]{4}$/;

/**
 * 问答ID参数校验
 * 用于需要传递ID的接口
 */
export const paramIdSchema = z.object({
  /** 问答ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

/**
 * 创建/更新问答参数校验
 */
export const upsertQuestionAndAnswerSchema = z.object({
  /** 问答ID，更新时必传，创建时可选 */
  id: z.string().uuid().optional(),
  /** 问题类型 */
  questionType: z.string().min(1, { message: '问题类型为必填项' }).max(100, { message: '问题类型不能超过100个字符' }),
  /** 回答数组（必须字段）使用数组存储多个样本 */
  answers: z.array(z.string().min(1, { message: '回答内容不能为空' })).min(1, { message: '至少需要一个回答' }),
  /** 分类编码，关联到KnowledgeCategory的code字段 */
  categoryCode: z.string().min(1, { message: '分类编码为必填项' }),
  /** 订单状态，使用四位二进制字符串表示 */
  orderStatus: z.string().regex(orderStatusPattern, {
    message: '订单状态必须是4位二进制字符串，如：1100表示售前|发货前，1111表示全流程',
  }),
  /** 常见问法样本数组 */
  commonQuestionSamples: z.array(z.string()).optional().default([]),
  /** 店铺名 */
  shopName: z.string().optional(),
  /** 店铺id */
  shopId: z.string().optional(),
  /** 商品名称 */
  productName: z.string().optional(),
  /** 商品链接 */
  productUrl: z.string().url().optional().or(z.literal('')),
});

/**
 * 问答查询参数校验
 */
export const questionAndAnswerQuerySchema = z.object({
  /** 按问题类型模糊搜索 */
  questionType: z.string().optional(),
  /** 按回答内容模糊搜索 */
  content: z.string().optional(),
  /** 按分类编码精确匹配 */
  categoryCode: z.string().optional(),
  /** 按订单状态精确匹配 */
  orderStatus: z.string().regex(orderStatusPattern).optional(),
  /** 按店铺名搜索 */
  shopName: z.string().optional(),
  /** 按店铺id搜索 */
  shopId: z.string().optional(),
  /** 按商品名称搜索 */
  productName: z.string().optional(),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => Math.min(parseInt(val, 10), 100)),
  /** 排序字段 */
  sortBy: z.enum(['createdAt', 'updatedAt', 'questionType', 'categoryCode']).optional().default('createdAt'),
  /** 排序方向 */
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
  /** 是否包含已删除 */
  includeDeleted: z
    .string()
    .optional()
    .default('false')
    .transform((val) => val === 'true'),
});

/**
 * 更新订单状态参数校验
 */
export const updateOrderStatusSchema = z.object({
  /** 问答ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
  /** 新的订单状态 */
  orderStatus: z.string().regex(orderStatusPattern, {
    message: '订单状态必须是4位二进制字符串',
  }),
});

/**
 * 统计查询参数校验
 */
export const statsQuerySchema = z.object({
  /** 开始日期 */
  startDate: z.string().datetime().optional(),
  /** 结束日期 */
  endDate: z.string().datetime().optional(),
  /** 分组方式 */
  groupBy: z.enum(['questionType', 'categoryCode', 'orderStatus']).optional().default('questionType'),
});

/**
 * 批量删除参数校验
 */
export const bulkDeleteSchema = z.object({
  /** 要删除的ID数组 */
  ids: z.array(z.string().uuid({ message: '无效的ID格式' })).min(1, { message: '至少需要选择一个项目' }),
});

/**
 * 根据分类编码查询参数校验
 */
export const categoryCodeParamSchema = z.object({
  /** 分类编码 */
  categoryCode: z.string().min(1, { message: '分类编码不能为空' }),
});

export const questionAndAnswerInputSchema = z.object({
  questionType: z.string(),
  categoryCode: z.string(),
  commonQuestionSamples: z.array(z.string()),
  answers: z.array(z.string()),
  orderStatus: z.string(),
  shopName: z.string().nullable().optional(),
  shopId: z.string().nullable().optional(),
  productName: z.string().nullable().optional(),
  productUrl: z.string().nullable().optional(),
});
