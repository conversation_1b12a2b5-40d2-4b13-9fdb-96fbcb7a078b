# 使用Python 3.11官方镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV POETRY_NO_INTERACTION=1
ENV POETRY_VENV_IN_PROJECT=1
ENV POETRY_CACHE_DIR=/tmp/poetry_cache

# 构建时的环境变量（用于索引构建）
ARG OPENAI_API_KEY
ARG OPENAI_API_BASE
ENV OPENAI_API_KEY=${OPENAI_API_KEY}
ENV OPENAI_API_BASE=${OPENAI_API_BASE}

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry

# 复制Poetry配置文件
COPY pyproject.toml poetry.lock* ./

# 安装Python依赖
RUN poetry config virtualenvs.create false \
    && poetry install --only main --no-root \
    && rm -rf $POETRY_CACHE_DIR

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p logs storage

# 预构建索引（如果提供了API密钥）
RUN if [ -n "$OPENAI_API_KEY" ] && [ -n "$OPENAI_API_BASE" ]; then \
        echo "🔧 开始预构建索引..."; \
        python scripts/build_index.py --force && \
        echo "✅ 索引预构建完成"; \
    else \
        echo "⚠️ 未提供API密钥，跳过索引预构建，将在运行时构建"; \
    fi

# 暴露端口
EXPOSE 8000

# # 健康检查
# HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
#     CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "main.py"]
