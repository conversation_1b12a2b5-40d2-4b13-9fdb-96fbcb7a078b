import { Prisma } from '@prisma/client';

import prisma from '../client/prisma.ts';
import type { BulkDeleteResultDto, ImportExportTaskDto, ImportExportTaskListDto, TaskStatisticsDto } from '../types/dto/importExportTask';
import type { ImportExportTaskInput, ImportExportTaskQuery, ImportExportTaskStatusUpdate } from '../types/request/importExportTask';

/**
 * 导入导出任务服务层，负责与数据库的交互
 */
export class ImportExportTaskService {
  /**
   * 创建或更新导入导出任务
   * @param data 任务输入参数
   */
  async upsert(data: ImportExportTaskInput): Promise<ImportExportTaskDto> {
    const { id, taskName, taskType, taskTime, fileName, status, result, filePath } = data;

    // 构建更新/创建数据对象
    const taskData = {
      taskName,
      taskType,
      taskTime,
      fileName,
      status: status || 'pending',
      result,
      filePath,
    };

    // upsert: 有id则更新，无id则创建
    return prisma.importExportTask.upsert({
      where: { id: id ?? '00000000-0000-0000-0000-000000000000' },
      update: taskData,
      create: taskData,
    }) as Promise<ImportExportTaskDto>;
  }

  /**
   * 获取单个任务详情
   * @param id 任务ID
   */
  async findById(id: string): Promise<ImportExportTaskDto | null> {
    return prisma.importExportTask.findUnique({
      where: { id, isDeleted: 0 },
    }) as Promise<ImportExportTaskDto | null>;
  }

  /**
   * 获取多个任务（分页）
   * @param params 查询参数
   */
  async findMany(params: ImportExportTaskQuery): Promise<ImportExportTaskListDto> {
    const { taskName, taskType, status, fileName, startTime, endTime, skip = 0, take = 10, includeDeleted = false } = params;

    const where: Prisma.ImportExportTaskWhereInput = {};

    if (!includeDeleted) where.isDeleted = 0;
    if (taskName) where.taskName = { contains: taskName, mode: 'insensitive' };
    if (taskType) where.taskType = taskType;
    if (status) where.status = status;
    if (fileName) where.fileName = { contains: fileName, mode: 'insensitive' };

    // 时间范围过滤
    if (startTime || endTime) {
      where.taskTime = {};
      if (startTime) where.taskTime.gte = startTime;
      if (endTime) where.taskTime.lte = endTime;
    }

    // 事务：同时查列表和总数
    const [tasks, total] = await prisma.$transaction([
      prisma.importExportTask.findMany({
        where,
        skip,
        take,
        orderBy: { taskTime: 'desc' },
      }),
      prisma.importExportTask.count({ where }),
    ]);

    return {
      items: tasks as ImportExportTaskDto[],
      total,
    };
  }

  /**
   * 更新任务状态
   * @param id 任务ID
   * @param updateData 更新数据
   */
  async updateStatus(id: string, updateData: ImportExportTaskStatusUpdate): Promise<ImportExportTaskDto> {
    const { status, result, filePath } = updateData;

    return prisma.importExportTask.update({
      where: { id },
      data: {
        status,
        result,
        filePath,
        updatedAt: new Date(),
      },
    }) as Promise<ImportExportTaskDto>;
  }

  /**
   * 软删除任务（逻辑删除）
   * @param id 任务ID
   */
  async softDelete(id: string): Promise<ImportExportTaskDto> {
    return prisma.importExportTask.update({
      where: { id },
      data: { isDeleted: 1 },
    }) as Promise<ImportExportTaskDto>;
  }

  /**
   * 恢复已删除任务
   * @param id 任务ID
   */
  async restore(id: string): Promise<ImportExportTaskDto> {
    return prisma.importExportTask.update({
      where: { id },
      data: { isDeleted: 0 },
    }) as Promise<ImportExportTaskDto>;
  }

  /**
   * 批量软删除任务
   * @param ids 任务ID数组
   */
  async bulkDelete(ids: string[]): Promise<BulkDeleteResultDto> {
    const result = await prisma.importExportTask.updateMany({
      where: { id: { in: ids }, isDeleted: 0 },
      data: { isDeleted: 1 },
    });

    return { count: result.count };
  }

  /**
   * 获取任务统计信息
   */
  async getStatistics(): Promise<TaskStatisticsDto> {
    const where = { isDeleted: 0 };

    const [totalTasks, successTasks, failedTasks, pendingTasks, importTasks, exportTasks] = await prisma.$transaction([
      prisma.importExportTask.count({ where }),
      prisma.importExportTask.count({ where: { ...where, status: 'success' } }),
      prisma.importExportTask.count({ where: { ...where, status: 'failed' } }),
      prisma.importExportTask.count({ where: { ...where, status: 'pending' } }),
      prisma.importExportTask.count({ where: { ...where, taskType: 'import' } }),
      prisma.importExportTask.count({ where: { ...where, taskType: 'export' } }),
    ]);

    return {
      totalTasks,
      successTasks,
      failedTasks,
      pendingTasks,
      importTasks,
      exportTasks,
    };
  }

  /**
   * 获取最近的任务列表
   * @param limit 获取数量限制
   * @param taskType 任务类型过滤
   */
  async getRecentTasks(limit = 10, taskType?: 'import' | 'export'): Promise<ImportExportTaskDto[]> {
    const where: Prisma.ImportExportTaskWhereInput = { isDeleted: 0 };
    if (taskType) where.taskType = taskType;

    const tasks = await prisma.importExportTask.findMany({
      where,
      take: limit,
      orderBy: { createdAt: 'desc' },
    });

    return tasks as ImportExportTaskDto[];
  }

  /**
   * 查询过期的导出任务
   * @param cutoffDate 截止日期，查询此日期之前的任务
   */
  async findExpiredExportTasks(cutoffDate: Date): Promise<ImportExportTaskDto[]> {
    const tasks = await prisma.importExportTask.findMany({
      where: {
        isDeleted: 0,
        taskType: 'export',
        status: 'success', // 只查询成功的导出任务
        createdAt: { lt: cutoffDate },
        filePath: { not: null }, // 确保有文件路径
      },
      orderBy: { createdAt: 'asc' },
    });

    return tasks as ImportExportTaskDto[];
  }

  /**
   * 清理旧任务（物理删除）
   * @param daysAgo 删除多少天前的任务
   */
  async cleanupOldTasks(daysAgo = 30): Promise<BulkDeleteResultDto> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysAgo);

    const result = await prisma.importExportTask.deleteMany({
      where: {
        OR: [
          { isDeleted: 1 },
          {
            status: { in: ['success', 'failed'] },
            createdAt: { lt: cutoffDate },
          },
        ],
      },
    });

    return { count: result.count };
  }
}
