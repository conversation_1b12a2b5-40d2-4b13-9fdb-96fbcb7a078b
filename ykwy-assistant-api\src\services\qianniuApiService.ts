/**
 * 千牛智能客服API服务
 * 封装所有千牛智能客服相关的API调用
 */

import { logger } from '../lib/logger';

/**
 * 千牛API调用参数接口
 */
export interface QianniuApiParams {
  method: string;
  param: Record<string, unknown>;
  httpMethod?: string;
  version?: string;
}

/**
 * 千牛API响应接口
 */
export interface QianniuApiResponse {
  success: boolean;
  data?: unknown;
  error?: string;
  method: string;
}

/**
 * 千牛智能客服API服务类
 */
export class QianniuApiService {
  /**
   * 邀请下单
   * @param encryptId 加密的用户ID
   * @param itemProps 商品属性信息
   * @param buyerNick 买家昵称
   * @param bizDomain 业务域名
   * @param encrypType 加密类型
   */
  async inviteOrder(params: {
    encryptId: string;
    itemProps: string;
    buyerNick?: string;
    bizDomain?: string;
    encrypType?: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建邀请下单API参数', params);

    return {
      method: 'mtop.taobao.qianniu.airisland.invite.order.send',
      param: {
        encryptId: params.encryptId,
        bizDomain: params.bizDomain ?? 'taobao',
        encrypType: params.encrypType ?? 'internal',
        buyerNick: params.buyerNick ?? '',
        itemProps: params.itemProps,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 查询近三个月订单
   * @param securityBuyerUid 用户的数字ID
   * @param orderStatus 订单状态
   */
  async queryRecentOrders(params: {
    securityBuyerUid: string;
    orderStatus?: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建近三个月订单查询API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cs.trade.query',
      param: {
        orderStatus: params.orderStatus ?? 'UNPAID_TRADE',
        securityBuyerUid: params.securityBuyerUid,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 查询历史订单
   * @param securityBuyerUid 用户的数字ID
   * @param pageNum 页码
   * @param pageSize 每页大小
   */
  async queryHistoryOrders(params: {
    securityBuyerUid: string;
    pageNum?: number;
    pageSize?: number;
  }): Promise<QianniuApiParams> {
    logger.debug('构建历史订单查询API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cs.trade.history.query',
      param: {
        securityBuyerUid: params.securityBuyerUid,
        pageNum: params.pageNum ?? 1,
        pageSize: params.pageSize ?? 10,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 发送商品卡片
   * @param encryptId 加密的用户ID
   * @param batchItemIds 商品ID数组
   * @param type 类型
   */
  async sendItemCard(params: {
    encryptId: string;
    batchItemIds: string;
    type?: number;
  }): Promise<QianniuApiParams> {
    logger.debug('构建发送商品卡片API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cs.item.recommend.send',
      param: {
        encryptId: params.encryptId,
        batchItemIds: params.batchItemIds,
        type: params.type ?? -1,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 查询订单物流信息
   * @param bizOrderId 业务订单ID
   */
  async queryOrderLogistics(params: {
    bizOrderId: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建订单物流查询API参数', params);

    return {
      method: 'mtop.alibaba.fulfillment.printorder.consign.logistics.query',
      param: {
        bizOrderId: params.bizOrderId,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 查询商品记录（咨询商品、上次购买、足迹）
   * @param encryptId 加密的用户ID
   */
  async queryItemRecord(params: {
    encryptId: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建商品记录查询API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cs.item.record.query',
      param: {
        encryptId: params.encryptId,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 查询客户信息
   * @param encryptId 加密的用户ID
   */
  async queryCustomerInfo(params: {
    encryptId: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建客户信息查询API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cs.user.query',
      param: {
        encryptId: params.encryptId,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 查询店铺优惠券
   */
  async queryShopCoupons(): Promise<QianniuApiParams> {
    logger.debug('构建店铺优惠券查询API参数');

    return {
      method: 'mtop.taobao.qianniu.cs.user.shop.coupon.query',
      param: {},
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 发送优惠券
   * @param name 优惠券名称
   * @param activityId 活动ID
   * @param description 描述
   * @param encryptId 加密的用户ID
   */
  async sendCoupon(params: {
    name: string;
    activityId: string;
    description: string;
    encryptId: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建发送优惠券API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cs.user.shop.coupon.send',
      param: {
        name: params.name,
        activityId: params.activityId,
        description: params.description,
        encryptId: params.encryptId,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 查询买家ID
   * @param searchQuery 买家昵称
   */
  async searchBuyerId(params: {
    searchQuery: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建查询买家ID API参数', params);

    return {
      method: 'mtop.taobao.qianniu.airisland.contact.search',
      param: {
        accessKey: 'qianniu-pc',
        accessSecret: 'qianniu-pc-secret',
        accountType: '3',
        searchQuery: params.searchQuery,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 挂起/取消挂起
   * @param accountId 账号ID
   * @param isSuspend 是否挂起
   * @param source 来源
   */
  async setSuspend(params: {
    accountId: number;
    isSuspend: boolean;
    source?: number;
  }): Promise<QianniuApiParams> {
    logger.debug('构建挂起设置API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cloudkefu.suspend.set',
      param: {
        account_id: params.accountId,
        source: params.source ?? 1,
        is_suspend: params.isSuspend,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 订单解密
   * @param tid 订单ID
   * @param bizType 业务类型
   * @param queryByTid 是否通过订单ID查询
   */
  async decryptOrder(params: {
    tid: string;
    bizType?: string;
    queryByTid?: boolean;
  }): Promise<QianniuApiParams> {
    logger.debug('构建订单解密API参数', params);

    return {
      method: 'mtop.taobao.tid.decrypt',
      param: {
        bizType: params.bizType ?? 'qianniu',
        tid: params.tid,
        queryByTid: params.queryByTid ?? true,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 获取店铺商品
   * @param pageSize 每页大小
   * @param pageNo 页码
   * @param keyWord 关键词
   * @param sortKey 排序字段
   * @param desc 是否降序
   * @param type 类型
   * @param queryGift 是否查询赠品
   * @param encryptId 加密的用户ID
   */
  async searchShopItems(params: {
    pageSize?: number;
    pageNo?: number;
    keyWord?: string;
    sortKey?: string;
    desc?: boolean;
    type?: number;
    queryGift?: boolean;
    encryptId?: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建店铺商品搜索API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cs.item.search',
      param: {
        pageSize: params.pageSize ?? 8,
        pageNo: params.pageNo ?? 1,
        keyWord: params.keyWord ?? '',
        sortKey: params.sortKey ?? 'sold',
        desc: params.desc ?? true,
        type: params.type ?? 0,
        queryGift: params.queryGift ?? false,
        encryptId: params.encryptId ?? '',
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 获取店铺客服
   * @param pageSize 每页大小
   */
  async getShopCustomerService(params: {
    pageSize?: number;
  }): Promise<QianniuApiParams> {
    logger.debug('构建获取店铺客服API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cloudkefu.accountstatus.getbyid',
      param: {
        pageSize: params.pageSize ?? 100,
      },
      httpMethod: 'post',
      version: '1.0',
    };
  }

  /**
   * 获取客服分组列表
   * @param loginDomain 登录域名
   */
  async getDispatchGroups(params: {
    loginDomain?: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建获取客服分组列表API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cloudkefu.dispatchgroups.get',
      param: {
        login_domain: params.loginDomain ?? 'cntaobao',
      },
      httpMethod: 'post',
      version: '2.0',
    };
  }

  /**
   * 转接到个人
   * @param buyerId 买家ID
   * @param toId 转接目标ID
   * @param reason 转接原因
   * @param appCid 应用会话ID
   * @param buyerDomain 买家域名
   * @param loginDomain 登录域名
   */
  async forwardToPerson(params: {
    buyerId: number;
    toId: number;
    reason?: string;
    appCid?: string;
    buyerDomain?: string;
    loginDomain?: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建转接到个人API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cloudkefu.forward',
      param: {
        buyerId: params.buyerId,
        toId: params.toId,
        reason: params.reason ?? '转接',
        options: JSON.stringify({
          appCid: params.appCid ?? '',
          buyerDomain: params.buyerDomain ?? 'cntaobao',
          loginDomain: params.loginDomain ?? 'cntaobao',
        }),
      },
      httpMethod: 'post',
      version: '3.0',
    };
  }

  /**
   * 转接到分组
   * @param buyerId 买家ID
   * @param toId 目标客服ID
   * @param groupId 分组ID
   * @param reason 转接原因
   * @param appCid 应用会话ID
   * @param forwardType 转接类型
   * @param charset 字符集
   * @param exceptUsers 排除用户
   * @param buyerDomain 买家域名
   * @param loginDomain 登录域名
   */
  async forwardToGroup(params: {
    buyerId: number;
    toId: number;
    groupId: number;
    reason?: string;
    appCid?: string;
    forwardType?: number;
    charset?: string;
    exceptUsers?: string;
    buyerDomain?: string;
    loginDomain?: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建转接到分组API参数', params);

    return {
      method: 'mtop.taobao.qianniu.cloudkefu.forward',
      param: {
        buyerId: params.buyerId,
        toId: params.toId,
        reason: params.reason ?? '转接',
        options: JSON.stringify({
          groupId: params.groupId,
          appCid: params.appCid ?? '',
          forwardType: params.forwardType ?? 2,
          charset: params.charset ?? 'utf-8',
          exceptUsers: params.exceptUsers ?? '',
          buyerDomain: params.buyerDomain ?? 'cntaobao',
          loginDomain: params.loginDomain ?? 'cntaobao',
        }),
      },
      httpMethod: 'post',
      version: '3.0',
    };
  }

  /**
   * 通用API调用方法
   * @param method API方法名
   * @param param 参数对象
   * @param httpMethod HTTP方法
   * @param version API版本
   */
  async callApi(params: {
    method: string;
    param: Record<string, unknown>;
    httpMethod?: string;
    version?: string;
  }): Promise<QianniuApiParams> {
    logger.debug('构建通用API调用参数', params);

    return {
      method: params.method,
      param: params.param,
      httpMethod: params.httpMethod ?? 'post',
      version: params.version ?? '1.0',
    };
  }

  /**
   * 批量API调用
   * @param apiCalls API调用数组
   */
  async batchCallApi(apiCalls: Array<{
    method: string;
    param: Record<string, unknown>;
    httpMethod?: string;
    version?: string;
  }>): Promise<QianniuApiParams[]> {
    logger.debug('构建批量API调用参数', { count: apiCalls.length });

    return apiCalls.map(call => ({
      method: call.method,
      param: call.param,
      httpMethod: call.httpMethod ?? 'post',
      version: call.version ?? '1.0',
    }));
  }
}

// 导出单例
export const qianniuApiService = new QianniuApiService();
