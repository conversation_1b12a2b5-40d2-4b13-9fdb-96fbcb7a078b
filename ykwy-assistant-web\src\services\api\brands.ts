// 品牌相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse, Brand } from '../types';

// 组织品牌列表查询选项
export const organizationBrandsQueryOptions = (organizationId: string) => ({
  queryKey: queryKeys.organizationBrands(organizationId),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<Brand[]>>(`brands/organization/${organizationId}`);
    return response;
  },
});

// 当前用户组织品牌列表查询选项
export const brandsQueryOptions = () => ({
  queryKey: queryKeys.brands(),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<Brand[]>>('brands');
    return response;
  },
});
