# 统一日志系统使用指南

## 🎯 概述

销售智能体现在使用与ykwy-assistant-api完全一致的统一日志系统，支持：

- ✅ **结构化日志** - JSON格式输出，便于日志收集和分析
- ✅ **Loki集成** - 统一日志收集到Loki服务
- ✅ **多级别日志** - ERROR, WARN, INFO, DEBUG, TRACE
- ✅ **请求上下文** - 自动关联请求ID、用户ID、组织ID
- ✅ **彩色输出** - 开发环境友好的控制台输出
- ✅ **向后兼容** - 保持原有日志函数可用

## 🔧 配置

### 环境变量

```bash
# 基础配置
NODE_ENV=production          # 生产环境启用JSON和Loki输出
LOG_DIR=/app/logs           # 日志目录

# Loki配置（与ykwy-assistant-api统一）
LOKI_URL=https://your-loki-server/loki/api/v1/push
LOKI_USERNAME=your_username
LOKI_PASSWORD=your_password
```

### Docker部署

```bash
# 设置Loki环境变量
export LOKI_URL="https://your-loki-server/loki/api/v1/push"
export LOKI_USERNAME="your_username"
export LOKI_PASSWORD="your_password"

# 运行容器
./run-sales-agent-docker.sh
```

## 📝 使用方法

### 1. 统一日志器（推荐）

```python
from src.sales_agent.utils.logger import unified_logger

# 基础日志
unified_logger.info("用户登录成功", {"userId": "123", "ip": "***********"})
unified_logger.error("API调用失败", {"endpoint": "/api/users"}, error)

# 带请求上下文的日志
unified_logger.info("处理用户请求", 
                   {"action": "query_orders"}, 
                   request_id="req_123",
                   user_id="user_456",
                   organization_id="org_789")
```

### 2. 便捷函数（向后兼容）

```python
from src.sales_agent.utils.logger import (
    log_info, log_error, log_warn, log_debug,
    log_api_call, log_api_response,
    log_tool_call, log_tool_result,
    log_user_interaction
)

# 基础日志
log_info("系统启动", {"version": "1.0.0"})
log_error("数据库连接失败", {"host": "localhost"}, error)

# API调用日志
log_api_call("query_orders", {"customer_id": "123"}, "req_456")
log_api_response("query_orders", True, {"count": 5}, request_id="req_456")

# 工具调用日志
log_tool_call("get_order_details", {"order_id": "789"}, "req_456")
log_tool_result("get_order_details", True, order_data, request_id="req_456")

# 用户交互日志
log_user_interaction("查看我的订单", "您有5个订单...", "req_456")
```

### 3. 传统logging（继续支持）

```python
import logging
from src.sales_agent.utils.logger import setup_detailed_logging

# 设置日志
setup_detailed_logging()
logger = logging.getLogger(__name__)

# 使用传统方式
logger.info("这是传统的日志消息")
logger.error("这是错误消息", exc_info=True)
```

## 📊 日志格式

### 控制台输出（开发环境）

```
[INFO] 2024-01-20T10:30:45.123Z - 用户登录成功 [req:req_123] [user:user_456]
  Context: {
    "userId": "123",
    "ip": "***********"
  }
```

### JSON输出（生产环境）

```json
{
  "timestamp": "2024-01-20T10:30:45.123Z",
  "level": 2,
  "message": "用户登录成功",
  "service": "sales-agent",
  "requestId": "req_123",
  "userId": "user_456",
  "organizationId": "org_789",
  "context": {
    "userId": "123",
    "ip": "***********"
  }
}
```

### Loki标签

```
service=sales-agent
level=info
env=production
request_id=req_123
user_id=user_456
organization_id=org_789
module=api
```

## 🔍 日志查看

### 本地文件日志

```bash
# 查看日志文件
./view-logs.sh

# 实时跟踪
./view-logs.sh -f

# 查看最后50行
./view-logs.sh -n 50
```

### Docker容器日志

```bash
# 查看容器日志
./view-logs.sh -c

# 实时跟踪容器日志
./view-logs.sh -c -f
```

### Loki查询

在Grafana中使用以下查询：

```logql
# 查看销售智能体所有日志
{service="sales-agent"}

# 查看特定请求的日志
{service="sales-agent", request_id="req_123"}

# 查看API调用日志
{service="sales-agent", module="api"}

# 查看错误日志
{service="sales-agent", level="error"}
```

## 🎯 最佳实践

1. **使用统一日志器** - 优先使用 `unified_logger` 而不是传统logging
2. **添加上下文信息** - 尽可能提供丰富的context信息
3. **传递请求ID** - 在整个请求链路中传递request_id
4. **合理使用日志级别** - ERROR用于错误，WARN用于警告，INFO用于重要信息
5. **结构化数据** - 使用字典而不是字符串拼接来传递结构化信息

## 🔧 故障排除

### Loki连接问题

```python
# 检查Loki配置
from src.sales_agent.utils.logger import unified_logger
print(f"Loki enabled: {unified_logger.loki_service.is_enabled()}")
print(f"Loki URL: {unified_logger.loki_service.url}")
```

### 日志不显示

1. 检查环境变量 `NODE_ENV`
2. 检查日志级别设置
3. 查看容器日志：`docker logs ykwy-sales-agent`

现在销售智能体的日志系统与ykwy-assistant-api完全统一，支持统一的日志收集、分析和监控！
