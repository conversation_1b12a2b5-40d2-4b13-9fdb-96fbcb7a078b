// 错误代码枚举
export enum ErrorCode {
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',

  // 认证错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',

  // 资源错误
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  DUPLICATE_ERROR = 'DUPLICATE_ERROR',

  // 数据库错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  FOREIGN_KEY_ERROR = 'FOREIGN_KEY_ERROR',
  RELATION_ERROR = 'RELATION_ERROR',

  // 网络错误
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',

  // 业务逻辑错误
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',

  // WebSocket错误
  WEBSOCKET_CONNECTION_FAILED = 'WEBSOCKET_CONNECTION_FAILED',
  WEBSOCKET_AUTH_FAILED = 'WEBSOCKET_AUTH_FAILED',

  // 文件上传错误
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
}

// 支持的语言
export type SupportedLanguage = 'zh-CN' | 'en-US';

// 错误消息映射
export const ERROR_MESSAGES: Record<SupportedLanguage, Record<ErrorCode, string>> = {
  'zh-CN': {
    [ErrorCode.UNKNOWN_ERROR]: '未知错误',
    [ErrorCode.INTERNAL_ERROR]: '内部服务器错误',
    [ErrorCode.VALIDATION_ERROR]: '请求参数验证失败',

    [ErrorCode.UNAUTHORIZED]: '未授权访问',
    [ErrorCode.FORBIDDEN]: '禁止访问',
    [ErrorCode.TOKEN_EXPIRED]: '登录已过期，请重新登录',
    [ErrorCode.INVALID_CREDENTIALS]: '用户名或密码错误',

    [ErrorCode.NOT_FOUND]: '资源不存在',
    [ErrorCode.CONFLICT]: '资源冲突',
    [ErrorCode.DUPLICATE_ERROR]: '数据已存在，请检查唯一性约束',

    [ErrorCode.DATABASE_ERROR]: '数据库操作失败',
    [ErrorCode.FOREIGN_KEY_ERROR]: '外键约束失败',
    [ErrorCode.RELATION_ERROR]: '数据关系冲突',

    [ErrorCode.NETWORK_ERROR]: '网络连接失败',
    [ErrorCode.TIMEOUT_ERROR]: '请求超时',

    [ErrorCode.INSUFFICIENT_PERMISSIONS]: '权限不足',
    [ErrorCode.QUOTA_EXCEEDED]: '配额已超限',
    [ErrorCode.OPERATION_NOT_ALLOWED]: '操作不被允许',

    [ErrorCode.WEBSOCKET_CONNECTION_FAILED]: 'WebSocket连接失败',
    [ErrorCode.WEBSOCKET_AUTH_FAILED]: 'WebSocket认证失败',

    [ErrorCode.FILE_TOO_LARGE]: '文件过大',
    [ErrorCode.INVALID_FILE_TYPE]: '不支持的文件类型',
    [ErrorCode.UPLOAD_FAILED]: '文件上传失败',
  },

  'en-US': {
    [ErrorCode.UNKNOWN_ERROR]: 'Unknown error',
    [ErrorCode.INTERNAL_ERROR]: 'Internal server error',
    [ErrorCode.VALIDATION_ERROR]: 'Request validation failed',

    [ErrorCode.UNAUTHORIZED]: 'Unauthorized access',
    [ErrorCode.FORBIDDEN]: 'Access forbidden',
    [ErrorCode.TOKEN_EXPIRED]: 'Token expired, please login again',
    [ErrorCode.INVALID_CREDENTIALS]: 'Invalid username or password',

    [ErrorCode.NOT_FOUND]: 'Resource not found',
    [ErrorCode.CONFLICT]: 'Resource conflict',
    [ErrorCode.DUPLICATE_ERROR]: 'Data already exists, please check uniqueness constraints',

    [ErrorCode.DATABASE_ERROR]: 'Database operation failed',
    [ErrorCode.FOREIGN_KEY_ERROR]: 'Foreign key constraint failed',
    [ErrorCode.RELATION_ERROR]: 'Data relationship conflict',

    [ErrorCode.NETWORK_ERROR]: 'Network connection failed',
    [ErrorCode.TIMEOUT_ERROR]: 'Request timeout',

    [ErrorCode.INSUFFICIENT_PERMISSIONS]: 'Insufficient permissions',
    [ErrorCode.QUOTA_EXCEEDED]: 'Quota exceeded',
    [ErrorCode.OPERATION_NOT_ALLOWED]: 'Operation not allowed',

    [ErrorCode.WEBSOCKET_CONNECTION_FAILED]: 'WebSocket connection failed',
    [ErrorCode.WEBSOCKET_AUTH_FAILED]: 'WebSocket authentication failed',

    [ErrorCode.FILE_TOO_LARGE]: 'File too large',
    [ErrorCode.INVALID_FILE_TYPE]: 'Unsupported file type',
    [ErrorCode.UPLOAD_FAILED]: 'File upload failed',
  },
};

// 用户友好的错误描述
export const ERROR_DESCRIPTIONS: Record<SupportedLanguage, Record<ErrorCode, string>> = {
  'zh-CN': {
    [ErrorCode.UNKNOWN_ERROR]: '系统遇到了意外错误，请稍后重试或联系技术支持',
    [ErrorCode.INTERNAL_ERROR]: '服务器内部错误，我们正在处理这个问题',
    [ErrorCode.VALIDATION_ERROR]: '您提交的信息格式不正确，请检查后重新提交',

    [ErrorCode.UNAUTHORIZED]: '您需要先登录才能访问此功能',
    [ErrorCode.FORBIDDEN]: '您没有权限执行此操作',
    [ErrorCode.TOKEN_EXPIRED]: '您的登录状态已过期，请重新登录',
    [ErrorCode.INVALID_CREDENTIALS]: '登录信息不正确，请检查用户名和密码',

    [ErrorCode.NOT_FOUND]: '您要访问的内容不存在或已被删除',
    [ErrorCode.CONFLICT]: '操作冲突，请刷新页面后重试',
    [ErrorCode.DUPLICATE_ERROR]: '该信息已存在，请使用不同的信息',

    [ErrorCode.DATABASE_ERROR]: '数据保存失败，请稍后重试',
    [ErrorCode.FOREIGN_KEY_ERROR]: '数据关联错误，请检查相关信息',
    [ErrorCode.RELATION_ERROR]: '数据关系错误，无法完成操作',

    [ErrorCode.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
    [ErrorCode.TIMEOUT_ERROR]: '请求超时，请检查网络连接后重试',

    [ErrorCode.INSUFFICIENT_PERMISSIONS]: '您的权限不足，请联系管理员',
    [ErrorCode.QUOTA_EXCEEDED]: '使用量已达上限，请升级套餐或联系管理员',
    [ErrorCode.OPERATION_NOT_ALLOWED]: '当前状态下不允许此操作',

    [ErrorCode.WEBSOCKET_CONNECTION_FAILED]: '实时连接失败，部分功能可能受影响',
    [ErrorCode.WEBSOCKET_AUTH_FAILED]: '实时连接认证失败，请重新登录',

    [ErrorCode.FILE_TOO_LARGE]: '文件大小超出限制，请选择较小的文件',
    [ErrorCode.INVALID_FILE_TYPE]: '不支持此文件类型，请选择其他格式',
    [ErrorCode.UPLOAD_FAILED]: '文件上传失败，请检查网络连接后重试',
  },

  'en-US': {
    [ErrorCode.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again later or contact support',
    [ErrorCode.INTERNAL_ERROR]: 'Internal server error. We are working on fixing this issue',
    [ErrorCode.VALIDATION_ERROR]: 'The information you submitted is not in the correct format. Please check and resubmit',

    [ErrorCode.UNAUTHORIZED]: 'You need to log in to access this feature',
    [ErrorCode.FORBIDDEN]: 'You do not have permission to perform this action',
    [ErrorCode.TOKEN_EXPIRED]: 'Your login session has expired. Please log in again',
    [ErrorCode.INVALID_CREDENTIALS]: 'Login information is incorrect. Please check your username and password',

    [ErrorCode.NOT_FOUND]: 'The content you are trying to access does not exist or has been deleted',
    [ErrorCode.CONFLICT]: 'Operation conflict. Please refresh the page and try again',
    [ErrorCode.DUPLICATE_ERROR]: 'This information already exists. Please use different information',

    [ErrorCode.DATABASE_ERROR]: 'Data save failed. Please try again later',
    [ErrorCode.FOREIGN_KEY_ERROR]: 'Data association error. Please check related information',
    [ErrorCode.RELATION_ERROR]: 'Data relationship error. Cannot complete the operation',

    [ErrorCode.NETWORK_ERROR]: 'Network connection failed. Please check your network settings',
    [ErrorCode.TIMEOUT_ERROR]: 'Request timeout. Please check your network connection and try again',

    [ErrorCode.INSUFFICIENT_PERMISSIONS]: 'You do not have sufficient permissions. Please contact an administrator',
    [ErrorCode.QUOTA_EXCEEDED]: 'Usage limit reached. Please upgrade your plan or contact an administrator',
    [ErrorCode.OPERATION_NOT_ALLOWED]: 'This operation is not allowed in the current state',

    [ErrorCode.WEBSOCKET_CONNECTION_FAILED]: 'Real-time connection failed. Some features may be affected',
    [ErrorCode.WEBSOCKET_AUTH_FAILED]: 'Real-time connection authentication failed. Please log in again',

    [ErrorCode.FILE_TOO_LARGE]: 'File size exceeds the limit. Please select a smaller file',
    [ErrorCode.INVALID_FILE_TYPE]: 'This file type is not supported. Please select a different format',
    [ErrorCode.UPLOAD_FAILED]: 'File upload failed. Please check your network connection and try again',
  },
};

// 错误解决建议
export const ERROR_SOLUTIONS: Record<SupportedLanguage, Record<ErrorCode, string[]>> = {
  'zh-CN': {
    [ErrorCode.UNKNOWN_ERROR]: ['刷新页面重试', '清除浏览器缓存', '联系技术支持'],
    [ErrorCode.INTERNAL_ERROR]: ['稍后重试', '联系技术支持'],
    [ErrorCode.VALIDATION_ERROR]: ['检查输入格式', '查看字段要求', '重新填写表单'],

    [ErrorCode.UNAUTHORIZED]: ['点击登录按钮', '检查登录状态', '清除浏览器缓存后重新登录'],
    [ErrorCode.FORBIDDEN]: ['联系管理员获取权限', '确认账户状态', '使用有权限的账户'],
    [ErrorCode.TOKEN_EXPIRED]: ['点击重新登录', '清除浏览器缓存', '检查系统时间'],
    [ErrorCode.INVALID_CREDENTIALS]: ['检查用户名拼写', '确认密码正确', '尝试密码重置'],

    [ErrorCode.NOT_FOUND]: ['检查URL是否正确', '返回上一页', '使用搜索功能'],
    [ErrorCode.CONFLICT]: ['刷新页面', '稍后重试', '检查数据是否被其他用户修改'],
    [ErrorCode.DUPLICATE_ERROR]: ['使用不同的名称', '检查现有数据', '修改重复的字段'],

    [ErrorCode.DATABASE_ERROR]: ['稍后重试', '检查网络连接', '联系技术支持'],
    [ErrorCode.FOREIGN_KEY_ERROR]: ['检查关联数据', '确认依赖项存在', '联系管理员'],
    [ErrorCode.RELATION_ERROR]: ['检查数据关系', '删除相关依赖', '联系技术支持'],

    [ErrorCode.NETWORK_ERROR]: ['检查网络连接', '尝试刷新页面', '检查防火墙设置'],
    [ErrorCode.TIMEOUT_ERROR]: ['检查网络速度', '稍后重试', '联系网络管理员'],

    [ErrorCode.INSUFFICIENT_PERMISSIONS]: ['联系管理员', '确认账户权限', '使用管理员账户'],
    [ErrorCode.QUOTA_EXCEEDED]: ['升级套餐', '清理不必要的数据', '联系销售团队'],
    [ErrorCode.OPERATION_NOT_ALLOWED]: ['检查操作条件', '确认数据状态', '联系管理员'],

    [ErrorCode.WEBSOCKET_CONNECTION_FAILED]: ['检查网络连接', '刷新页面', '检查防火墙设置'],
    [ErrorCode.WEBSOCKET_AUTH_FAILED]: ['重新登录', '清除浏览器缓存', '检查登录状态'],

    [ErrorCode.FILE_TOO_LARGE]: ['压缩文件', '选择较小的文件', '联系管理员提高限制'],
    [ErrorCode.INVALID_FILE_TYPE]: ['转换文件格式', '查看支持的格式', '使用其他文件'],
    [ErrorCode.UPLOAD_FAILED]: ['检查网络连接', '重新选择文件', '稍后重试'],
  },

  'en-US': {
    [ErrorCode.UNKNOWN_ERROR]: ['Refresh the page and try again', 'Clear browser cache', 'Contact technical support'],
    [ErrorCode.INTERNAL_ERROR]: ['Try again later', 'Contact technical support'],
    [ErrorCode.VALIDATION_ERROR]: ['Check input format', 'Review field requirements', 'Refill the form'],

    [ErrorCode.UNAUTHORIZED]: ['Click the login button', 'Check login status', 'Clear browser cache and log in again'],
    [ErrorCode.FORBIDDEN]: ['Contact administrator for permissions', 'Confirm account status', 'Use an authorized account'],
    [ErrorCode.TOKEN_EXPIRED]: ['Click to log in again', 'Clear browser cache', 'Check system time'],
    [ErrorCode.INVALID_CREDENTIALS]: ['Check username spelling', 'Confirm password is correct', 'Try password reset'],

    [ErrorCode.NOT_FOUND]: ['Check if URL is correct', 'Go back to previous page', 'Use search function'],
    [ErrorCode.CONFLICT]: ['Refresh the page', 'Try again later', 'Check if data was modified by other users'],
    [ErrorCode.DUPLICATE_ERROR]: ['Use a different name', 'Check existing data', 'Modify duplicate fields'],

    [ErrorCode.DATABASE_ERROR]: ['Try again later', 'Check network connection', 'Contact technical support'],
    [ErrorCode.FOREIGN_KEY_ERROR]: ['Check related data', 'Confirm dependencies exist', 'Contact administrator'],
    [ErrorCode.RELATION_ERROR]: ['Check data relationships', 'Remove related dependencies', 'Contact technical support'],

    [ErrorCode.NETWORK_ERROR]: ['Check network connection', 'Try refreshing the page', 'Check firewall settings'],
    [ErrorCode.TIMEOUT_ERROR]: ['Check network speed', 'Try again later', 'Contact network administrator'],

    [ErrorCode.INSUFFICIENT_PERMISSIONS]: ['Contact administrator', 'Confirm account permissions', 'Use administrator account'],
    [ErrorCode.QUOTA_EXCEEDED]: ['Upgrade plan', 'Clean up unnecessary data', 'Contact sales team'],
    [ErrorCode.OPERATION_NOT_ALLOWED]: ['Check operation conditions', 'Confirm data status', 'Contact administrator'],

    [ErrorCode.WEBSOCKET_CONNECTION_FAILED]: ['Check network connection', 'Refresh the page', 'Check firewall settings'],
    [ErrorCode.WEBSOCKET_AUTH_FAILED]: ['Log in again', 'Clear browser cache', 'Check login status'],

    [ErrorCode.FILE_TOO_LARGE]: ['Compress the file', 'Select a smaller file', 'Contact administrator to increase limit'],
    [ErrorCode.INVALID_FILE_TYPE]: ['Convert file format', 'Check supported formats', 'Use a different file'],
    [ErrorCode.UPLOAD_FAILED]: ['Check network connection', 'Reselect the file', 'Try again later'],
  },
};

/**
 * 获取错误消息
 */
export function getErrorMessage(code: ErrorCode | string, language: SupportedLanguage = 'zh-CN'): string {
  const errorCode = code as ErrorCode;
  return ERROR_MESSAGES[language]?.[errorCode] || ERROR_MESSAGES['zh-CN'][ErrorCode.UNKNOWN_ERROR];
}

/**
 * 获取错误描述
 */
export function getErrorDescription(code: ErrorCode | string, language: SupportedLanguage = 'zh-CN'): string {
  const errorCode = code as ErrorCode;
  return ERROR_DESCRIPTIONS[language]?.[errorCode] || ERROR_DESCRIPTIONS['zh-CN'][ErrorCode.UNKNOWN_ERROR];
}

/**
 * 获取错误解决方案
 */
export function getErrorSolutions(code: ErrorCode | string, language: SupportedLanguage = 'zh-CN'): string[] {
  const errorCode = code as ErrorCode;
  return ERROR_SOLUTIONS[language]?.[errorCode] || ERROR_SOLUTIONS['zh-CN'][ErrorCode.UNKNOWN_ERROR];
}

/**
 * 创建完整的错误信息对象
 */
export function createErrorInfo(code: ErrorCode | string, language: SupportedLanguage = 'zh-CN') {
  return {
    code,
    message: getErrorMessage(code, language),
    description: getErrorDescription(code, language),
    solutions: getErrorSolutions(code, language),
  };
}
