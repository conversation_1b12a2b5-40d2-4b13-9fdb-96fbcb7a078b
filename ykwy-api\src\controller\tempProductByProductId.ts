import Bun from 'bun';
import type { Context } from 'hono';

import redis from '../client/redis.js';
import { BusinessLogicError, ValidationError } from '../errors/custom.error.ts';
import { TempProductService } from '../services/tempProductService';
import { tempProductParamProductIdSchema } from '../types/validators/tempProductValidator';
import { R } from '../utils/Response.ts';

const tempProductService = new TempProductService();

/**
 * 临时商品按productId查询控制器
 */
export class TempProductByProductIdController {
  /**
   * 通过productId获取临时商品详情
   * @param c Hono Context
   */
  public async findByProductId(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = tempProductParamProductIdSchema.safeParse(params);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'GET /api/v1/temp-product/by-product-id/:productId',
      });
    }

    const productId = validation.data.productId;
    const cacheKey = `temp-product:productId:${productId}`;

    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return R.success(c, JSON.parse(cached));
      }
    } catch (cacheError) {
      console.error('缓存读取失败:', cacheError);
    }

    // 查询临时商品
    const result = await tempProductService.findByProductId(productId);
    if (!result) {
      throw new BusinessLogicError('临时商品未找到', 'BIZ_PRODUCT_NOT_FOUND', 404, {
        productId,
        endpoint: 'GET /api/v1/temp-product/by-product-id/:productId',
      });
    }

    // 缓存结果
    try {
      const expire = parseInt(Bun.env['REDIS_CACHE_EXPIRE'] || '3600');
      await redis.setex(cacheKey, expire, JSON.stringify(result));
    } catch (cacheError) {
      console.error('缓存写入失败:', cacheError);
    }

    return R.success(c, result);
  }
}
