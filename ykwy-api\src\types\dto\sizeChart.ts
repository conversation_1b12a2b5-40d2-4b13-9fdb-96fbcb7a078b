import type { Size<PERSON><PERSON>, <PERSON>ze<PERSON>hartEntry, SizeChartSubEntry, SizeChartType, SubTableDef } from '@prisma/client';

/**
 * 尺码表类型 DTO - 基本信息
 */
export interface SizeChartTypeDto extends SizeChartType {
  sizeChartsCount?: number;
}

/**
 * 尺码表类型 DTO - 详细信息（包含附表定义和尺码表）
 */
export interface SizeChartTypeDetailDto extends SizeChartType {
  subTableDefs: SubTableDefDto[];
  sizeCharts: SizeChartDto[];
}

/**
 * 附表结构定义 DTO
 */
export type SubTableDefDto = SubTableDef;

/**
 * 尺码表 DTO - 基本信息
 */
export interface SizeChartDto extends SizeChart {
  type?: SizeChartTypeDto;
  parent?: SizeChartDto;
  children?: SizeChartDto[];
  entriesCount?: number;
  productCount?: number;
}

/**
 * 尺码表 DTO - 详细信息（包含完整数据）
 */
export interface SizeChartDetailD<PERSON> extends Size<PERSON>hart {
  type: SizeChartTypeDto;
  parent?: SizeChartDto;
  children: SizeChartDto[];
  entries: SizeChartEntryDto[];
  subEntries: SizeChartSubEntryDto[];
  products: Array<{
    id: string;
    title: string;
  }>;
}

/**
 * 尺码表条目 DTO
 */
export type SizeChartEntryDto = SizeChartEntry;

/**
 * 尺码表附表条目 DTO
 */
export type SizeChartSubEntryDto = SizeChartSubEntry;

/**
 * 尺码表类型列表 DTO
 */
export interface SizeChartTypeListDto {
  items: SizeChartTypeDto[];
  total: number;
}

/**
 * 尺码表列表 DTO
 */
export interface SizeChartListDto {
  items: SizeChartDto[];
  total: number;
}

/**
 * 批量删除结果 DTO
 */
export interface BulkDeleteResultDto {
  count: number;
}

/**
 * API 通用响应格式
 */
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: {
    total: number;
    skip: number;
    take?: number;
  };
}
