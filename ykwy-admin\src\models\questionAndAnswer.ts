/**
 * 问答知识库相关的类型定义
 * 基于后端QuestionAndAnswer模型设计
 */

/** 问答知识库基础信息 */
export interface QuestionAndAnswer {
  /** 问答ID */
  id: string;
  /** 问题类型 */
  questionType: string;
  /** 订单状态，使用四位二进制字符串表示（1111表示全流程：售前|发货前|发货后|售后） */
  orderStatus: string;
  /** 常见问法样本数组 */
  commonQuestionSamples: string[];
  /** 回答数组（必须字段）使用数组存储多个样本 */
  answers: string[];
  /** 分类编码，关联到KnowledgeCategory的code字段 */
  categoryCode: string;
  /** 店铺名 */
  shopName?: string | null;
  /** 店铺id */
  shopId?: string | null;
  /** 商品名称 */
  productName?: string | null;
  /** 商品链接 */
  productUrl?: string | null;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 是否已删除 */
  isDeleted: number;
}

/** 问答知识库列表返回数据 */
export interface QuestionAndAnswerListResponse {
  /** 问答列表 */
  items: QuestionAndAnswer[];
  /** 总数 */
  total: number;
}

/** 订单状态解析结果 */
export interface OrderStatus {
  /** 原始状态码 */
  raw: string;
  /** 售前状态 */
  preSale: boolean;
  /** 发货前状态 */
  beforeShip: boolean;
  /** 发货后状态 */
  afterShip: boolean;
  /** 售后状态 */
  afterSale: boolean;
  /** 可读描述 */
  description: string;
}

/** 问答知识库详细信息（包含分类信息） */
export interface QuestionAndAnswerDetail extends QuestionAndAnswer {
  /** 关联的知识库分类信息 */
  category: {
    id: string;
    name: string;
    code: string;
    level: number;
    description?: string | null;
    isActive: boolean;
    parent?: {
      id: string;
      name: string;
      code: string;
    } | null;
  };
}

/** 知识库分类信息 */
export interface KnowledgeCategory {
  /** 分类ID */
  id: string;
  /** 分类名称 */
  name: string;
  /** 分类编码 */
  code: string;
  /** 分类级别：1=大分类，2=子分类，3=子子分类 */
  level: number;
  /** 排序号 */
  sortOrder: number;
  /** 分类描述 */
  description?: string | null;
  /** 是否启用 */
  isActive: boolean;
  /** 父分类ID */
  parentId?: string | null;
  /** 子分类列表 */
  children?: KnowledgeCategory[];
  /** 父分类信息 */
  parent?: {
    id: string;
    name: string;
    code: string;
  } | null;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 是否已删除 */
  isDeleted: number;
}

/** 知识库分类简化信息 - 用于下拉选择 */
export interface KnowledgeCategorySimple {
  /** 分类编码 */
  code: string;
  /** 分类名称 */
  name: string;
  /** 分类级别 */
  level: number;
  /** 父分类编码 */
  parentCode?: string | null;
}

/** 查询参数 */
export interface QuestionAndAnswerQueryParams {
  /** 按问题类型模糊搜索 */
  questionType?: string;
  /** 按回答内容模糊搜索 */
  content?: string;
  /** 按分类编码精确匹配 */
  categoryCode?: string;
  /** 按订单状态精确匹配 */
  orderStatus?: string;
  /** 按店铺名搜索 */
  shopName?: string;
  /** 按店铺id搜索 */
  shopId?: string;
  /** 按商品名称搜索 */
  productName?: string;
  /** 跳过条数（分页） */
  skip?: number;
  /** 获取条数（分页） */
  take?: number;
  /** 排序字段 */
  sortBy?: 'createdAt' | 'updatedAt' | 'questionType' | 'categoryCode';
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 是否包含已删除 */
  includeDeleted?: boolean;
  /** 当前页码 */
  current?: number;
  /** 每页大小 */
  pageSize?: number;
}

/** 创建/更新问答输入参数 */
export interface QuestionAndAnswerInput {
  /** 问答ID，更新时必传，创建时可选 */
  id?: string;
  /** 问题类型 */
  questionType: string;
  /** 回答数组（必须字段）使用数组存储多个样本 */
  answers: string[];
  /** 分类编码，关联到KnowledgeCategory的code字段 */
  categoryCode: string;
  /** 订单状态，使用四位二进制字符串表示 */
  orderStatus: string;
  /** 常见问法样本数组 */
  commonQuestionSamples?: string[];
  /** 店铺名 */
  shopNmae?: string;
  /** 店铺id */
  shopId?: string;
  /** 商品名称 */
  productName?: string;
  /** 商品链接 */
  productUrl?: string;
}

/** 批量删除结果 */
export interface BulkDeleteResult {
  /** 删除成功的数量 */
  deletedCount: number;
  /** 删除失败的ID列表 */
  failedIds: string[];
}

/** 问答统计信息 */
export interface QuestionAndAnswerStats {
  /** 总问答数量 */
  total: number;
  /** 各问题类型统计 */
  typeStats: Array<{
    questionType: string;
    count: number;
  }>;
  /** 各分类统计 */
  categoryStats: Array<{
    categoryCode: string;
    categoryName: string;
    count: number;
  }>;
}

/** API响应结构 */
export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/** 订单状态常量定义 */
export const ORDER_STATUS = {
  PRE_SALE: '1000', // 售前
  BEFORE_SHIP: '0100', // 发货前
  AFTER_SHIP: '0010', // 发货后
  AFTER_SALE: '0001', // 售后
  ALL: '1111', // 全流程
} as const;

/** 订单状态标签映射 */
export const ORDER_STATUS_LABELS = {
  '1000': '售前',
  '0100': '发货前',
  '0010': '发货后',
  '0001': '售后',
  '1100': '售前|发货前',
  '1010': '售前|发货后',
  '1001': '售前|售后',
  '0110': '发货前|发货后',
  '0101': '发货前|售后',
  '0011': '发货后|售后',
  '1110': '售前|发货前|发货后',
  '1101': '售前|发货前|售后',
  '1011': '售前|发货后|售后',
  '0111': '发货前|发货后|售后',
  '1111': '全流程',
  '0000': '无',
} as const;

/**
 * 解析订单状态
 * @param orderStatus 四位二进制字符串
 * @returns 订单状态解析结果
 */
export const parseOrderStatus = (orderStatus: string): OrderStatus => {
  if (!orderStatus || orderStatus.length !== 4) {
    return {
      raw: orderStatus,
      preSale: false,
      beforeShip: false,
      afterShip: false,
      afterSale: false,
      description: '无效状态',
    };
  }

  const [preSale, beforeShip, afterShip, afterSale] = orderStatus
    .split('')
    .map((bit) => bit === '1');

  return {
    raw: orderStatus,
    preSale,
    beforeShip,
    afterShip,
    afterSale,
    description:
      ORDER_STATUS_LABELS[orderStatus as keyof typeof ORDER_STATUS_LABELS] ||
      '未知状态',
  };
};

/**
 * 获取订单状态标签
 * @param orderStatus 四位二进制字符串
 * @returns 状态标签
 */
export const getOrderStatusLabel = (orderStatus: string): string => {
  if (!orderStatus || orderStatus.length !== 4) return '无效状态';
  const labels = ['售前', '发货前', '发货后', '售后'];
  const result: string[] = [];
  orderStatus.split('').forEach((bit, idx) => {
    if (bit === '1') result.push(labels[idx]);
  });
  return result.length > 0 ? result.join('、') : '无';
};
