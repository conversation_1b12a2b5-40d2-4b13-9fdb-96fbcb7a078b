// 千牛监控相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse, ConnectionStats, MessageStats, QianniuConnection } from '../types';

// 连接统计查询选项
export const connectionStatsQueryOptions = () => ({
  queryKey: queryKeys.connectionStats(),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<ConnectionStats>>('platform-ws/stats');
    return (
      response.data || {
        totalConnections: 0,
        activeConnections: 0,
        onlineClients: 0,
        offlineClients: 0,
        websocket: {
          totalConnections: 0,
          connectionsByType: {},
          connectionsByOrg: {},
          connectionsByTeam: {},
        },
        database: {
          total: 0,
          online: 0,
          offline: 0,
          byOrganization: {},
        },
        realTime: {
          activeConnections: 0,
          qianniuConnections: 0,
        },
      }
    );
  },
});

// 活跃连接列表查询选项
export const activeConnectionsQueryOptions = () => ({
  queryKey: queryKeys.activeConnections(),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<QianniuConnection[]>>('platform-ws/connections');
    return response.data || [];
  },
});

// 消息统计查询选项
export const messageStatsQueryOptions = () => ({
  queryKey: queryKeys.messageStats(),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<MessageStats>>('messages/stats');
    return (
      response.data || {
        overview: {
          total: 0,
          today: 0,
          week: 0,
          month: 0,
        },
        bySenderType: {},
        byMessageType: {},
        hourlyDistribution: [],
      }
    );
  },
});
