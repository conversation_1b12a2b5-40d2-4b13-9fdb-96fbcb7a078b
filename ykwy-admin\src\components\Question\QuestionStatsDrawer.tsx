import type { QuestionAndAnswerStats } from '@/models/questionAndAnswer';
import { Card, Col, Drawer, Row, Statistic } from 'antd';
import React from 'react';

interface QuestionStatsDrawerProps {
  visible: boolean;
  stats: QuestionAndAnswerStats | null;
  categoryMap: Record<string, string>;
  onClose: () => void;
}

const QuestionStatsDrawer: React.FC<QuestionStatsDrawerProps> = ({
  visible,
  stats,
  categoryMap,
  onClose,
}) => {
  return (
    <Drawer title="统计信息" open={visible} onClose={onClose} width={500}>
      {stats && (
        <div>
          <Card style={{ marginBottom: 16 }}>
            <Statistic title="总问答数量" value={stats.total} />
          </Card>

          {stats.categoryStats && stats.categoryStats.length > 0 && (
            <Card title="分类统计">
              {stats.categoryStats.map((item, index) => (
                <Row key={index} style={{ marginBottom: 8 }}>
                  <Col span={16}>
                    {item.categoryName ||
                      categoryMap[item.categoryCode] ||
                      item.categoryCode}
                  </Col>
                  <Col span={8} style={{ textAlign: 'right' }}>
                    {item.count} 条
                  </Col>
                </Row>
              ))}
            </Card>
          )}
        </div>
      )}
    </Drawer>
  );
};

export default QuestionStatsDrawer;
