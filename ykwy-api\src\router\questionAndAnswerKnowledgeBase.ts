import { Hono } from 'hono';

import { QuestionAndAnswerKnowledgeBaseController } from '../controller/questionAndAnswerKnowledgeBase';

const router = new Hono();
const questionAndAnswerKnowledgeBase = new QuestionAndAnswerKnowledgeBaseController();

// ================================
// 问答知识库相关 API 路由
// ================================

// 创建或更新问答知识库（使用组合唯一键upsert）
// POST /api/v1/qa-knowledge-base
router.post('/qa-knowledge-base', questionAndAnswerKnowledgeBase.upsert);

// 获取问答知识库列表（支持分页、过滤、排序）
// GET /api/v1/qa-knowledge-base
router.get('/qa-knowledge-base', questionAndAnswerKnowledgeBase.findMany);

// 根据ID获取问答知识库详情
// GET /api/v1/qa-knowledge-base/:id
router.get('/qa-knowledge-base/:id', questionAndAnswerKnowledgeBase.findById);

// 软删除问答知识库
// DELETE /api/v1/qa-knowledge-base/:id
router.delete('/qa-knowledge-base/:id', questionAndAnswerKnowledgeBase.delete);

// 根据分类编码获取问答知识库
// GET /api/v1/qa-knowledge-base/category/:categoryCode
router.get('/qa-knowledge-base/category/:categoryCode', questionAndAnswerKnowledgeBase.findByCategoryCode);

// 获取统计信息
// GET /api/v1/qa-knowledge-base/stats
// router.get('/qa-knowledge-base/stats', questionAndAnswerKnowledgeBase.getStats);

// 搜索问答知识库
// GET /api/v1/qa-knowledge-base/search
router.get('/qa-knowledge-base/search', questionAndAnswerKnowledgeBase.searchByQuery);

// 批量删除问答知识库
// POST /api/v1/qa-knowledge-base/bulk-delete
router.post('/qa-knowledge-base/bulk-delete', questionAndAnswerKnowledgeBase.bulkDelete);

export default router;
