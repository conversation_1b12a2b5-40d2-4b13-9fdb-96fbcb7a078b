import { Prisma } from '@prisma/client';

import prisma from '../client/prisma.ts';
import type { BulkDeleteResultDto, ProductDetailDto, ProductDto, ProductListDto } from '../types/dto/product';
import type { ProductInput, ProductQuery } from '../types/request/product';

/**
 * 商品服务层，负责与数据库的交互
 */
export class ProductService {
  /**
   * 创建或更新商品
   * @param data 商品输入参数
   */
  async upsert(data: ProductInput): Promise<ProductDto> {
    const {
      id,
      title,
      sourceUrl,
      status,
      category,
      model,
      tags,
      // 平台相关字段
      platform100146,
      platform162887,
      platform180243,
      platformLaunchTime,
      platformFunction,
      platformThickness,
      platformPattern,
      platformInsert,
      platformMaterial,
      platformStyle,
      platformTrendElement,
      platformFit,
      platformWearingMethod,
      platformCategory,
      platformCupStyle,
      platformShoulder,
      platformClothingLength,
      platformClothingFront,
      platformSleeveLength,
      platformPantsLength,
      platformPantsFront,
      platformTargetGroup,
      platformSeason,
      platformGender,
      platformSport,
      platformLiningMaterial,
      platformFabric,
      platformCollarType,
    } = data;

    // 构建更新/创建数据对象
    const productData = {
      title,
      sourceUrl,
      status,
      category,
      model,
      tags: tags || [],
      // 平台相关字段
      platform100146,
      platform162887,
      platform180243,
      platformLaunchTime,
      platformFunction,
      platformThickness,
      platformPattern,
      platformInsert,
      platformMaterial,
      platformStyle,
      platformTrendElement,
      platformFit,
      platformWearingMethod,
      platformCategory,
      platformCupStyle,
      platformShoulder,
      platformClothingLength,
      platformClothingFront,
      platformSleeveLength,
      platformPantsLength,
      platformPantsFront,
      platformTargetGroup,
      platformSeason,
      platformGender,
      platformSport,
      platformLiningMaterial,
      platformFabric,
      platformCollarType,
    };

    // upsert: 有id则更新，无id则创建
    return prisma.product.upsert({
      where: { id: id ?? '00000000-0000-0000-0000-000000000000' },
      update: productData,
      create: productData,
    });
  }

  /**
   * 获取单个商品详情（含关联尺码表和标识符）
   * @param id 商品ID
   */
  async findById(id: string): Promise<ProductDetailDto | null> {
    const product = await prisma.product.findUnique({
      where: { id, isDeleted: 0 },
      include: {
        // 关联的尺码表绑定关系
        sizeChartBindings: {
          where: { sizeChart: { isDeleted: 0 } },
          include: {
            sizeChart: { select: { id: true, name: true } },
          },
        },
        // 关联的商品标识符
        identifiers: {
          select: {
            id: true,
            identifierType: true,
            value: true,
          },
        },
      },
    });
    if (!product) return null;
    // 返回商品详情及其关联的尺码表列表和标识符，将bigint的id转为string
    return {
      ...product,
      sizeCharts: product.sizeChartBindings.map((b) => b.sizeChart),
      identifiers: product.identifiers.map((identifier) => ({
        ...identifier,
        id: identifier.id.toString(),
      })),
    };
  }

  /**
   * 获取多个商品（分页）
   * @param params 查询参数
   */
  async findMany(params: ProductQuery): Promise<ProductListDto> {
    const { title, status, category, model, skip = 0, take = 10, includeDeleted = false } = params;
    const where: Prisma.ProductWhereInput = {};

    if (!includeDeleted) where.isDeleted = 0;
    if (title) where.title = { contains: title, mode: 'insensitive' };
    if (status !== undefined) where.status = status;
    if (category) where.category = { contains: category, mode: 'insensitive' };
    if (model) where.model = { contains: model, mode: 'insensitive' };

    // 事务：同时查列表和总数
    const [products, total] = await prisma.$transaction([
      prisma.product.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: 'desc' },
        include: {
          _count: { select: { sizeChartBindings: true } },
        },
      }),
      prisma.product.count({ where }),
    ]);
    // 返回商品列表及每个商品关联的尺码表数量
    const items = products.map((p) => ({ ...p, sizeChartCount: p._count.sizeChartBindings }));
    return { items, total };
  }

  /**
   * 软删除商品（逻辑删除）
   * @param id 商品ID
   */
  async softDelete(id: string): Promise<ProductDto> {
    return prisma.product.update({
      where: { id },
      data: { isDeleted: 1 },
    });
  }

  /**
   * 恢复已删除商品
   * @param id 商品ID
   */
  async restore(id: string): Promise<ProductDto> {
    return prisma.product.update({
      where: { id },
      data: { isDeleted: 0 },
    });
  }

  /**
   * 批量软删除商品
   * @param ids 商品ID数组
   */
  async bulkDelete(ids: string[]): Promise<BulkDeleteResultDto> {
    const result = await prisma.product.updateMany({
      where: { id: { in: ids }, isDeleted: 0 },
      data: { isDeleted: 1 },
    });
    return { count: result.count };
  }
}
