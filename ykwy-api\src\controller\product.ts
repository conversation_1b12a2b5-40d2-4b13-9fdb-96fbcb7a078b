import type { Context } from 'hono';

import { ErrorCode } from '../constants/errorCodes.ts';
import { AppError } from '../errors/custom.error.ts';
import { ProductService } from '../services/productService';
import { bulkDeleteSchema, paramIdSchema, productQuerySchema, upsertProductSchema } from '../types/validators/productValidator';
import { R } from '../utils/Response.ts';

const productService = new ProductService();

/**
 * 商品控制器，处理商品相关的请求
 */
export class ProductController {
  /**
   * 创建或更新商品
   * @param c Hono Context
   */
  public async upsert(c: Context) {
    const body = await c.req.json();
    // 参数校验
    const validation = upsertProductSchema.safeParse(body);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }
    // 调用服务层
    const result = await productService.upsert(validation.data);
    return R.success(c, result);
  }

  /**
   * 获取单个商品详情
   * @param c Hono Context
   */
  public async findById(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }
    // 查询商品
    const result = await productService.findById(validation.data.id);
    if (!result) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '商品未找到',
      });
    }
    return R.success(c, result);
  }

  /**
   * 获取商品列表（支持分页、过滤）
   * @param c Hono Context
   */
  public async findMany(c: Context) {
    const query = c.req.query();
    // 参数校验
    const validation = productQuerySchema.safeParse(query);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }
    // 查询商品列表
    const result = await productService.findMany(validation.data);
    return R.success(c, result);
  }

  /**
   * 软删除单个商品
   * @param c Hono Context
   */
  public async delete(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }
    // 软删除
    const result = await productService.softDelete(validation.data.id);
    return R.success(c, result);
  }

  /**
   * 恢复已删除商品
   * @param c Hono Context
   */
  public async restore(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }
    // 恢复
    const result = await productService.restore(validation.data.id);

    return R.success(c, result);
  }

  /**
   * 批量软删除商品
   * @param c Hono Context
   */
  public async bulkDelete(c: Context) {
    const body = await c.req.json();
    // 参数校验
    const validation = bulkDeleteSchema.safeParse(body);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
        issues: validation.error.issues,
      });
    }
    // 批量软删除
    const result = await productService.bulkDelete(validation.data.ids);

    return R.success(c, result);
  }
}
