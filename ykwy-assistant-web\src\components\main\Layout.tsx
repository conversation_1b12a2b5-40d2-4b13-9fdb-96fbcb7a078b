import { useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';
import { Toaster } from 'sonner';

import { useOrganization } from '../../hooks/useOrganization';
import { useWebSocket, WSStatus } from '../../hooks/useWebSocket';
import { authClient } from '../../lib/auth-client';
import { useAppStore } from '../../lib/store';
import Navbar from './Navbar';
import PageContainer from './PageContainer';
import Sidebar from './Sidebar';

import { cn } from '@/lib/utils';

export default function Layout() {
  /* 用户会话 */
  const { data: session } = authClient.useSession();
  const { organizationId, hasOrganization } = useOrganization();
  const sidebarCollapsed = useAppStore((state) => state.sidebarCollapsed);
  const isMobileView = useAppStore((state) => state.isMobileView);
  const setIsMobileView = useAppStore((state) => state.setIsMobileView);
  const mobileSidebarOpen = useAppStore((state) => state.mobileSidebarOpen);

  // 添加小屏幕PC判断
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  const user = session?.user;

  const wsStatus: WSStatus = useWebSocket({
    userId: user?.id,
    organizationId: organizationId,
    enabled: !!user?.id && !!organizationId,
  });

  // 监听窗口大小变化，设置移动视图状态和小屏幕状态
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const isMobile = width < 768; // md 断点
      const isSmall = width >= 768 && width < 1024; // 介于md和lg之间

      setIsMobileView(isMobile);
      setIsSmallScreen(isSmall);
    };

    // 初始调用一次
    handleResize();

    // 添加监听
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [setIsMobileView]);

  if (!hasOrganization) {
    console.log(`[Layout] 您当前未加入任何组织，无法使用系统，请联系管理员邀请您加入组织`);
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <Toaster position="top-right" richColors />
      <Navbar wsStatus={wsStatus} isSmallScreen={isSmallScreen} />

      {/* 移动端侧边栏遮罩 */}
      {isMobileView && mobileSidebarOpen && <div className="fixed inset-0 bg-black/30 z-40 md:hidden" onClick={() => useAppStore.getState().closeMobileSidebar()} />}

      {/* 响应式侧边栏 */}
      <Sidebar isSmallScreen={isSmallScreen} />

      {/* 响应式主内容区域 */}
      <main
        className={cn(
          'fixed top-16 right-0 bottom-0 transition-all duration-300 ease-in-out',
          // 大屏幕桌面视图
          !isMobileView && !isSmallScreen && (sidebarCollapsed ? 'left-16' : 'left-64'),
          // 小屏幕PC视图 - 永久折叠侧边栏
          isSmallScreen && 'left-16',
          // 移动视图
          isMobileView && 'left-0',
        )}
      >
        <PageContainer>
          <Outlet />
        </PageContainer>
      </main>
    </div>
  );
}
