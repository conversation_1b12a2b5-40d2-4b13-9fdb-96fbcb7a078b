.login-container {
  max-width: clamp(90vw, 500px, 540px);
  transition: all 300ms ease;
  transform: translateX(1rem);
}

@media (min-width: 640px) {
  .login-container {
    transform: translateX(2rem);
  }
}

@media (min-width: 768px) {
  .login-container {
    transform: translateX(4rem);
  }
}

@media (min-width: 1024px) {
  .login-container {
    transform: translate(5vw, 2vh);
  }
}

@media (min-width: 1280px) {
  .login-container {
    transform: translate(8vw, 3vh);
  }
}

@media (min-width: 1536px) {
  .login-container {
    transform: translate(10vw, 4vh);
  }
}
