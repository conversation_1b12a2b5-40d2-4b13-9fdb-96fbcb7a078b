import { exec, execSync, spawn } from 'node:child_process';
import fs from 'node:fs';
import path from 'node:path';

import ConfigManager from '../config/config-manager.js';
import ApiService from '../services/api-service.js';
import AuthService from '../services/auth-service.js';
import ProcessManager from '../utils/process-manager.js';

/**
 * 千牛连接器核心类
 * 负责协调各个模块完成千牛连接的完整流程
 */
class QianniuConnector {
  private scriptDir: string;
  private qianniuRunnerPath: string;
  private tcpProxyPath: string;
  private qnQtHelpPath: string;
  private proxyProcess: import('child_process').ChildProcess | null = null;
  private processMonitor: { stop: () => void } | null = null;
  private processedPids: Set<string> = new Set();
  private configManager: ConfigManager;
  private authService: AuthService;
  private apiService: ApiService;

  constructor() {
    // 支持独立模式：如果设置了 QIANNIU_RESOURCE_DIR 环境变量，使用该目录
    this.scriptDir = process.env['QIANNIU_RESOURCE_DIR'] || path.join(__dirname, '../..');
    this.qianniuRunnerPath = path.join(this.scriptDir, 'tools/qianniu-runner.cjs');
    this.tcpProxyPath = path.join(this.scriptDir, 'tools/tcp-proxy.cjs');
    this.qnQtHelpPath = path.join(this.scriptDir, 'bin/QNQtHelp64.exe');
    this.proxyProcess = null;
    this.processMonitor = null;
    this.processedPids = new Set(); // 记录已处理的进程PID

    // 初始化服务
    this.configManager = new ConfigManager();
    this.authService = new AuthService(this.configManager.getConfig());
    this.apiService = new ApiService(this.configManager.getConfig(), this.authService);
  }

  /**
   * 设置千牛连接（注入脚本）
   * @param {string} scriptUrl - 脚本地址
   * @returns {Promise<boolean>} 是否成功
   */
  async setupConnection(scriptUrl: string): Promise<boolean> {
    console.log('🔧 开始设置千牛连接...');

    // 1. 检查千牛是否关闭
    console.log('📋 检查千牛进程状态...');
    const processes = await ProcessManager.checkQianniuProcess();

    if (processes.length > 0) {
      console.log('⚠️  检测到千牛正在运行，需要先关闭千牛客户端');
      console.log('💡 请手动关闭千牛客户端，然后重新运行此命令');
      console.log('🔍 当前运行的千牛进程:');
      ProcessManager.displayProcesses(processes);
      return false;
    }

    console.log('✅ 千牛客户端已关闭，可以继续');

    // 2. 执行脚本注入
    console.log('💉 开始注入脚本...');
    try {
      const command = `node "${this.qianniuRunnerPath}" inject --url "${scriptUrl}"`;
      console.log(`🚀 执行命令: ${command}`);

      execSync(command, {
        stdio: 'inherit',
        cwd: this.scriptDir,
      });

      console.log('✅ 脚本注入完成！');
      console.log('💡 现在可以启动千牛客户端，然后运行消息发送服务');
      return true;
    } catch (error) {
      console.error('❌ 脚本注入失败:', (error as Error).message);
      return false;
    }
  }

  /**
   * 启动消息发送服务
   * @param {string} backendHost - 后端主机
   * @param {number} backendPort - 后端端口
   * @returns {Promise<boolean>} 是否成功
   */
  async startMessageService(backendHost?: string, backendPort?: number) {
    console.log('🚀 启动消息发送服务...');

    // 使用配置中的默认值
    const config = this.configManager.getConfig();
    const host = backendHost || config.tcpProxy.backendHost;
    const port = backendPort || config.tcpProxy.backendPort;

    // 1. 检查千牛是否运行
    console.log('📋 检查千牛进程状态...');
    const processes = await ProcessManager.checkQianniuProcess();

    if (processes.length === 0) {
      console.log('⚠️  未检测到千牛进程，请先启动千牛客户端');
      console.log('💡 启动千牛后，请重新运行此命令');
      return false;
    }

    console.log('✅ 检测到千牛进程:');
    ProcessManager.displayProcesses(processes);

    // 2. 启动TCP代理服务
    console.log('🌐 启动TCP代理服务...');
    try {
      this.proxyProcess = spawn('node', [this.tcpProxyPath, host, String(port)], {
        cwd: this.scriptDir,
        stdio: 'inherit',
      });

      // 等待代理服务启动
      await new Promise((resolve) => setTimeout(resolve, 2000));

      console.log('✅ TCP代理服务已启动');
    } catch (error) {
      console.error('❌ TCP代理服务启动失败:', error.message);
      return false;
    }

    // 3. 执行QNQtHelp64.exe
    console.log('🔧 执行千牛助手...');
    try {
      // 使用第一个找到的千牛进程PID
      const targetPid = processes[0].pid;
      const command = `"${this.qnQtHelpPath}" ${targetPid}`;

      console.log(`🚀 执行命令: ${command}`);

      exec(command, { cwd: this.scriptDir }, (error, stdout) => {
        if (error) {
          console.error('❌ QNQtHelp64执行出错:', error.message);
        } else {
          console.log('✅ QNQtHelp64执行完成');
          if (stdout) console.log('输出:', stdout);
        }
      });

      console.log('🎉 消息发送服务启动完成！');
      console.log('📡 TCP代理监听端口: 9996');
      console.log(`🎯 转发到后端: ${backendHost}:${backendPort}`);
      console.log('💡 按 Ctrl+C 停止服务');

      return true;
    } catch (error) {
      console.error('❌ QNQtHelp64执行失败:', error.message);
      return false;
    }
  }

  /**
   * 检查千牛是否已经被注入过脚本
   * @returns {Promise<boolean>} 是否已注入
   */
  async checkIfAlreadyInjected() {
    console.log('🔍 检查千牛是否已被注入脚本...');

    try {
      // 常见的千牛安装路径
      const possiblePaths = [
        'C:\\Program Files\\AliWorkbench',
        'C:\\Program Files (x86)\\AliWorkbench',
        'D:\\Program Files\\AliWorkbench',
        'D:\\Program Files (x86)\\AliWorkbench',
        'C:\\Program Files\\qianniu',
        'C:\\Program Files (x86)\\qianniu',
        'D:\\Program Files\\qianniu',
      ];

      for (const basePath of possiblePaths) {
        if (fs.existsSync(basePath)) {
          console.log(`🔍 检查路径: ${basePath}`);

          // 查找版本目录
          const items = fs.readdirSync(basePath);
          for (const item of items) {
            const itemPath = path.join(basePath, item);
            if (fs.statSync(itemPath).isDirectory() && /^\d+\.\d+\.\d+/.test(item)) {
              const webuiPath = path.join(itemPath, 'Resources', 'newWebui', 'webui.zip');
              const backupPath = webuiPath + '.backup';

              if (fs.existsSync(backupPath)) {
                console.log('✅ 检测到备份文件，千牛已被注入脚本');
                return true;
              }
            }
          }
        }
      }

      console.log('📝 未检测到注入痕迹，千牛尚未被注入脚本');
      return false;
    } catch (error) {
      console.log('📝 无法确定注入状态，假设未注入');
      console.log(`检查详情: ${error.message}`);
      return false;
    }
  }

  /**
   * 启动TCP代理服务
   * @param {string} backendHost - 后端主机
   * @param {number} backendPort - 后端端口
   * @returns {Promise<boolean>} 是否成功
   */
  async startTcpProxy(backendHost?: string, backendPort?: number): Promise<boolean> {
    // 使用配置中的默认值
    const config = this.configManager.getConfig();
    const host = backendHost || config.tcpProxy.backendHost;
    const port = backendPort || config.tcpProxy.backendPort;
    return new Promise((resolve) => {
      try {
        this.proxyProcess = spawn('node', [this.tcpProxyPath, host, String(port)], {
          cwd: this.scriptDir,
          stdio: 'inherit',
        });

        // 监听进程错误
        this.proxyProcess.on('error', (error) => {
          console.error('❌ TCP代理进程启动失败:', error.message);
          resolve(false);
        });

        // 监听进程退出
        this.proxyProcess.on('exit', (code) => {
          if (code !== 0 && code !== null) {
            console.error(`❌ TCP代理进程异常退出，退出码: ${code}`);
            resolve(false);
          }
        });

        // 等待代理服务启动
        setTimeout(() => {
          if (this.proxyProcess && !this.proxyProcess.killed) {
            console.log('✅ TCP代理服务已启动');
            console.log('📡 TCP代理监听端口: 9996');
            console.log(`🎯 转发到后端: ${backendHost}:${backendPort}`);
            resolve(true);
          } else {
            console.error('❌ TCP代理进程启动失败');
            resolve(false);
          }
        }, 3000); // 增加等待时间到3秒，确保能捕获到端口占用错误
      } catch (error) {
        console.error('❌ TCP代理服务启动失败:', error.message);
        resolve(false);
      }
    });
  }

  /**
   * 启动持续的千牛进程监听
   * @param {string} backendHost - 后端主机
   * @param {number} backendPort - 后端端口
   */
  async startContinuousMonitoring(): Promise<void> {
    console.log('🔄 启动千牛进程持续监听...');
    console.log('💡 每当检测到新的千牛进程启动时，将自动执行QNQtHelp64');

    // 停止之前的监听器
    if (this.processMonitor) {
      this.processMonitor.stop();
    }

    // 首先检查当前已存在的进程
    console.log('🔍 检查当前已存在的千牛进程...');
    try {
      const existingProcesses = await ProcessManager.checkQianniuProcess();
      console.log(`📊 当前检测到 ${existingProcesses.length} 个千牛进程:`);
      ProcessManager.displayProcesses(existingProcesses);

      // 处理所有现有进程
      for (const process of existingProcesses) {
        if (!this.processedPids.has(process.pid)) {
          console.log(`🎯 处理现有进程 PID ${process.pid}...`);
          this.processedPids.add(process.pid);

          try {
            await this.executeQNQtHelp(process.pid);
            console.log(`✅ 现有进程 PID ${process.pid} 处理完成`);
          } catch (error) {
            console.error(`❌ 处理现有进程 PID ${process.pid} 时出错: ${error.message}`);
          }
        } else {
          console.log(`⏭️  跳过已处理的进程 PID ${process.pid}`);
        }
      }
    } catch (error) {
      console.error('❌ 检查现有进程时出错:', error.message);
    }

    // 启动新的监听器
    this.processMonitor = ProcessManager.startProcessMonitor(
      async (process) => {
        // 新进程启动时的处理
        console.log(`🔍 监听器检测到进程: PID ${process.pid}`);
        if (!this.processedPids.has(process.pid)) {
          console.log(`🎯 为新进程 PID ${process.pid} 执行QNQtHelp64...`);
          this.processedPids.add(process.pid);

          try {
            await this.executeQNQtHelp(process.pid);
            console.log(`✅ 新进程 PID ${process.pid} 处理完成`);
          } catch (error) {
            console.error(`❌ 处理新进程 PID ${process.pid} 时出错: ${error.message}`);
          }
        } else {
          console.log(`⏭️  跳过已处理的进程 PID ${process.pid}`);
        }
      },
      (process) => {
        // 进程退出时的处理
        console.log(`🚪 进程 PID ${process.pid} 已退出`);
        this.processedPids.delete(process.pid);
      },
      2000, // 每2秒检测一次
    );

    console.log('✅ 千牛进程监听已启动');
  }

  /**
   * 执行QNQtHelp64.exe
   * @param {string} targetPid - 目标进程PID
   * @returns {Promise<boolean>} 是否成功
   */
  async executeQNQtHelp(targetPid) {
    return new Promise((resolve, reject) => {
      const command = `"${this.qnQtHelpPath}" ${targetPid}`;
      console.log(`🚀 执行命令: ${command}`);

      // 使用 spawn 并设置为 detached 模式，让程序在后台运行
      const child = spawn(this.qnQtHelpPath, [targetPid], {
        cwd: this.scriptDir,
        detached: true, // 分离进程，让它独立运行
        stdio: 'ignore', // 忽略所有 stdio，避免挂起
        windowsHide: true, // Windows 上隐藏窗口
      });

      // 立即分离子进程，让它独立运行
      child.unref();

      // 给一点时间让程序启动，然后就认为成功
      setTimeout(() => {
        console.log(`✅ QNQtHelp64已启动 (PID ${targetPid})`);
        resolve(true);
      }, 500); // 500ms后就认为启动成功

      child.on('error', (error) => {
        console.error(`❌ QNQtHelp64启动失败 (PID ${targetPid}):`, error.message);
        reject(error);
      });
    });
  }

  /**
   * 停止服务
   */
  stopService() {
    if (this.processMonitor) {
      console.log('🛑 停止千牛进程监听...');
      this.processMonitor.stop();
      this.processMonitor = null;
    }

    if (this.proxyProcess) {
      console.log('🛑 正在停止TCP代理服务...');
      this.proxyProcess.kill('SIGINT');
      this.proxyProcess = null;
      console.log('✅ 服务已停止');
    }
  }

  /**
   * 获取配置管理器
   */
  getConfigManager() {
    return this.configManager;
  }

  /**
   * 获取API服务
   */
  getApiService() {
    return this.apiService;
  }
}

export default QianniuConnector;
