import { wsManager } from '../lib/websocket';
import type { Pattern } from '../shared/query-utils';

/** 构造失效消息 { type:'invalidate', payload:{ pattern, exact } } */
function buildInvalidate(pattern: Pattern, exact = true) {
  return JSON.stringify({
    type: 'invalidate',
    payload: { pattern, exact },
  });
}

// 会话列表分页（所有页）
export function notifyConversationListChanged(orgId: string) {
  const pattern = ['app', 'conversations', {}] as const;
  const message = buildInvalidate(pattern);
  console.log('[WS] send invalidate (conv-list):', pattern);
  wsManager.broadcast(orgId, message);
}

export function notifyMessageListChanged(orgId: string, cid: string) {
  const pattern = ['app', 'conversations', cid, 'messages'] as const;
  const message = buildInvalidate(pattern);
  console.log('[WS] send invalidate (msg-list):', pattern);
  wsManager.broadcast(orgId, message);
}

// 未读消息数变化通知
export function notifyUnreadCountChanged(orgId: string, conversationId: string, unreadCount: number) {
  const message = JSON.stringify({
    type: 'unread-count-changed',
    payload: { conversationId, unreadCount },
  });
  console.log('[WS] send unread-count-changed:', conversationId, unreadCount);
  wsManager.broadcast(orgId, message);
}

// 千牛客户端列表变化通知
export function notifyQianniuClientListChanged(orgId: string) {
  const pattern = ['qianniu-clients'] as const;
  const message = buildInvalidate(pattern);
  console.log('[WS] send invalidate (qianniu-clients):', pattern);
  wsManager.broadcast(orgId, message);
}

// 连接邀请列表变化通知
export function notifyConnectionInvitationListChanged(orgId: string) {
  const pattern = ['connection-invitations'] as const;
  const message = buildInvalidate(pattern);
  console.log('[WS] send invalidate (connection-invitations):', pattern);
  wsManager.broadcast(orgId, message);
}

// 自动回复状态变更通知（关闭）
export function notifyAutoReplyDisabled(orgId: string, conversationId: string, customerName: string) {
  const message = JSON.stringify({
    type: 'auto-reply-disabled',
    payload: {
      conversationId,
      customerName,
      timestamp: new Date().toISOString(),
    },
  });
  console.log('[WS] send auto-reply-disabled:', conversationId);
  wsManager.broadcast(orgId, message);
}

// 自动回复状态变更通知（开启）
export function notifyAutoReplyEnabled(orgId: string, conversationId: string, customerName: string) {
  const message = JSON.stringify({
    type: 'auto-reply-enabled',
    payload: {
      conversationId,
      customerName,
      timestamp: new Date().toISOString(),
    },
  });
  console.log('[WS] send auto-reply-enabled:', conversationId);
  wsManager.broadcast(orgId, message);
}

// 通用的自动回复状态变更通知
export function notifyAutoReplyStatusChanged(orgId: string, conversationId: string, customerName: string, enabled: boolean) {
  const message = JSON.stringify({
    type: enabled ? 'auto-reply-enabled' : 'auto-reply-disabled',
    payload: {
      conversationId,
      customerName,
      timestamp: new Date().toISOString(),
      enabled,
    },
  });
  console.log(`[WS] send auto-reply-status-changed (${enabled ? 'enabled' : 'disabled'}):`, conversationId);
  wsManager.broadcast(orgId, message);
}

// 客户新消息通知
export function notifyCustomerMessage(orgId: string, messageId: string, conversationId: string, customerName: string, isAutoReplied: boolean) {
  const message = JSON.stringify({
    type: 'customer-message',
    payload: {
      messageId,
      conversationId,
      customerName,
      timestamp: new Date().toISOString(),
      isAutoReplied,
    },
  });
  console.log('[WS] send customer-message:', conversationId, messageId);
  wsManager.broadcast(orgId, message);
}

// 客服正在输入状态通知
export function notifyTypingStatus(orgId: string, conversationId: string, isTyping: boolean) {
  const message = JSON.stringify({
    type: 'typing-status',
    payload: {
      conversationId,
      isTyping,
      timestamp: new Date().toISOString(),
    },
  });
  console.log(`[WS] send typing-status (${isTyping ? 'typing' : 'stopped'}):`, conversationId);
  wsManager.broadcast(orgId, message);
}
