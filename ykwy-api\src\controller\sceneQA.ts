import type { Context } from 'hono';

import { ValidationError } from '../errors/custom.error.ts';
import { SceneQAService } from '../services/sceneQAService';
import { sceneQABulkDeleteSchema, sceneQAInputSchema, sceneQAParamIdSchema, sceneQAQuerySchema, sceneQASearchByQuerySchema } from '../types/validators/sceneQAValidator';
import { R } from '../utils/Response.ts';

const sceneQAService = new SceneQAService();

/**
 * 场景Q&A控制器，处理场景Q&A相关的请求
 * 只提供V2权限的接口
 */
export class SceneQAController {
  /**
   * 创建或更新场景Q&A (基于question+intent组合唯一键)
   * @param c Hono Context
   */
  public async upsert(c: Context) {
    const body = await c.req.json();

    // 参数校验
    const validation = sceneQAInputSchema.safeParse(body);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v2/scene-qa',
      });
    }

    // 调用服务层
    const result = await sceneQAService.upsert(validation.data);

    return R.success(c, result);
  }

  /**
   * 获取单个场景Q&A详情
   * @param c Hono Context
   */
  public async findById(c: Context) {
    const params = c.req.param();

    // 参数校验
    const validation = sceneQAParamIdSchema.safeParse(params);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'GET /api/v2/scene-qa/:id',
      });
    }

    // 查询场景Q&A
    const result = await sceneQAService.findById(validation.data.id);
    if (!result) {
      return c.json(
        {
          code: 404,
          message: '场景Q&A未找到',
          data: null,
        },
        404,
      );
    }

    return R.success(c, result);
  }

  /**
   * 根据店铺ID和其他条件搜索场景Q&A
   * @param c Hono Context
   */
  public async searchByQuery(c: Context) {
    try {
      const searchParams = {
        shopId: c.req.query('shopId'),
        question: c.req.query('question'),
        answer: c.req.query('answer'),
        keyword: c.req.query('keyword'),
        intent: c.req.query('intent'),
        productUrl: c.req.query('productUrl'),
        query: c.req.query('query'),
        skip: c.req.query('skip'),
        take: c.req.query('take'),
      };
      // 参数校验
      const validation = sceneQASearchByQuerySchema.safeParse(searchParams);
      if (!validation.success) {
        return c.json(
          {
            code: 400,
            message: validation.error.issues[0]?.message || '参数验证失败',
            data: { items: [], total: 0 },
          },
          400,
        );
      }

      const results = await sceneQAService.searchByQuery(validation.data);

      return c.json({
        code: 0,
        message: 'success',
        data: results,
      });
    } catch (error) {
      console.error('搜索场景Q&A失败:', error);
      return c.json(
        {
          code: 500,
          message: 'fail',
          data: [],
        },
        500,
      );
    }
  }

  /**
   * 获取场景Q&A列表（支持分页、过滤）
   * @param c Hono Context
   */
  public async findMany(c: Context) {
    const query = c.req.query();

    // 参数校验
    const validation = sceneQAQuerySchema.safeParse(query);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestQuery: query,
        endpoint: 'GET /api/v2/scene-qa',
      });
    }

    // 业务逻辑校验：分页参数非法
    if (validation.data.take <= 0 || validation.data.skip < 0) {
      return c.json(
        {
          code: 400,
          message: '分页参数非法',
          data: null,
        },
        400,
      );
    }

    // 查询场景Q&A列表
    const result = await sceneQAService.findMany(validation.data);

    return R.success(c, result);
  }

  /**
   * 删除单个场景Q&A
   * @param c Hono Context
   */
  public async delete(c: Context) {
    const params = c.req.param();

    // 参数校验
    const validation = sceneQAParamIdSchema.safeParse(params);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'DELETE /api/v2/scene-qa/:id',
      });
    }

    try {
      // 删除
      const result = await sceneQAService.delete(validation.data.id);
      return R.success(c, result);
    } catch (error) {
      console.error('删除场景Q&A失败:', error);
      return c.json(
        {
          code: 404,
          message: '场景Q&A未找到或删除失败',
          data: null,
        },
        404,
      );
    }
  }

  /**
   * 批量删除场景Q&A
   * @param c Hono Context
   */
  public async bulkDelete(c: Context) {
    const body = await c.req.json();

    // 参数校验
    const validation = sceneQABulkDeleteSchema.safeParse(body);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v2/scene-qa/bulk-delete',
      });
    }

    // 业务逻辑校验：ids 不能为空
    if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
      return c.json(
        {
          code: 400,
          message: '删除ID列表不能为空',
          data: null,
        },
        400,
      );
    }

    // 批量删除
    const result = await sceneQAService.bulkDelete(validation.data.ids);

    return R.success(c, result);
  }

  /**
   * 批量创建场景Q&A
   * @param c Hono Context
   */
  public async bulkCreate(c: Context) {
    const body = await c.req.json();

    // 参数校验 - 这里简单验证是数组
    if (!Array.isArray(body)) {
      throw new ValidationError([], {
        message: '请求体必须是数组',
        requestBody: body,
        endpoint: 'POST /api/v2/scene-qa/bulk-create',
      });
    }

    // 业务逻辑校验：数组不能为空
    if (body.length === 0) {
      return c.json(
        {
          code: 400,
          message: '批量创建列表不能为空',
          data: null,
        },
        400,
      );
    }

    // 批量创建
    const result = await sceneQAService.bulkCreate(body);

    return R.success(c, result);
  }

  /**
   * 清空所有场景Q&A
   * @param c Hono Context
   */
  public async truncate(c: Context) {
    const result = await sceneQAService.truncate();

    return R.success(c, result);
  }
}
