"""
淘宝产品信息智能销售代理启动入口
"""
import uvicorn
from src.sales_agent.api.app import app
from src.sales_agent.utils.config import settings


if __name__ == "__main__":
    print("🚀 启动淘宝产品信息智能销售代理...")

    # 打印所有环境变量配置
    settings.print_all_env_vars()

    print(f"📊 数据目录: {settings.data_dir}")
    print(f"🌐 服务地址: http://{settings.api_host}:{settings.api_port}")
    print(f"🔧 调试模式: {settings.debug}")

    uvicorn.run(
        "src.sales_agent.api.app:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
