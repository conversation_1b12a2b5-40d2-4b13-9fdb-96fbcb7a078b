### 问答知识库 API 测试

### 1. 获取问答列表
GET {{host}}/v1/questionAndAnswers
Content-Type: application/json

### 2. 分页和筛选获取问答列表
GET {{host}}/v1/questionAndAnswers?questionType=发什么快递&take=5&sortBy=consultCount&sortOrder=desc
Content-Type: application/json

### 3. 获取单个问答详情
GET {{host}}/v1/questionAndAnswer/da59e057-e847-4515-8924-50b28b918c20
Content-Type: application/json

### 4. 根据问题类型查找问答
GET {{host}}/v1/questionAndAnswers/type/发什么快递
Content-Type: application/json

### 5. 根据订单状态查找问答
GET {{host}}/v1/questionAndAnswers/status/1111
Content-Type: application/json

### 6. 获取所有唯一的问题类型
GET {{host}}/v1/questionAndAnswers/types
Content-Type: application/json

### 7. 获取统计数据
GET {{host}}/v1/questionAndAnswers/stats?topLimit=5
Content-Type: application/json

### 8. 创建新问答
POST {{host}}/v1/questionAndAnswer
Content-Type: application/json

{
  "questionType": "测试问题类型",
  "answer1": "这是测试回答1",
  "answer2": "这是测试回答2",
  "orderStatus": "1010",
  "commonQuestionSamples": ["测试问法1", "测试问法2"],
  "consultCount": 5
}

### 9. 更新问答
POST {{host}}/v1/questionAndAnswer
Content-Type: application/json

{
  "id": "da59e057-e847-4515-8924-50b28b918c20",
  "questionType": "发什么快递（已更新）",
  "answer1": "QK为了给友友们更好更快的体验,大部分地区发顺丰快递哦...",
  "answer2": "您好，请稍等一下",
  "orderStatus": "1111",
  "commonQuestionSamples": ["请问寄的什么快递？", "亲，你们发圆通吗"],
  "consultCount": 35
}

### 10. 增加咨询次数
POST {{host}}/v1/questionAndAnswer/da59e057-e847-4515-8924-50b28b918c20/increment
Content-Type: application/json

{
  "increment": 2
}

### 11. 更新订单状态
PATCH {{host}}/v1/questionAndAnswer/da59e057-e847-4515-8924-50b28b918c20/status
Content-Type: application/json

{
  "orderStatus": "1110"
}

### 12. 根据常见问法样本搜索
GET {{host}}/v1/questionAndAnswers/sample/请问寄的什么快递？
Content-Type: application/json

### 13. 软删除问答
DELETE {{host}}/v1/questionAndAnswer/da59e057-e847-4515-8924-50b28b918c20
Content-Type: application/json

### 14. 恢复已删除问答
POST {{host}}/v1/questionAndAnswer/da59e057-e847-4515-8924-50b28b918c20/restore
Content-Type: application/json

### 15. 批量删除问答
POST {{host}}/v1/questionAndAnswers/bulk-delete
Content-Type: application/json

{
  "ids": [
    "85819b87-2266-4b79-aa27-a29390e5e8eb",
    "ddb92fed-23d5-480a-ae58-828bb0e64d06"
  ]
}

### 16. 按内容搜索问答
GET {{host}}/v1/questionAndAnswers?content=快递&take=3
Content-Type: application/json

### 17. 按咨询次数范围筛选
GET {{host}}/v1/questionAndAnswers?minConsultCount=50&maxConsultCount=200&sortBy=consultCount&sortOrder=desc
Content-Type: application/json

### 18. 获取包含已删除记录的列表
GET {{host}}/v1/questionAndAnswers?includeDeleted=true&take=20
Content-Type: application/json

### 19. 统计数据（指定时间范围）
GET {{host}}/v1/questionAndAnswers/stats?startDate=2024-01-01&endDate=2024-12-31&topLimit=10
Content-Type: application/json 