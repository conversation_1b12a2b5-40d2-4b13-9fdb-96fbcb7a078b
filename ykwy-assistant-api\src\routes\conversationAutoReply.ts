import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { prisma } from '../lib/db';
import { createErrorResponse, createResponse, handleError } from '../lib/utils';
import { notifyAutoReplyStatusChanged } from '../services/eventService';
import { salesAgentIntegrationService } from '../services/salesAgentIntegrationService';

const conversationAutoReplyRoute = new Hono();

// 获取对话自动回复状态
conversationAutoReplyRoute.get(
  '/:conversationId/auto-reply',
  zValidator(
    'param',
    z.object({
      conversationId: z.string().uuid('无效的对话ID'),
    }),
  ),
  async (c) => {
    try {
      const { conversationId } = c.req.valid('param');

      const conversation = await prisma.conversation.findUnique({
        where: { id: conversationId },
        select: {
          id: true,
          autoReplyEnabled: true,
          organizationId: true,
        },
      });

      if (!conversation) {
        return c.json(createErrorResponse('对话不存在'), 404);
      }

      return c.json(
        createResponse(
          {
            conversationId: conversation.id,
            autoReplyEnabled: conversation.autoReplyEnabled,
          },
          '获取自动回复状态成功',
        ),
      );
    } catch (error) {
      return handleError(c, error);
    }
  },
);

/**
 * 设置对话自动回复状态
 * 注意：自动回复状态现在由系统自动控制，不再提供手动切换功能
 * - 当启用自动回复时，系统会处理用户最新消息并通知客服人员
 * - 当关闭自动回复时，系统会通知客服人员接管会话
 */
conversationAutoReplyRoute.put(
  '/:conversationId/auto-reply',
  zValidator(
    'param',
    z.object({
      conversationId: z.string().uuid('无效的对话ID'),
    }),
  ),
  zValidator(
    'json',
    z.object({
      enabled: z.boolean(),
    }),
  ),
  async (c) => {
    try {
      const { conversationId } = c.req.valid('param');
      const { enabled } = c.req.valid('json');

      // 检查对话是否存在
      const existingConversation = await prisma.conversation.findUnique({
        where: { id: conversationId },
        select: {
          id: true,
          organizationId: true,
          autoReplyEnabled: true,
          customerId: true,
        },
      });

      if (!existingConversation) {
        return c.json(createErrorResponse('对话不存在'), 404);
      }

      // 更新自动回复状态
      console.log(`[Auto Reply] Attempting to update conversation ${conversationId} with enabled=${enabled}`);

      const updatedConversation = await prisma.conversation.update({
        where: { id: conversationId },
        data: { autoReplyEnabled: enabled },
        select: {
          id: true,
          autoReplyEnabled: true,
        },
      });

      console.log(`[Auto Reply] Update result:`, updatedConversation);

      const changed = existingConversation.autoReplyEnabled !== enabled;

      console.log(`[Auto Reply] Conversation ${conversationId} auto reply ${enabled ? 'enabled' : 'disabled'}`);
      console.log(`[Auto Reply] Database updated:`, {
        before: existingConversation.autoReplyEnabled,
        after: updatedConversation.autoReplyEnabled,
        requestedValue: enabled,
        changed: changed,
      });

      // 如果自动回复状态发生变化，无论是开启还是关闭，都发送通知
      if (changed) {
        console.log(`[Auto Reply] Auto reply status changed, sending notification...`);

        // 立即获取客户信息并发送通知
        try {
          // 获取客户信息
          const customer = await prisma.customer.findUnique({
            where: { id: existingConversation.customerId },
            select: { nickname: true },
          });

          // 发送WebSocket通知
          if (customer) {
            notifyAutoReplyStatusChanged(existingConversation.organizationId, conversationId, customer.nickname, enabled);
            console.log(`[Auto Reply] Notification sent for customer: ${customer.nickname}, auto reply ${enabled ? 'enabled' : 'disabled'}`);
          }
        } catch (error) {
          console.error(`[Auto Reply] Error sending notification:`, error);
        }
      }

      // 如果是开启自动回复，检查是否需要立即回复最新的客户消息
      if (enabled && changed) {
        console.log(`[Auto Reply] Auto reply enabled, checking for pending customer messages...`);

        // 立即处理最新消息
        try {
          // 获取最新一条消息
          const latestMessage = await prisma.message.findFirst({
            where: { conversationId },
            orderBy: { createdAt: 'desc' },
          });

          // 如果最新消息是客户消息，立即触发自动回复
          if (latestMessage && latestMessage.senderType === 'CUSTOMER') {
            console.log(`[Auto Reply] Found pending customer message, triggering immediate auto reply...`);

            // 获取组织ID
            const conversation = await prisma.conversation.findUnique({
              where: { id: conversationId },
              select: { organizationId: true },
            });

            if (conversation) {
              await salesAgentIntegrationService.handleNewMessage(latestMessage);
            }
          } else {
            console.log(`[Auto Reply] No pending customer messages found`);
          }
        } catch (error) {
          console.error(`[Auto Reply] Error processing immediate auto reply:`, error);
        }
      }

      return c.json(
        createResponse(
          {
            conversationId: updatedConversation.id,
            autoReplyEnabled: updatedConversation.autoReplyEnabled,
            changed: changed,
          },
          `自动回复已${enabled ? '开启' : '关闭'}`,
        ),
      );
    } catch (error) {
      console.error(`[Auto Reply] Error updating conversation:`, error);
      return handleError(c, error);
    }
  },
);

// 批量设置多个对话的自动回复状态（可选功能）
conversationAutoReplyRoute.put(
  '/batch/auto-reply',
  zValidator(
    'json',
    z.object({
      conversationIds: z.array(z.string().uuid()),
      enabled: z.boolean(),
    }),
  ),
  async (c) => {
    try {
      const { conversationIds, enabled } = c.req.valid('json');

      if (conversationIds.length === 0) {
        return c.json(createErrorResponse('对话ID列表不能为空'), 400);
      }

      if (conversationIds.length > 100) {
        return c.json(createErrorResponse('一次最多只能操作100个对话'), 400);
      }

      // 先获取所有会话的当前状态，用于之后判断哪些需要发送通知
      const existingConversations = await prisma.conversation.findMany({
        where: {
          id: { in: conversationIds },
        },
        select: {
          id: true,
          organizationId: true,
          customerId: true,
          autoReplyEnabled: true,
        },
      });

      // 批量更新
      const result = await prisma.conversation.updateMany({
        where: {
          id: { in: conversationIds },
        },
        data: { autoReplyEnabled: enabled },
      });

      console.log(`[Auto Reply] Batch ${enabled ? 'enabled' : 'disabled'} auto reply for ${result.count} conversations`);

      // 如果有状态变化的会话，发送WebSocket通知
      if (result.count > 0 && existingConversations.length > 0) {
        // 找出那些状态会发生变化的会话
        const changedConversations = existingConversations.filter((conv) => conv.autoReplyEnabled !== enabled);

        console.log(`[Auto Reply] Found ${changedConversations.length} conversations with status change`);

        // 异步发送WebSocket通知
        Promise.all(
          changedConversations.map(async (conv) => {
            try {
              // 跳过没有organizationId的会话
              if (!conv.organizationId) {
                console.log(`[Auto Reply] Skipping conversation ${conv.id} without organizationId`);
                return;
              }

              // 获取客户信息
              const customer = await prisma.customer.findUnique({
                where: { id: conv.customerId },
                select: { nickname: true },
              });

              if (customer) {
                // 发送WebSocket通知
                notifyAutoReplyStatusChanged(conv.organizationId, conv.id, customer.nickname, enabled);
                console.log(`[Auto Reply] Notification sent for conversation ${conv.id}, customer: ${customer.nickname}, auto reply ${enabled ? 'enabled' : 'disabled'}`);
              }
            } catch (error) {
              console.error(`[Auto Reply] Error sending notification for conversation ${conv.id}:`, error);
            }
          }),
        ).catch((error) => {
          console.error(`[Auto Reply] Error processing batch notifications:`, error);
        });
      }

      return c.json(
        createResponse(
          {
            updatedCount: result.count,
            autoReplyEnabled: enabled,
          },
          `批量${enabled ? '开启' : '关闭'}自动回复成功，影响${result.count}个对话`,
        ),
      );
    } catch (error) {
      return handleError(c, error);
    }
  },
);

export default conversationAutoReplyRoute;
