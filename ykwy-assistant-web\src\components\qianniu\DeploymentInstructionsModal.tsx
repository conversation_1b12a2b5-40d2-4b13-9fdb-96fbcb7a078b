import { Alert<PERSON><PERSON>gle, <PERSON><PERSON>ircle, Copy, Download, Info, Terminal } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface DeploymentInstructionsModalProps {
  invitation: {
    invitation: {
      id: string;
      name: string;
      team?: { id: string; name: string };
      organization?: { id: string; name: string };
      status?: string;
    };
    cdnUrl: string;
    deploymentInstructions: {
      method1: { title: string; command: string; description: string };
      method2: { title: string; steps: string[] };
      notes: string[];
    };
    team?: { name: string };
    status?: string;
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DeploymentInstructionsModal({ invitation, open, onOpenChange }: DeploymentInstructionsModalProps) {
  const [copiedCommand, setCopiedCommand] = useState(false);
  const [copiedUrl, setCopiedUrl] = useState(false);

  const handleCopyCommand = (command: string) => {
    navigator.clipboard.writeText(command);
    setCopiedCommand(true);
    toast.success('命令已复制到剪贴板');
    setTimeout(() => setCopiedCommand(false), 2000);
  };

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    setCopiedUrl(true);
    toast.success('CDN链接已复制到剪贴板');
    setTimeout(() => setCopiedUrl(false), 2000);
  };

  const cdnToolsUrl = 'https://image.zhaoman.me/qianniu-tools/qianniu-runner.js';

  const deploymentInstructions = invitation.deploymentInstructions || {
    method1: {
      title: '方法1: 一键注入（推荐）',
      command: `curl -fsSL ${cdnToolsUrl} | node - inject --url "${invitation.cdnUrl || ''}"`,
      description: '使用CDN工具一键注入连接脚本到千牛客户端',
    },
    method2: {
      title: '方法2: 下载工具后执行',
      steps: [`1. 下载千牛工具：curl -o qianniu-runner.js ${cdnToolsUrl}`, `2. 注入连接脚本：node qianniu-runner.js inject --url "${invitation.cdnUrl || ''}"`, '3. 重启千牛客户端完成连接'],
    },
    notes: [
      '🔒 注入前请确保千牛客户端已完全关闭',
      '🛡️ 工具会自动备份原始文件，支持一键恢复',
      '⚡ 注入成功后千牛将自动连接到您的系统',
      '📱 支持实时消息同步、会话管理等功能',
      `🔄 如需恢复：curl -fsSL ${cdnToolsUrl} | node - restore`,
    ],
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Terminal className="w-5 h-5" />
            千牛客户端注入说明
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 连接信息 */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
            <h3 className="font-medium text-blue-900 mb-3 flex items-center gap-2">
              <Info className="w-4 h-4" />
              连接配置信息
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-blue-700">连接名称：</span>
                <span className="font-medium">{invitation.invitation?.name || '未知连接'}</span>
              </div>
              <div>
                <span className="text-blue-700">团队：</span>
                <span className="font-medium">{invitation.invitation?.team?.name || invitation.team?.name || '未知'}</span>
              </div>
              <div>
                <span className="text-blue-700">连接ID：</span>
                <span className="font-mono text-xs">{invitation.invitation?.id || '未知'}</span>
              </div>
              <div>
                <span className="text-blue-700">状态：</span>
                <Badge variant={(invitation.invitation?.status || invitation.status) === 'PENDING' ? 'secondary' : 'default'}>
                  {(invitation.invitation?.status || invitation.status) === 'PENDING' ? '待激活' : '已激活'}
                </Badge>
              </div>
            </div>
            <div className="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800">💡 此脚本包含您的组织和团队配置，注入后千牛将自动连接到指定系统</div>
          </div>

          {/* 工具说明 */}
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h3 className="font-medium text-green-900 mb-2 flex items-center gap-2">
              <Terminal className="w-4 h-4" />
              千牛注入工具
            </h3>
            <div className="text-sm text-green-800 space-y-2">
              <p>
                • <strong>注入工具</strong>：自动修改千牛客户端，注入连接脚本
              </p>
              <p>
                • <strong>恢复工具</strong>：一键恢复千牛客户端到原始状态
              </p>
              <p>
                • <strong>CDN分发</strong>：工具已上传到全球CDN，无需下载源码
              </p>
            </div>
          </div>

          {/* 连接脚本链接 */}
          <div className="space-y-2">
            <h3 className="font-medium flex items-center gap-2">
              <Download className="w-4 h-4" />
              连接脚本地址
            </h3>
            <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
              <code className="flex-1 text-sm font-mono break-all">{invitation.cdnUrl || '加载中...'}</code>
              <Button size="sm" variant="outline" onClick={() => handleCopyUrl(invitation.cdnUrl || '')}>
                {copiedUrl ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
            <p className="text-xs text-gray-600">📝 此脚本将被注入到千牛客户端，包含WebSocket连接和事件监听代码</p>
          </div>

          {/* 部署方法1 */}
          <div className="space-y-3">
            <h3 className="font-medium text-green-700">{deploymentInstructions.method1.title}</h3>
            <p className="text-sm text-gray-600">{deploymentInstructions.method1.description}</p>
            <div className="space-y-2">
              <div className="flex items-center gap-2 p-3 bg-gray-900 text-green-400 rounded-lg font-mono text-sm">
                <span className="flex-1">{deploymentInstructions.method1.command}</span>
                <Button size="sm" variant="secondary" onClick={() => handleCopyCommand(deploymentInstructions.method1.command)}>
                  {copiedCommand ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          </div>

          {/* 部署方法2 */}
          <div className="space-y-3">
            <h3 className="font-medium text-blue-700">{deploymentInstructions.method2.title}</h3>
            <ol className="space-y-2">
              {deploymentInstructions.method2.steps.map((step: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-sm font-medium">{index + 1}</span>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded flex-1">{step}</code>
                </li>
              ))}
            </ol>
          </div>

          {/* 注意事项 */}
          <div className="space-y-3">
            <h3 className="font-medium text-orange-700 flex items-center gap-2">
              <AlertTriangle className="w-4 h-4" />
              注意事项
            </h3>
            <ul className="space-y-2">
              {deploymentInstructions.notes.map((note: string, index: number) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <Info className="w-4 h-4 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span>{note}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
            <Button onClick={() => handleCopyCommand(deploymentInstructions.method1.command)}>复制部署命令</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
