import { message } from 'antd';
import logger from './logger';

// 错误类型映射
const ERROR_TYPES = {
  SCRIPT_ERROR: 'ScriptError',
  PROMISE_REJECTION: 'UnhandledPromiseRejection',
  RESOURCE_ERROR: 'ResourceError',
  NETWORK_ERROR: 'NetworkError',
  RUNTIME_ERROR: 'RuntimeError',
  COMPONENT_ERROR: 'ComponentError',
};

// 判断是否应该忽略的错误
function shouldIgnoreError(errorMessage: string, filename?: string): boolean {
  const ignoredMessages = [
    'Script error.',
    'ResizeObserver loop limit exceeded',
    'Non-Error promise rejection captured',
    'Network request failed',
    'Loading chunk',
    'ChunkLoadError',
  ];

  const ignoredFiles = [
    'extensions/',
    'chrome-extension://',
    'moz-extension://',
    'webkit-masked-url://',
  ];

  // 检查错误消息
  if (ignoredMessages.some((ignored) => errorMessage.includes(ignored))) {
    return true;
  }

  // 检查文件名
  if (filename && ignoredFiles.some((ignored) => filename.includes(ignored))) {
    return true;
  }

  return false;
}

// 判断是否应该忽略的Promise rejection
function shouldIgnorePromiseRejection(errorMessage: string): boolean {
  const ignoredMessages = [
    'Network request failed',
    'fetch',
    'AbortError',
    'The user aborted a request',
    'The operation was aborted',
    'Request timeout',
  ];

  return ignoredMessages.some((ignored) =>
    errorMessage.toLowerCase().includes(ignored.toLowerCase()),
  );
}

// 判断是否为网络错误
function isNetworkError(errorMessage: string): boolean {
  const networkErrorKeywords = [
    'network',
    'fetch',
    'timeout',
    'connection',
    'request failed',
    'net::',
  ];

  return networkErrorKeywords.some((keyword) =>
    errorMessage.toLowerCase().includes(keyword.toLowerCase()),
  );
}

// 设置全局错误处理器
export function setupGlobalErrorHandler() {
  if (typeof window === 'undefined') return;

  // 捕获JavaScript运行时错误
  window.addEventListener('error', (event) => {
    const { message: errorMessage, filename, lineno, colno, error } = event;

    // 过滤掉一些不重要的错误
    if (shouldIgnoreError(errorMessage, filename)) {
      return;
    }

    const errorInfo = {
      message: errorMessage,
      filename,
      line: lineno,
      column: colno,
      stack: error?.stack,
      type: ERROR_TYPES.SCRIPT_ERROR,
    };

    // 判断错误类型
    let errorType = ERROR_TYPES.RUNTIME_ERROR;
    if (filename && (filename.includes('.js') || filename.includes('.ts'))) {
      errorType = ERROR_TYPES.SCRIPT_ERROR;
    } else if (
      filename &&
      (filename.includes('.css') ||
        filename.includes('.png') ||
        filename.includes('.jpg'))
    ) {
      errorType = ERROR_TYPES.RESOURCE_ERROR;
    }

    // 记录错误日志
    logger.error(`JavaScript错误: ${errorMessage}`, 'error', {
      stack: error?.stack || `at ${filename}:${lineno}:${colno}`,
      errorType,
    });

    // 显示用户友好的错误提示
    if (errorType !== ERROR_TYPES.RESOURCE_ERROR) {
      message.error('页面出现异常，请刷新重试');
    }

    console.error('Global Error:', errorInfo);
  });

  // 捕获未处理的Promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const { reason } = event;

    let errorMessage = '未处理的Promise异常';
    let stack = '';

    if (reason instanceof Error) {
      errorMessage = reason.message;
      stack = reason.stack || '';
    } else if (typeof reason === 'string') {
      errorMessage = reason;
    } else if (reason && typeof reason === 'object') {
      errorMessage = reason.message || JSON.stringify(reason);
      stack = reason.stack || '';
    }

    // 过滤掉一些不重要的Promise rejection
    if (shouldIgnorePromiseRejection(errorMessage)) {
      return;
    }

    // 记录错误日志
    logger.error(`Promise异常: ${errorMessage}`, 'error', {
      stack,
      errorType: ERROR_TYPES.PROMISE_REJECTION,
    });

    // 显示用户友好的错误提示
    if (!isNetworkError(errorMessage)) {
      message.error('操作失败，请重试');
    }

    console.error('Unhandled Promise Rejection:', reason);
  });

  // 监听资源加载错误
  window.addEventListener(
    'error',
    (event) => {
      const target = event.target;

      if (target && target !== window && target instanceof HTMLElement) {
        const tagName = target.tagName?.toLowerCase();
        const src = (target as any).src || (target as any).href;

        if (tagName && src) {
          logger.error(`资源加载失败: ${tagName} - ${src}`, 'error', {
            errorType: ERROR_TYPES.RESOURCE_ERROR,
          });

          console.error('Resource Load Error:', { tagName, src });
        }
      }
    },
    true,
  ); // 使用捕获阶段

  // 捕获网络错误（通过监听离线状态）
  window.addEventListener('offline', () => {
    logger.warn('网络连接断开', 'error', {
      errorType: ERROR_TYPES.NETWORK_ERROR,
    });
    message.warning('网络连接断开，请检查网络设置');
  });

  window.addEventListener('online', () => {
    message.success('网络连接已恢复');
  });
}

// React错误边界处理器
export function setupReactErrorHandler() {
  // 创建一个全局的React错误处理函数
  (window as any).__REACT_ERROR_HANDLER__ = (error: Error, errorInfo: any) => {
    logger.error(`React组件错误: ${error.message}`, 'component', {
      stack: error.stack,
      errorType: ERROR_TYPES.COMPONENT_ERROR,
      ...errorInfo,
    });

    message.error('页面组件出现异常，请刷新重试');
    console.error('React Error:', error, errorInfo);
  };
}

// 手动错误报告API
export function reportError(
  error: Error | string,
  context?: {
    module?: string;
    action?: string;
    data?: any;
  },
) {
  const errorMessage = error instanceof Error ? error.message : error;
  const stack = error instanceof Error ? error.stack : undefined;

  logger.error(`手动报告错误: ${errorMessage}`, 'error', {
    stack,
    errorType: 'ManualReport',
    ...context,
  });
}
