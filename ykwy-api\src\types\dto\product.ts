import type { Product } from '@prisma/client';

/**
 * 商品 DTO - 基本信息
 */
export interface ProductDto extends Product {
  /** 关联的尺码表数量 */
  sizeChartCount?: number;
}

/**
 * 商品 DTO - 详细信息（包含关联尺码表）
 */
export interface ProductDetailDto extends Product {
  /** 关联的尺码表列表 */
  sizeCharts: Array<{
    id: string;
    name: string;
  }>;
  /** 关联的商品标识符列表 */
  identifiers: Array<{
    id: string;
    identifierType: string;
    value: string;
  }>;
}

/**
 * 商品列表 DTO
 */
export interface ProductListDto {
  /** 商品列表 */
  items: ProductDto[];
  /** 总数 */
  total: number;
}

/**
 * 批量删除结果 DTO
 */
export interface BulkDeleteResultDto {
  /** 实际删除的数量 */
  count: number;
}

/**
 * API 通用响应格式
 */
export interface ApiResponse<T> {
  /** 是否成功 */
  success: boolean;
  /** 返回数据 */
  data?: T;
  /** 错误信息 */
  error?: string;
  /** 分页信息 */
  pagination?: {
    total: number;
    skip: number;
    take?: number;
  };
}
