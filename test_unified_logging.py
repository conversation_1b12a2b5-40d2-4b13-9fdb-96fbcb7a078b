#!/usr/bin/env python3
"""
测试统一日志系统
"""
import os
import sys
import time

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mock-production', 'src'))

def load_env_file():
    """加载环境变量文件"""
    env_file = os.path.join(os.path.dirname(__file__), 'mock-production', '.env')
    if os.path.exists(env_file):
        print(f"📁 加载环境变量文件: {env_file}")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # 移除引号
                    value = value.strip('"\'')
                    os.environ[key] = value
        return True
    else:
        print(f"⚠️ 环境变量文件不存在: {env_file}")
        return False

def test_unified_logger():
    """测试统一日志器"""
    print("\n🧪 测试统一日志器...")
    
    try:
        from sales_agent.utils.logger import unified_logger, setup_detailed_logging
        
        # 设置日志
        setup_detailed_logging()
        
        # 检查Loki配置
        print(f"📊 Loki服务状态:")
        print(f"   URL: {unified_logger.loki_service.url}")
        print(f"   启用: {'✅ 是' if unified_logger.loki_service.is_enabled() else '❌ 否'}")
        print(f"   JSON输出: {'✅ 是' if unified_logger.enable_json else '❌ 否'}")
        print(f"   控制台输出: {'✅ 是' if unified_logger.enable_console else '❌ 否'}")
        print(f"   Loki输出: {'✅ 是' if unified_logger.enable_loki else '❌ 否'}")
        
        # 发送测试日志
        request_id = f"test_unified_{int(time.time())}"
        
        print(f"\n🚀 发送测试日志...")
        unified_logger.info("🧪 统一日志系统测试 - INFO", {
            "test_type": "unified_logging_test",
            "timestamp": time.time(),
            "service": "sales-agent"
        }, request_id)
        
        unified_logger.warn("⚠️ 统一日志系统测试 - WARN", {
            "test_type": "unified_logging_test",
            "level": "warning"
        }, request_id)
        
        unified_logger.error("❌ 统一日志系统测试 - ERROR", {
            "test_type": "unified_logging_test",
            "level": "error"
        }, Exception("这是一个测试异常"), request_id)
        
        print(f"✅ 统一日志器测试完成")
        print(f"📋 请求ID: {request_id}")
        return True
        
    except Exception as e:
        print(f"❌ 统一日志器测试失败: {e}")
        return False

def test_agent_logging():
    """测试Agent模块日志"""
    print("\n🧪 测试Agent模块日志...")
    
    try:
        from sales_agent.core.agent import agent_logger
        
        request_id = f"test_agent_{int(time.time())}"
        
        agent_logger.info("🤖 Agent模块日志测试", {
            "module": "agent_test",
            "test_type": "agent_logging"
        }, request_id)
        
        print(f"✅ Agent模块日志测试完成")
        return True
        
    except Exception as e:
        print(f"❌ Agent模块日志测试失败: {e}")
        return False

def test_tools_logging():
    """测试Tools模块日志"""
    print("\n🧪 测试Tools模块日志...")
    
    try:
        from sales_agent.tools.sales_tools import tools_logger
        
        request_id = f"test_tools_{int(time.time())}"
        
        tools_logger.info("🔧 Tools模块日志测试", {
            "module": "tools_test",
            "test_type": "tools_logging"
        }, request_id)
        
        print(f"✅ Tools模块日志测试完成")
        return True
        
    except Exception as e:
        print(f"❌ Tools模块日志测试失败: {e}")
        return False

def test_convenience_functions():
    """测试便捷日志函数"""
    print("\n🧪 测试便捷日志函数...")
    
    try:
        from sales_agent.utils.logger import (
            log_info, log_error, log_warn,
            log_api_call, log_api_response,
            log_tool_call, log_tool_result
        )
        
        request_id = f"test_convenience_{int(time.time())}"
        
        # 测试基础日志函数
        log_info("📝 便捷函数测试 - INFO", {"test": "convenience"}, request_id)
        log_warn("⚠️ 便捷函数测试 - WARN", {"test": "convenience"}, request_id)
        log_error("❌ 便捷函数测试 - ERROR", {"test": "convenience"}, Exception("测试异常"), request_id)
        
        # 测试API日志函数
        log_api_call("test_api", {"param1": "value1"}, request_id)
        log_api_response("test_api", True, {"result": "success"}, None, request_id)
        
        # 测试工具日志函数
        log_tool_call("test_tool", {"param1": "value1"}, request_id)
        log_tool_result("test_tool", True, {"result": "success"}, None, request_id)
        
        print(f"✅ 便捷日志函数测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 便捷日志函数测试失败: {e}")
        return False

def main():
    print("🔧 统一日志系统测试工具")
    print("=" * 50)
    
    # 加载环境变量
    if not load_env_file():
        print("⚠️ 无法加载环境变量，继续测试...")
    
    # 运行所有测试
    tests = [
        ("统一日志器", test_unified_logger),
        ("Agent模块日志", test_agent_logging),
        ("Tools模块日志", test_tools_logging),
        ("便捷日志函数", test_convenience_functions)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！统一日志系统工作正常")
        print("💡 提示:")
        print("   1. 检查控制台是否有彩色日志输出")
        print("   2. 如果配置了Loki，日志应该已上传到服务器")
        print("   3. 在Grafana中查询验证日志上传")
    else:
        print("⚠️ 部分测试失败，请检查配置和代码")

if __name__ == "__main__":
    main()
