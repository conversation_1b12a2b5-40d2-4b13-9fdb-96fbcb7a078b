"""
Loki 日志服务 - 与 ykwy-assistant-api 统一的日志收集
"""
import os
import json
import time
import base64
from typing import Dict, Any, Optional
from enum import Enum


class LogLevel(Enum):
    """日志级别枚举"""
    ERROR = 0
    WARN = 1
    INFO = 2
    DEBUG = 3
    TRACE = 4


class LokiService:
    """Loki 日志服务类"""

    def __init__(self):
        """初始化 Loki 服务"""
        self.url = os.environ.get('LOKI_URL', '')
        self.username = os.environ.get('LOKI_USERNAME', '')
        self.password = os.environ.get('LOKI_PASSWORD', '')
        self.server_name = 'sales-agent'  # 服务名称
        self.enabled = bool(self.url and self.username and self.password)
        print('Loki Enabled: {}'.format(self.enabled))
        print(f'Loki URL: {self.url}')
        print(f'Loki Username: {self.username}')
        print(f'Loki Password: {self.password}')
        print(f'Loki Server Name: {self.server_name}')


        if not self.enabled:
            print('[Loki] Loki配置不完整，日志将不会发送到Loki服务')
        else:
            async_mode = os.environ.get('LOKI_ASYNC_MODE', 'false').lower() == 'true'
            mode = '异步' if async_mode else '同步'
            print(f'[Loki] Loki服务已启用，发送模式: {mode}')

    def is_enabled(self) -> bool:
        """检查 Loki 服务是否启用"""
        return self.enabled

    def _log_level_to_string(self, level: LogLevel) -> str:
        """将日志级别转换为字符串"""
        level_map = {
            LogLevel.ERROR: 'error',
            LogLevel.WARN: 'warn',
            LogLevel.INFO: 'info',
            LogLevel.DEBUG: 'debug',
            LogLevel.TRACE: 'trace'
        }
        return level_map.get(level, 'info')

    def _build_stream_labels(self, level: LogLevel, context: Optional[Dict[str, Any]] = None,
                           request_id: Optional[str] = None, user_id: Optional[str] = None,
                           organization_id: Optional[str] = None) -> Dict[str, str]:
        """构建流标签"""
        labels = {
            'service': self.server_name,
            'level': self._log_level_to_string(level),
            'env': os.environ.get('NODE_ENV', 'development')
        }

        # 添加可选标签
        if request_id:
            labels['request_id'] = request_id
        if user_id:
            labels['user_id'] = user_id
        if organization_id:
            labels['organization_id'] = organization_id

        # 从上下文中提取模块信息
        if context and 'module' in context:
            labels['module'] = str(context['module'])

        return labels

    def _format_log_message(self, message: str, context: Optional[Dict[str, Any]] = None,
                          error: Optional[Exception] = None) -> str:
        """格式化日志消息"""
        formatted_message = message

        # 添加上下文信息
        if context and context:
            formatted_message += f"\nContext: {json.dumps(context, ensure_ascii=False, indent=2)}"

        # 添加错误信息
        if error:
            formatted_message += f"\nError: {type(error).__name__}: {str(error)}"
            if hasattr(error, '__traceback__') and error.__traceback__:
                import traceback
                formatted_message += f"\nStack: {''.join(traceback.format_tb(error.__traceback__))}"

        return formatted_message

    # 异步方法已移除，现在统一使用同步方法避免事件循环问题

    def send_log(self, level: LogLevel, message: str,
                context: Optional[Dict[str, Any]] = None,
                error: Optional[Exception] = None,
                request_id: Optional[str] = None,
                user_id: Optional[str] = None,
                organization_id: Optional[str] = None) -> None:
        """发送日志到 Loki（现在默认使用同步方式避免异步问题）"""
        # 直接调用同步方法，避免所有异步相关问题
        self.send_log_sync(level, message, context, error, request_id, user_id, organization_id)

    # 回调方法已移除，不再需要异步任务处理

    def send_log_sync(self, level: LogLevel, message: str,
                     context: Optional[Dict[str, Any]] = None,
                     error: Optional[Exception] = None,
                     request_id: Optional[str] = None,
                     user_id: Optional[str] = None,
                     organization_id: Optional[str] = None) -> None:
        """同步发送日志到 Loki（使用 requests，避免异步问题）"""
        if not self.enabled:
            return

        try:
            import requests

            # 生成纳秒时间戳
            timestamp = str(int(time.time() * 1000000000))

            # 构建流标签和消息
            stream_labels = self._build_stream_labels(level, context, request_id, user_id, organization_id)
            log_message = self._format_log_message(message, context, error)

            # 构建 Loki 数据格式
            log_data = {
                "streams": [
                    {
                        "stream": stream_labels,
                        "values": [[timestamp, log_message]]
                    }
                ]
            }

            # 构建认证头
            auth_string = f"{self.username}:{self.password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

            headers = {
                'Content-Type': 'application/json',
                'X-Scope-OrgID': 'fake',
                'Authorization': f'Basic {auth_b64}'
            }

            # 发送到 Loki
            response = requests.post(self.url, json=log_data, headers=headers, timeout=5)
            if not response.ok:
                print(f'[Loki] 同步发送失败: HTTP {response.status_code}')
            else:
                # 成功发送，可以添加调试信息
                pass

        except ImportError:
            print('[Loki] requests库未安装，无法使用同步模式')
        except Exception as e:
            print(f'[Loki] 同步发送日志失败: {e}')


# 创建全局 Loki 服务实例
loki_service = LokiService()
