import { z } from 'zod';

// 通用验证
export const IdParamSchema = z.object({
  id: z.string().uuid('无效的ID格式'),
});

// 分页验证
export const PaginationSchema = z.object({
  page: z.coerce.number().min(1, '页码必须大于0').default(1),
  limit: z.coerce.number().min(1, '每页数量必须大于0').max(100, '每页最多100条').default(20),
});

// 组织相关验证
export const CreateOrganizationSchema = z.object({
  name: z.string().min(1, '组织名称不能为空').max(100, '组织名称不能超过100个字符'),
  slug: z
    .string()
    .min(1, 'Slug 不能为空')
    .max(50, 'Slug 不能超过 50 个字符')
    .regex(/^[a-z0-9-]+$/, 'Slug 仅支持小写字母、数字和连字符'),
  description: z.string().optional(),
  logoUrl: z.string().url('无效的Logo URL').optional(),
  website: z.string().url('无效的网站URL').optional(),
  contactEmail: z.string().email('无效的邮箱地址').optional(),
});

export const UpdateOrganizationSchema = CreateOrganizationSchema.partial();

// 客户相关验证
export const CreateCustomerSchema = z.object({
  qianniuAccountId: z.string().uuid('无效的千牛账号ID'),
  platformCustomerId: z.string().min(1, '平台客户ID不能为空'),
  nickname: z.string().min(1, '客户昵称不能为空'),
  avatar: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email('无效的邮箱地址').optional(),
  realName: z.string().optional(),
  gender: z.string().optional(),
  location: z.string().optional(),
  vipLevel: z.string().optional(),
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  totalOrders: z.number().int().min(0).optional(),
  totalAmount: z.number().min(0).optional(),
});

export const UpdateCustomerSchema = CreateCustomerSchema.partial().omit({
  qianniuAccountId: true,
  platformCustomerId: true,
});

// 对话相关验证
export const CreateConversationSchema = z.object({
  qianniuAccountId: z.string().uuid('无效的千牛账号ID'),
  customerId: z.string().uuid('无效的客户ID'),
  title: z.string().optional(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).default('NORMAL'),
  tags: z.array(z.string()).default([]),
});

export const UpdateConversationSchema = z.object({
  title: z.string().optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'WAITING', 'RESOLVED', 'CLOSED']).optional(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).optional(),
  tags: z.array(z.string()).optional(),
  assignedUserId: z.string().uuid('无效的用户ID').optional(),
});

// 消息相关验证
export const CreateMessageSchema = z.object({
  conversationId: z.string().uuid('无效的对话ID'),
  content: z.string().min(1, '消息内容不能为空'),
  messageType: z.enum(['TEXT', 'IMAGE', 'FILE', 'AUDIO', 'VIDEO', 'SYSTEM']).default('TEXT'),
  parentMessageId: z.string().uuid('无效的父消息ID').optional(),
  metadata: z.any().optional(),
});

// 查询过滤器
export const ConversationFilterSchema = z.object({
  search: z.string().optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'WAITING', 'RESOLVED', 'CLOSED']).optional(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).optional(),
  assignedUserId: z.string().uuid('无效的用户ID').optional(),
  organizationId: z.string().uuid('无效的组织ID').optional(),
  qianniuAccountId: z.string().uuid('无效的千牛账号ID').optional(),
  customerId: z.string().uuid('无效的客户ID').optional(),
  startDate: z.string().datetime('无效的开始时间').optional(),
  endDate: z.string().datetime('无效的结束时间').optional(),
});

// 组织用户相关验证（单一组织模式下直接管理用户）
export const AddUserToOrganizationSchema = z.object({
  organizationId: z.string().uuid('无效的组织ID'),
  userId: z.string().uuid('无效的用户ID'),
  role: z.enum(['SYSTEM_ADMIN', 'ORGANIZATION_ADMIN', 'TEAM_MANAGER', 'CUSTOMER_SERVICE']).default('CUSTOMER_SERVICE'),
});

// 任务相关验证
export const CreateTaskSchema = z.object({
  organizationId: z.string().uuid('无效的组织ID'),
  title: z.string().min(1, '任务标题不能为空'),
  description: z.string().optional(),
  taskType: z.enum(['FOLLOW_UP', 'ISSUE_RESOLVE', 'ORDER_PROCESS', 'COMPLAINT', 'TRAINING', 'OTHER']),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).default('NORMAL'),
  assignedUserId: z.string().uuid('无效的用户ID').optional(),
  dueDate: z.string().datetime('无效的截止时间').optional(),
});

export const UpdateTaskSchema = z.object({
  title: z.string().min(1, '任务标题不能为空').optional(),
  description: z.string().optional(),
  taskType: z.enum(['FOLLOW_UP', 'ISSUE_RESOLVE', 'ORDER_PROCESS', 'COMPLAINT', 'TRAINING', 'OTHER']).optional(),
  status: z.enum(['TODO', 'IN_PROGRESS', 'DONE', 'CANCELLED']).optional(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).optional(),
  assignedUserId: z.string().uuid('无效的用户ID').optional(),
  dueDate: z.string().datetime('无效的截止时间').optional(),
});

// 统计相关验证
export const CreateStatsSchema = z.object({
  organizationId: z.string().uuid('无效的组织ID'),
  date: z.string().date('无效的日期格式'),
  totalConversations: z.number().int().min(0).default(0),
  newConversations: z.number().int().min(0).default(0),
  resolvedConversations: z.number().int().min(0).default(0),
  avgResponseTime: z.number().min(0).optional(),
  customerSatisfaction: z.number().min(0).max(5).optional(),
});

export const UpdateStatsSchema = CreateStatsSchema.partial().omit({
  organizationId: true,
  date: true,
});

// 活动记录相关验证
export const CreateActivitySchema = z.object({
  conversationId: z.string().uuid('无效的对话ID'),
  userId: z.string().uuid('无效的用户ID').optional(),
  action: z.enum(['ENTER', 'LOCK', 'UNLOCK', 'TYPING', 'LEAVE', 'HEARTBEAT']),
  metadata: z.any().optional(),
});

// 用户相关验证
export const UpdateUserSchema = z.object({
  name: z.string().min(1, '用户名不能为空').optional(),
  role: z.enum(['SYSTEM_ADMIN', 'ORGANIZATION_ADMIN', 'TEAM_MANAGER', 'CUSTOMER_SERVICE']).optional(),
  image: z.string().url('无效的头像URL').optional(),
});

export const UpdateUserRoleSchema = z.object({
  role: z.enum(['SYSTEM_ADMIN', 'ORGANIZATION_ADMIN', 'TEAM_MANAGER', 'CUSTOMER_SERVICE']),
});

// 团队相关验证
export const CreateTeamSchema = z.object({
  name: z.string().min(1, '团队名称不能为空').max(100, '团队名称不能超过100个字符'),
  description: z.string().max(500, '团队描述不能超过500个字符').optional(),
  isActive: z.boolean().default(true),
  managerId: z.string().uuid('无效的管理员ID').optional(),
  maxMembers: z.number().int().min(1).max(1000).optional(),
  workingHours: z.record(z.any()).optional(), // JSON 工作时间配置
});

export const UpdateTeamSchema = z.object({
  name: z.string().min(1, '团队名称不能为空').max(100, '团队名称不能超过100个字符').optional(),
  description: z.string().max(500, '团队描述不能超过500个字符').optional(),
  isActive: z.boolean().optional(),
  managerId: z.string().uuid('无效的管理员ID').optional(),
  maxMembers: z.number().int().min(1).max(1000).optional(),
  workingHours: z.record(z.any()).optional(),
});
