#!/usr/bin/env node

/**
 * 千牛工具一键执行器
 * 从CDN下载并执行千牛注入/恢复工具
 */

const https = require('https')
const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')
const os = require('os')

const TOOLS = {
  inject: 'https://image.zhaoman.me/qianniu-tools/repack-webui.js',
  restore: 'https://image.zhaoman.me/qianniu-tools/restore-webui.js'
}

function downloadAndExecute(toolName, args) {
  return new Promise((resolve, reject) => {
    const url = TOOLS[toolName]
    if (!url) {
      reject(new Error(`未知工具: ${toolName}`))
      return
    }

    console.log(`📥 下载工具: ${toolName}`)

    https
      .get(url, res => {
        if (res.statusCode !== 200) {
          reject(new Error(`下载失败: HTTP ${res.statusCode}`))
          return
        }

        let data = ''
        res.on('data', chunk => (data += chunk))
        res.on('end', () => {
          try {
            const tempFile = path.join(os.tmpdir(), `qianniu-${toolName}.js`)
            fs.writeFileSync(tempFile, data)

            console.log(`🚀 执行工具: ${toolName}`)
            const command = `node "${tempFile}" ${args.join(' ')}`
            execSync(command, { stdio: 'inherit' })

            fs.unlinkSync(tempFile)
            resolve()
          } catch (error) {
            reject(error)
          }
        })
      })
      .on('error', reject)
  })
}

async function main() {
  const args = process.argv.slice(2)

  if (args.length === 0 || args.includes('--help')) {
    console.log(`
🛠️  千牛工具一键执行器

使用方法:
  node qianniu-runner.js inject --url "https://your-api.com/script.js"
  node qianniu-runner.js restore

或者一行命令:
  curl -fsSL https://image.zhaoman.me/qianniu-tools/qianniu-runner.js | node - inject --url "https://your-api.com/script.js"
  curl -fsSL https://image.zhaoman.me/qianniu-tools/qianniu-runner.js | node - restore

特性:
  - 🚀 一行命令执行，无需下载源码
  - 🌐 利用Cloudflare CDN，全球加速
  - 🧹 自动清理临时文件
  - 🔄 支持所有原有功能
    `)
    return
  }

  const command = args[0]
  const commandArgs = args.slice(1)

  try {
    await downloadAndExecute(command, commandArgs)
    console.log('✅ 执行完成')
  } catch (error) {
    console.error('❌ 执行失败:', error.message)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
