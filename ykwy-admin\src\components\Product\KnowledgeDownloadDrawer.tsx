import { CloseOutlined } from '@ant-design/icons';
import { Button, Drawer, message, Table } from 'antd';
import React, { useState } from 'react';

const mockTasks = [
  {
    key: '1',
    name: '商品知识导出-1749450172',
    time: '2025/06/09 14:22:53',
    file: '商品知识导出-1749450172',
    result: '成功0条，失败0条',
    percent: '0%',
    status: '进行中',
  },
  {
    key: '2',
    name: '商品知识导出-1749450161',
    time: '2025/06/09 14:22:41',
    file: '商品知识导出-1749450161',
    result: '成功0条，失败0条',
    percent: '100%',
    status: '成功',
  },
  {
    key: '3',
    name: '商品知识导出-1749434712',
    time: '2025/06/09 10:05:12',
    file: '商品知识导出-1749434712',
    result: '成功0条，失败0条',
    percent: '100%',
    status: '成功',
  },
];

const columns = [
  { title: '任务名称', dataIndex: 'name', key: 'name' },
  { title: '时间', dataIndex: 'time', key: 'time' },
  { title: '文件', dataIndex: 'file', key: 'file' },
  { title: '结果', dataIndex: 'result', key: 'result' },
  {
    title: '操作',
    key: 'action',
    render: (_: any, record: any) => (
      <>
        {record.status === '进行中' ? (
          <Button
            type="link"
            onClick={() => message.info('已取消')}
            data-oid="5fa1n2t"
          >
            取消
          </Button>
        ) : (
          <Button
            type="link"
            onClick={() => message.success('开始下载')}
            data-oid="sh4wtne"
          >
            下载
          </Button>
        )}
      </>
    ),
  },
];

const tabList = [
  { key: 'download', label: '下载中心' },
  { key: 'sync', label: '同步任务' },
];

const KnowledgeDownloadDrawer: React.FC<{
  visible: boolean;
  onClose: () => void;
}> = ({ visible, onClose }) => {
  const [activeTab, setActiveTab] = useState('download');

  return (
    <Drawer
      placement="right"
      open={visible}
      onClose={onClose}
      width={600}
      closable={false}
      bodyStyle={{ padding: 0 }}
      headerStyle={{ display: 'none' }}
      data-oid="u61v1:r"
    >
      {/* 顶部自定义Tab、提示和关闭按钮 */}
      <div
        className="flex items-center pt-5 px-8 border-b border-b-[#f0f0f0] min-h-[56px]"
        data-oid="-vct2nw"
      >
        <div className="flex gap-6" data-oid="bjoldd5">
          {tabList.map((tab) => (
            <button
              key={tab.key}
              type="button"
              onClick={() => setActiveTab(tab.key)}
              className={`
                bg-none border-none outline-none text-base cursor-pointer transition-colors
                ${
                  activeTab === tab.key
                    ? 'font-semibold text-[#1677ff] border-b-2 border-b-[#1677ff]'
                    : 'font-normal text-[#222] border-b-2 border-b-transparent'
                }
                pb-2
              `}
              data-oid="4f81id9"
            >
              {tab.label}
            </button>
          ))}
        </div>
        <div className="flex-1" data-oid="pe.r36z" />
        {/* 右侧提示文字 */}
        <span className="text-[#888] text-xs mr-4" data-oid="k26n5so">
          完成导入后请刷新查看，仅保留最近30天数据
        </span>
        <Button
          type="text"
          icon={<CloseOutlined className="text-lg" data-oid="d4vkyho" />}
          onClick={onClose}
          className="text-[#999] -mr-2"
          data-oid="wrd8-.t"
        />
      </div>
      {/* 内容区 */}
      <div className="pt-6 px-8" data-oid="vs-61hb">
        {activeTab === 'download' ? (
          <Table
            columns={columns}
            dataSource={mockTasks}
            pagination={false}
            rowKey="key"
            className="min-h-[300px]"
            data-oid=":yl0kxt"
          />
        ) : (
          <div className="text-center text-[#aaa] py-15" data-oid="jp_-_mf">
            暂无数据
          </div>
        )}
      </div>
    </Drawer>
  );
};

export default KnowledgeDownloadDrawer;
