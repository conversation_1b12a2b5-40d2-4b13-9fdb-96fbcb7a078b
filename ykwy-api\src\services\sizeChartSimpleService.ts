import { PrismaClient } from '@prisma/client';

import type { CreateSizeChartSimpleDto, SizeChartSimpleDto, SizeChartSimpleListResponseDto, SizeChartSimpleQueryDto, UpdateSizeChartSimpleDto } from '../types/dto/sizeChartSimple.js';

/**
 * SizeChartSimple 服务层
 */
export class SizeChartSimpleService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * 检查名称是否已存在
   */
  async isNameExists(name: string, excludeId?: string): Promise<boolean> {
    try {
      const existingRecord = await this.prisma.sizeChartSimple.findFirst({
        where: {
          name,
          isDeleted: 0,
          ...(excludeId && { id: { not: excludeId } }),
        },
      });

      return !!existingRecord;
    } catch (error) {
      console.error('检查名称是否存在失败:', error);
      throw new Error('检查名称是否存在失败');
    }
  }

  /**
   * 创建简单尺码表（使用upsert逻辑）
   */
  async createSizeChartSimple(data: CreateSizeChartSimpleDto): Promise<SizeChartSimpleDto> {
    try {
      // 检查名称是否已存在
      const existingRecord = await this.prisma.sizeChartSimple.findFirst({
        where: {
          name: data.name,
          isDeleted: 0,
        },
      });

      if (existingRecord) {
        // 如果存在，则更新现有记录
        console.log(`名称 "${data.name}" 已存在，正在更新现有记录`);
        const updatedRecord = await this.prisma.sizeChartSimple.update({
          where: {
            id: existingRecord.id,
          },
          data: {
            sizeRange: data.sizeRange,
            sizeValue: data.sizeValue,
            updatedAt: new Date(),
          },
        });
        return updatedRecord;
      } else {
        // 如果不存在，则创建新记录
        const sizeChartSimple = await this.prisma.sizeChartSimple.create({
          data,
        });
        return sizeChartSimple;
      }
    } catch (error: unknown) {
      console.error('创建/更新简单尺码表失败:', error);

      // 处理其他可能的数据库错误
      if ((error as { code?: string }).code === 'P2002') {
        // 如果仍然有唯一性约束错误，再次尝试更新
        try {
          const existingRecord = await this.prisma.sizeChartSimple.findFirst({
            where: {
              name: data.name,
              isDeleted: 0,
            },
          });

          if (existingRecord) {
            const updatedRecord = await this.prisma.sizeChartSimple.update({
              where: {
                id: existingRecord.id,
              },
              data: {
                sizeRange: data.sizeRange,
                sizeValue: data.sizeValue,
                updatedAt: new Date(),
              },
            });
            return updatedRecord;
          }
        } catch (retryError) {
          console.error('重试更新失败:', retryError);
        }
      }

      throw new Error((error as Error).message || '创建简单尺码表失败');
    }
  }

  /**
   * 根据ID获取简单尺码表
   */
  async getSizeChartSimpleById(id: string): Promise<SizeChartSimpleDto | null> {
    try {
      const sizeChartSimple = await this.prisma.sizeChartSimple.findFirst({
        where: {
          id,
          isDeleted: 0,
        },
      });

      return sizeChartSimple;
    } catch (error) {
      console.error('获取简单尺码表失败:', error);
      throw new Error('获取简单尺码表失败');
    }
  }

  /**
   * 获取简单尺码表列表（分页）
   */
  async getSizeChartSimpleList(query: SizeChartSimpleQueryDto): Promise<SizeChartSimpleListResponseDto> {
    try {
      const { page = 1, limit = 10, name, sizeRange, sizeValue } = query;
      const offset = (page - 1) * limit;

      // 构建查询条件
      const where = {
        isDeleted: 0,
        ...(name && { name: { contains: name } }),
        ...(sizeRange && { sizeRange: { contains: sizeRange } }),
        ...(sizeValue && { sizeValue: { contains: sizeValue } }),
      };

      // 执行查询
      const [data, total] = await Promise.all([
        this.prisma.sizeChartSimple.findMany({
          where,
          skip: offset,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
        }),
        this.prisma.sizeChartSimple.count({
          where,
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      console.error('获取简单尺码表列表失败:', error);
      throw new Error('获取简单尺码表列表失败');
    }
  }

  /**
   * 更新简单尺码表
   */
  async updateSizeChartSimple(id: string, data: UpdateSizeChartSimpleDto): Promise<SizeChartSimpleDto> {
    try {
      // 先检查记录是否存在
      const existingSizeChartSimple = await this.getSizeChartSimpleById(id);
      if (!existingSizeChartSimple) {
        throw new Error('简单尺码表不存在');
      }

      // 如果要更新名称，检查名称是否重复
      if (data.name) {
        const nameExists = await this.isNameExists(data.name, id);
        if (nameExists) {
          throw new Error('名称已存在，请使用其他名称');
        }
      }

      const updatedSizeChartSimple = await this.prisma.sizeChartSimple.update({
        where: {
          id,
        },
        data,
      });

      return updatedSizeChartSimple;
    } catch (error: unknown) {
      console.error('更新简单尺码表失败:', error);

      // 处理数据库唯一性约束错误
      const prismaError = error as { code?: string; meta?: { target?: string[] } };
      if (prismaError.code === 'P2002' && prismaError.meta?.target?.includes('name')) {
        throw new Error('名称已存在，请使用其他名称');
      }

      // 抛出自定义错误消息或原始错误
      throw new Error((error as Error).message || '更新简单尺码表失败');
    }
  }

  /**
   * 删除简单尺码表（软删除）
   */
  async deleteSizeChartSimple(id: string): Promise<boolean> {
    try {
      // 先检查记录是否存在
      const existingSizeChartSimple = await this.getSizeChartSimpleById(id);
      if (!existingSizeChartSimple) {
        throw new Error('简单尺码表不存在');
      }

      await this.prisma.sizeChartSimple.update({
        where: {
          id,
        },
        data: {
          isDeleted: 1,
        },
      });

      return true;
    } catch (error) {
      console.error('删除简单尺码表失败:', error);
      throw new Error('删除简单尺码表失败');
    }
  }
}
