import Bun from 'bun';

import { LogLevel } from './logger';

// Loki配置接口
interface LokiConfig {
  url: string;
  username: string;
  password: string;
  serverName: string;
  enabled: boolean;
}

// 流选项接口
interface StreamOptions {
  service: string;
  level: 'error' | 'warn' | 'info' | 'debug' | 'trace';
  module?: string;
  request_id?: string;
  user_id?: string;
  organization_id?: string;
  env?: string;
  [key: string]: string | number | boolean | undefined;
}

// 日志条目接口（与logger.ts保持一致）
interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, unknown>;
  requestId?: string;
  userId?: string;
  organizationId?: string;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

class LokiService {
  private config: LokiConfig;

  constructor() {
    this.config = {
      url: Bun.env['LOKI_URL'] || '',
      username: Bun.env['LOKI_USERNAME'] || '',
      password: Bun.env['LOKI_PASSWORD'] || '',
      serverName: 'ykwy-assistant-api', // 从package.json获取
      enabled: !!(Bun.env['LOKI_URL'] && Bun.env['LOKI_USERNAME'] && Bun.env['LOKI_PASSWORD']),
    };

    if (!this.config.enabled) {
      console.warn('[Loki] Loki配置不完整，日志将不会发送到Loki服务');
    }
  }

  // 将LogLevel转换为Loki级别字符串
  private logLevelToString(level: LogLevel): 'error' | 'warn' | 'info' | 'debug' | 'trace' {
    switch (level) {
      case LogLevel.ERROR:
        return 'error';
      case LogLevel.WARN:
        return 'warn';
      case LogLevel.INFO:
        return 'info';
      case LogLevel.DEBUG:
        return 'debug';
      case LogLevel.TRACE:
        return 'trace';
      default:
        return 'info';
    }
  }

  // 构建流标签
  private buildStreamLabels(entry: LogEntry): StreamOptions {
    const labels: StreamOptions = {
      service: this.config.serverName,
      level: this.logLevelToString(entry.level),
      env: Bun.env.NODE_ENV || 'development',
    };

    // 添加可选标签
    if (entry.requestId) {
      labels.request_id = entry.requestId;
    }

    if (entry.userId) {
      labels.user_id = entry.userId;
    }

    if (entry.organizationId) {
      labels.organization_id = entry.organizationId;
    }

    // 从上下文中提取模块信息
    if (entry.context?.['module']) {
      labels.module = String(entry.context?.['module']);
    }

    return labels;
  }

  // 格式化日志消息
  private formatLogMessage(entry: LogEntry): string {
    let message = entry.message;

    // 添加上下文信息
    if (entry.context && Object.keys(entry.context).length > 0) {
      message += `\nContext: ${JSON.stringify(entry.context, null, 2)}`;
    }

    // 添加错误信息
    if (entry.error) {
      message += `\nError: ${entry.error.name}: ${entry.error.message}`;
      if (entry.error.stack) {
        message += `\nStack: ${entry.error.stack}`;
      }
    }

    return message;
  }

  // 发送日志到Loki
  async sendLog(entry: LogEntry): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    try {
      const timestamp = (Date.now() * 1000000).toString(); // 纳秒时间戳
      const streamLabels = this.buildStreamLabels(entry);
      const logMessage = this.formatLogMessage(entry);

      const logData = {
        streams: [
          {
            stream: streamLabels,
            values: [[timestamp, logMessage]],
          },
        ],
      };

      const authorization = `Basic ${Buffer.from(`${this.config.username}:${this.config.password}`).toString('base64')}`;

      const response = await fetch(this.config.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Scope-OrgID': 'fake',
          Authorization: authorization,
        },
        body: JSON.stringify(logData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      // 避免日志发送失败影响主业务，只在控制台输出错误
      console.error('[Loki] 发送日志到Loki失败:', error);
    }
  }

  // 检查Loki服务是否可用
  isEnabled(): boolean {
    return this.config.enabled;
  }
}

// 创建全局Loki服务实例
export const lokiService = new LokiService();
