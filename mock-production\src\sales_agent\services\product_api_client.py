"""
商品API客户端 - 调用真实的商品API接口
"""
import logging
import requests
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = logging.getLogger(__name__)


@dataclass
class Product:
    """商品信息数据类"""
    item_id: str
    name: str
    description: str
    price: float
    category: str
    features: List[str]
    scenarios: List[str]
    target: List[str]
    image_url: Optional[str] = None
    status: Optional[str] = None


class ProductApiClient:
    """商品API客户端"""

    def __init__(self):
        self.base_url = "https://ykwy-api.wuyoutansuo.com"
        self.shop_id = "wyts666"
        self.access_token = "PoNyP4f0zYDWuXg8NytR"
        # 配置超时参数 (连接超时, 读取超时)
        self.timeout = (10, 30)  # 连接10秒，读取30秒

        # 检查是否需要代理配置
        import os
        self.proxies = {}

        # 常见的代理配置
        proxy_candidates = [
            os.getenv('HTTP_PROXY'),
            os.getenv('HTTPS_PROXY'),
            os.getenv('http_proxy'),
            os.getenv('https_proxy'),
            'http://127.0.0.1:7890',  # 常见的本地代理
            'http://localhost:7890'
        ]

        # 暂时禁用自动代理检测，因为代理可能导致API访问问题
        # 如果需要使用代理，请手动设置环境变量 FORCE_PROXY=true
        force_proxy = os.getenv('FORCE_PROXY', '').lower() == 'true'

        if force_proxy:
            # 只有明确要求时才使用代理
            for proxy in proxy_candidates:
                if proxy:
                    try:
                        import socket
                        if '127.0.0.1:7890' in proxy or 'localhost:7890' in proxy:
                            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                            sock.settimeout(1)
                            result = sock.connect_ex(('127.0.0.1', 7890))
                            sock.close()
                            if result == 0:  # 连接成功
                                self.proxies = {'http': proxy, 'https': proxy}
                                logger.info(f"🌐 强制使用代理: {proxy}")
                                break
                    except:
                        continue
        else:
            logger.info("🌐 使用直连模式（无代理）")

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Access-Token": self.access_token,
            "Content-Type": "application/json",
            "User-Agent": "SalesAgent/1.0"
        }

    async def _make_request(self, method: str, endpoint: str, params: Dict = None) -> Dict:
        """发起HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers()

        # 尝试多种配置，自动回退
        configs_to_try = [
            {"name": "直连", "proxies": None, "verify": False, "timeout": (5, 15)},
        ]

        # 如果配置了代理，先尝试代理
        if self.proxies:
            configs_to_try.insert(0, {
                "name": "代理",
                "proxies": self.proxies,
                "verify": False,
                "timeout": (10, 30)
            })

        last_error = None

        for config in configs_to_try:
            try:
                logger.info(f"🔄 尝试{config['name']}连接...")

                # 使用 requests 进行同步请求，然后在异步上下文中运行
                def make_sync_request():
                    session = requests.Session()
                    session.verify = config['verify']

                    if config['proxies']:
                        session.proxies.update(config['proxies'])

                    # 添加更多请求头以提高兼容性
                    headers.update({
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Cache-Control': 'no-cache',
                        'Connection': 'keep-alive'
                    })

                    if method.upper() == "GET":
                        response = session.get(url, headers=headers, params=params or {}, timeout=config['timeout'])
                    else:
                        response = session.request(method, url, headers=headers, params=params or {}, timeout=config['timeout'])

                    response.raise_for_status()
                    return response.json()

                # 在异步上下文中运行同步请求
                response_data = await asyncio.to_thread(make_sync_request)
                logger.info(f"✅ {config['name']}连接成功")
                return response_data

            except Exception as e:
                last_error = e
                logger.warning(f"⚠️ {config['name']}连接失败: {e}")
                continue

        # 所有配置都失败了
        if last_error:
            if isinstance(last_error, requests.Timeout):
                logger.error(f"商品API请求超时: {url}")
                raise Exception("请求超时")
            elif isinstance(last_error, requests.HTTPError):
                logger.error(f"商品API请求失败: {last_error.response.status_code} - {url}")
                raise Exception(f"请求失败: {last_error.response.status_code}")
            elif isinstance(last_error, requests.RequestException):
                logger.error(f"商品API请求异常: {last_error}")
                raise Exception(f"请求异常: {str(last_error)}")
            else:
                logger.error(f"商品API请求异常: {last_error}")
                raise Exception(f"请求异常: {str(last_error)}")
        else:
            raise Exception("所有连接方式都失败")

    async def get_all_products(self) -> List[Product]:
        """获取所有商品列表"""
        try:
            logger.info(f"📦 获取所有商品列表，shopId: {self.shop_id}")

            response_data = await self._make_request(
                "GET",
                "/api/v2/temp-product/search",
                params={"shopId": self.shop_id}
            )

            products = []
            data_list = response_data.get("data", [])

            for item in data_list:
                # 解析description字段中的详细信息
                description = item.get("description", {})
                basic_info = description.get("basic_info", {})

                # 从basic_info中提取信息
                product = Product(
                    item_id=str(item.get("productId", "")),  # 使用productId
                    name=basic_info.get("service_name", item.get("name", "")),
                    description=basic_info.get("tagline", ""),
                    price=0.0,  # 价格信息在service_packages中，这里先设为0
                    category=basic_info.get("category", ""),
                    features=basic_info.get("main_features", []),
                    scenarios=list(description.get("user_scenarios", {}).keys()),
                    target=list(description.get("user_scenarios", {}).keys()),
                    image_url=item.get("imageUrl"),
                    status=item.get("status")
                )
                products.append(product)

            logger.info(f"✅ 成功获取 {len(products)} 个商品")
            return products

        except Exception as e:
            logger.error(f"❌ 获取商品列表失败: {e}")
            # 当API不可用时，返回模拟数据
            logger.info("🔄 API不可用，返回模拟商品数据")
            return self._get_mock_products()

    async def search_product_by_query(self, query: str) -> Optional[Product]:
        """根据查询字符串搜索商品（通常是item_id）"""
        try:
            logger.info(f"🔍 搜索商品，query: {query}")

            response_data = await self._make_request(
                "GET",
                "/api/v2/temp-product/search",
                params={
                    "query": query,
                    "shopId": self.shop_id
                }
            )

            data_list = response_data.get("data", [])
            if not data_list:
                logger.warning(f"⚠️ 未找到商品: {query}")
                return None

            # 取第一个匹配的商品
            item = data_list[0]

            # 解析description字段中的详细信息
            description = item.get("description", {})
            basic_info = description.get("basic_info", {})

            product = Product(
                item_id=str(item.get("productId", "")),
                name=basic_info.get("service_name", item.get("name", "")),
                description=basic_info.get("tagline", ""),
                price=0.0,  # 价格信息在service_packages中
                category=basic_info.get("category", ""),
                features=basic_info.get("main_features", []),
                scenarios=list(description.get("user_scenarios", {}).keys()),
                target=list(description.get("user_scenarios", {}).keys()),
                image_url=item.get("imageUrl"),
                status=item.get("status")
            )

            logger.info(f"✅ 找到商品: {product.name}")
            return product

        except Exception as e:
            logger.error(f"❌ 搜索商品失败: {e}")
            return None




# 创建全局实例
product_api_client = ProductApiClient()