import type { Context } from 'hono';

import { lokiService } from './loki';

// 日志级别枚举
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4,
}

// 用户类型定义
interface User {
  id: string;
  email: string;
  role: string;
  organizationId?: string;
  teamId?: string;
}

// 日志级别名称映射
export const LOG_LEVEL_NAMES: Record<LogLevel, string> = {
  [LogLevel.ERROR]: 'ERROR',
  [LogLevel.WARN]: 'WARN',
  [LogLevel.INFO]: 'INFO',
  [LogLevel.DEBUG]: 'DEBUG',
  [LogLevel.TRACE]: 'TRACE',
} as const;

// 日志级别颜色（用于控制台输出）
const LOG_LEVEL_COLORS: Record<LogLevel, string> = {
  [LogLevel.ERROR]: '\x1b[31m', // 红色
  [LogLevel.WARN]: '\x1b[33m', // 黄色
  [LogLevel.INFO]: '\x1b[32m', // 绿色
  [LogLevel.DEBUG]: '\x1b[35m', // 紫色
  [LogLevel.TRACE]: '\x1b[37m', // 白色
} as const;

const RESET_COLOR = '\x1b[0m';

// 日志条目接口
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, unknown>;
  requestId?: string;
  userId?: string;
  organizationId?: string;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

// 日志配置接口
export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  enableJson: boolean;
  enableLoki: boolean;
  filePath?: string;
  maxFileSize?: number;
  maxFiles?: number;
}

// 默认配置
const DEFAULT_CONFIG: LoggerConfig = {
  level: !(Bun.env.NODE_ENV === 'development') ? LogLevel.INFO : LogLevel.DEBUG,
  enableConsole: true,
  enableFile: !(Bun.env.NODE_ENV === 'development'),
  enableJson: !(Bun.env.NODE_ENV === 'development'),
  enableLoki: !(Bun.env.NODE_ENV === 'development'), // 非开发环境默认启用Loki
  filePath: './logs/app.log',
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5,
};

class Logger {
  private config: LoggerConfig;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // 创建日志条目
  private createLogEntry(level: LogLevel, message: string, context?: Record<string, unknown>, error?: Error): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
    };

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
      };
    }

    return entry;
  }

  // 格式化控制台输出
  private formatConsoleOutput(entry: LogEntry): string {
    const color = LOG_LEVEL_COLORS[entry.level];
    const levelName = LOG_LEVEL_NAMES[entry.level];
    const timestamp = entry.timestamp;

    let output = `${color}[${levelName}]${RESET_COLOR} ${timestamp} - ${entry.message}`;

    if (entry.requestId) {
      output += ` [req:${entry.requestId}]`;
    }

    if (entry.userId) {
      output += ` [user:${entry.userId}]`;
    }

    if (entry.organizationId) {
      output += ` [org:${entry.organizationId}]`;
    }

    if (entry.context && Object.keys(entry.context).length > 0) {
      output += `\n  Context: ${JSON.stringify(entry.context, null, 2)}`;
    }

    if (entry.error) {
      output += `\n  Error: ${entry.error.name}: ${entry.error.message}`;
      if (entry.error.stack && this.config.level >= LogLevel.DEBUG) {
        output += `\n  Stack: ${entry.error.stack}`;
      }
    }

    return output;
  }

  // 输出日志
  private output(entry: LogEntry): void {
    // 检查日志级别
    if (entry.level > this.config.level) {
      return;
    }

    // 控制台输出
    if (this.config.enableConsole) {
      const formatted = this.formatConsoleOutput(entry);
      console.log(formatted);
    }

    // JSON 格式输出（用于日志收集）
    if (this.config.enableJson) {
      console.log(JSON.stringify(entry));
    }

    // Loki日志输出
    if (this.config.enableLoki) {
      // 异步发送到Loki，不阻塞主流程
      lokiService.sendLog(entry).catch((error) => {
        // 静默处理Loki发送错误，避免影响主业务
        if (this.config.enableConsole) {
          console.error('[Logger] Loki发送失败:', error);
        }
      });
    }

    // 文件输出（在生产环境中可以集成文件日志库）
    if (this.config.enableFile) {
      // 这里可以集成如 winston 或其他文件日志库
      // 目前先使用简单的控制台输出
    }
  }

  // 公共日志方法
  error(message: string, context?: Record<string, unknown>, error?: Error): void {
    this.output(this.createLogEntry(LogLevel.ERROR, message, context, error));
  }

  warn(message: string, context?: Record<string, unknown>, error?: Error): void {
    this.output(this.createLogEntry(LogLevel.WARN, message, context, error));
  }

  info(message: string, context?: Record<string, unknown>): void {
    this.output(this.createLogEntry(LogLevel.INFO, message, context));
  }

  debug(message: string, context?: Record<string, unknown>): void {
    this.output(this.createLogEntry(LogLevel.DEBUG, message, context));
  }

  trace(message: string, context?: Record<string, unknown>): void {
    this.output(this.createLogEntry(LogLevel.TRACE, message, context));
  }

  // 带请求上下文的日志方法
  withContext(c: Context) {
    const requestId = (c.get('requestId') as string) || 'unknown';
    const user = c.get('user') as User | undefined;
    const userId = user?.id;
    const organizationId = user?.organizationId;

    return {
      error: (message: string, context?: Record<string, unknown>, error?: Error) => {
        const entry = this.createLogEntry(LogLevel.ERROR, message, context, error);
        entry.requestId = requestId;
        entry.userId = userId;
        entry.organizationId = organizationId;
        this.output(entry);
      },
      warn: (message: string, context?: Record<string, unknown>) => {
        const entry = this.createLogEntry(LogLevel.WARN, message, context);
        entry.requestId = requestId;
        entry.userId = userId;
        entry.organizationId = organizationId;
        this.output(entry);
      },
      info: (message: string, context?: Record<string, unknown>) => {
        const entry = this.createLogEntry(LogLevel.INFO, message, context);
        entry.requestId = requestId;
        entry.userId = userId;
        entry.organizationId = organizationId;
        this.output(entry);
      },
      debug: (message: string, context?: Record<string, unknown>) => {
        const entry = this.createLogEntry(LogLevel.DEBUG, message, context);
        entry.requestId = requestId;
        entry.userId = userId;
        entry.organizationId = organizationId;
        this.output(entry);
      },
      trace: (message: string, context?: Record<string, unknown>) => {
        const entry = this.createLogEntry(LogLevel.TRACE, message, context);
        entry.requestId = requestId;
        entry.userId = userId;
        entry.organizationId = organizationId;
        this.output(entry);
      },
    };
  }
}

// 创建全局日志实例
export const logger = new Logger();

// 导出日志级别用于配置
export { LogLevel as LogLevels };
