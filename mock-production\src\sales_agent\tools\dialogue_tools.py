"""
对话工具类 - 提供对话相关的辅助工具
"""
import logging
from typing import List, Dict, Any
from .base_tools import BaseTools

logger = logging.getLogger(__name__)

class DialogueTools(BaseTools):
    """对话工具类 - 提供对话相关的辅助功能"""

    def get_similar_dialogues(self, user_message: str) -> str:
        """从真实对话中查找相似问题的回答作为参考"""
        logger.info("🔧 [工具调用开始] get_similar_dialogues")
        logger.info(f"   📥 参数: user_message='{user_message[:50]}{'...' if len(user_message) > 50 else ''}'")

        if not self.index:
            logger.error("   ❌ 索引不存在")
            result = "未找到相关对话"
            logger.info(f"   📤 返回: {result}")
            logger.info("🏁 [工具调用结束] get_similar_dialogues - 失败")
            return result

        try:
            from llama_index.core.vector_stores.types import MetadataFilters, MetadataFilter, FilterOperator

            filters = MetadataFilters(
                filters=[
                    MetadataFilter(key="type", value="dialogue", operator=FilterOperator.EQ)
                ]
            )

            query_engine = self.index.as_query_engine(
                similarity_top_k=5,
                filters=filters
            )

            logger.info(f"   🔍 检索相似的真实对话，查询: '{user_message}'")
            logger.info(f"   🔍 查询长度: {len(user_message)} 字符")
            nodes = query_engine.retrieve(user_message)
            logger.info(f"   ✅ 检索完成")

            logger.info(f"   📊 检索结果: 找到 {len(nodes)} 个节点")

            if not nodes:
                logger.info("   ❌ 未找到相似对话")
                # 尝试不使用过滤器检索
                logger.info("   🔄 尝试不使用过滤器检索...")
                query_engine_no_filter = self.index.as_query_engine(similarity_top_k=3)
                nodes_no_filter = query_engine_no_filter.retrieve(user_message)
                logger.info(f"   📊 无过滤器检索结果: 找到 {len(nodes_no_filter)} 个节点")

                if nodes_no_filter:
                    for i, node in enumerate(nodes_no_filter[:3]):
                        node_type = node.metadata.get("type", "未知")
                        logger.info(f"   📄 节点{i+1} 类型: {node_type}")

                return "未找到相似的对话参考"

            references = []
            for i, node in enumerate(nodes[:5], 1):
                # 详细日志记录节点信息
                logger.info(f"   📄 对话节点{i} 详情:")
                logger.info(f"      文本: {node.text[:100]}...")
                logger.info(f"      元数据: {node.metadata}")

                # 客户消息在node.text中，客服回复在metadata中
                customer_msg = node.text  # 客户消息就是节点文本
                service_response = node.metadata.get("service_response", "")
                similarity = f"{node.score:.3f}" if hasattr(node, 'score') else "N/A"

                logger.info(f"      客户消息: {customer_msg[:50]}...")
                logger.info(f"      客服回复: {service_response[:50]}...")

                if customer_msg and service_response:
                    references.append(f"""
参考{i} (相似度: {similarity}):
客户: {customer_msg}
客服: {service_response}
""".strip())

            if references:
                result = "真实对话参考:\n\n" + "\n\n".join(references)
                result_preview = result[:200] + "..." if len(result) > 200 else result
                logger.info(f"   📤 返回: {result_preview}")
                logger.info(f"   ✅ 找到 {len(references)} 条参考对话")
                logger.info("🏁 [工具调用结束] get_similar_dialogues")
                return result
            else:
                result = "对话数据格式不正确，无法提供参考"
                logger.info(f"   📤 返回: {result}")
                logger.info("🏁 [工具调用结束] get_similar_dialogues - 无有效数据")
                return result

        except Exception as e:
            logger.error(f"   ❌ 检索对话时出错: {str(e)}")
            result = f"检索对话时出错: {str(e)}"
            logger.info(f"   📤 返回: {result}")
            logger.info("🏁 [工具调用结束] get_similar_dialogues - 异常")
            return result

    def get_standard_answers(self, user_message: str, sales_stage: str = "") -> str:
        """从标准QA库中查找最合适的回答，支持按销售阶段筛选"""
        logger.info(f"🔧 [工具调用] get_standard_answers")
        logger.info(f"   📥 输入参数: user_message='{user_message}', sales_stage='{sales_stage}'")
        logger.info(f"   📊 索引状态: {'存在' if self.index else '不存在'}")

        if not self.index:
            logger.error("   ❌ 索引不存在")
            return "未找到标准话术"

        try:
            # 先尝试使用QA类型过滤器检索
            from llama_index.core.vector_stores.types import MetadataFilters, MetadataFilter, FilterOperator

            filters = MetadataFilters(
                filters=[
                    MetadataFilter(key="type", value="qa", operator=FilterOperator.EQ)
                ]
            )

            retriever = self.index.as_retriever(
                similarity_top_k=3,  # 减少检索数量，提高精准度
                filters=filters
            )

            logger.info("   🔍 检索标准QA回答...")
            nodes = retriever.retrieve(user_message)

            logger.info(f"   📊 QA检索结果: 找到 {len(nodes)} 个节点")

            # 如果没有找到，尝试不使用过滤器
            if not nodes:
                logger.info("   ❌ 使用过滤器未找到，尝试不使用过滤器...")
                retriever_no_filter = self.index.as_retriever(similarity_top_k=3)
                nodes = retriever_no_filter.retrieve(user_message)
                logger.info(f"   📊 无过滤器检索结果: 找到 {len(nodes)} 个节点")

            if not nodes:
                return "未找到相关的标准回答"

            # 根据销售阶段筛选QA
            if sales_stage:
                # 阶段映射
                stage_keywords = {
                    "初步了解": ["初次接触", "初次接触阶段"],
                    "初次接触": ["初次接触", "初次接触阶段"],
                    "需求确认": ["需求确认", "需求确认阶段"],
                    "方案展示": ["方案展示", "方案展示阶段"],
                    "合作引导": ["合作引导", "合作引导阶段"],
                    "异议处理": ["异议处理", "异议处理类"],
                    "转折引导": ["转折引导", "转折点引导类"]
                }

                target_keywords = []
                for key, keywords in stage_keywords.items():
                    if key in sales_stage:
                        target_keywords.extend(keywords)

                if target_keywords:
                    filtered_nodes = []
                    for node in nodes:
                        node_stage = node.metadata.get('stage', '')
                        node_category = node.metadata.get('category', '')
                        if any(keyword in node_stage or keyword in node_category for keyword in target_keywords):
                            filtered_nodes.append(node)

                    if filtered_nodes:
                        nodes = filtered_nodes[:5]
                        logger.info(f"   🎯 按阶段筛选后: 找到 {len(nodes)} 个匹配节点")
                    else:
                        logger.info(f"   📝 未找到阶段匹配，使用通用结果")
                        nodes = nodes[:5]
                else:
                    nodes = nodes[:5]
            else:
                nodes = nodes[:5]

            answers = []
            for i, node in enumerate(nodes[:5], 1):
                # 详细日志记录QA节点信息
                logger.info(f"   📄 QA节点{i} 详情:")
                logger.info(f"      文本: {node.text[:100]}...")
                logger.info(f"      元数据: {node.metadata}")

                # 问题在node.text中，答案在metadata中
                question = node.text  # 问题就是节点文本
                answer = node.metadata.get("answer", "")
                product = node.metadata.get("product", "通用")
                stage = node.metadata.get("stage", "通用")
                purpose = node.metadata.get("purpose", "")
                keywords = node.metadata.get("keywords", [])
                similarity = f"{node.score:.3f}" if hasattr(node, 'score') else "N/A"

                logger.info(f"      问题: {question[:50]}...")
                logger.info(f"      答案: {answer[:50]}...")
                logger.info(f"      阶段: {stage}")

                if question and answer:
                    answer_text = f"""
【{stage}】标准回答{i} (相似度: {similarity}):
问题: {question}
回答: {answer}"""
                    if purpose:
                        answer_text += f"\n目的: {purpose}"
                    if keywords:
                        answer_text += f"\n关键词: {', '.join(keywords)}"

                    answers.append(answer_text.strip())

            if answers:
                result = "📋 标准话术参考：\n\n" + "\n\n".join(answers)
                logger.info(f"   ✅ 找到 {len(answers)} 条标准回答")
                return result
            else:
                return "QA数据格式不正确，无法提供标准回答"

        except Exception as e:
            logger.error(f"   ❌ 检索QA时出错: {str(e)}")
            return f"检索QA时出错: {str(e)}"