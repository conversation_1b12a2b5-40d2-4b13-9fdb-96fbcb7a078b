import { Alert<PERSON><PERSON>gle, Bug, Home, RefreshCw } from 'lucide-react';
import React, { Component, ReactNode } from 'react';

import { Button } from '@/components/ui/button';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  showReportButton?: boolean;
}

/**
 * 全局React错误边界组件
 * 捕获React组件树中的JavaScript错误，记录错误并显示降级UI
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // 更新state以显示降级UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo,
    });

    // 调用外部错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 记录到控制台（开发环境）
    if (import.meta.env.DEV) {
      console.group('🚨 React Error Boundary Caught an Error');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }

    // 在生产环境中可以发送错误报告到监控服务
    if (import.meta.env.PROD) {
      this.reportError(error, errorInfo);
    }
  }

  // 错误上报方法
  private reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    try {
      // 这里可以集成错误监控服务，如 Sentry, LogRocket 等
      const errorReport = {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.getUserId(), // 如果有用户信息
        sessionId: this.getSessionId(), // 如果有会话信息
      };

      // 发送到错误监控服务
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // })

      console.log('Error report prepared:', errorReport);
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private getUserId = (): string | null => {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const data = JSON.parse(authStorage);
        return data.user?.id || null;
      }
    } catch {
      // 忽略解析错误
    }
    return null;
  };

  private getSessionId = (): string | null => {
    try {
      return sessionStorage.getItem('sessionId') || null;
    } catch {
      return null;
    }
  };

  // 重试方法
  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  // 返回首页
  private handleGoHome = () => {
    window.location.href = '/';
  };

  // 复制错误信息
  private handleCopyError = () => {
    const { error, errorInfo, errorId } = this.state;
    const errorText = `
错误ID: ${errorId}
时间: ${new Date().toISOString()}
错误信息: ${error?.message}
错误堆栈: ${error?.stack}
组件堆栈: ${errorInfo?.componentStack}
页面URL: ${window.location.href}
用户代理: ${navigator.userAgent}
    `.trim();

    navigator.clipboard
      .writeText(errorText)
      .then(() => {
        alert('错误信息已复制到剪贴板');
      })
      .catch(() => {
        // 降级方案
        try {
          const textArea = document.createElement('textarea');
          textArea.value = errorText;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          const successful = document.execCommand('copy');
          document.body.removeChild(textArea);
          if (successful) {
            alert('错误信息已复制到剪贴板');
          } else {
            alert('复制失败，请手动复制错误信息');
          }
        } catch (err) {
          console.error('复制到剪贴板失败:', err);
          alert('复制失败，请手动复制错误信息');
        }
      });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义降级UI，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorId } = this.state;

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
            <div className="mb-4">
              <AlertTriangle className="w-16 h-16 text-red-500 mx-auto" />
            </div>

            <h1 className="text-xl font-semibold text-gray-900 mb-2">页面出现了错误</h1>

            <p className="text-gray-600 mb-6">很抱歉，页面遇到了意外错误。我们已经记录了这个问题，请稍后重试。</p>

            {/* 错误ID */}
            <div className="bg-gray-100 rounded p-2 mb-4 text-xs text-gray-500">错误ID: {errorId}</div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <Button onClick={this.handleRetry} className="w-full flex items-center justify-center gap-2">
                <RefreshCw className="w-4 h-4" />
                重试
              </Button>

              <Button variant="outline" onClick={this.handleGoHome} className="w-full flex items-center justify-center gap-2">
                <Home className="w-4 h-4" />
                返回首页
              </Button>

              {this.props.showReportButton && (
                <Button variant="ghost" onClick={this.handleCopyError} className="w-full flex items-center justify-center gap-2 text-sm">
                  <Bug className="w-4 h-4" />
                  复制错误信息
                </Button>
              )}
            </div>

            {/* 开发环境显示详细错误信息 */}
            {import.meta.env.DEV && error && (
              <details className="mt-6 text-left">
                <summary className="text-sm text-gray-500 cursor-pointer mb-2">开发调试信息</summary>
                <div className="bg-red-50 border border-red-200 rounded p-3 text-xs">
                  <div className="font-semibold text-red-800 mb-2">错误信息:</div>
                  <div className="text-red-700 mb-3">{error.message}</div>

                  {error.stack && (
                    <>
                      <div className="font-semibold text-red-800 mb-2">错误堆栈:</div>
                      <pre className="text-red-600 whitespace-pre-wrap text-xs overflow-auto max-h-32">{error.stack}</pre>
                    </>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 高阶组件，用于包装其他组件
export function withErrorBoundary<P extends object>(Component: React.ComponentType<P>, errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

export default ErrorBoundary;
