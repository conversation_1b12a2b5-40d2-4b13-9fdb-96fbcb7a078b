#!/usr/bin/env python3
"""
测试Loki日志上传功能
"""
import os
import sys
import time

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mock-production', 'src'))

def test_loki_logging():
    """测试Loki日志上传"""
    print("🧪 测试Loki日志上传功能...")
    
    # 检查环境变量
    print("\n📋 检查环境变量:")
    loki_url = os.environ.get('LOKI_URL', '')
    loki_username = os.environ.get('LOKI_USERNAME', '')
    loki_password = os.environ.get('LOKI_PASSWORD', '')
    node_env = os.environ.get('NODE_ENV', 'development')
    
    print(f"   LOKI_URL: {'✅ 已配置' if loki_url else '❌ 未配置'}")
    print(f"   LOKI_USERNAME: {'✅ 已配置' if loki_username else '❌ 未配置'}")
    print(f"   LOKI_PASSWORD: {'✅ 已配置' if loki_password else '❌ 未配置'}")
    print(f"   NODE_ENV: {node_env}")
    
    if not (loki_url and loki_username and loki_password):
        print("\n❌ Loki配置不完整，日志不会上传到服务器")
        print("💡 请确保设置了 LOKI_URL, LOKI_USERNAME, LOKI_PASSWORD 环境变量")
        return False
    
    # 导入日志模块
    try:
        from sales_agent.utils.logger import unified_logger, setup_detailed_logging
        print("\n✅ 成功导入日志模块")
    except ImportError as e:
        print(f"\n❌ 导入日志模块失败: {e}")
        return False
    
    # 设置日志
    setup_detailed_logging()
    
    # 检查Loki服务状态
    print(f"\n📊 Loki服务状态:")
    print(f"   Loki启用: {'✅ 是' if unified_logger.loki_service.is_enabled() else '❌ 否'}")
    print(f"   JSON输出: {'✅ 是' if unified_logger.enable_json else '❌ 否'}")
    print(f"   控制台输出: {'✅ 是' if unified_logger.enable_console else '❌ 否'}")
    print(f"   Loki输出: {'✅ 是' if unified_logger.enable_loki else '❌ 否'}")
    
    if not unified_logger.enable_loki:
        print("\n❌ Loki输出未启用，日志不会上传到服务器")
        return False
    
    # 发送测试日志
    print(f"\n🚀 发送测试日志到Loki服务器...")
    test_request_id = f"test_req_{int(time.time())}"
    
    try:
        # 发送不同级别的测试日志
        unified_logger.info("🧪 Loki日志测试 - INFO级别", {
            "test_type": "loki_upload_test",
            "timestamp": time.time(),
            "service": "sales-agent"
        }, test_request_id)
        
        unified_logger.warn("⚠️ Loki日志测试 - WARN级别", {
            "test_type": "loki_upload_test",
            "level": "warning"
        }, test_request_id)
        
        unified_logger.error("❌ Loki日志测试 - ERROR级别", {
            "test_type": "loki_upload_test",
            "level": "error"
        }, Exception("这是一个测试异常"), test_request_id)
        
        print("✅ 测试日志已发送")
        print(f"📋 请求ID: {test_request_id}")
        print(f"🔍 在Grafana中查询: {{service=\"sales-agent\", request_id=\"{test_request_id}\"}}")
        
        return True
        
    except Exception as e:
        print(f"❌ 发送测试日志失败: {e}")
        return False

def main():
    print("🔧 Loki日志上传测试工具")
    print("=" * 50)
    
    # 加载环境变量
    env_file = os.path.join(os.path.dirname(__file__), 'mock-production', '.env')
    if os.path.exists(env_file):
        print(f"📁 加载环境变量文件: {env_file}")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # 移除引号
                    value = value.strip('"\'')
                    os.environ[key] = value
    else:
        print(f"⚠️ 环境变量文件不存在: {env_file}")
    
    # 运行测试
    success = test_loki_logging()
    
    if success:
        print("\n🎉 测试完成！日志应该已经上传到Loki服务器")
        print("💡 提示:")
        print("   1. 检查控制台输出是否包含彩色日志")
        print("   2. 如果NODE_ENV不是development，还会有JSON格式输出")
        print("   3. 在Grafana中查询日志验证上传成功")
    else:
        print("\n❌ 测试失败，请检查配置")

if __name__ == "__main__":
    main()
