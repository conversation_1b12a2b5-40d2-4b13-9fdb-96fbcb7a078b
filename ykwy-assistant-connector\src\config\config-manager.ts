import fs from 'node:fs';
import path from 'node:path';
import readline from 'node:readline';

interface Config {
  apiHost: string;
  apiPort: number;
  email: string;
  password: string;
  teamId: string;
  connectionName: string;
  connectionDescription: string;
  expiresInDays: number;
  tcpProxy: {
    backendHost: string;
    backendPort: number;
  };
}

/**
 * 配置管理器
 * 负责配置文件的读取、保存和配置向导
 */
export class ConfigManager {
  private configPath: string;
  private defaultConfig: Config;
  private config: Config;

  constructor() {
    this.configPath = path.join(__dirname, '../../config.json');

    // 默认配置
    this.defaultConfig = {
      apiHost: 'localhost',
      apiPort: 3002,
      email: '',
      password: '',
      teamId: '',
      connectionName: '千牛连接',
      connectionDescription: '自动创建的千牛连接',
      expiresInDays: 7,
      tcpProxy: {
        backendHost: '127.0.0.1',
        backendPort: 9997,
      },
    };

    this.config = this.loadConfig();
  }

  /**
   * 加载配置文件
   */
  loadConfig() {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        return { ...this.defaultConfig, ...JSON.parse(configData) };
      }
    } catch {
      console.log('⚠️  配置文件读取失败，使用默认配置');
    }
    return { ...this.defaultConfig };
  }

  /**
   * 保存配置文件
   */
  saveConfig() {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('❌ 配置文件保存失败:', (error as Error).message);
    }
  }

  /**
   * 获取配置
   */
  getConfig() {
    return this.config;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<Config>) {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
  }

  /**
   * 检查配置是否完整
   */
  isConfigComplete() {
    return !!(this.config.teamId && this.config.email && this.config.password);
  }

  /**
   * 配置向导
   */
  async configWizard() {
    console.log('🔧 首次运行配置向导');
    console.log('请按照提示输入配置信息（直接回车使用默认值）\n');

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    const question = (prompt: string): Promise<string> => new Promise((resolve) => rl.question(prompt, resolve));

    try {
      const apiHost = await question(`API服务器地址 [${this.config.apiHost}]: `);
      if ((apiHost as string).trim()) this.config.apiHost = (apiHost as string).trim();

      const apiPort = await question(`API服务器端口 [${this.config.apiPort}]: `);
      if ((apiPort as string).trim()) this.config.apiPort = parseInt((apiPort as string).trim()) || this.config.apiPort;

      console.log('\n🔐 登录认证信息:');
      const email = await question(`邮箱 (必填): `);
      if ((email as string).trim()) this.config.email = (email as string).trim();

      const password = await question(`密码 (必填): `);
      if ((password as string).trim()) this.config.password = (password as string).trim();

      console.log('\n📋 团队信息:');
      const teamId = await question(`团队ID (必填): `);
      if ((teamId as string).trim()) this.config.teamId = (teamId as string).trim();

      const connectionName = await question(`连接名称 [${this.config.connectionName}]: `);
      if ((connectionName as string).trim()) this.config.connectionName = (connectionName as string).trim();

      console.log('\n🌐 TCP代理配置:');
      const backendHost = await question(`后端服务器地址 [${this.config.tcpProxy.backendHost}]: `);
      if ((backendHost as string).trim()) this.config.tcpProxy.backendHost = (backendHost as string).trim();

      const backendPort = await question(`后端服务器端口 [${this.config.tcpProxy.backendPort}]: `);
      if ((backendPort as string).trim()) this.config.tcpProxy.backendPort = parseInt((backendPort as string).trim()) || this.config.tcpProxy.backendPort;

      rl.close();

      if (!this.config.email || !this.config.password || !this.config.teamId) {
        console.error('❌ 邮箱、密码和团队ID都不能为空，请重新运行配置');
        process.exit(1);
      }

      this.saveConfig();
      console.log('✅ 配置保存成功！\n');
    } catch (error) {
      rl.close();
      console.error('❌ 配置失败:', (error as Error).message);
      process.exit(1);
    }
  }
}

export default ConfigManager;
