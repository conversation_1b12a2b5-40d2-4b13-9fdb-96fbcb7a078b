import Bun from 'bun';

interface RagflowDocument {
  id: string;
  name: string;
}

interface RagflowUploadResponse {
  data: RagflowDocument[];
}

export class RagflowUploader {
  private readonly BASE_URL = Bun.env['RAGFLOW_BASE_URL'];
  private readonly DATASET_ID = Bun.env['RAGFLOW_DATASET_ID'];
  private readonly TOKEN = Bun.env['RAGFLOW_API_KEY'];

  async clearDocumentsByPrefix(prefix: string): Promise<void> {
    const listUrl = `${this.BASE_URL}/api/v1/datasets/${this.DATASET_ID}/documents`;
    const response = await fetch(`${listUrl}?keywords=${encodeURIComponent(prefix)}`, {
      headers: { Authorization: `Bearer ${this.TOKEN}` },
    });
    if (!response.ok) throw new Error('Failed to list documents');
    const data = (await response.json()) as { data: { docs: RagflowDocument[] } };
    const ids = (data.data?.docs || []).filter((doc: RagflowDocument) => doc.name.startsWith(prefix)).map((doc: RagflowDocument) => doc.id);

    if (ids.length > 0) {
      const delResponse = await fetch(listUrl, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${this.TOKEN}`, 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids }),
      });
      if (!delResponse.ok) throw new Error('Failed to delete documents');
    }
  }

  async uploadFileFromStream(content: string, filename: string): Promise<RagflowDocument> {
    const form = new FormData();
    const blob = new Blob([content]);
    form.append('file', blob, filename);

    const response = await fetch(`${this.BASE_URL}/api/v1/datasets/${this.DATASET_ID}/documents`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${this.TOKEN}` },
      body: form,
    });

    if (!response.ok) throw new Error('Upload failed');
    const data: RagflowUploadResponse = await response.json();
    const uploadedDoc = data.data[0];
    if (!uploadedDoc) throw new Error('Upload failed: no document returned');
    return uploadedDoc;
  }

  async updateDocumentParserConfig(docId: string, delimiter: string): Promise<void> {
    const response = await fetch(`${this.BASE_URL}/api/v1/datasets/${this.DATASET_ID}/documents/${docId}`, {
      method: 'PUT',
      headers: { Authorization: `Bearer ${this.TOKEN}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ parser_config: { delimiter, chunk_token_num: 1024 } }),
    });
    if (!response.ok) throw new Error('Failed to update parser config');
  }

  async parseDocuments(docIds: string[]): Promise<void> {
    const response = await fetch(`${this.BASE_URL}/api/v1/datasets/${this.DATASET_ID}/chunks`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${this.TOKEN}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ document_ids: docIds }),
    });
    if (!response.ok) throw new Error('Failed to parse documents');
  }
}
