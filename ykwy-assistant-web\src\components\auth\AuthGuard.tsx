import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';

import { authClient } from '../../lib/auth-client';

interface AuthGuardProps {
  children?: React.ReactNode;
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const { data: session, isPending } = authClient.useSession();
  const location = useLocation();

  // 如果正在加载会话信息，显示加载状态
  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  // 如果用户已登录，重定向到首页或者来源页面
  if (session?.user) {
    // 检查是否有来源页面的状态
    const from = location.state?.from?.pathname || '/';
    return <Navigate to={from} replace />;
  }

  // 如果用户未登录，允许访问认证页面
  return children ? <>{children}</> : <Outlet />;
}
