{"cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "pkg": {"name": "ykwy-admin", "version": "0.1.6", "private": true, "description": "易康无忧客服管理系统", "author": "<PERSON><PERSON> <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "preview": "max preview", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/charts": "^2.6.1", "@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.10", "@umijs/max": "^4.4.11", "antd": "^5.26.2", "dayjs": "^1.11.13", "js-cookie": "^3.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "husky": "^9.1.7", "lint-staged": "^13.3.0", "postcss": "^8.5.6", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-packagejson": "^2.5.15", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.14.2+sha512.6e2baf77d06b9362294152c851c4f278ede37ab1eba3a55fda317a4a17b209f4dbb973fb250a77abc463a341fcb1f17f17cfa24091c4eb319cda0d9b84278387"}, "pkgPath": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/max/dist/preset": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/max/dist/preset.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/max/dist/preset", "key": "preset"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [33]}, "register": 20}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 8}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react", "react-dom": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react-dom", "react-router": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react-router", "react-router-dom": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 68}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 105}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 18}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 27}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 6}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 17}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 15}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 34}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {"modifyAppData": [19], "onStart": [0]}, "register": 39}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 20}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/access.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/access", "key": "access"}, "./node_modules/@umijs/plugins/dist/analytics": {"config": {"onChange": "reload"}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/analytics.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/analytics", "key": "analytics"}, "./node_modules/@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {"modifyConfig": [4], "modifyAppData": [0]}, "register": 5}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/antd.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/antd", "key": "antd"}, "./node_modules/@umijs/plugins/dist/dva": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/dva.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/dva", "key": "dva"}, "./node_modules/@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/initial-state", "key": "initialState"}, "./node_modules/@umijs/plugins/dist/layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {"modifyConfig": [0], "addLayouts": [0], "modifyAppData": [0]}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/layout.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/layout", "key": "layout"}, "./node_modules/@umijs/plugins/dist/locale": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/locale.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/locale", "key": "locale"}, "./node_modules/@umijs/plugins/dist/mf": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/mf.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/mf", "key": "mf"}, "./node_modules/@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/model.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/model", "key": "model"}, "./node_modules/@umijs/plugins/dist/moment2dayjs": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/moment2dayjs.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/moment2dayjs", "key": "moment2dayjs"}, "./node_modules/@umijs/plugins/dist/qiankun": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/qiankun.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/qiankun", "key": "qiankun"}, "./node_modules/@umijs/plugins/dist/qiankun/master": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/qiankun/master.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/qiankun/master", "key": "<PERSON><PERSON><PERSON>n-master"}, "./node_modules/@umijs/plugins/dist/qiankun/slave": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/qiankun/slave.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/qiankun/slave", "key": "qiankun-slave"}, "./node_modules/@umijs/plugins/dist/react-query": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/react-query.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/react-query", "key": "reactQuery"}, "./node_modules/@umijs/plugins/dist/request": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/request.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/request", "key": "request"}, "./node_modules/@umijs/plugins/dist/styled-components": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/styled-components.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/styled-components", "key": "styledComponents"}, "./node_modules/@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}, "./node_modules/@umijs/plugins/dist/valtio": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/plugins/dist/valtio.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/plugins/dist/valtio", "key": "valtio"}, "./node_modules/@umijs/max/dist/plugins/maxAlias": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/max/dist/plugins/maxAlias.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/max/dist/plugins/maxAlias", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/max/dist/plugins/maxAppData": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/max/dist/plugins/maxAppData.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/max/dist/plugins/maxAppData", "key": "maxAppData"}, "./node_modules/@umijs/max/dist/plugins/maxChecker": {"config": {}, "time": {"hooks": {"onCheckPkgJSON": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/max/dist/plugins/maxChecker.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/max/dist/plugins/maxChecker", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "dev", "args": {"_": []}, "userConfig": {"exportStatic": {"ignorePreRenderError": false}, "esbuildMinifyIIFE": true, "hash": true, "antd": {}, "access": {}, "model": {}, "initialState": {}, "request": {"dataField": "data"}, "layout": {"title": "易康无忧智能客服"}, "routes": [{"path": "/", "redirect": "/home"}, {"name": "登录", "path": "/login", "component": "./Login", "layout": false}, {"name": "首页", "path": "/home", "component": "./Home", "layout": false, "wrappers": ["@/components/ProtectedRoute"]}, {"name": "店铺首页", "path": "/shop/shop-home", "component": "./Shop/ShopHome", "wrappers": ["@/components/ProtectedRoute"]}, {"name": "问答", "path": "/shop/question", "wrappers": ["@/components/ProtectedRoute"], "routes": [{"name": "问答知识库", "path": "/shop/question/knowledge", "component": "./Shop/Question/Knowledge"}, {"name": "自动学习", "path": "/shop/question/auto-learning", "component": "./Shop/Question/AutoLearning"}, {"name": "精准意图", "path": "/shop/question/intent", "component": "./Shop/Question/Intent"}, {"name": "活动管理", "path": "/shop/question/activity", "component": "./Shop/Question/Activity"}]}, {"name": "商品知识库", "path": "/shop/product", "wrappers": ["@/components/ProtectedRoute"], "routes": [{"name": "商品列表", "path": "/shop/product/list-simple", "component": "./Shop/Product/ListSimple"}, {"name": "尺码表", "path": "/shop/product/size-table-simple", "component": "./Shop/Product/SizeTableSimple"}]}, {"name": "智能跟单", "path": "/shop/smart-orders", "wrappers": ["@/components/ProtectedRoute"], "routes": [{"name": "跟单任务管理", "path": "/shop/smart-orders/order-management", "component": "./Shop/SmartOrders/OrderManagement"}, {"name": "发货受限地址", "path": "/shop/smart-orders/shipping-restricted-addresses", "component": "./Shop/SmartOrders/ShippingRestrictedAddresses"}]}], "npmClient": "pnpm", "tailwindcss": {}, "define": {"process.env.UMI_APP_API_URL": "http://localhost:3009"}}, "mainConfigFile": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\.umirc.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/", "svgr": {}, "publicPath": "/", "mfsu": {"strategy": "eager"}, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react", "react-dom": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react-dom", "react-router": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react-router", "react-router-dom": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react-router-dom", "@": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src", "@@": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/.umi", "regenerator-runtime": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\@umijs\\preset-umi\\node_modules\\regenerator-runtime", "antd": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\antd", "@umijs/max": "@@/exports"}, "exportStatic": {"ignorePreRenderError": false}, "esbuildMinifyIIFE": true, "hash": true, "antd": {}, "access": {}, "model": {}, "initialState": {}, "request": {"dataField": "data"}, "layout": {"title": "易康无忧智能客服"}, "routes": [{"path": "/", "redirect": "/home"}, {"name": "登录", "path": "/login", "component": "./Login", "layout": false}, {"name": "首页", "path": "/home", "component": "./Home", "layout": false, "wrappers": ["@/components/ProtectedRoute"]}, {"name": "店铺首页", "path": "/shop/shop-home", "component": "./Shop/ShopHome", "wrappers": ["@/components/ProtectedRoute"]}, {"name": "问答", "path": "/shop/question", "wrappers": ["@/components/ProtectedRoute"], "routes": [{"name": "问答知识库", "path": "/shop/question/knowledge", "component": "./Shop/Question/Knowledge"}, {"name": "自动学习", "path": "/shop/question/auto-learning", "component": "./Shop/Question/AutoLearning"}, {"name": "精准意图", "path": "/shop/question/intent", "component": "./Shop/Question/Intent"}, {"name": "活动管理", "path": "/shop/question/activity", "component": "./Shop/Question/Activity"}]}, {"name": "商品知识库", "path": "/shop/product", "wrappers": ["@/components/ProtectedRoute"], "routes": [{"name": "商品列表", "path": "/shop/product/list-simple", "component": "./Shop/Product/ListSimple"}, {"name": "尺码表", "path": "/shop/product/size-table-simple", "component": "./Shop/Product/SizeTableSimple"}]}, {"name": "智能跟单", "path": "/shop/smart-orders", "wrappers": ["@/components/ProtectedRoute"], "routes": [{"name": "跟单任务管理", "path": "/shop/smart-orders/order-management", "component": "./Shop/SmartOrders/OrderManagement"}, {"name": "发货受限地址", "path": "/shop/smart-orders/shipping-restricted-addresses", "component": "./Shop/SmartOrders/ShippingRestrictedAddresses"}]}], "npmClient": "pnpm", "tailwindcss": {}, "define": {"process.env.UMI_APP_API_URL": "http://localhost:3009"}, "targets": {"chrome": 80}, "theme": {"blue-base": "#1890ff", "blue-1": "#e6f7ff", "blue-2": "#bae7ff", "blue-3": "#91d5ff", "blue-4": "#69c0ff", "blue-5": "#40a9ff", "blue-6": "#1890ff", "blue-7": "#096dd9", "blue-8": "#0050b3", "blue-9": "#003a8c", "blue-10": "#002766", "purple-base": "#722ed1", "purple-1": "#f9f0ff", "purple-2": "#efdbff", "purple-3": "#d3adf7", "purple-4": "#b37feb", "purple-5": "#9254de", "purple-6": "#722ed1", "purple-7": "#531dab", "purple-8": "#391085", "purple-9": "#22075e", "purple-10": "#120338", "cyan-base": "#13c2c2", "cyan-1": "#e6fffb", "cyan-2": "#b5f5ec", "cyan-3": "#87e8de", "cyan-4": "#5cdbd3", "cyan-5": "#36cfc9", "cyan-6": "#13c2c2", "cyan-7": "#08979c", "cyan-8": "#006d75", "cyan-9": "#00474f", "cyan-10": "#002329", "green-base": "#52c41a", "green-1": "#f6ffed", "green-2": "#d9f7be", "green-3": "#b7eb8f", "green-4": "#95de64", "green-5": "#73d13d", "green-6": "#52c41a", "green-7": "#389e0d", "green-8": "#237804", "green-9": "#135200", "green-10": "#092b00", "magenta-base": "#eb2f96", "magenta-1": "#fff0f6", "magenta-2": "#ffd6e7", "magenta-3": "#ffadd2", "magenta-4": "#ff85c0", "magenta-5": "#f759ab", "magenta-6": "#eb2f96", "magenta-7": "#c41d7f", "magenta-8": "#9e1068", "magenta-9": "#780650", "magenta-10": "#520339", "pink-base": "#eb2f96", "pink-1": "#fff0f6", "pink-2": "#ffd6e7", "pink-3": "#ffadd2", "pink-4": "#ff85c0", "pink-5": "#f759ab", "pink-6": "#eb2f96", "pink-7": "#c41d7f", "pink-8": "#9e1068", "pink-9": "#780650", "pink-10": "#520339", "red-base": "#f5222d", "red-1": "#fff1f0", "red-2": "#ffccc7", "red-3": "#ffa39e", "red-4": "#ff7875", "red-5": "#ff4d4f", "red-6": "#f5222d", "red-7": "#cf1322", "red-8": "#a8071a", "red-9": "#820014", "red-10": "#5c0011", "orange-base": "#fa8c16", "orange-1": "#fff7e6", "orange-2": "#ffe7ba", "orange-3": "#ffd591", "orange-4": "#ffc069", "orange-5": "#ffa940", "orange-6": "#fa8c16", "orange-7": "#d46b08", "orange-8": "#ad4e00", "orange-9": "#873800", "orange-10": "#612500", "yellow-base": "#fadb14", "yellow-1": "#feffe6", "yellow-2": "#ffffb8", "yellow-3": "#fffb8f", "yellow-4": "#fff566", "yellow-5": "#ffec3d", "yellow-6": "#fadb14", "yellow-7": "#d4b106", "yellow-8": "#ad8b00", "yellow-9": "#876800", "yellow-10": "#614700", "volcano-base": "#fa541c", "volcano-1": "#fff2e8", "volcano-2": "#ffd8bf", "volcano-3": "#ffbb96", "volcano-4": "#ff9c6e", "volcano-5": "#ff7a45", "volcano-6": "#fa541c", "volcano-7": "#d4380d", "volcano-8": "#ad2102", "volcano-9": "#871400", "volcano-10": "#610b00", "geekblue-base": "#2f54eb", "geekblue-1": "#f0f5ff", "geekblue-2": "#d6e4ff", "geekblue-3": "#adc6ff", "geekblue-4": "#85a5ff", "geekblue-5": "#597ef7", "geekblue-6": "#2f54eb", "geekblue-7": "#1d39c4", "geekblue-8": "#10239e", "geekblue-9": "#061178", "geekblue-10": "#030852", "lime-base": "#a0d911", "lime-1": "#fcffe6", "lime-2": "#f4ffb8", "lime-3": "#eaff8f", "lime-4": "#d3f261", "lime-5": "#bae637", "lime-6": "#a0d911", "lime-7": "#7cb305", "lime-8": "#5b8c00", "lime-9": "#3f6600", "lime-10": "#254000", "gold-base": "#faad14", "gold-1": "#fffbe6", "gold-2": "#fff1b8", "gold-3": "#ffe58f", "gold-4": "#ffd666", "gold-5": "#ffc53d", "gold-6": "#faad14", "gold-7": "#d48806", "gold-8": "#ad6800", "gold-9": "#874d00", "gold-10": "#613400", "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,", "theme": "default", "ant-prefix": "ant", "html-selector": "html", "primary-color": "#1890ff", "primary-color-hover": "#40a9ff", "primary-color-active": "#096dd9", "primary-color-outline": "rgba(24, 144, 255, 0.2)", "processing-color": "#1890ff", "info-color": "#1890ff", "info-color-deprecated-bg": "#e6f7ff", "info-color-deprecated-border": "#91d5ff", "success-color": "#52c41a", "success-color-hover": "#73d13d", "success-color-active": "#389e0d", "success-color-outline": "rgba(82, 196, 26, 0.2)", "success-color-deprecated-bg": "#f6ffed", "success-color-deprecated-border": "#b7eb8f", "warning-color": "#faad14", "warning-color-hover": "#ffc53d", "warning-color-active": "#d48806", "warning-color-outline": "rgba(250, 173, 20, 0.2)", "warning-color-deprecated-bg": "#fffbe6", "warning-color-deprecated-border": "#ffe58f", "error-color": "#ff4d4f", "error-color-hover": "#ff7875", "error-color-active": "#d9363e", "error-color-outline": "rgba(255, 77, 79, 0.2)", "error-color-deprecated-bg": "#fff2f0", "error-color-deprecated-border": "#ffccc7", "highlight-color": "#ff4d4f", "normal-color": "#d9d9d9", "white": "#fff", "black": "#000", "primary-1": "#e6f7ff", "primary-2": "#bae7ff", "primary-3": "#91d5ff", "primary-4": "#69c0ff", "primary-5": "#40a9ff", "primary-6": "#1890ff", "primary-7": "#096dd9", "primary-8": "#0050b3", "primary-9": "#003a8c", "primary-10": "#002766", "component-background": "#fff", "popover-background": "#fff", "popover-customize-border-color": "#f0f0f0", "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "code-family": "'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace", "text-color": "rgba(0, 0, 0, 0.85)", "text-color-secondary": "rgba(0, 0, 0, 0.45)", "text-color-inverse": "#fff", "icon-color": "inherit", "icon-color-hover": "rgba(0, 0, 0, 0.75)", "heading-color": "rgba(0, 0, 0, 0.85)", "text-color-dark": "rgba(255, 255, 255, 0.85)", "text-color-secondary-dark": "rgba(255, 255, 255, 0.65)", "text-selection-bg": "#1890ff", "font-variant-base": "tabular-nums", "font-feature-settings-base": "tnum", "font-size-base": "14px", "font-size-lg": "16px", "font-size-sm": "12px", "heading-1-size": "38px", "heading-2-size": "30px", "heading-3-size": "24px", "heading-4-size": "20px", "heading-5-size": "16px", "line-height-base": "1.5715", "border-radius-base": "2px", "border-radius-sm": "2px", "control-border-radius": "2px", "arrow-border-radius": "2px", "padding-lg": "24px", "padding-md": "16px", "padding-sm": "12px", "padding-xs": "8px", "padding-xss": "4px", "control-padding-horizontal": "12px", "control-padding-horizontal-sm": "8px", "margin-lg": "24px", "margin-md": "16px", "margin-sm": "12px", "margin-xs": "8px", "margin-xss": "4px", "height-base": "32px", "height-lg": "40px", "height-sm": "24px", "item-active-bg": "#e6f7ff", "item-hover-bg": "#f5f5f5", "iconfont-css-prefix": "anticon", "link-color": "#1890ff", "link-hover-color": "#40a9ff", "link-active-color": "#096dd9", "link-decoration": "none", "link-hover-decoration": "none", "link-focus-decoration": "none", "link-focus-outline": "0", "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)", "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)", "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)", "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)", "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)", "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)", "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)", "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)", "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)", "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)", "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)", "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)", "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)", "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)", "border-color-base": "#d9d9d9", "border-color-split": "#f0f0f0", "border-color-inverse": "#fff", "border-width-base": "1px", "border-style-base": "solid", "outline-blur-size": "0", "outline-width": "2px", "outline-color": "#1890ff", "outline-fade": "20%", "background-color-light": "#fafafa", "background-color-base": "#f5f5f5", "disabled-color": "rgba(0, 0, 0, 0.25)", "disabled-bg": "#f5f5f5", "disabled-active-bg": "#e6e6e6", "disabled-color-dark": "rgba(255, 255, 255, 0.35)", "shadow-color": "rgba(0, 0, 0, 0.15)", "shadow-color-inverse": "#fff", "box-shadow-base": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "btn-font-weight": "400", "btn-border-radius-base": "2px", "btn-border-radius-sm": "2px", "btn-border-width": "1px", "btn-border-style": "solid", "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)", "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)", "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)", "btn-primary-color": "#fff", "btn-primary-bg": "#1890ff", "btn-default-color": "rgba(0, 0, 0, 0.85)", "btn-default-bg": "#fff", "btn-default-border": "#d9d9d9", "btn-danger-color": "#fff", "btn-danger-bg": "#ff4d4f", "btn-danger-border": "#ff4d4f", "btn-disable-color": "rgba(0, 0, 0, 0.25)", "btn-disable-bg": "#f5f5f5", "btn-disable-border": "#d9d9d9", "btn-default-ghost-color": "#fff", "btn-default-ghost-bg": "transparent", "btn-default-ghost-border": "#fff", "btn-font-size-lg": "16px", "btn-font-size-sm": "14px", "btn-padding-horizontal-base": "15px", "btn-padding-horizontal-lg": "15px", "btn-padding-horizontal-sm": "7px", "btn-height-base": "32px", "btn-height-lg": "40px", "btn-height-sm": "24px", "btn-line-height": "1.5715", "btn-circle-size": "32px", "btn-circle-size-lg": "40px", "btn-circle-size-sm": "24px", "btn-square-size": "32px", "btn-square-size-lg": "40px", "btn-square-size-sm": "24px", "btn-square-only-icon-size": "16px", "btn-square-only-icon-size-sm": "14px", "btn-square-only-icon-size-lg": "18px", "btn-group-border": "#40a9ff", "btn-link-hover-bg": "transparent", "btn-text-hover-bg": "rgba(0, 0, 0, 0.018)", "checkbox-size": "16px", "checkbox-color": "#1890ff", "checkbox-check-color": "#fff", "checkbox-check-bg": "#fff", "checkbox-border-width": "1px", "checkbox-border-radius": "2px", "checkbox-group-item-margin-right": "8px", "descriptions-bg": "#fafafa", "descriptions-title-margin-bottom": "20px", "descriptions-default-padding": "16px 24px", "descriptions-middle-padding": "12px 24px", "descriptions-small-padding": "8px 16px", "descriptions-item-padding-bottom": "16px", "descriptions-item-trailing-colon": "true", "descriptions-item-label-colon-margin-right": "8px", "descriptions-item-label-colon-margin-left": "2px", "descriptions-extra-color": "rgba(0, 0, 0, 0.85)", "divider-text-padding": "1em", "divider-orientation-margin": "5%", "divider-color": "rgba(0, 0, 0, 0.06)", "divider-vertical-gutter": "8px", "dropdown-selected-color": "#1890ff", "dropdown-menu-submenu-disabled-bg": "#fff", "dropdown-selected-bg": "#e6f7ff", "empty-font-size": "14px", "radio-size": "16px", "radio-top": "0.2em", "radio-border-width": "1px", "radio-dot-size": "8px", "radio-dot-color": "#1890ff", "radio-dot-disabled-color": "rgba(0, 0, 0, 0.2)", "radio-solid-checked-color": "#fff", "radio-button-bg": "#fff", "radio-button-checked-bg": "#fff", "radio-button-color": "rgba(0, 0, 0, 0.85)", "radio-button-hover-color": "#40a9ff", "radio-button-active-color": "#096dd9", "radio-button-padding-horizontal": "15px", "radio-disabled-button-checked-bg": "#e6e6e6", "radio-disabled-button-checked-color": "rgba(0, 0, 0, 0.25)", "radio-wrapper-margin-right": "8px", "screen-xs": "480px", "screen-xs-min": "480px", "screen-sm": "576px", "screen-sm-min": "576px", "screen-md": "768px", "screen-md-min": "768px", "screen-lg": "992px", "screen-lg-min": "992px", "screen-xl": "1200px", "screen-xl-min": "1200px", "screen-xxl": "1600px", "screen-xxl-min": "1600px", "screen-xs-max": "575px", "screen-sm-max": "767px", "screen-md-max": "991px", "screen-lg-max": "1199px", "screen-xl-max": "1599px", "grid-columns": "24", "layout-header-background": "#001529", "layout-header-height": "64px", "layout-header-padding": "0 50px", "layout-header-color": "rgba(0, 0, 0, 0.85)", "layout-footer-padding": "24px 50px", "layout-footer-background": "#f0f2f5", "layout-sider-background": "#001529", "layout-trigger-height": "48px", "layout-trigger-background": "#002140", "layout-trigger-color": "#fff", "layout-zero-trigger-width": "36px", "layout-zero-trigger-height": "42px", "layout-sider-background-light": "#fff", "layout-trigger-background-light": "#fff", "layout-trigger-color-light": "rgba(0, 0, 0, 0.85)", "zindex-badge": "auto", "zindex-table-fixed": "2", "zindex-affix": "10", "zindex-back-top": "10", "zindex-picker-panel": "10", "zindex-popup-close": "10", "zindex-modal": "1000", "zindex-modal-mask": "1000", "zindex-message": "1010", "zindex-notification": "1010", "zindex-popover": "1030", "zindex-dropdown": "1050", "zindex-picker": "1050", "zindex-popoconfirm": "1060", "zindex-tooltip": "1070", "zindex-image": "1080", "animation-duration-slow": "0.3s", "animation-duration-base": "0.2s", "animation-duration-fast": "0.1s", "collapse-panel-border-radius": "2px", "dropdown-menu-bg": "#fff", "dropdown-vertical-padding": "5px", "dropdown-edge-child-vertical-padding": "4px", "dropdown-font-size": "14px", "dropdown-line-height": "22px", "label-required-color": "#ff4d4f", "label-color": "rgba(0, 0, 0, 0.85)", "form-warning-input-bg": "#fff", "form-item-margin-bottom": "24px", "form-item-trailing-colon": "true", "form-vertical-label-padding": "0 0 8px", "form-vertical-label-margin": "0", "form-item-label-font-size": "14px", "form-item-label-height": "32px", "form-item-label-colon-margin-right": "8px", "form-item-label-colon-margin-left": "2px", "form-error-input-bg": "#fff", "input-height-base": "32px", "input-height-lg": "40px", "input-height-sm": "24px", "input-padding-horizontal": "11px", "input-padding-horizontal-base": "11px", "input-padding-horizontal-sm": "7px", "input-padding-horizontal-lg": "11px", "input-padding-vertical-base": "4px", "input-padding-vertical-sm": "0px", "input-padding-vertical-lg": "6.5px", "input-placeholder-color": "#bfbfbf", "input-color": "rgba(0, 0, 0, 0.85)", "input-icon-color": "rgba(0, 0, 0, 0.85)", "input-border-color": "#d9d9d9", "input-bg": "#fff", "input-number-hover-border-color": "#40a9ff", "input-number-handler-active-bg": "#f4f4f4", "input-number-handler-hover-bg": "#40a9ff", "input-number-handler-bg": "#fff", "input-number-handler-border-color": "#d9d9d9", "input-addon-bg": "#fafafa", "input-hover-border-color": "#40a9ff", "input-disabled-bg": "#f5f5f5", "input-outline-offset": "0 0", "input-icon-hover-color": "rgba(0, 0, 0, 0.85)", "input-disabled-color": "rgba(0, 0, 0, 0.25)", "mentions-dropdown-bg": "#fff", "mentions-dropdown-menu-item-hover-bg": "#fff", "select-border-color": "#d9d9d9", "select-item-selected-color": "rgba(0, 0, 0, 0.85)", "select-item-selected-font-weight": "600", "select-dropdown-bg": "#fff", "select-item-selected-bg": "#e6f7ff", "select-item-active-bg": "#f5f5f5", "select-dropdown-vertical-padding": "5px", "select-dropdown-font-size": "14px", "select-dropdown-line-height": "22px", "select-dropdown-height": "32px", "select-background": "#fff", "select-clear-background": "#fff", "select-selection-item-bg": "#f5f5f5", "select-selection-item-border-color": "#f0f0f0", "select-single-item-height-lg": "40px", "select-multiple-item-height": "24px", "select-multiple-item-height-lg": "32px", "select-multiple-item-spacing-half": "2px", "select-multiple-disabled-background": "#f5f5f5", "select-multiple-item-disabled-color": "#bfbfbf", "select-multiple-item-disabled-border-color": "#d9d9d9", "cascader-bg": "#fff", "cascader-item-selected-bg": "#e6f7ff", "cascader-menu-bg": "#fff", "cascader-menu-border-color-split": "#f0f0f0", "cascader-dropdown-vertical-padding": "5px", "cascader-dropdown-edge-child-vertical-padding": "4px", "cascader-dropdown-font-size": "14px", "cascader-dropdown-line-height": "22px", "anchor-bg": "transparent", "anchor-border-color": "#f0f0f0", "anchor-link-top": "4px", "anchor-link-left": "16px", "anchor-link-padding": "4px 0 4px 16px", "tooltip-max-width": "250px", "tooltip-color": "#fff", "tooltip-bg": "rgba(0, 0, 0, 0.75)", "tooltip-arrow-width": "11.3137085px", "tooltip-distance": "14.3137085px", "tooltip-arrow-color": "rgba(0, 0, 0, 0.75)", "tooltip-border-radius": "2px", "popover-bg": "#fff", "popover-color": "rgba(0, 0, 0, 0.85)", "popover-min-width": "177px", "popover-min-height": "32px", "popover-arrow-width": "11.3137085px", "popover-arrow-color": "#fff", "popover-arrow-outer-color": "#fff", "popover-distance": "15.3137085px", "popover-padding-horizontal": "16px", "modal-header-padding-vertical": "16px", "modal-header-padding-horizontal": "24px", "modal-header-bg": "#fff", "modal-header-padding": "16px 24px", "modal-header-border-width": "1px", "modal-header-border-style": "solid", "modal-header-title-line-height": "22px", "modal-header-title-font-size": "16px", "modal-header-border-color-split": "#f0f0f0", "modal-header-close-size": "54px", "modal-content-bg": "#fff", "modal-heading-color": "rgba(0, 0, 0, 0.85)", "modal-close-color": "rgba(0, 0, 0, 0.45)", "modal-footer-bg": "transparent", "modal-footer-border-color-split": "#f0f0f0", "modal-footer-border-style": "solid", "modal-footer-padding-vertical": "10px", "modal-footer-padding-horizontal": "16px", "modal-footer-border-width": "1px", "modal-mask-bg": "rgba(0, 0, 0, 0.45)", "modal-confirm-title-font-size": "16px", "modal-border-radius": "2px", "progress-default-color": "#1890ff", "progress-remaining-color": "#f5f5f5", "progress-info-text-color": "rgba(0, 0, 0, 0.85)", "progress-radius": "100px", "progress-steps-item-bg": "#f3f3f3", "progress-text-font-size": "1em", "progress-text-color": "rgba(0, 0, 0, 0.85)", "progress-circle-text-font-size": "1em", "menu-inline-toplevel-item-height": "40px", "menu-item-height": "40px", "menu-item-group-height": "1.5715", "menu-collapsed-width": "80px", "menu-bg": "#fff", "menu-popup-bg": "#fff", "menu-item-color": "rgba(0, 0, 0, 0.85)", "menu-inline-submenu-bg": "#fafafa", "menu-highlight-color": "#1890ff", "menu-highlight-danger-color": "#ff4d4f", "menu-item-active-bg": "#e6f7ff", "menu-item-active-danger-bg": "#fff1f0", "menu-item-active-border-width": "3px", "menu-item-group-title-color": "rgba(0, 0, 0, 0.45)", "menu-item-vertical-margin": "4px", "menu-item-font-size": "14px", "menu-item-boundary-margin": "8px", "menu-item-padding-horizontal": "20px", "menu-item-padding": "0 20px", "menu-horizontal-line-height": "46px", "menu-icon-margin-right": "10px", "menu-icon-size": "14px", "menu-icon-size-lg": "16px", "menu-item-group-title-font-size": "14px", "menu-dark-color": "rgba(255, 255, 255, 0.65)", "menu-dark-danger-color": "#ff4d4f", "menu-dark-bg": "#001529", "menu-dark-arrow-color": "#fff", "menu-dark-inline-submenu-bg": "#000c17", "menu-dark-highlight-color": "#fff", "menu-dark-item-active-bg": "#1890ff", "menu-dark-item-active-danger-bg": "#ff4d4f", "menu-dark-selected-item-icon-color": "#fff", "menu-dark-selected-item-text-color": "#fff", "menu-dark-item-hover-bg": "transparent", "spin-dot-size-sm": "14px", "spin-dot-size": "20px", "spin-dot-size-lg": "32px", "table-bg": "#fff", "table-header-bg": "#fafafa", "table-header-color": "rgba(0, 0, 0, 0.85)", "table-header-sort-bg": "#f5f5f5", "table-row-hover-bg": "#fafafa", "table-selected-row-color": "inherit", "table-selected-row-bg": "#e6f7ff", "table-selected-row-hover-bg": "#dcf4ff", "table-expanded-row-bg": "#fbfbfb", "table-padding-vertical": "16px", "table-padding-horizontal": "16px", "table-padding-vertical-md": "12px", "table-padding-horizontal-md": "8px", "table-padding-vertical-sm": "8px", "table-padding-horizontal-sm": "8px", "table-border-color": "#f0f0f0", "table-border-radius-base": "2px", "table-footer-bg": "#fafafa", "table-footer-color": "rgba(0, 0, 0, 0.85)", "table-header-bg-sm": "#fafafa", "table-font-size": "14px", "table-font-size-md": "14px", "table-font-size-sm": "14px", "table-header-cell-split-color": "rgba(0, 0, 0, 0.06)", "table-header-sort-active-bg": "rgba(0, 0, 0, 0.04)", "table-fixed-header-sort-active-bg": "#f5f5f5", "table-header-filter-active-bg": "rgba(0, 0, 0, 0.04)", "table-filter-btns-bg": "inherit", "table-filter-dropdown-bg": "#fff", "table-expand-icon-bg": "#fff", "table-selection-column-width": "32px", "table-sticky-scroll-bar-bg": "rgba(0, 0, 0, 0.35)", "table-sticky-scroll-bar-radius": "4px", "tag-border-radius": "2px", "tag-default-bg": "#fafafa", "tag-default-color": "rgba(0, 0, 0, 0.85)", "tag-font-size": "12px", "tag-line-height": "20px", "picker-bg": "#fff", "picker-basic-cell-hover-color": "#f5f5f5", "picker-basic-cell-active-with-range-color": "#e6f7ff", "picker-basic-cell-hover-with-range-color": "#cbe6ff", "picker-basic-cell-disabled-bg": "rgba(0, 0, 0, 0.04)", "picker-border-color": "#f0f0f0", "picker-date-hover-range-border-color": "#7ec1ff", "picker-date-hover-range-color": "#cbe6ff", "picker-time-panel-column-width": "56px", "picker-time-panel-column-height": "224px", "picker-time-panel-cell-height": "28px", "picker-panel-cell-height": "24px", "picker-panel-cell-width": "36px", "picker-text-height": "40px", "picker-panel-without-time-cell-height": "66px", "calendar-bg": "#fff", "calendar-input-bg": "#fff", "calendar-border-color": "#fff", "calendar-item-active-bg": "#e6f7ff", "calendar-column-active-bg": "rgba(230, 247, 255, 0.2)", "calendar-full-bg": "#fff", "calendar-full-panel-bg": "#fff", "carousel-dot-width": "16px", "carousel-dot-height": "3px", "carousel-dot-active-width": "24px", "badge-height": "20px", "badge-height-sm": "14px", "badge-dot-size": "6px", "badge-font-size": "12px", "badge-font-size-sm": "12px", "badge-font-weight": "normal", "badge-status-size": "6px", "badge-text-color": "#fff", "badge-color": "#ff4d4f", "rate-star-color": "#fadb14", "rate-star-bg": "#f0f0f0", "rate-star-size": "20px", "rate-star-hover-scale": "scale(1.1)", "card-head-color": "rgba(0, 0, 0, 0.85)", "card-head-background": "transparent", "card-head-font-size": "16px", "card-head-font-size-sm": "14px", "card-head-padding": "16px", "card-head-padding-sm": "8px", "card-head-height": "48px", "card-head-height-sm": "36px", "card-inner-head-padding": "12px", "card-padding-base": "24px", "card-padding-base-sm": "12px", "card-actions-background": "#fff", "card-actions-li-margin": "12px 0", "card-skeleton-bg": "#cfd8dc", "card-background": "#fff", "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)", "card-radius": "2px", "card-head-tabs-margin-bottom": "-17px", "card-head-extra-color": "rgba(0, 0, 0, 0.85)", "comment-bg": "inherit", "comment-padding-base": "16px 0", "comment-nest-indent": "44px", "comment-font-size-base": "14px", "comment-font-size-sm": "12px", "comment-author-name-color": "rgba(0, 0, 0, 0.45)", "comment-author-time-color": "#ccc", "comment-action-color": "rgba(0, 0, 0, 0.45)", "comment-action-hover-color": "#595959", "comment-actions-margin-bottom": "inherit", "comment-actions-margin-top": "12px", "comment-content-detail-p-margin-bottom": "inherit", "tabs-card-head-background": "#fafafa", "tabs-card-height": "40px", "tabs-card-active-color": "#1890ff", "tabs-card-horizontal-padding": "8px 16px", "tabs-card-horizontal-padding-sm": "6px 16px", "tabs-card-horizontal-padding-lg": "7px 16px 6px", "tabs-title-font-size": "14px", "tabs-title-font-size-lg": "16px", "tabs-title-font-size-sm": "14px", "tabs-ink-bar-color": "#1890ff", "tabs-bar-margin": "0 0 16px 0", "tabs-horizontal-gutter": "32px", "tabs-horizontal-margin": "0 0 0 32px", "tabs-horizontal-margin-rtl": "0 0 0 32px", "tabs-horizontal-padding": "12px 0", "tabs-horizontal-padding-lg": "16px 0", "tabs-horizontal-padding-sm": "8px 0", "tabs-vertical-padding": "8px 24px", "tabs-vertical-margin": "16px 0 0 0", "tabs-scrolling-size": "32px", "tabs-highlight-color": "#1890ff", "tabs-hover-color": "#40a9ff", "tabs-active-color": "#096dd9", "tabs-card-gutter": "2px", "tabs-card-tab-active-border-top": "2px solid transparent", "back-top-color": "#fff", "back-top-bg": "rgba(0, 0, 0, 0.45)", "back-top-hover-bg": "rgba(0, 0, 0, 0.85)", "avatar-size-base": "32px", "avatar-size-lg": "40px", "avatar-size-sm": "24px", "avatar-font-size-base": "18px", "avatar-font-size-lg": "24px", "avatar-font-size-sm": "14px", "avatar-bg": "#ccc", "avatar-color": "#fff", "avatar-border-radius": "2px", "avatar-group-overlapping": "-8px", "avatar-group-space": "3px", "avatar-group-border-color": "#fff", "switch-height": "22px", "switch-sm-height": "16px", "switch-min-width": "44px", "switch-sm-min-width": "28px", "switch-disabled-opacity": "0.4", "switch-color": "#1890ff", "switch-bg": "#fff", "switch-shadow-color": "rgba(0, 35, 11, 0.2)", "switch-padding": "2px", "switch-inner-margin-min": "7px", "switch-inner-margin-max": "25px", "switch-sm-inner-margin-min": "5px", "switch-sm-inner-margin-max": "18px", "pagination-item-bg": "#fff", "pagination-item-size": "32px", "pagination-item-size-sm": "24px", "pagination-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "pagination-font-weight-active": "500", "pagination-item-bg-active": "#fff", "pagination-item-link-bg": "#fff", "pagination-item-disabled-color-active": "rgba(0, 0, 0, 0.25)", "pagination-item-disabled-bg-active": "#e6e6e6", "pagination-item-input-bg": "#fff", "pagination-mini-options-size-changer-top": "0px", "page-header-padding": "24px", "page-header-padding-vertical": "16px", "page-header-padding-breadcrumb": "12px", "page-header-content-padding-vertical": "12px", "page-header-back-color": "#000", "page-header-ghost-bg": "inherit", "page-header-heading-title": "20px", "page-header-heading-sub-title": "14px", "page-header-tabs-tab-font-size": "16px", "breadcrumb-base-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-last-item-color": "rgba(0, 0, 0, 0.85)", "breadcrumb-font-size": "14px", "breadcrumb-icon-font-size": "14px", "breadcrumb-link-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-link-color-hover": "rgba(0, 0, 0, 0.85)", "breadcrumb-separator-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-separator-margin": "0 8px", "slider-margin": "10px 6px 10px", "slider-rail-background-color": "#f5f5f5", "slider-rail-background-color-hover": "#e1e1e1", "slider-track-background-color": "#91d5ff", "slider-track-background-color-hover": "#69c0ff", "slider-handle-border-width": "2px", "slider-handle-background-color": "#fff", "slider-handle-color": "#91d5ff", "slider-handle-color-hover": "#69c0ff", "slider-handle-color-focus": "#46a6ff", "slider-handle-color-focus-shadow": "rgba(24, 144, 255, 0.12)", "slider-handle-color-tooltip-open": "#1890ff", "slider-handle-size": "14px", "slider-handle-margin-top": "-5px", "slider-handle-shadow": "0", "slider-dot-border-color": "#f0f0f0", "slider-dot-border-color-active": "#8cc8ff", "slider-disabled-color": "rgba(0, 0, 0, 0.25)", "slider-disabled-background-color": "#fff", "tree-bg": "#fff", "tree-title-height": "24px", "tree-child-padding": "18px", "tree-directory-selected-color": "#fff", "tree-directory-selected-bg": "#1890ff", "tree-node-hover-bg": "#f5f5f5", "tree-node-selected-bg": "#bae7ff", "collapse-header-padding": "12px 16px", "collapse-header-padding-extra": "40px", "collapse-header-bg": "#fafafa", "collapse-content-padding": "16px", "collapse-content-bg": "#fff", "collapse-header-arrow-left": "16px", "skeleton-color": "rgba(190, 190, 190, 0.2)", "skeleton-to-color": "rgba(129, 129, 129, 0.24)", "skeleton-paragraph-margin-top": "28px", "skeleton-paragraph-li-margin-top": "16px", "skeleton-paragraph-li-height": "16px", "skeleton-title-height": "16px", "skeleton-title-paragraph-margin-top": "24px", "transfer-header-height": "40px", "transfer-item-height": "32px", "transfer-disabled-bg": "#f5f5f5", "transfer-list-height": "200px", "transfer-item-hover-bg": "#f5f5f5", "transfer-item-selected-hover-bg": "#dcf4ff", "transfer-item-padding-vertical": "6px", "transfer-list-search-icon-top": "12px", "message-notice-content-padding": "10px 16px", "message-notice-content-bg": "#fff", "wave-animation-width": "6px", "alert-success-border-color": "#b7eb8f", "alert-success-bg-color": "#f6ffed", "alert-success-icon-color": "#52c41a", "alert-info-border-color": "#91d5ff", "alert-info-bg-color": "#e6f7ff", "alert-info-icon-color": "#1890ff", "alert-warning-border-color": "#ffe58f", "alert-warning-bg-color": "#fffbe6", "alert-warning-icon-color": "#faad14", "alert-error-border-color": "#ffccc7", "alert-error-bg-color": "#fff2f0", "alert-error-icon-color": "#ff4d4f", "alert-message-color": "rgba(0, 0, 0, 0.85)", "alert-text-color": "rgba(0, 0, 0, 0.85)", "alert-close-color": "rgba(0, 0, 0, 0.45)", "alert-close-hover-color": "rgba(0, 0, 0, 0.75)", "alert-no-icon-padding-vertical": "8px", "alert-with-description-no-icon-padding-vertical": "15px", "alert-with-description-padding-vertical": "15px", "alert-with-description-padding": "15px 15px 15px 24px", "alert-icon-top": "12.0005px", "alert-with-description-icon-size": "24px", "list-header-background": "transparent", "list-footer-background": "transparent", "list-empty-text-padding": "16px", "list-item-padding": "12px 0", "list-item-padding-sm": "8px 16px", "list-item-padding-lg": "16px 24px", "list-item-meta-margin-bottom": "16px", "list-item-meta-avatar-margin-right": "16px", "list-item-meta-title-margin-bottom": "12px", "list-customize-card-bg": "#fff", "list-item-meta-description-font-size": "14px", "statistic-title-font-size": "14px", "statistic-content-font-size": "24px", "statistic-unit-font-size": "24px", "statistic-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "drawer-header-padding": "16px 24px", "drawer-bg": "#fff", "drawer-footer-padding-vertical": "10px", "drawer-footer-padding-horizontal": "16px", "drawer-header-close-size": "56px", "drawer-title-font-size": "16px", "drawer-title-line-height": "22px", "timeline-width": "2px", "timeline-color": "#f0f0f0", "timeline-dot-border-width": "2px", "timeline-dot-color": "#1890ff", "timeline-dot-bg": "#fff", "timeline-item-padding-bottom": "20px", "typography-title-font-weight": "600", "typography-title-margin-top": "1.2em", "typography-title-margin-bottom": "0.5em", "upload-actions-color": "rgba(0, 0, 0, 0.45)", "process-tail-color": "#f0f0f0", "steps-nav-arrow-color": "rgba(0, 0, 0, 0.25)", "steps-background": "#fff", "steps-icon-size": "32px", "steps-icon-custom-size": "32px", "steps-icon-custom-top": "0px", "steps-icon-custom-font-size": "24px", "steps-icon-top": "-0.5px", "steps-icon-font-size": "16px", "steps-icon-margin": "0 8px 0 0", "steps-title-line-height": "32px", "steps-small-icon-size": "24px", "steps-small-icon-margin": "0 8px 0 0", "steps-dot-size": "8px", "steps-dot-top": "2px", "steps-current-dot-size": "10px", "steps-description-max-width": "140px", "steps-nav-content-max-width": "auto", "steps-vertical-icon-width": "16px", "steps-vertical-tail-width": "16px", "steps-vertical-tail-width-sm": "12px", "notification-bg": "#fff", "notification-padding-vertical": "16px", "notification-padding-horizontal": "24px", "result-title-font-size": "24px", "result-subtitle-font-size": "14px", "result-icon-font-size": "72px", "result-extra-margin": "24px 0 0 0", "image-size-base": "48px", "image-font-size-base": "24px", "image-bg": "#f5f5f5", "image-color": "#fff", "image-mask-font-size": "16px", "image-preview-operation-size": "18px", "image-preview-operation-color": "rgba(255, 255, 255, 0.85)", "image-preview-operation-disabled-color": "rgba(255, 255, 255, 0.25)", "segmented-bg": "rgba(0, 0, 0, 0.04)", "segmented-hover-bg": "rgba(0, 0, 0, 0.06)", "segmented-selected-bg": "#fff", "segmented-label-color": "rgba(0, 0, 0, 0.65)", "segmented-label-hover-color": "#262626"}}, "routes": {"1": {"path": "/", "redirect": "/home", "parentId": "ant-design-pro-layout", "id": "1", "absPath": "/"}, "2": {"name": "登录", "path": "/login", "layout": false, "file": "@/pages/Login/index.tsx", "id": "2", "absPath": "/login", "__content": "import background from '@/assets/image/jpg/bg.jpg';\r\nimport LoginForm from '@/components/Login/LoginForm';\r\nimport { login, register } from '@/services/user';\r\nimport { useNavigate } from '@umijs/max';\r\nimport { Tabs, message } from 'antd';\r\nimport Cookies from 'js-cookie';\r\nimport React, { useEffect, useState } from 'react';\r\nimport type { UserStore } from '../../store/userStore';\r\nimport useStore from '../../store/userStore';\r\nimport logger from '../../utils/logger';\r\nimport './login.css';\r\n\r\nexport const layout = false;\r\nconst LoginPage: React.FC = () => {\r\n  const [loading, setLoading] = useState(false);\r\n  const [tab, setTab] = useState('login');\r\n  const [initialValues, setInitialValues] = useState<{\r\n    email: string;\r\n    password: string;\r\n    remember: boolean;\r\n  }>({ email: '', password: '', remember: false });\r\n  const navigate = useNavigate();\r\n  const setUserInfo = useStore((s: UserStore) => s.setUserInfo);\r\n\r\n  // 页面加载时从Cookie读取保存的账号\r\n  useEffect(() => {\r\n    const savedEmail = Cookies.get('saved_email') || '';\r\n    const remember = !!savedEmail;\r\n    setInitialValues({ email: savedEmail, password: '', remember });\r\n  }, []);\r\n\r\n  const onFinish = async (values: {\r\n    email: string;\r\n    password: string;\r\n    remember: boolean;\r\n  }) => {\r\n    setLoading(true);\r\n    try {\r\n      if (tab === 'login') {\r\n        const res = await login(values);\r\n\r\n        setUserInfo(res.data.user);\r\n        Cookies.set('access_token', res.data.accessToken, {\r\n          expires: 7,\r\n          secure: process.env.NODE_ENV === 'production',\r\n        });\r\n\r\n        // 记住我状态下只保存邮箱到Cookie，不保存密码\r\n        if (values.remember) {\r\n          Cookies.set('saved_email', values.email, { expires: 3 });\r\n        } else {\r\n          // 未勾选时清除保存的账号\r\n          Cookies.remove('saved_email');\r\n        }\r\n        // 设置用户ID到日志系统\r\n        logger.setUserId(res.data.user.id.toString());\r\n\r\n        message.success('登录成功');\r\n        navigate('/home');\r\n      } else {\r\n        await register(values);\r\n        message.success('注册成功，请登录');\r\n        setTab('login');\r\n      }\r\n    } catch (e: unknown) {\r\n      const errorMessage = e instanceof Error ? e.message : '操作失败';\r\n      message.error(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const items = [\r\n    {\r\n      key: 'login',\r\n      label: '登录',\r\n      children: (\r\n        <LoginForm\r\n          onFinish={onFinish}\r\n          buttonText=\"登录\"\r\n          loading={loading}\r\n          initialValues={initialValues}\r\n        />\r\n      ),\r\n    },\r\n    {\r\n      key: 'register',\r\n      label: '注册',\r\n      children: (\r\n        <LoginForm\r\n          onFinish={onFinish}\r\n          buttonText=\"注册\"\r\n          loading={loading}\r\n          initialValues={{ email: '', password: '', remember: false }}\r\n        />\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"relative h-screen w-full overflow-hidden\">\r\n      <div\r\n        className=\"relative h-full bg-cover bg-center\"\r\n        style={{ backgroundImage: `url(${background})` }}\r\n      >\r\n        <div className=\"absolute inset-0 flex items-center p-4\">\r\n          <div className=\"login-container\">\r\n            <div className=\"bg-white rounded-lg shadow-xl p-[clamp(1rem,3vw,2rem)] max-w-md min-h-[clamp(300px,50vh,500px)] transition-all duration-300\">\r\n              <h1 className=\"text-black text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-center mb-8\">\r\n                易康无忧智能客服\r\n              </h1>\r\n              <Tabs activeKey={tab} onChange={setTab} centered items={items} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginPage;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Login/index.tsx"}, "3": {"name": "首页", "path": "", "layout": false, "file": "@/pages/Home/index.tsx", "parentId": "4", "id": "3", "absPath": "/home", "originPath": "/home", "__content": "import CustomerService from '@/components/Assistant/CustomerService';\r\nimport useStore from '@/store/userStore';\r\n// 引入 UserStore 类型\r\nimport type { UserStore } from '@/store/userStore';\r\nimport logger from '@/utils/logger';\r\nimport {\r\n  DownOutlined,\r\n  ShopOutlined,\r\n  TaobaoCircleOutlined,\r\n  TikTokOutlined,\r\n  UserOutlined,\r\n} from '@ant-design/icons';\r\nimport { useNavigate } from '@umijs/max';\r\nimport {\r\n  Button,\r\n  Card,\r\n  Col,\r\n  Dropdown,\r\n  Modal,\r\n  Row,\r\n  Typography,\r\n  message,\r\n} from 'antd';\r\nimport Cookies from 'js-cookie';\r\nimport React from 'react';\r\n\r\nexport const layout = false;\r\n\r\nconst shopCards = [\r\n  { title: 'QINKUNG旗舰店', icon: 'taobao', desc: 'qinkung旗舰店' },\r\n  { title: 'QINKUNG轻功体育官方店', icon: 'taobao', desc: '轻功体育' },\r\n  { title: '轻功跑步官方旗舰店', icon: 'other', desc: 'qinkung' },\r\n  { title: 'QINKUNG轻功体育官方旗舰店', icon: 'douyin', desc: '********' },\r\n  { title: 'QINKUNG轻功体育跑步旗舰店', icon: 'douyin', desc: '*********' },\r\n  { title: '轻功体育服饰配件旗舰店', icon: 'douyin', desc: '*********' },\r\n];\r\n\r\nconst HomePage: React.FC = () => {\r\n  const userInfo = useStore((s: UserStore) => s.userInfo);\r\n  const clearUser = useStore((s: UserStore) => s.clearUser);\r\n  const navigate = useNavigate();\r\n\r\n  // 账户信息弹窗\r\n  const showAccountInfo = () => {\r\n    Modal.info({\r\n      title: '账户信息',\r\n      content: (\r\n        <div>\r\n          <div>邮箱：{userInfo?.email}</div>\r\n          <div>ID：{userInfo?.id}</div>\r\n        </div>\r\n      ),\r\n      okText: '关闭',\r\n    });\r\n  };\r\n\r\n  // 退出登录\r\n  const handleLogout = () => {\r\n    logger.clearUserId();\r\n\r\n    Cookies.remove('access_token');\r\n    clearUser();\r\n    message.success('已退出登录');\r\n    navigate('/login');\r\n  };\r\n\r\n  // 菜单点击事件\r\n  const handleMenuClick = ({ key }: { key: string }) => {\r\n    if (key === 'account') {\r\n      showAccountInfo();\r\n    } else if (key === 'logout') {\r\n      handleLogout();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"h-screen flex flex-col bg-[#f7f8fa] overflow-hidden\"\r\n      data-oid=\":p-ub8u\"\r\n    >\r\n      {/* 顶部栏 */}\r\n      <CustomerService data-oid=\"ix_hulr\" />\r\n      <div\r\n        className=\"h-14 bg-white border-b border-[#f0f0f0] flex items-center justify-between px-8 flex-shrink-0\"\r\n        data-oid=\"cw9c0bf\"\r\n      >\r\n        <div className=\"font-bold text-5xl text-[#222]\" data-oid=\"1v4_8-i\">\r\n          易康无忧智能客服\r\n        </div>\r\n        <Dropdown\r\n          menu={{\r\n            items: [\r\n              { key: 'account', label: '账户信息' },\r\n              { key: 'logout', label: '退出登录' },\r\n            ],\r\n            onClick: handleMenuClick,\r\n          }}\r\n          placement=\"bottomRight\"\r\n          data-oid=\"9jivh1y\"\r\n        >\r\n          <Button\r\n            icon={<UserOutlined data-oid=\":2lulb:\" />}\r\n            className=\"font-medium\"\r\n            data-oid=\"fldbitd\"\r\n          >\r\n            {userInfo?.email || '用户'} <DownOutlined data-oid=\"o4rhm_u\" />\r\n          </Button>\r\n        </Dropdown>\r\n      </div>\r\n      {/* 内容区 */}\r\n      <div\r\n        className=\"flex-1  max-w-[1200px] mx-auto mt-8 bg-transparent\"\r\n        data-oid=\"80g.1us\"\r\n      >\r\n        <Typography.Title level={4} className=\"mb-6\" data-oid=\"zex34pj\">\r\n          我的产品\r\n        </Typography.Title>\r\n        <Row gutter={[24, 24]} data-oid=\"2eyn1ui\">\r\n          {shopCards.map((item) => (\r\n            <Col span={8} key={item.title} data-oid=\"lcvxlm8\">\r\n              <Card\r\n                hoverable\r\n                className=\"rounded-lg min-h-20\"\r\n                onClick={() => navigate('/shop/shop-home')}\r\n                data-oid=\"_oc9iq3\"\r\n              >\r\n                <div className=\"flex items-center\" data-oid=\"a23pbmg\">\r\n                  <div\r\n                    className=\"w-10 h-10 bg-[#f5f5f5] rounded-lg flex items-center justify-center mr-4\"\r\n                    data-oid=\"ulxjbj_\"\r\n                  >\r\n                    {item.icon === 'taobao' && (\r\n                      <TaobaoCircleOutlined\r\n                        className=\"text-7xl text-[#ff5000]\"\r\n                        data-oid=\"n94keqm\"\r\n                      />\r\n                    )}\r\n                    {item.icon === 'douyin' && (\r\n                      <TikTokOutlined\r\n                        className=\"text-7xl text-black\"\r\n                        data-oid=\"-p-.kfo\"\r\n                      />\r\n                    )}\r\n                    {item.icon === 'other' && (\r\n                      <ShopOutlined\r\n                        className=\"text-7xl text-red-600\"\r\n                        data-oid=\"t5463ja\"\r\n                      />\r\n                    )}\r\n                  </div>\r\n                  <div data-oid=\"zwtu5ad\">\r\n                    <div className=\"font-medium text-base\" data-oid=\"u1g_m-3\">\r\n                      {item.title}\r\n                    </div>\r\n                    <div className=\"text-[#888] text-sm\" data-oid=\"rjy-bc1\">\r\n                      {item.desc}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </Card>\r\n            </Col>\r\n          ))}\r\n        </Row>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Home/index.tsx"}, "4": {"path": "/home", "isWrapper": true, "layout": false, "file": "@/components/ProtectedRoute.tsx", "id": "4", "absPath": "/home", "__content": "import { message } from 'antd';\r\nimport Cookies from 'js-cookie';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Navigate, Outlet, useLocation } from 'umi';\r\n\r\nconst ProtectedRoute: React.FC = () => {\r\n  const location = useLocation();\r\n  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);\r\n  const isLoginPage = location.pathname === '/login';\r\n\r\n  useEffect(() => {\r\n    const token = Cookies.get('access_token');\r\n    setIsTokenValid(!!token);\r\n  }, [location.pathname]);\r\n\r\n  useEffect(() => {\r\n    if (isTokenValid === false && !isLoginPage) {\r\n      Cookies.remove('access_token');\r\n      localStorage.removeItem('kywy');\r\n      message.error('请先登录系统');\r\n    }\r\n  }, [isTokenValid, isLoginPage]);\r\n\r\n  if (isTokenValid === null) return null;\r\n\r\n  if (!isTokenValid && !isLoginPage) {\r\n    return <Navigate to=\"/login\" replace state={{ from: location }} />;\r\n  }\r\n\r\n  return <Outlet />;\r\n};\r\n\r\nexport default ProtectedRoute;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/components/ProtectedRoute.tsx"}, "5": {"name": "店铺首页", "path": "", "file": "@/pages/Shop/ShopHome.tsx", "parentId": "6", "id": "5", "absPath": "/shop/shop-home", "originPath": "/shop/shop-home", "__content": "import { Area, Column, Line, Pie } from '@ant-design/charts';\r\nimport {\r\n  Area<PERSON>hartOutlined,\r\n  Bar<PERSON><PERSON>Outlined,\r\n  BulbOutlined,\r\n  CheckCircleOutlined,\r\n  LineChartOutlined,\r\n  PieChartOutlined,\r\n  RobotOutlined,\r\n  SwapOutlined,\r\n  TeamOutlined,\r\n  UserOutlined,\r\n} from '@ant-design/icons';\r\nimport { Button, Card, Progress } from 'antd';\r\nimport React, { useMemo, useState } from 'react';\r\n\r\n// 生成最近7天的日期数组\r\nconst generateLast7Days = () => {\r\n  const dates = [];\r\n  const today = new Date();\r\n\r\n  for (let i = 6; i >= 0; i--) {\r\n    const date = new Date(today);\r\n    date.setDate(today.getDate() - i);\r\n    const month = date.getMonth() + 1;\r\n    const day = date.getDate();\r\n    dates.push(`${month}/${day}`);\r\n  }\r\n\r\n  return dates;\r\n};\r\n\r\n// 生成日期范围描述\r\nconst getDateRangeDescription = () => {\r\n  const today = new Date();\r\n  const startDate = new Date(today);\r\n  startDate.setDate(today.getDate() - 6);\r\n\r\n  const startMonth = startDate.getMonth() + 1;\r\n  const startDay = startDate.getDate();\r\n  const endMonth = today.getMonth() + 1;\r\n  const endDay = today.getDate();\r\n\r\n  return `${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`;\r\n};\r\n\r\n// 图表错误边界组件\r\nclass ChartErrorBoundary extends React.Component<\r\n  { children: React.ReactNode; fallback?: React.ReactNode },\r\n  { hasError: boolean }\r\n> {\r\n  constructor(props: {\r\n    children: React.ReactNode;\r\n    fallback?: React.ReactNode;\r\n  }) {\r\n    super(props);\r\n    this.state = { hasError: false };\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error) {\r\n    console.error('Chart render error:', error);\r\n    return { hasError: true };\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\r\n    console.error('Chart error details:', error, errorInfo);\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasError) {\r\n      return (\r\n        this.props.fallback || (\r\n          <div className=\"text-center text-gray-400 text-sm py-4\">\r\n            图表加载失败\r\n          </div>\r\n        )\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\nconst ShopHome: React.FC = () => {\r\n  // 动态生成最近7天日期\r\n  const last7Days = useMemo(() => generateLast7Days(), []);\r\n  const dateRangeDesc = useMemo(() => getDateRangeDescription(), []);\r\n\r\n  // 接待量数据（使用动态日期）\r\n  const receptionData = useMemo(\r\n    () => [\r\n      {\r\n        title: '总接待量',\r\n        value: '2,865',\r\n        unit: '人次',\r\n        icon: <TeamOutlined className=\"text-xl\" />,\r\n        chartData: last7Days.map((day, index) => ({\r\n          day,\r\n          count: [245, 312, 189, 456, 398, 278, 987][index],\r\n        })),\r\n      },\r\n      {\r\n        title: '全自动接待人数',\r\n        value: '1,542',\r\n        unit: '人',\r\n        icon: <RobotOutlined className=\"text-xl\" />,\r\n        chartData: last7Days.map((day, index) => ({\r\n          day,\r\n          count: [142, 189, 98, 234, 201, 145, 533][index],\r\n        })),\r\n      },\r\n      {\r\n        title: '辅助接待人数',\r\n        value: '1,323',\r\n        unit: '人',\r\n        icon: <UserOutlined className=\"text-xl\" />,\r\n        chartData: last7Days.map((day, index) => ({\r\n          day,\r\n          count: [103, 123, 91, 222, 197, 133, 454][index],\r\n        })),\r\n      },\r\n    ],\r\n    [last7Days],\r\n  );\r\n\r\n  // 解决率数据\r\n  const resolutionData = [\r\n    {\r\n      title: '转人工率',\r\n      value: '12.8',\r\n      unit: '%',\r\n      icon: <SwapOutlined className=\"text-xl\" />,\r\n      color: '#ff7875',\r\n      chartData: [\r\n        { type: '已转人工', value: 12.8 },\r\n        { type: '未转人工', value: 87.2 },\r\n      ],\r\n    },\r\n    {\r\n      title: '智能辅助回复率',\r\n      value: '87.2',\r\n      unit: '%',\r\n      icon: <BulbOutlined className=\"text-xl\" />,\r\n      color: '#40a9ff',\r\n      chartData: [\r\n        { type: '智能回复', value: 87.2 },\r\n        { type: '人工回复', value: 12.8 },\r\n      ],\r\n    },\r\n    {\r\n      title: '问题解决率',\r\n      value: '94.5',\r\n      unit: '%',\r\n      icon: <CheckCircleOutlined className=\"text-xl\" />,\r\n      color: '#73d13d',\r\n      chartData: [\r\n        { type: '已解决', value: 94.5 },\r\n        { type: '未解决', value: 5.5 },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  // 图表类型状态管理\r\n  const [chartTypes, setChartTypes] = useState<{ [key: number]: string }>({\r\n    0: 'column', // 总接待量默认柱状图\r\n    1: 'line', // 全自动接待默认折线图\r\n    2: 'area', // 辅助接待默认面积图\r\n  });\r\n\r\n  const [resolutionChartTypes, setResolutionChartTypes] = useState<{\r\n    [key: number]: string;\r\n  }>({\r\n    0: 'pie', // 转人工率默认饼图\r\n    1: 'progress', // 智能辅助回复率默认进度条\r\n    2: 'pie', // 问题解决率默认饼图\r\n  });\r\n\r\n  // 切换接待量图表类型\r\n  const toggleReceptionChart = (index: number) => {\r\n    const types = ['column', 'line', 'area'];\r\n    const currentIndex = types.indexOf(chartTypes[index]);\r\n    const nextIndex = (currentIndex + 1) % types.length;\r\n    setChartTypes((prev) => ({ ...prev, [index]: types[nextIndex] }));\r\n  };\r\n\r\n  // 切换解决率图表类型\r\n  const toggleResolutionChart = (index: number) => {\r\n    const types = ['pie', 'progress'];\r\n    const currentIndex = types.indexOf(resolutionChartTypes[index]);\r\n    const nextIndex = (currentIndex + 1) % types.length;\r\n    setResolutionChartTypes((prev) => ({ ...prev, [index]: types[nextIndex] }));\r\n  };\r\n\r\n  // 柱状图配置\r\n  const getColumnConfig = (data: any[]) => ({\r\n    data: data || [],\r\n    xField: 'day',\r\n    yField: 'count',\r\n    height: 120,\r\n    autoFit: true,\r\n    columnStyle: {\r\n      radius: [4, 4, 0, 0],\r\n    },\r\n    color: '#40a9ff',\r\n    xAxis: {\r\n      label: {\r\n        style: {\r\n          fontSize: 10,\r\n          fill: '#666',\r\n        },\r\n      },\r\n      line: null,\r\n      tickLine: null,\r\n    },\r\n    yAxis: {\r\n      label: {\r\n        style: {\r\n          fontSize: 10,\r\n          fill: '#666',\r\n        },\r\n      },\r\n      grid: {\r\n        line: {\r\n          style: {\r\n            stroke: '#f0f0f0',\r\n            lineWidth: 1,\r\n          },\r\n        },\r\n      },\r\n    },\r\n  });\r\n\r\n  // 折线图配置\r\n  const getLineConfig = (data: any[]) => ({\r\n    data: data || [],\r\n    xField: 'day',\r\n    yField: 'count',\r\n    height: 120,\r\n    autoFit: true,\r\n    color: '#52c41a',\r\n    smooth: true,\r\n    point: {\r\n      size: 4,\r\n      shape: 'circle',\r\n    },\r\n    xAxis: {\r\n      label: {\r\n        style: {\r\n          fontSize: 10,\r\n          fill: '#666',\r\n        },\r\n      },\r\n      line: null,\r\n      tickLine: null,\r\n    },\r\n    yAxis: {\r\n      label: {\r\n        style: {\r\n          fontSize: 10,\r\n          fill: '#666',\r\n        },\r\n      },\r\n      grid: {\r\n        line: {\r\n          style: {\r\n            stroke: '#f0f0f0',\r\n            lineWidth: 1,\r\n          },\r\n        },\r\n      },\r\n    },\r\n  });\r\n\r\n  // 面积图配置\r\n  const getAreaConfig = (data: any[]) => ({\r\n    data: data || [],\r\n    xField: 'day',\r\n    yField: 'count',\r\n    height: 120,\r\n    autoFit: true,\r\n    color: '#722ed1',\r\n    smooth: true,\r\n    areaStyle: {\r\n      fillOpacity: 0.6,\r\n    },\r\n    xAxis: {\r\n      label: {\r\n        style: {\r\n          fontSize: 10,\r\n          fill: '#666',\r\n        },\r\n      },\r\n      line: null,\r\n      tickLine: null,\r\n    },\r\n    yAxis: {\r\n      label: {\r\n        style: {\r\n          fontSize: 10,\r\n          fill: '#666',\r\n        },\r\n      },\r\n      grid: {\r\n        line: {\r\n          style: {\r\n            stroke: '#f0f0f0',\r\n            lineWidth: 1,\r\n          },\r\n        },\r\n      },\r\n    },\r\n  });\r\n\r\n  // 饼图配置\r\n  const getPieConfig = (data: any[], color: string) => ({\r\n    data: data || [],\r\n    angleField: 'value',\r\n    colorField: 'type',\r\n    radius: 0.8,\r\n    height: 96,\r\n    width: 96,\r\n    autoFit: false,\r\n    color: [color, '#f0f0f0'],\r\n    legend: false,\r\n    label: false,\r\n    interactions: [{ type: 'element-active' }],\r\n  });\r\n\r\n  // 渲染接待量图表\r\n  const renderReceptionChart = (data: any[], type: string) => {\r\n    switch (type) {\r\n      case 'line':\r\n        return <Line {...getLineConfig(data)} />;\r\n      case 'area':\r\n        return <Area {...getAreaConfig(data)} />;\r\n      case 'column':\r\n      default:\r\n        return <Column {...getColumnConfig(data)} />;\r\n    }\r\n  };\r\n\r\n  // 获取图表切换按钮图标\r\n  const getChartIcon = (type: string) => {\r\n    switch (type) {\r\n      case 'line':\r\n        return <LineChartOutlined />;\r\n      case 'area':\r\n        return <AreaChartOutlined />;\r\n      case 'pie':\r\n        return <PieChartOutlined />;\r\n      case 'column':\r\n      default:\r\n        return <BarChartOutlined />;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-background p-8\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* 页面标题 */}\r\n        <div className=\"mb-8\">\r\n          <h1 className=\"text-3xl font-bold text-foreground mb-2\">\r\n            店铺数据总览\r\n          </h1>\r\n          <p className=\"text-muted-foreground\">\r\n            实时监控店铺接待量和问题解决情况\r\n          </p>\r\n        </div>\r\n\r\n        {/* 接待量数据 */}\r\n        <div className=\"mb-8\">\r\n          <h2 className=\"text-xl font-semibold text-foreground mb-4 flex items-center\">\r\n            <TeamOutlined className=\"mr-2\" />\r\n            接待量统计\r\n          </h2>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n            {receptionData.map((item, index) => (\r\n              <Card\r\n                key={item.title}\r\n                className=\"border-border bg-card hover:shadow-md transition-shadow duration-200\"\r\n                style={{ minHeight: '240px' }}\r\n              >\r\n                <div className=\"p-6 h-full flex flex-col\">\r\n                  {/* 顶部区域：图标和数据 */}\r\n                  <div className=\"flex items-start justify-between mb-6\">\r\n                    <div className=\"flex items-start space-x-3\">\r\n                      <div className=\"p-2 bg-primary/10 rounded-lg mt-1\">\r\n                        {item.icon}\r\n                      </div>\r\n                      <div>\r\n                        <div className=\"text-muted-foreground text-sm mb-1\">\r\n                          {item.title}\r\n                        </div>\r\n                        <div className=\"flex items-baseline\">\r\n                          <span className=\"text-2xl font-bold text-foreground\">\r\n                            {item.value}\r\n                          </span>\r\n                          <span className=\"text-muted-foreground text-sm ml-1\">\r\n                            {item.unit}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    {/* 图表切换按钮 */}\r\n                    <Button\r\n                      type=\"text\"\r\n                      size=\"small\"\r\n                      icon={getChartIcon(chartTypes[index])}\r\n                      onClick={() => toggleReceptionChart(index)}\r\n                      className=\"text-muted-foreground hover:text-foreground\"\r\n                    />\r\n                  </div>\r\n\r\n                  {/* 底部：图表 */}\r\n                  <div className=\"flex-1 flex flex-col\">\r\n                    <div className=\"text-xs text-muted-foreground mb-3\">\r\n                      {dateRangeDesc}\r\n                    </div>\r\n                    <div className=\"flex-1\">\r\n                      <ChartErrorBoundary>\r\n                        {renderReceptionChart(\r\n                          item.chartData,\r\n                          chartTypes[index],\r\n                        )}\r\n                      </ChartErrorBoundary>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* 解决率数据 */}\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold text-foreground mb-4 flex items-center\">\r\n            <BulbOutlined className=\"mr-2\" />\r\n            解决率统计\r\n          </h2>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n            {resolutionData.map((item, index) => (\r\n              <Card\r\n                key={item.title}\r\n                className=\"border-border bg-card hover:shadow-md transition-shadow duration-200\"\r\n              >\r\n                <div className=\"p-6\">\r\n                  <div className=\"flex items-center justify-between mb-4\">\r\n                    <div className=\"p-2 bg-primary/10 rounded-lg\">\r\n                      {item.icon}\r\n                    </div>\r\n                    {/* 图表切换按钮 */}\r\n                    <Button\r\n                      type=\"text\"\r\n                      size=\"small\"\r\n                      icon={getChartIcon(resolutionChartTypes[index])}\r\n                      onClick={() => toggleResolutionChart(index)}\r\n                      className=\"text-muted-foreground hover:text-foreground\"\r\n                    />\r\n                  </div>\r\n\r\n                  {/* 主要内容：左侧数据，右侧图表 */}\r\n                  <div className=\"flex items-center justify-between\">\r\n                    {/* 左侧数据 */}\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"text-muted-foreground text-sm mb-2\">\r\n                        {item.title}\r\n                      </div>\r\n                      <div className=\"flex items-baseline mb-3\">\r\n                        <span className=\"text-3xl font-bold text-foreground\">\r\n                          {item.value}\r\n                        </span>\r\n                        <span className=\"text-muted-foreground text-sm ml-1\">\r\n                          {item.unit}\r\n                        </span>\r\n                      </div>\r\n                      {resolutionChartTypes[index] === 'progress' && (\r\n                        <Progress\r\n                          percent={parseFloat(item.value)}\r\n                          strokeColor={item.color}\r\n                          showInfo={false}\r\n                          size=\"small\"\r\n                        />\r\n                      )}\r\n                      {resolutionChartTypes[index] === 'pie' && (\r\n                        <div className=\"text-xs text-muted-foreground mt-2\">\r\n                          比例分布\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* 右侧图表 */}\r\n                    {resolutionChartTypes[index] === 'pie' && (\r\n                      <div className=\"w-24 h-24 ml-4 flex items-center justify-center\">\r\n                        <ChartErrorBoundary\r\n                          fallback={\r\n                            <div className=\"w-full h-full bg-gray-100 rounded-full flex items-center justify-center text-xs text-gray-400\">\r\n                              图表\r\n                            </div>\r\n                          }\r\n                        >\r\n                          <Pie\r\n                            {...getPieConfig(item.chartData, item.color)}\r\n                            onReady={() =>\r\n                              console.log(\r\n                                `饼图 ${index} 渲染成功`,\r\n                                item.chartData,\r\n                              )\r\n                            }\r\n                            onError={(error: Error) =>\r\n                              console.error(`饼图 ${index} 渲染错误:`, error)\r\n                            }\r\n                          />\r\n                        </ChartErrorBoundary>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ShopHome;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Shop/ShopHome.tsx"}, "6": {"path": "/shop/shop-home", "isWrapper": true, "file": "@/components/ProtectedRoute.tsx", "parentId": "ant-design-pro-layout", "id": "6", "absPath": "/shop/shop-home", "__content": "import { message } from 'antd';\r\nimport Cookies from 'js-cookie';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Navigate, Outlet, useLocation } from 'umi';\r\n\r\nconst ProtectedRoute: React.FC = () => {\r\n  const location = useLocation();\r\n  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);\r\n  const isLoginPage = location.pathname === '/login';\r\n\r\n  useEffect(() => {\r\n    const token = Cookies.get('access_token');\r\n    setIsTokenValid(!!token);\r\n  }, [location.pathname]);\r\n\r\n  useEffect(() => {\r\n    if (isTokenValid === false && !isLoginPage) {\r\n      Cookies.remove('access_token');\r\n      localStorage.removeItem('kywy');\r\n      message.error('请先登录系统');\r\n    }\r\n  }, [isTokenValid, isLoginPage]);\r\n\r\n  if (isTokenValid === null) return null;\r\n\r\n  if (!isTokenValid && !isLoginPage) {\r\n    return <Navigate to=\"/login\" replace state={{ from: location }} />;\r\n  }\r\n\r\n  return <Outlet />;\r\n};\r\n\r\nexport default ProtectedRoute;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/components/ProtectedRoute.tsx"}, "7": {"name": "问答", "path": "", "parentId": "8", "id": "7", "absPath": "/shop/question", "originPath": "/shop/question"}, "8": {"path": "/shop/question", "isWrapper": true, "file": "@/components/ProtectedRoute.tsx", "parentId": "ant-design-pro-layout", "id": "8", "absPath": "/shop/question", "__content": "import { message } from 'antd';\r\nimport Cookies from 'js-cookie';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Navigate, Outlet, useLocation } from 'umi';\r\n\r\nconst ProtectedRoute: React.FC = () => {\r\n  const location = useLocation();\r\n  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);\r\n  const isLoginPage = location.pathname === '/login';\r\n\r\n  useEffect(() => {\r\n    const token = Cookies.get('access_token');\r\n    setIsTokenValid(!!token);\r\n  }, [location.pathname]);\r\n\r\n  useEffect(() => {\r\n    if (isTokenValid === false && !isLoginPage) {\r\n      Cookies.remove('access_token');\r\n      localStorage.removeItem('kywy');\r\n      message.error('请先登录系统');\r\n    }\r\n  }, [isTokenValid, isLoginPage]);\r\n\r\n  if (isTokenValid === null) return null;\r\n\r\n  if (!isTokenValid && !isLoginPage) {\r\n    return <Navigate to=\"/login\" replace state={{ from: location }} />;\r\n  }\r\n\r\n  return <Outlet />;\r\n};\r\n\r\nexport default ProtectedRoute;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/components/ProtectedRoute.tsx"}, "9": {"name": "问答知识库", "path": "/shop/question/knowledge", "file": "@/pages/Shop/Question/Knowledge.tsx", "parentId": "7", "id": "9", "absPath": "/shop/question/knowledge", "__content": "import CategorySelector from '@/components/Question/Q&A/CategoryCascader';\r\nimport type { KnowledgeCategory } from '@/models/questionAndAnswer';\r\nimport { getKnowledgeCategoryTree } from '@/services/knowledgeCategory';\r\nimport { getKnowledgeBaseList } from '@/services/questionAndAnswerKnowledgeBase';\r\nimport type {\r\n  ActionType,\r\n  ProColumns,\r\n  ProFormInstance,\r\n} from '@ant-design/pro-components';\r\nimport { PageContainer, ProTable } from '@ant-design/pro-components';\r\nimport React, { useEffect, useRef, useState } from 'react';\r\n\r\nconst Knowledge: React.FC = () => {\r\n  const [categoryTree, setCategoryTree] = useState<KnowledgeCategory[]>([]);\r\n  const [activeTab, setActiveTab] = useState('list');\r\n\r\n  const actionRef = useRef<ActionType>();\r\n  const formRef = useRef<ProFormInstance>();\r\n\r\n  useEffect(() => {\r\n    getKnowledgeCategoryTree().then((res) => {\r\n      if (res.code === 200 && Array.isArray(res.data)) {\r\n        setCategoryTree(res.data);\r\n      }\r\n    });\r\n  }, []);\r\n\r\n  const columns: ProColumns<any>[] = [\r\n    {\r\n      title: '问题分类',\r\n      dataIndex: 'categoryId',\r\n      hideInTable: true,\r\n      colSize: 24,\r\n      order: 0,\r\n      renderFormItem: (_, __, form) => (\r\n        <div style={{ width: '100%', paddingTop: 5 }}>\r\n          <CategorySelector\r\n            categoryTree={categoryTree}\r\n            onChange={(id, code) => {\r\n              form.setFieldsValue({ categoryId: code });\r\n              setTimeout(() => {\r\n                formRef.current?.submit(); // 自动查询\r\n              });\r\n            }}\r\n          />\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '问题类型',\r\n      dataIndex: 'questionType',\r\n      valueType: 'text',\r\n      search: true,\r\n    },\r\n    {\r\n      title: '7日咨询量',\r\n      dataIndex: 'weeklyConsultationVolume',\r\n      valueType: 'text',\r\n      search: false,\r\n    },\r\n    {\r\n      title: '关联商品数量',\r\n      dataIndex: 'numberOfRelatedProducts',\r\n      valueType: 'text',\r\n      search: false,\r\n    },\r\n    {\r\n      title: '答案数量',\r\n      dataIndex: 'numberOfAnswers',\r\n      valueType: 'text',\r\n      search: false,\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <PageContainer\r\n      header={{\r\n        title: '问答知识库',\r\n      }}\r\n      tabList={[\r\n        {\r\n          tab: '行业场景',\r\n          key: 'list',\r\n        },\r\n        {\r\n          tab: '自定义问题',\r\n          key: 'custom',\r\n        },\r\n      ]}\r\n      breadcrumbRender={false}\r\n      tabActiveKey={activeTab}\r\n      onTabChange={setActiveTab}\r\n    >\r\n      {activeTab === 'list' && (\r\n        <ProTable\r\n          actionRef={actionRef}\r\n          formRef={formRef}\r\n          rowKey=\"id\"\r\n          columns={columns}\r\n          request={async (params) => {\r\n            const res = await getKnowledgeBaseList(params);\r\n\r\n            const mockData = [\r\n              {\r\n                id: 'mock-id-1',\r\n                categoryId: 'mock-cat-001',\r\n                questionType: '发货时效',\r\n                weeklyConsultationVolume: '120',\r\n                numberOfRelatedProducts: '12',\r\n                numberOfAnswers: '2',\r\n              },\r\n              {\r\n                id: 'mock-id-2',\r\n                categoryId: 'mock-cat-002',\r\n                questionType: '售后服务',\r\n                weeklyConsultationVolume: '75',\r\n                numberOfRelatedProducts: '5',\r\n                numberOfAnswers: '1',\r\n              },\r\n              {\r\n                id: 'mock-id-3',\r\n                categoryId: 'mock-cat-003',\r\n                questionType: '支付问题',\r\n                weeklyConsultationVolume: '98',\r\n                numberOfRelatedProducts: '7',\r\n                numberOfAnswers: '3',\r\n              },\r\n            ];\r\n\r\n            const realData = Array.isArray(res.data?.data) ? res.data.data : [];\r\n\r\n            return {\r\n              data: [...mockData, ...realData],\r\n              total: (res.data?.total || 0) + mockData.length,\r\n              success: true,\r\n            };\r\n          }}\r\n          pagination={{ pageSize: 10 }}\r\n          search={{\r\n            labelWidth: 100,\r\n            defaultCollapsed: false,\r\n          }}\r\n          cardBordered\r\n        />\r\n      )}\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default Knowledge;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Shop/Question/Knowledge.tsx"}, "10": {"name": "自动学习", "path": "/shop/question/auto-learning", "file": "@/pages/Shop/Question/AutoLearning.tsx", "parentId": "7", "id": "10", "absPath": "/shop/question/auto-learning", "__content": "import { QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';\r\nimport { <PERSON><PERSON>, Card, Input, Progress, Table, Tabs, Tooltip } from 'antd';\r\nimport type { ColumnsType } from 'antd/es/table';\r\nimport { useState } from 'react';\r\nimport AIReview from '../../../components/Question/AIReview';\r\nimport VagueQuestions from '../../../components/Question/VagueQuestions';\r\n\r\nconst { TabPane } = Tabs;\r\n\r\n// 模拟数据\r\ninterface LearningItem {\r\n  key: string;\r\n  id: string;\r\n  title: string;\r\n  currentScore: number;\r\n  totalScore: number;\r\n  objectives: string[];\r\n  image: string;\r\n}\r\n\r\n// 已通过项目数据接口\r\ninterface ApprovedItem {\r\n  key: string;\r\n  id: string;\r\n  title: string;\r\n  image: string;\r\n  industryTag: string;\r\n  questionFormat: string;\r\n  reviewer: string;\r\n  reviewTime: string;\r\n}\r\n\r\n// 不再学习问题数据接口\r\ninterface RejectedItem {\r\n  key: string;\r\n  question: string;\r\n}\r\n\r\nconst learningItems: LearningItem[] = [\r\n  {\r\n    key: '1',\r\n    id: '100765058017',\r\n    title:\r\n      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',\r\n    currentScore: 0,\r\n    totalScore: 7,\r\n    objectives: ['1. 掌握产品介绍', '2. 明确提供内容了'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '2',\r\n    id: '101380561647',\r\n    title:\r\n      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护手霜+洗面奶【套装】',\r\n    currentScore: 0,\r\n    totalScore: 7,\r\n    objectives: ['1. 什么是科技'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '3',\r\n    id: '100765058063',\r\n    title:\r\n      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护手霜+洗面奶【套装】',\r\n    currentScore: 0,\r\n    totalScore: 7,\r\n    objectives: ['1. 什么是科技'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '4',\r\n    id: '101908930533',\r\n    title:\r\n      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',\r\n    currentScore: 0,\r\n    totalScore: 2,\r\n    objectives: ['1. 掌握产品介绍'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '5',\r\n    id: '101561590324',\r\n    title:\r\n      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',\r\n    currentScore: 0,\r\n    totalScore: 4,\r\n    objectives: ['1. 什么是科技'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '6',\r\n    id: '101398600730',\r\n    title:\r\n      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',\r\n    currentScore: 1,\r\n    totalScore: 4,\r\n    objectives: ['1. 掌握产品介绍', '2. 掌握产品卖点'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '7',\r\n    id: '101320603165',\r\n    title:\r\n      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',\r\n    currentScore: 0,\r\n    totalScore: 2,\r\n    objectives: ['1. 掌握产品介绍'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '8',\r\n    id: '101398601529',\r\n    title:\r\n      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',\r\n    currentScore: 0,\r\n    totalScore: 2,\r\n    objectives: ['1. 掌握产品介绍'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '9',\r\n    id: '101320603154',\r\n    title:\r\n      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',\r\n    currentScore: 0,\r\n    totalScore: 2,\r\n    objectives: ['1. 掌握产品介绍'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '10',\r\n    id: '101528100513',\r\n    title:\r\n      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',\r\n    currentScore: 0,\r\n    totalScore: 2,\r\n    objectives: ['1. 掌握产品介绍'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '11',\r\n    id: '101528100514',\r\n    title:\r\n      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',\r\n    currentScore: 0,\r\n    totalScore: 2,\r\n    objectives: ['1. 掌握产品介绍'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n  {\r\n    key: '12',\r\n    id: '101528100515',\r\n    title:\r\n      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',\r\n    currentScore: 0,\r\n    totalScore: 2,\r\n    objectives: ['1. 掌握产品介绍'],\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n  },\r\n];\r\n\r\n// 已通过项目模拟数据\r\nconst approvedItems: ApprovedItem[] = [\r\n  {\r\n    key: '1',\r\n    id: '101320622154',\r\n    title:\r\n      'QINKUNG【豪华版货源】蓝白女装道服AIR白芯9小岁事约与朝底苍之当吧编手对练【男款】黑色L',\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n    industryTag: '面料面料整理',\r\n    questionFormat: '1. 155mm我买下了',\r\n    reviewer: 'QINKUNG官方旗舰店 QINKUNG',\r\n    reviewTime: '2024-07-24 15:27:07',\r\n  },\r\n  {\r\n    key: '2',\r\n    id: '101320622154',\r\n    title:\r\n      'QINKUNG【豪华版货源】蓝白女装道服AIR白芯9小岁事约与朝底苍之当吧编手对练【男款】黑色L',\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n    industryTag: '适合有内衬',\r\n    questionFormat: '1. 适合有内衬',\r\n    reviewer: 'QINKUNG官方旗舰店 QINKUNG',\r\n    reviewTime: '2024-07-24 15:26:51',\r\n  },\r\n  {\r\n    key: '3',\r\n    id: '101070565697',\r\n    title:\r\n      'QINKUNG经典为手不页编理与总经QINKLING经典外设面编过最近手了子约的灰色了, 明装来这去',\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n    industryTag: '黄口罩质',\r\n    questionFormat: '1. 有寸生化罩质',\r\n    reviewer: 'QINKUNG官方旗舰店 QINKUNG',\r\n    reviewTime: '2024-07-24 15:26:23',\r\n  },\r\n  {\r\n    key: '4',\r\n    id: '100956830462',\r\n    title:\r\n      'QINKUNG【豪华版货源】蓝白女装道服AIR白芯9小岁事约与朝底苍之当吧编手对练【男款】蓝架M',\r\n    image:\r\n      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',\r\n    industryTag: '黄口罩质',\r\n    questionFormat: '1. 还有面料是哪的了',\r\n    reviewer: 'QINKUNG官方旗舰店 QINKUNG',\r\n    reviewTime: '2024-05-22 15:42:26',\r\n  },\r\n];\r\n\r\n// 不再学习问题模拟数据\r\nconst rejectedItems: RejectedItem[] = [\r\n  {\r\n    key: '1',\r\n    question: '有什么区别，哪款比较好',\r\n  },\r\n  {\r\n    key: '2',\r\n    question: '请求推荐商品',\r\n  },\r\n];\r\n\r\nconst AutoLearning: React.FC = () => {\r\n  const [activeMainTab, setActiveMainTab] = useState('auto-learning');\r\n  const [activeContentTab, setActiveContentTab] = useState('content-search');\r\n  const [searchValue, setSearchValue] = useState('');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [pageSize, setPageSize] = useState(10);\r\n\r\n  // 待审核表格列定义\r\n  const pendingColumns: ColumnsType<LearningItem> = [\r\n    {\r\n      title: '商品名称',\r\n      dataIndex: 'title',\r\n      key: 'title',\r\n      width: '45%',\r\n      render: (text: string, record: LearningItem) => (\r\n        <div className=\"flex items-start gap-3\" data-oid=\"w3qcyx3\">\r\n          <div\r\n            className=\"w-10 h-10 bg-gray-200 rounded flex-shrink-0 overflow-hidden\"\r\n            data-oid=\"dyfagpc\"\r\n          >\r\n            <img\r\n              src={record.image}\r\n              alt=\"商品图片\"\r\n              className=\"w-full h-full object-cover\"\r\n              onError={(e) => {\r\n                const target = e.target as HTMLImageElement;\r\n                target.src =\r\n                  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMiAxMkgyOFYyOEgxMlYxMloiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+';\r\n              }}\r\n              data-oid=\"rshxvg0\"\r\n            />\r\n          </div>\r\n          <div className=\"flex-1\" data-oid=\"6ggdm:g\">\r\n            <div\r\n              className=\"text-sm text-gray-800 leading-5 mb-1\"\r\n              style={{\r\n                display: '-webkit-box',\r\n                WebkitLineClamp: 2,\r\n                WebkitBoxOrient: 'vertical',\r\n                overflow: 'hidden',\r\n              }}\r\n              data-oid=\"p43hv79\"\r\n            >\r\n              {text}\r\n            </div>\r\n            <div className=\"text-xs text-gray-500\" data-oid=\"73lsfoh\">\r\n              商品ID: {record.id}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '有效应答 / 咨询总次数',\r\n      dataIndex: 'score',\r\n      key: 'score',\r\n      width: '20%',\r\n      align: 'center',\r\n      render: (_, record: LearningItem) => (\r\n        <div className=\"text-center\" data-oid=\"7_85.5b\">\r\n          <div className=\"text-sm text-gray-800 mb-2\" data-oid=\"kobaka0\">\r\n            {record.currentScore}/{record.totalScore}\r\n          </div>\r\n          <Progress\r\n            percent={\r\n              record.totalScore > 0\r\n                ? Math.round((record.currentScore / record.totalScore) * 100)\r\n                : 0\r\n            }\r\n            size=\"small\"\r\n            strokeColor=\"#1890ff\"\r\n            className=\"w-16\"\r\n            data-oid=\"je79k0n\"\r\n          />\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '已学习问题',\r\n      dataIndex: 'objectives',\r\n      key: 'objectives',\r\n      width: '25%',\r\n      render: (objectives: string[]) => (\r\n        <div className=\"space-y-1\" data-oid=\"d8evvg2\">\r\n          {objectives.map((objective, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"text-sm text-gray-700\"\r\n              data-oid=\"v.er6ne\"\r\n            >\r\n              {objective}\r\n            </div>\r\n          ))}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      width: '10%',\r\n      align: 'center',\r\n      fixed: 'right',\r\n      render: () => (\r\n        <Button\r\n          type=\"link\"\r\n          className=\"text-blue-500 p-0 text-sm\"\r\n          data-oid=\"_00zzg_\"\r\n        >\r\n          立即审核\r\n        </Button>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 已通过表格列定义\r\n  const approvedColumns: ColumnsType<ApprovedItem> = [\r\n    {\r\n      title: '商品名称/商品分类',\r\n      dataIndex: 'title',\r\n      key: 'title',\r\n      width: '35%',\r\n      render: (text: string, record: ApprovedItem) => (\r\n        <div className=\"flex items-start gap-3\" data-oid=\"4i7w3_3\">\r\n          <div\r\n            className=\"w-10 h-10 bg-gray-200 rounded flex-shrink-0 overflow-hidden\"\r\n            data-oid=\"_q9a8jz\"\r\n          >\r\n            <img\r\n              src={record.image}\r\n              alt=\"商品图片\"\r\n              className=\"w-full h-full object-cover\"\r\n              onError={(e) => {\r\n                const target = e.target as HTMLImageElement;\r\n                target.src =\r\n                  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMiAxMkgyOFYyOEgxMlYxMloiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+';\r\n              }}\r\n              data-oid=\"ddk_kzr\"\r\n            />\r\n          </div>\r\n          <div className=\"flex-1\" data-oid=\"szsr6vk\">\r\n            <div\r\n              className=\"text-sm text-gray-800 leading-5 mb-1\"\r\n              style={{\r\n                display: '-webkit-box',\r\n                WebkitLineClamp: 2,\r\n                WebkitBoxOrient: 'vertical',\r\n                overflow: 'hidden',\r\n              }}\r\n              data-oid=\"gjyxz3:\"\r\n            >\r\n              {text}\r\n            </div>\r\n            <div className=\"text-xs text-gray-500\" data-oid=\"mjx_xv.\">\r\n              商品ID: {record.id}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '行业标签',\r\n      dataIndex: 'industryTag',\r\n      key: 'industryTag',\r\n      width: '15%',\r\n      render: (text: string) => (\r\n        <span className=\"text-sm text-gray-600\" data-oid=\"wpsqo2r\">\r\n          {text}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '问法',\r\n      dataIndex: 'questionFormat',\r\n      key: 'questionFormat',\r\n      width: '20%',\r\n      render: (text: string) => (\r\n        <span className=\"text-sm text-gray-600\" data-oid=\"_.ub3hp\">\r\n          {text}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '审核负责人',\r\n      dataIndex: 'reviewer',\r\n      key: 'reviewer',\r\n      width: '15%',\r\n      render: (text: string) => (\r\n        <span className=\"text-sm text-gray-600\" data-oid=\"l75bsig\">\r\n          {text}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '审核时间',\r\n      dataIndex: 'reviewTime',\r\n      key: 'reviewTime',\r\n      width: '10%',\r\n      render: (text: string) => (\r\n        <span className=\"text-sm text-gray-600\" data-oid=\"4ni1ei9\">\r\n          {text}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      width: '5%',\r\n      align: 'center',\r\n      fixed: 'right',\r\n      render: () => (\r\n        <div className=\"flex gap-2\" data-oid=\"6kmmyki\">\r\n          <Button\r\n            type=\"link\"\r\n            className=\"text-blue-500 p-0 text-sm\"\r\n            data-oid=\"mpi5rd0\"\r\n          >\r\n            会话详情\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            className=\"text-blue-500 p-0 text-sm\"\r\n            data-oid=\"mch383:\"\r\n          >\r\n            查看答案\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 不再学习问题表格列定义\r\n  const rejectedColumns: ColumnsType<RejectedItem> = [\r\n    {\r\n      title: '问题',\r\n      dataIndex: 'question',\r\n      key: 'question',\r\n      width: '90%',\r\n      render: (text: string) => (\r\n        <span className=\"text-sm text-gray-800\" data-oid=\"17ls55.\">\r\n          {text}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      width: '10%',\r\n      align: 'center',\r\n      fixed: 'right',\r\n      render: () => (\r\n        <Button\r\n          type=\"link\"\r\n          className=\"text-blue-500 p-0 text-sm\"\r\n          data-oid=\"vqam976\"\r\n        >\r\n          重新关注\r\n        </Button>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 根据当前内容标签页获取对应的表格配置\r\n  const getTableConfig = () => {\r\n    switch (activeContentTab) {\r\n      case 'content-search':\r\n        return {\r\n          columns: pendingColumns,\r\n          dataSource: learningItems,\r\n          placeholder: '输入关键词搜索问题',\r\n        };\r\n      case 'approved':\r\n        return {\r\n          columns: approvedColumns,\r\n          dataSource: approvedItems,\r\n          placeholder: '输入关键词搜索问题',\r\n        };\r\n      case 'rejected':\r\n        return {\r\n          columns: rejectedColumns,\r\n          dataSource: rejectedItems,\r\n          placeholder: '输入人员问题',\r\n        };\r\n      default:\r\n        return {\r\n          columns: pendingColumns,\r\n          dataSource: learningItems,\r\n          placeholder: '输入关键词搜索问题',\r\n        };\r\n    }\r\n  };\r\n\r\n  // 渲染自动学习页面内容\r\n  const renderAutoLearningContent = () => {\r\n    const tableConfig = getTableConfig();\r\n\r\n    return (\r\n      <div className=\" md:px-6 pb-6\" data-oid=\"31l3rui\">\r\n        <Card data-oid=\"go6gkix\">\r\n          {/* 内容标签页和搜索 */}\r\n          <div\r\n            className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-4\"\r\n            data-oid=\"t6lc9tf\"\r\n          >\r\n            <Tabs\r\n              activeKey={activeContentTab}\r\n              onChange={setActiveContentTab}\r\n              className=\"[&_.ant-tabs-tab]:px-4 [&_.ant-tabs-tab]:py-2 [&_.ant-tabs-tab]:text-sm [&_.ant-tabs-tab-active]:text-blue-500 [&_.ant-tabs-ink-bar]:bg-blue-500 [&_.ant-tabs-ink-bar]:h-0.5\"\r\n              data-oid=\"ys3mfg6\"\r\n            >\r\n              <TabPane tab=\"待审核\" key=\"content-search\" data-oid=\"k748i.f\" />\r\n              <TabPane tab=\"已通过\" key=\"approved\" data-oid=\"lgu1yj:\" />\r\n              <TabPane tab=\"不再学习问题\" key=\"rejected\" data-oid=\"7fl7u-m\" />\r\n            </Tabs>\r\n\r\n            <div className=\"flex items-center gap-3\" data-oid=\"e-lr-1o\">\r\n              <Input\r\n                placeholder={tableConfig.placeholder}\r\n                value={searchValue}\r\n                onChange={(e) => setSearchValue(e.target.value)}\r\n                prefix={\r\n                  <SearchOutlined\r\n                    className=\"text-gray-400\"\r\n                    data-oid=\"4m6t-0_\"\r\n                  />\r\n                }\r\n                className=\"w-full md:w-64\"\r\n                allowClear\r\n                data-oid=\"i512ufv\"\r\n              />\r\n\r\n              {activeContentTab === 'content-search' && (\r\n                <Button\r\n                  type=\"primary\"\r\n                  className=\"bg-blue-500 whitespace-nowrap\"\r\n                  data-oid=\":9b81x_\"\r\n                >\r\n                  立即审核\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 学习项目表格 */}\r\n          <div\r\n            className=\"overflow-x-auto overflow-y-auto w-full px-2 flex-grow\"\r\n            data-oid=\"gh_a:qt\"\r\n          >\r\n            <Table\r\n              columns={tableConfig.columns as any}\r\n              dataSource={tableConfig.dataSource as any}\r\n              pagination={{\r\n                current: currentPage,\r\n                pageSize: pageSize,\r\n                total: tableConfig.dataSource.length,\r\n                showSizeChanger: true,\r\n                showQuickJumper: true,\r\n                showTotal: (total, range) =>\r\n                  `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,\r\n                onChange: (page, size) => {\r\n                  setCurrentPage(page);\r\n                  setPageSize(size || 10);\r\n                },\r\n                onShowSizeChange: (current, size) => {\r\n                  setCurrentPage(1);\r\n                  setPageSize(size);\r\n                },\r\n                pageSizeOptions: ['10', '20', '50', '100'],\r\n                responsive: true,\r\n              }}\r\n              className=\"[&_.ant-table-thead>tr>th]:bg-gray-50 [&_.ant-table-thead>tr>th]:font-medium [&_.ant-table-thead>tr>th]:text-gray-700 [&_.ant-table-tbody>tr:hover>td]:bg-blue-50\"\r\n              size=\"middle\"\r\n              bordered={false}\r\n              scroll={{ x: 'max-content', y: 'calc(100vh - 480px)' }}\r\n              data-oid=\"tsxltj-\"\r\n            />\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 渲染AI推荐答案审核页面内容\r\n  const renderAIReviewContent = () => <AIReview data-oid=\"-ko:_ut\" />;\r\n\r\n  // 渲染问法模糊的问题页面内容\r\n  const renderVagueQuestionsContent = () => (\r\n    <VagueQuestions data-oid=\"xgyh0l1\" />\r\n  );\r\n\r\n  // 根据选中的主标签页渲染不同内容\r\n  const renderContentByTab = () => {\r\n    switch (activeMainTab) {\r\n      case 'auto-learning':\r\n        return renderAutoLearningContent();\r\n      case 'ai-review':\r\n        return renderAIReviewContent();\r\n      case 'model-training':\r\n        return renderVagueQuestionsContent();\r\n      default:\r\n        return renderAutoLearningContent();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-[#f5f5f5] w-full\" data-oid=\"w1je2wa\">\r\n      <div className=\"w-full h-full\" data-oid=\":njwlbt\">\r\n        {/* 主要标签页 */}\r\n        <div className=\"mb-6 px-4 md:px-6 pt-4\" data-oid=\"bxf8:_0\">\r\n          <Tabs\r\n            activeKey={activeMainTab}\r\n            onChange={setActiveMainTab}\r\n            className=\"[&_.ant-tabs-tab]:px-6 [&_.ant-tabs-tab]:py-3 [&_.ant-tabs-tab]:text-base [&_.ant-tabs-tab]:font-medium [&_.ant-tabs-tab-active]:text-blue-500 [&_.ant-tabs-ink-bar]:bg-blue-500 [&_.ant-tabs-ink-bar]:h-0.5\"\r\n            data-oid=\"1-5azgi\"\r\n          >\r\n            <TabPane tab=\"自动学习\" key=\"auto-learning\" data-oid=\"22clfnl\" />\r\n            <TabPane\r\n              tab={\r\n                <span data-oid=\"lhevczn\">\r\n                  AI推荐答案审核\r\n                  <span\r\n                    className=\"ml-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full\"\r\n                    data-oid=\"rtw.4jt\"\r\n                  >\r\n                    HOT\r\n                  </span>\r\n                </span>\r\n              }\r\n              key=\"ai-review\"\r\n              data-oid=\".3rakgo\"\r\n            />\r\n\r\n            <TabPane\r\n              tab=\"问法模糊的问题\"\r\n              key=\"model-training\"\r\n              data-oid=\"46z0h1h\"\r\n            />\r\n          </Tabs>\r\n        </div>\r\n\r\n        {/* 统计卡片区域 - 只在自动学习页面显示 */}\r\n        {activeMainTab === 'auto-learning' && (\r\n          <div\r\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6 px-4 md:px-6\"\r\n            data-oid=\"7s2pnoo\"\r\n          >\r\n            {/* 机器人自动学习卡片 */}\r\n            <Card className=\"col-span-1\" data-oid=\".k58025\">\r\n              <div className=\"flex items-center\" data-oid=\"9_edg93\">\r\n                <div\r\n                  className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-4\"\r\n                  data-oid=\"gd5nlum\"\r\n                >\r\n                  <span\r\n                    className=\"text-white text-xl font-bold\"\r\n                    data-oid=\"mindhog\"\r\n                  >\r\n                    AI\r\n                  </span>\r\n                </div>\r\n                <div data-oid=\"l.4kxei\">\r\n                  <div\r\n                    className=\"text-lg font-semibold text-gray-800 mb-1\"\r\n                    data-oid=\"hh3bpe5\"\r\n                  >\r\n                    机器人自动学习\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600\" data-oid=\"k3:qzxe\">\r\n                    机器人自动学习客服应答，并通过分析\r\n                    咨询频次，为您推荐最值得审核的内容\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </Card>\r\n\r\n            {/* 累计自动学习卡片 */}\r\n            <Card className=\"col-span-1\" data-oid=\"a4u:_-g\">\r\n              <div className=\"text-center\" data-oid=\"_uu72so\">\r\n                <div\r\n                  className=\"text-3xl font-bold text-gray-800 mb-2\"\r\n                  data-oid=\"62iplvv\"\r\n                >\r\n                  4.00\r\n                </div>\r\n                <div\r\n                  className=\"text-sm text-gray-600 flex items-center justify-center gap-1\"\r\n                  data-oid=\"s4lsz0-\"\r\n                >\r\n                  累计添加答案\r\n                  <Tooltip title=\"累计添加答案\" data-oid=\"p1cqlj8\">\r\n                    <QuestionCircleOutlined\r\n                      className=\"text-gray-400 text-xs cursor-help\"\r\n                      data-oid=\":xkp8xt\"\r\n                    />\r\n                  </Tooltip>\r\n                </div>\r\n              </div>\r\n            </Card>\r\n\r\n            {/* 累计平均学习时长卡片 */}\r\n            <Card className=\"col-span-1\" data-oid=\"p144w5j\">\r\n              <div className=\"text-center\" data-oid=\"_0e7spm\">\r\n                <div\r\n                  className=\"text-3xl font-bold text-gray-800 mb-2\"\r\n                  data-oid=\"w97_96-\"\r\n                >\r\n                  2.0\r\n                </div>\r\n                <div\r\n                  className=\"text-sm text-gray-600 flex items-center justify-center gap-1\"\r\n                  data-oid=\"b67itaa\"\r\n                >\r\n                  累计节约配置时间（分钟）\r\n                  <Tooltip title=\"按照每条答案大约30秒\" data-oid=\"h-tljy0\">\r\n                    <QuestionCircleOutlined\r\n                      className=\"text-gray-400 text-xs cursor-help\"\r\n                      data-oid=\"ho7cjg1\"\r\n                    />\r\n                  </Tooltip>\r\n                </div>\r\n              </div>\r\n            </Card>\r\n          </div>\r\n        )}\r\n\r\n        {/* 内容区域 - 根据选中的主标签页显示不同内容 */}\r\n        {renderContentByTab()}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AutoLearning;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Shop/Question/AutoLearning.tsx"}, "11": {"name": "精准意图", "path": "/shop/question/intent", "file": "@/pages/Shop/Question/Intent.tsx", "parentId": "7", "id": "11", "absPath": "/shop/question/intent", "__content": "import React from 'react';\r\nconst Intent: React.FC = () => <div data-oid=\"2qurqpa\">精准意图</div>;\r\nexport default Intent;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Shop/Question/Intent.tsx"}, "12": {"name": "活动管理", "path": "/shop/question/activity", "file": "@/pages/Shop/Question/Activity.tsx", "parentId": "7", "id": "12", "absPath": "/shop/question/activity", "__content": "import React from 'react';\r\nconst Activity: React.FC = () => <div data-oid=\"4wzia1t\">活动管理</div>;\r\nexport default Activity;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Shop/Question/Activity.tsx"}, "13": {"name": "商品知识库", "path": "", "parentId": "14", "id": "13", "absPath": "/shop/product", "originPath": "/shop/product"}, "14": {"path": "/shop/product", "isWrapper": true, "file": "@/components/ProtectedRoute.tsx", "parentId": "ant-design-pro-layout", "id": "14", "absPath": "/shop/product", "__content": "import { message } from 'antd';\r\nimport Cookies from 'js-cookie';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Navigate, Outlet, useLocation } from 'umi';\r\n\r\nconst ProtectedRoute: React.FC = () => {\r\n  const location = useLocation();\r\n  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);\r\n  const isLoginPage = location.pathname === '/login';\r\n\r\n  useEffect(() => {\r\n    const token = Cookies.get('access_token');\r\n    setIsTokenValid(!!token);\r\n  }, [location.pathname]);\r\n\r\n  useEffect(() => {\r\n    if (isTokenValid === false && !isLoginPage) {\r\n      Cookies.remove('access_token');\r\n      localStorage.removeItem('kywy');\r\n      message.error('请先登录系统');\r\n    }\r\n  }, [isTokenValid, isLoginPage]);\r\n\r\n  if (isTokenValid === null) return null;\r\n\r\n  if (!isTokenValid && !isLoginPage) {\r\n    return <Navigate to=\"/login\" replace state={{ from: location }} />;\r\n  }\r\n\r\n  return <Outlet />;\r\n};\r\n\r\nexport default ProtectedRoute;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/components/ProtectedRoute.tsx"}, "15": {"name": "商品列表", "path": "/shop/product/list-simple", "file": "@/pages/Shop/Product/ListSimple.tsx", "parentId": "13", "id": "15", "absPath": "/shop/product/list-simple", "__content": "import ExportProductModal from '@/components/Product/ExportProductModal';\r\nimport TaskCenter from '@/components/TaskCenter';\r\nimport type {\r\n  TempProduct,\r\n  TempProductInput,\r\n  TempProductQueryParams,\r\n} from '@/models/tempProduct';\r\nimport { getProductStatusLabel } from '@/models/tempProduct';\r\nimport {\r\n  batchImportTempProducts,\r\n  deleteTempProduct,\r\n  downloadImportTemplate,\r\n  getTempProductByProductId,\r\n  getTempProductList,\r\n  syncTempProductsToRagflow,\r\n  upsertTempProduct,\r\n} from '@/services/tempProduct';\r\nimport {\r\n  ControlOutlined,\r\n  DeleteOutlined,\r\n  DownloadOutlined,\r\n  EditOutlined,\r\n  EyeOutlined,\r\n  InboxOutlined,\r\n  PlusOutlined,\r\n  ReloadOutlined,\r\n  SearchOutlined,\r\n  UploadOutlined,\r\n} from '@ant-design/icons';\r\nimport { ProColumns, ProTable } from '@ant-design/pro-components';\r\nimport {\r\n  Alert,\r\n  Button,\r\n  Card,\r\n  Col,\r\n  Drawer,\r\n  Form,\r\n  Image,\r\n  Input,\r\n  Modal,\r\n  Popconfirm,\r\n  Row,\r\n  Select,\r\n  Space,\r\n  Tag,\r\n  Tooltip,\r\n  Typography,\r\n  Upload,\r\n  message,\r\n} from 'antd';\r\nimport type { TablePaginationConfig } from 'antd/es/table';\r\nimport dayjs from 'dayjs';\r\nimport React, { useCallback, useEffect, useState } from 'react';\r\n\r\nconst { Search } = Input;\r\nconst { Option } = Select;\r\nconst { Dragger } = Upload;\r\n\r\nconst FALLBACK_IMAGE_SRC =\r\n  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';\r\n\r\nconst ListSimple: React.FC = () => {\r\n  const [loading, setLoading] = useState(false);\r\n  const [dataSource, setDataSource] = useState<TempProduct[]>([]);\r\n  const [pagination, setPagination] = useState<TablePaginationConfig>({\r\n    current: 1,\r\n    pageSize: 10,\r\n    total: 0,\r\n    showSizeChanger: true,\r\n    showQuickJumper: true,\r\n    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\r\n  });\r\n  const [searchParams, setSearchParams] = useState<TempProductQueryParams>({});\r\n\r\n  // 批量导入相关状态\r\n  const [importModalVisible, setImportModalVisible] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\r\n\r\n  // 任务中心相关状态\r\n  const [taskCenterVisible, setTaskCenterVisible] = useState(false);\r\n  const [taskCenterRefreshTrigger, setTaskCenterRefreshTrigger] = useState(0);\r\n  //同步商品知识库\r\n  const handleSyncToKnowledgeBase = async () => {\r\n    const hide = message.loading('正在同步商品数据到知识库...', 0);\r\n    try {\r\n      const res = await syncTempProductsToRagflow();\r\n      hide();\r\n      if (res.code === 0) {\r\n        message.success('商品数据同步成功');\r\n      } else {\r\n        message.error(res.msg || '商品数据同步失败');\r\n      }\r\n    } catch (error) {\r\n      message.error('商品数据同步失败，请稍后重试');\r\n      hide();\r\n    }\r\n  };\r\n  // 导出弹窗状态\r\n  const [exportModalVisible, setExportModalVisible] = useState(false);\r\n  const [modalVisible, setModalVisible] = useState<boolean>(false);\r\n  const [currentProduct, setCurrentProduct] = useState<TempProduct | null>(\r\n    null,\r\n  );\r\n  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);\r\n  const [currentDetailProduct, setCurrentDetailProduct] =\r\n    useState<TempProduct | null>(null);\r\n\r\n  // 加载数据\r\n  const loadData = async (params?: TempProductQueryParams) => {\r\n    setLoading(true);\r\n    try {\r\n      const queryParams = {\r\n        ...searchParams,\r\n        ...params,\r\n        current: params?.current || pagination.current,\r\n        pageSize: params?.pageSize || pagination.pageSize,\r\n      };\r\n\r\n      const response = await getTempProductList(queryParams);\r\n\r\n      if (response.code === 200) {\r\n        setDataSource(response.data.items);\r\n        setPagination((prev) => ({\r\n          ...prev,\r\n          current: queryParams.current,\r\n          pageSize: queryParams.pageSize,\r\n          total: response.data.total,\r\n        }));\r\n      } else {\r\n        message.error(response.msg || '获取数据失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取商品列表失败:', error);\r\n      message.error('获取数据失败，请稍后重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 初始化加载数据\r\n  useEffect(() => {\r\n    loadData();\r\n  }, []);\r\n\r\n  // 搜索处理\r\n  const handleSearch = (value: string) => {\r\n    const newParams = { ...searchParams, name: value, current: 1 };\r\n    setSearchParams(newParams);\r\n    loadData(newParams);\r\n  };\r\n\r\n  // 状态筛选处理\r\n  const handleStatusFilter = (value: string) => {\r\n    const newParams = {\r\n      ...searchParams,\r\n      status: value || undefined,\r\n      current: 1,\r\n    };\r\n    setSearchParams(newParams);\r\n    loadData(newParams);\r\n  };\r\n\r\n  // 重置搜索\r\n  const handleReset = () => {\r\n    setSearchParams({});\r\n    loadData({ current: 1, pageSize: pagination.pageSize });\r\n  };\r\n\r\n  // 刷新数据\r\n  const handleRefresh = () => {\r\n    loadData({ ...searchParams });\r\n  };\r\n\r\n  // 下载模版文件\r\n  const handleDownloadTemplate = async (format: 'excel' | 'csv' = 'excel') => {\r\n    try {\r\n      const { blob, filename } = await downloadImportTemplate(format);\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = filename;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n      message.success(`${format.toUpperCase()}模版文件下载成功`);\r\n    } catch (error) {\r\n      console.error('下载模版失败:', error);\r\n      message.error('下载模版失败，请稍后重试');\r\n    }\r\n  };\r\n\r\n  // 打开批量导入弹窗\r\n  const handleOpenImportModal = () => {\r\n    setImportModalVisible(true);\r\n    setSelectedFile(null);\r\n  };\r\n\r\n  // 关闭批量导入弹窗\r\n  const handleCloseImportModal = () => {\r\n    setImportModalVisible(false);\r\n    setSelectedFile(null);\r\n  };\r\n\r\n  // 处理文件选择（不立即上传）\r\n  const handleFileSelect = (file: File) => {\r\n    // 验证文件类型\r\n    const isValidFile =\r\n      file.type ===\r\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n      file.type === 'application/vnd.ms-excel' ||\r\n      file.type === 'text/csv' ||\r\n      file.type === 'application/csv' ||\r\n      file.name.toLowerCase().endsWith('.xlsx') ||\r\n      file.name.toLowerCase().endsWith('.xls') ||\r\n      file.name.toLowerCase().endsWith('.csv');\r\n\r\n    if (!isValidFile) {\r\n      message.error('只能上传Excel或CSV文件！');\r\n      return false;\r\n    }\r\n\r\n    // 验证文件大小\r\n    const isLt10M = file.size / 1024 / 1024 < 10;\r\n    if (!isLt10M) {\r\n      message.error('文件大小不能超过10MB！');\r\n      return false;\r\n    }\r\n\r\n    setSelectedFile(file);\r\n    return false; // 阻止默认上传行为\r\n  };\r\n\r\n  // 处理文件上传\r\n  const handleFileUpload = async (file: File) => {\r\n    try {\r\n      // 显示开始导入的消息\r\n      message.loading('正在导入数据，请在任务中心查看进度...', 2);\r\n\r\n      const response = await batchImportTempProducts(file);\r\n\r\n      if (response.code === 200) {\r\n        // 任务已创建成功，结果将在任务中心显示\r\n        message.success('导入任务已结束，请在任务中心查看详细结果');\r\n\r\n        // 触发任务中心刷新\r\n        setTaskCenterRefreshTrigger((prev) => prev + 1);\r\n\r\n        // 如果有成功导入的数据，延迟刷新列表\r\n        if (response.data.successCount > 0) {\r\n          setTimeout(() => {\r\n            loadData({ ...searchParams });\r\n          }, 3000); // 延长到3秒，给任务处理更多时间\r\n        }\r\n      } else {\r\n        throw new Error(response.msg || '导入失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('批量导入失败:', error);\r\n      message.error('批量导入失败，请检查文件格式并重试');\r\n    }\r\n  };\r\n\r\n  // 确认上传文件\r\n  const handleConfirmUpload = () => {\r\n    if (selectedFile) {\r\n      // 关闭导入弹窗\r\n      handleCloseImportModal();\r\n      // 开始上传\r\n      handleFileUpload(selectedFile);\r\n      // 稍微延迟打开任务中心，确保任务已创建\r\n      setTimeout(() => {\r\n        setTaskCenterVisible(true);\r\n      }, 500);\r\n    }\r\n  };\r\n\r\n  // 处理导出\r\n  // const handleOpenExport = () => {\r\n  //   setExportModalVisible(true);\r\n  // };\r\n\r\n  // 打开新建/编辑模态框\r\n  const handleOpenModal = (record?: TempProduct) => {\r\n    setCurrentProduct(record || null);\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 关闭模态框\r\n  const handleCloseModal = () => {\r\n    setModalVisible(false);\r\n    setCurrentProduct(null);\r\n  };\r\n\r\n  // 保存商品信息\r\n  const handleSaveProduct = async (values: TempProductInput) => {\r\n    try {\r\n      if (currentProduct) {\r\n        // 更新商品\r\n        await upsertTempProduct({\r\n          id: currentProduct.id,\r\n          ...values,\r\n        });\r\n\r\n        message.success('商品更新成功');\r\n      } else {\r\n        // 创建新商品\r\n        await upsertTempProduct(values);\r\n\r\n        message.success('商品创建成功');\r\n      }\r\n      handleCloseModal();\r\n      loadData();\r\n    } catch {}\r\n  };\r\n\r\n  // 查看商品详情\r\n  const handleView = useCallback(async (record: TempProduct) => {\r\n    try {\r\n      const response = await getTempProductByProductId(record.productId);\r\n      if (response.code === 200) {\r\n        setCurrentDetailProduct(response.data);\r\n        setDetailDrawerVisible(true);\r\n      } else {\r\n        message.error(response.msg || '获取商品详情失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取商品详情失败:', error);\r\n      message.error('获取商品详情失败');\r\n    }\r\n  }, []);\r\n\r\n  // 删除商品\r\n  const handleDeleteProduct = async (id: string) => {\r\n    try {\r\n      await deleteTempProduct(id);\r\n      message.success('商品删除成功');\r\n      loadData();\r\n    } catch (error) {\r\n      message.error('删除失败，请稍后重试');\r\n    }\r\n  };\r\n\r\n  // 导出成功回调\r\n  const handleExportSuccess = () => {\r\n    // 刷新任务中心（如果开着）\r\n    setTaskCenterRefreshTrigger((prev) => prev + 1);\r\n    // 可以选择自动打开任务中心查看进度\r\n    message.info('可在任务中心查看导出进度和下载文件');\r\n  };\r\n\r\n  // 取消选择文件\r\n  const handleCancelSelect = () => {\r\n    setSelectedFile(null);\r\n  };\r\n\r\n  // 表格列定义\r\n  const columns: ProColumns<TempProduct>[] = [\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      width: 80,\r\n      fixed: 'right',\r\n      render: (_, record: TempProduct) => (\r\n        <Space size=\"small\">\r\n          <Tooltip title=\"查看详情\">\r\n            <Button\r\n              type=\"link\"\r\n              icon={<EyeOutlined />}\r\n              onClick={() => handleView(record)}\r\n            />\r\n          </Tooltip>\r\n          <Tooltip title=\"编辑\">\r\n            <Button\r\n              type=\"link\"\r\n              icon={<EditOutlined />}\r\n              onClick={() => handleOpenModal(record)}\r\n              style={{ color: '#1890ff' }}\r\n            />\r\n          </Tooltip>\r\n          <Popconfirm\r\n            title=\"确定要删除该商品吗？\"\r\n            onConfirm={() => handleDeleteProduct(record.id)}\r\n            okText=\"确定\"\r\n            cancelText=\"取消\"\r\n          >\r\n            <Tooltip title=\"删除\">\r\n              <Button type=\"text\" danger icon={<DeleteOutlined />} />\r\n            </Tooltip>\r\n          </Popconfirm>\r\n        </Space>\r\n      ),\r\n    },\r\n    {\r\n      title: '商品图片',\r\n      dataIndex: 'imageUrl',\r\n      key: 'imageUrl',\r\n      width: 100,\r\n      fixed: 'left',\r\n      render: (_, record: TempProduct) => (\r\n        <Image\r\n          src={record.imageUrl || ''}\r\n          alt={record.name}\r\n          width={60}\r\n          height={60}\r\n          className=\"object-cover rounded\"\r\n          fallback={FALLBACK_IMAGE_SRC}\r\n          preview={{\r\n            mask: <EyeOutlined />,\r\n          }}\r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: '商品名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n      width: 300,\r\n      ellipsis: {\r\n        showTitle: false,\r\n      },\r\n      render: (_, record: TempProduct) => (\r\n        <div style={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>\r\n          <Tooltip title={record.name} placement=\"topLeft\">\r\n            <Typography.Text className=\"flex-1 min-w-0 mr-2\" ellipsis>\r\n              <a\r\n                href={record.linkOrId}\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"text-blue-500 no-underline\"\r\n              >\r\n                {record.name}\r\n              </a>\r\n            </Typography.Text>\r\n          </Tooltip>\r\n          <Typography.Text\r\n            copyable={{\r\n              text: record.name,\r\n              tooltips: ['复制商品名称', '已复制'],\r\n            }}\r\n            style={{ flexShrink: 0 }}\r\n          />\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '商品ID',\r\n      dataIndex: 'productId',\r\n      key: 'productId',\r\n      width: 120,\r\n      ellipsis: true,\r\n      copyable: true,\r\n    },\r\n    {\r\n      title: '货号/款号',\r\n      dataIndex: 'styleNumber',\r\n      key: 'styleNumber',\r\n      width: 120,\r\n      ellipsis: true,\r\n      render: (_, record: TempProduct) => (\r\n        <Typography.Text\r\n          copyable={record.styleNumber ? { text: record.styleNumber } : false}\r\n        >\r\n          {record.styleNumber || '-'}\r\n        </Typography.Text>\r\n      ),\r\n    },\r\n    {\r\n      title: '商品状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      width: 100,\r\n      align: 'center',\r\n      render: (_, record: TempProduct) => {\r\n        const statusConfig = getProductStatusLabel(record.status);\r\n        return (\r\n          <Tag color={statusConfig.color} style={{ margin: 0 }}>\r\n            {statusConfig.text}\r\n          </Tag>\r\n        );\r\n      },\r\n      filters: [\r\n        { text: '已上架', value: '已上架' },\r\n        { text: '已下架', value: '已下架' },\r\n      ],\r\n      onFilter: (value: any, record: TempProduct) => record.status === value,\r\n    },\r\n\r\n    {\r\n      title: '创建时间',\r\n      dataIndex: 'createdAt',\r\n      key: 'createdAt',\r\n      width: 150,\r\n      sorter: true,\r\n      render: (_, record: TempProduct) => (\r\n        <span style={{ fontSize: '12px', color: '#666' }}>\r\n          {dayjs(record.createdAt).format('YYYY-MM-DD HH:mm')}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '更新时间',\r\n      dataIndex: 'updatedAt',\r\n      key: 'updatedAt',\r\n      width: 150,\r\n      sorter: true,\r\n      render: (_, record: TempProduct) => (\r\n        <span style={{ fontSize: '12px', color: '#666' }}>\r\n          {dayjs(record.updatedAt).format('YYYY-MM-DD HH:mm')}\r\n        </span>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 表格变化处理\r\n  const handleTableChange = (\r\n    newPagination: TablePaginationConfig,\r\n    filters: any,\r\n    sorter: any,\r\n  ) => {\r\n    const params: TempProductQueryParams = {\r\n      ...searchParams,\r\n      current: newPagination.current,\r\n      pageSize: newPagination.pageSize,\r\n    };\r\n\r\n    // 处理排序\r\n    if (sorter && sorter.field && sorter.order) {\r\n      params.sortBy = sorter.field;\r\n      params.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';\r\n    }\r\n\r\n    loadData(params);\r\n  };\r\n\r\n  return (\r\n    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>\r\n      <Card\r\n        title=\"商品列表\"\r\n        extra={\r\n          <Button\r\n            type=\"text\"\r\n            icon={<ReloadOutlined />}\r\n            onClick={handleRefresh}\r\n            loading={loading}\r\n          >\r\n            刷新\r\n          </Button>\r\n        }\r\n      >\r\n        {/* 操作区域 */}\r\n        <div style={{ marginBottom: 16 }}>\r\n          <div className=\"flex justify-between items-center\">\r\n            {/* 搜索区域 */}\r\n            <Space size=\"middle\">\r\n              <Search\r\n                placeholder=\"搜索商品名称\"\r\n                allowClear\r\n                style={{ width: 300 }}\r\n                onSearch={handleSearch}\r\n                enterButton={<SearchOutlined />}\r\n              />\r\n              <Select\r\n                placeholder=\"选择状态\"\r\n                allowClear\r\n                style={{ width: 120 }}\r\n                onChange={handleStatusFilter}\r\n                value={searchParams.status}\r\n              >\r\n                <Option value=\"已上架\">已上架</Option>\r\n                <Option value=\"已下架\">已下架</Option>\r\n                <Option value=\"待上架\">待上架</Option>\r\n                <Option value=\"草稿\">草稿</Option>\r\n              </Select>\r\n              <Button onClick={handleReset}>重置</Button>\r\n            </Space>\r\n\r\n            {/* 批量操作区域 */}\r\n            <Space size=\"middle\">\r\n              <Button\r\n                type=\"primary\"\r\n                icon={<PlusOutlined />}\r\n                onClick={() => setModalVisible(true)}\r\n              >\r\n                新建商品\r\n              </Button>\r\n              <Popconfirm\r\n                key=\"sync\"\r\n                title=\"确定要同步商品数据到知识库吗？\"\r\n                description=\"此操作将把当前商品数据同步到知识库系统中，请确认是否继续。\"\r\n                onConfirm={handleSyncToKnowledgeBase}\r\n                okText=\"确定\"\r\n                cancelText=\"取消\"\r\n              >\r\n                <Button>同步到知识库</Button>\r\n              </Popconfirm>\r\n              <Button\r\n                icon={<ControlOutlined />}\r\n                onClick={() => setTaskCenterVisible(true)}\r\n              >\r\n                任务中心\r\n              </Button>\r\n\r\n              <Button\r\n                type=\"primary\"\r\n                icon={<UploadOutlined />}\r\n                onClick={handleOpenImportModal}\r\n              >\r\n                批量导入\r\n              </Button>\r\n              {/*<Button icon={<ExportOutlined />} onClick={handleOpenExport}>*/}\r\n              {/*  导出数据*/}\r\n              {/*</Button>*/}\r\n            </Space>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 数据表格 */}\r\n        <ProTable\r\n          columns={columns}\r\n          dataSource={dataSource}\r\n          rowKey=\"id\"\r\n          loading={loading}\r\n          pagination={pagination}\r\n          onChange={handleTableChange}\r\n          scroll={{ x: 'max-content', y: 'calc(100vh - 300px)' }}\r\n          size=\"middle\"\r\n          bordered\r\n          search={false}\r\n          toolBarRender={false}\r\n          style={{\r\n            background: '#fff',\r\n            borderRadius: '6px',\r\n          }}\r\n        />\r\n      </Card>\r\n\r\n      {/* 批量导入弹窗 */}\r\n      <Modal\r\n        title=\"批量导入商品\"\r\n        open={importModalVisible}\r\n        onCancel={handleCloseImportModal}\r\n        footer={null}\r\n        width={600}\r\n        destroyOnHidden\r\n      >\r\n        <div style={{ padding: '20px 0' }}>\r\n          <div style={{ marginBottom: 16, textAlign: 'right' }}>\r\n            <Space size=\"middle\">\r\n              <Button\r\n                icon={<DownloadOutlined />}\r\n                onClick={() => handleDownloadTemplate('excel')}\r\n              >\r\n                下载Excel模版\r\n              </Button>\r\n              <Button\r\n                icon={<DownloadOutlined />}\r\n                onClick={() => handleDownloadTemplate('csv')}\r\n              >\r\n                下载CSV模版\r\n              </Button>\r\n            </Space>\r\n          </div>\r\n          {!selectedFile && (\r\n            <>\r\n              <Alert\r\n                message=\"导入说明\"\r\n                description={\r\n                  <div>\r\n                    <p>1. 请先下载模版文件，按照模版格式填写数据</p>\r\n                    <p>2. 支持上传 .xlsx、.xls 或 .csv 格式的文件</p>\r\n                    <p>\r\n                      3.\r\n                      商品名称、商品链接或ID、商品ID、商品状态为必填字段，货号/款号为可选字段\r\n                    </p>\r\n                    <p>4. 缺少必填字段的数据将被跳过，不影响其他数据导入</p>\r\n                  </div>\r\n                }\r\n                type=\"info\"\r\n                showIcon\r\n                style={{ marginBottom: 20 }}\r\n              />\r\n\r\n              <Dragger\r\n                name=\"file\"\r\n                multiple={false}\r\n                accept=\".xlsx,.xls,.csv\"\r\n                beforeUpload={handleFileSelect}\r\n                showUploadList={false}\r\n              >\r\n                <p className=\"ant-upload-drag-icon\">\r\n                  <InboxOutlined />\r\n                </p>\r\n                <p className=\"ant-upload-text\">点击或拖拽文件到此区域选择</p>\r\n                <p className=\"ant-upload-hint\">\r\n                  支持单个文件选择，文件格式：.xlsx、.xls、.csv，大小限制：10MB\r\n                </p>\r\n              </Dragger>\r\n            </>\r\n          )}\r\n\r\n          {selectedFile && (\r\n            <>\r\n              <Alert\r\n                message=\"文件选择确认\"\r\n                description={\r\n                  <div>\r\n                    <p>您已选择文件，请确认是否开始导入：</p>\r\n                  </div>\r\n                }\r\n                type=\"warning\"\r\n                showIcon\r\n                style={{ marginBottom: 20 }}\r\n              />\r\n\r\n              <div\r\n                style={{\r\n                  border: '1px solid #d9d9d9',\r\n                  borderRadius: '6px',\r\n                  padding: '16px',\r\n                  marginBottom: '20px',\r\n                  backgroundColor: '#fafafa',\r\n                }}\r\n              >\r\n                <div\r\n                  style={{\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    marginBottom: '8px',\r\n                  }}\r\n                >\r\n                  <InboxOutlined\r\n                    style={{\r\n                      fontSize: '16px',\r\n                      color: '#1890ff',\r\n                      marginRight: '8px',\r\n                    }}\r\n                  />\r\n                  <span style={{ fontWeight: 'bold' }}>选中的文件：</span>\r\n                </div>\r\n                <div style={{ marginLeft: '24px' }}>\r\n                  <p style={{ margin: 0, color: '#1890ff' }}>\r\n                    📄 {selectedFile.name}\r\n                  </p>\r\n                  <p\r\n                    style={{\r\n                      margin: '4px 0 0 0',\r\n                      color: '#999',\r\n                      fontSize: '12px',\r\n                    }}\r\n                  >\r\n                    文件大小：{(selectedFile.size / 1024 / 1024).toFixed(2)} MB\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div style={{ textAlign: 'center' }}>\r\n                <Space size=\"middle\">\r\n                  <Button onClick={handleCancelSelect}>重新选择</Button>\r\n                  <Button type=\"primary\" onClick={handleConfirmUpload}>\r\n                    确认导入\r\n                  </Button>\r\n                </Space>\r\n              </div>\r\n            </>\r\n          )}\r\n        </div>\r\n      </Modal>\r\n\r\n      {/* 导出弹窗 */}\r\n      <ExportProductModal\r\n        visible={exportModalVisible}\r\n        onClose={() => setExportModalVisible(false)}\r\n        onExportSuccess={handleExportSuccess}\r\n      />\r\n\r\n      {/* 新建/编辑商品弹窗 */}\r\n      <Modal\r\n        title={currentProduct ? '编辑商品' : '新建商品'}\r\n        open={modalVisible}\r\n        onCancel={handleCloseModal}\r\n        footer={null}\r\n        width={800}\r\n        destroyOnHidden\r\n      >\r\n        <Form\r\n          layout=\"vertical\"\r\n          initialValues={\r\n            currentProduct || {\r\n              name: '',\r\n              linkOrId: '',\r\n              productId: '',\r\n              status: '已上架',\r\n              styleNumber: '',\r\n              imageUrl: '',\r\n            }\r\n          }\r\n          onFinish={handleSaveProduct}\r\n          onFinishFailed={() => message.error('请检查表单填写是否正确')}\r\n        >\r\n          <Row gutter={24}>\r\n            <Col span={24}>\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"商品名称\"\r\n                rules={[{ required: true, message: '请输入商品名称' }]}\r\n              >\r\n                <Input placeholder=\"请输入商品名称\" />\r\n              </Form.Item>\r\n            </Col>\r\n\r\n            <Col span={12}>\r\n              <Form.Item\r\n                name=\"productId\"\r\n                label=\"商品ID\"\r\n                rules={[\r\n                  { required: true, message: '请输入商品ID' },\r\n                  { pattern: /^\\d{12}$/, message: '商品ID必须为12位数字' },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入12位数字商品ID\" />\r\n              </Form.Item>\r\n            </Col>\r\n\r\n            <Col span={12}>\r\n              <Form.Item\r\n                name=\"status\"\r\n                label=\"商品状态\"\r\n                rules={[{ required: true, message: '请选择商品状态' }]}\r\n              >\r\n                <Select placeholder=\"请选择商品状态\">\r\n                  <Option value=\"已上架\">已上架</Option>\r\n                  <Option value=\"已下架\">已下架</Option>\r\n                  <Option value=\"待上架\">待上架</Option>\r\n                  <Option value=\"草稿\">草稿</Option>\r\n                </Select>\r\n              </Form.Item>\r\n            </Col>\r\n\r\n            <Col span={24}>\r\n              <Form.Item\r\n                name=\"linkOrId\"\r\n                label=\"商品链接或ID\"\r\n                rules={[{ required: true, message: '请输入商品链接或ID' }]}\r\n              >\r\n                <Input placeholder=\"请输入商品链接或ID\" />\r\n              </Form.Item>\r\n            </Col>\r\n\r\n            <Col span={12}>\r\n              <Form.Item name=\"styleNumber\" label=\"货号/款号\">\r\n                <Input placeholder=\"请输入货号/款号\" />\r\n              </Form.Item>\r\n            </Col>\r\n\r\n            <Col span={12}>\r\n              <Form.Item\r\n                name=\"imageUrl\"\r\n                label=\"商品图片链接\"\r\n                rules={[\r\n                  {\r\n                    type: 'url',\r\n                    message: '请输入有效的图片URL地址',\r\n                  },\r\n                ]}\r\n              >\r\n                <Input placeholder=\"请输入商品图片URL\" />\r\n              </Form.Item>\r\n            </Col>\r\n\r\n            {currentProduct?.imageUrl && (\r\n              <Col span={24}>\r\n                <Form.Item label=\"当前商品图片\">\r\n                  <Image\r\n                    src={currentProduct.imageUrl}\r\n                    width={120}\r\n                    height={120}\r\n                    style={{ objectFit: 'cover' }}\r\n                    fallback={FALLBACK_IMAGE_SRC}\r\n                  />\r\n                </Form.Item>\r\n              </Col>\r\n            )}\r\n          </Row>\r\n\r\n          <Form.Item style={{ marginTop: 24, textAlign: 'right' }}>\r\n            <Space>\r\n              <Button onClick={handleCloseModal}>取消</Button>\r\n              <Button type=\"primary\" htmlType=\"submit\">\r\n                {currentProduct ? '更新商品' : '创建商品'}\r\n              </Button>\r\n            </Space>\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 商品详情抽屉 */}\r\n      <Drawer\r\n        title=\"商品详情\"\r\n        placement=\"right\"\r\n        open={detailDrawerVisible}\r\n        onClose={() => setDetailDrawerVisible(false)}\r\n        width={600}\r\n      >\r\n        {currentDetailProduct && (\r\n          <div className=\"p-5\">\r\n            <div className=\"mb-4 text-center\">\r\n              <Image\r\n                src={currentDetailProduct.imageUrl || ''}\r\n                alt={currentDetailProduct.name}\r\n                width={400}\r\n                height={400}\r\n                className=\"object-cover rounded\"\r\n                fallback={FALLBACK_IMAGE_SRC}\r\n              />\r\n            </div>\r\n            <div className=\"mb-3\">\r\n              <h3 className=\"mb-2 text-base font-medium\">商品信息</h3>\r\n              <p>\r\n                <strong>商品名称：</strong>\r\n                {currentDetailProduct.name}\r\n              </p>\r\n              <p>\r\n                <strong>商品ID：</strong>\r\n                {currentDetailProduct.productId}\r\n              </p>\r\n              <p>\r\n                <strong>货号/款号：</strong>\r\n                {currentDetailProduct.styleNumber || '-'}\r\n              </p>\r\n              <p>\r\n                <strong>商品状态：</strong>\r\n                <Tag\r\n                  color={\r\n                    getProductStatusLabel(currentDetailProduct.status).color\r\n                  }\r\n                >\r\n                  {getProductStatusLabel(currentDetailProduct.status).text}\r\n                </Tag>\r\n              </p>\r\n              <p>\r\n                <strong>商品链接：</strong>\r\n                <a\r\n                  href={currentDetailProduct.linkOrId}\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                >\r\n                  {currentDetailProduct.linkOrId}\r\n                </a>\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <h3 className=\"mb-2 text-base font-medium\">时间信息</h3>\r\n              <p>\r\n                <strong>创建时间：</strong>\r\n                {dayjs(currentDetailProduct.createdAt).format(\r\n                  'YYYY-MM-DD HH:mm:ss',\r\n                )}\r\n              </p>\r\n              <p>\r\n                <strong>更新时间：</strong>\r\n                {dayjs(currentDetailProduct.updatedAt).format(\r\n                  'YYYY-MM-DD HH:mm:ss',\r\n                )}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </Drawer>\r\n\r\n      {/* 任务中心 */}\r\n      <TaskCenter\r\n        visible={taskCenterVisible}\r\n        onClose={() => setTaskCenterVisible(false)}\r\n        defaultActiveTab=\"import\"\r\n        refreshTrigger={taskCenterRefreshTrigger}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ListSimple;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Shop/Product/ListSimple.tsx"}, "16": {"name": "尺码表", "path": "/shop/product/size-table-simple", "file": "@/pages/Shop/Product/SizeTableSimple.tsx", "parentId": "13", "id": "16", "absPath": "/shop/product/size-table-simple", "__content": "import type {\r\n  CompositeSizeChartSimple,\r\n  CreateCompositeSizeChartSimpleInput,\r\n  CreateSizeChartSimpleInput,\r\n  SizeChartSimple,\r\n  UpdateCompositeSizeChartSimpleInput,\r\n  UpdateSizeChartSimpleInput,\r\n} from '@/models/sizeChart';\r\nimport {\r\n  createCompositeSizeChartSimple,\r\n  createSizeChartSimple,\r\n  deleteCompositeSizeChartSimple,\r\n  deleteSizeChartSimple,\r\n  getCompositeSizeChartSimpleList,\r\n  getSizeChartSimpleList,\r\n  updateCompositeSizeChartSimple,\r\n  updateSizeChartSimple,\r\n} from '@/services/sizeChart';\r\nimport {\r\n  DeleteOutlined,\r\n  EditOutlined,\r\n  PlusOutlined,\r\n  ReloadOutlined,\r\n} from '@ant-design/icons';\r\nimport { PageContainer } from '@ant-design/pro-components';\r\nimport {\r\n  Button,\r\n  Card,\r\n  Form,\r\n  Input,\r\n  message,\r\n  Modal,\r\n  Popconfirm,\r\n  Space,\r\n  Table,\r\n  Tabs,\r\n  Tag,\r\n} from 'antd';\r\nimport type { ColumnsType } from 'antd/es/table';\r\nimport React, { useEffect, useState } from 'react';\r\n\r\nconst { Search } = Input;\r\nconst { TabPane } = Tabs;\r\n\r\nconst SizeTableSimple: React.FC = () => {\r\n  // 状态管理\r\n  const [activeTab, setActiveTab] = useState<string>('sizeChart');\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n\r\n  // 简单尺码表相关状态\r\n  const [sizeChartList, setSizeChartList] = useState<SizeChartSimple[]>([]);\r\n  const [sizeChartTotal, setSizeChartTotal] = useState<number>(0);\r\n  const [sizeChartPage, setSizeChartPage] = useState<number>(1);\r\n  const [sizeChartPageSize, setSizeChartPageSize] = useState<number>(10);\r\n  const [sizeChartSearchName, setSizeChartSearchName] = useState<string>('');\r\n\r\n  // 复合尺码表相关状态\r\n  const [compositeSizeChartList, setCompositeSizeChartList] = useState<\r\n    CompositeSizeChartSimple[]\r\n  >([]);\r\n  const [compositeSizeChartTotal, setCompositeSizeChartTotal] =\r\n    useState<number>(0);\r\n  const [compositeSizeChartPage, setCompositeSizeChartPage] =\r\n    useState<number>(1);\r\n  const [compositeSizeChartPageSize, setCompositeSizeChartPageSize] =\r\n    useState<number>(10);\r\n  const [compositeSizeChartSearchName, setCompositeSizeChartSearchName] =\r\n    useState<string>('');\r\n\r\n  // 弹窗相关状态\r\n  const [modalVisible, setModalVisible] = useState<boolean>(false);\r\n  const [modalType, setModalType] = useState<'create' | 'edit'>('create');\r\n  const [currentRecord, setCurrentRecord] = useState<\r\n    SizeChartSimple | CompositeSizeChartSimple | null\r\n  >(null);\r\n  const [form] = Form.useForm();\r\n\r\n  // 获取简单尺码表列表\r\n  const fetchSizeChartList = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const params = {\r\n        page: sizeChartPage,\r\n        limit: sizeChartPageSize,\r\n        name: sizeChartSearchName || undefined,\r\n      };\r\n\r\n      const response = await getSizeChartSimpleList(params);\r\n\r\n      if (response.code === 200) {\r\n        setSizeChartList(response.data.data);\r\n        setSizeChartTotal(response.data.total);\r\n      } else {\r\n        message.error(response.msg || '获取尺码表列表失败');\r\n      }\r\n    } catch (error) {\r\n      message.error('获取尺码表列表失败');\r\n      console.error('获取尺码表列表失败:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取复合尺码表列表\r\n  const fetchCompositeSizeChartList = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const params = {\r\n        page: compositeSizeChartPage,\r\n        limit: compositeSizeChartPageSize,\r\n        name: compositeSizeChartSearchName || undefined,\r\n      };\r\n\r\n      const response = await getCompositeSizeChartSimpleList(params);\r\n\r\n      if (response.code === 200) {\r\n        setCompositeSizeChartList(response.data.data);\r\n        setCompositeSizeChartTotal(response.data.total);\r\n      } else {\r\n        message.error(response.msg || '获取复合尺码表列表失败');\r\n      }\r\n    } catch (error) {\r\n      message.error('获取复合尺码表列表失败');\r\n      console.error('获取复合尺码表列表失败:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 处理创建/编辑提交\r\n  const handleSubmit = async (values: any) => {\r\n    try {\r\n      if (activeTab === 'sizeChart') {\r\n        if (modalType === 'create') {\r\n          const response = await createSizeChartSimple(\r\n            values as CreateSizeChartSimpleInput,\r\n          );\r\n          if (response.code === 200) {\r\n            message.success('创建尺码表成功');\r\n            fetchSizeChartList();\r\n          } else {\r\n            message.error(response.msg || '创建尺码表失败');\r\n          }\r\n        } else {\r\n          if (currentRecord) {\r\n            const response = await updateSizeChartSimple(\r\n              currentRecord.id,\r\n              values as UpdateSizeChartSimpleInput,\r\n            );\r\n            if (response.code === 200) {\r\n              message.success('更新尺码表成功');\r\n              fetchSizeChartList();\r\n            } else {\r\n              message.error(response.msg || '更新尺码表失败');\r\n            }\r\n          }\r\n        }\r\n      } else {\r\n        if (modalType === 'create') {\r\n          const response = await createCompositeSizeChartSimple(\r\n            values as CreateCompositeSizeChartSimpleInput,\r\n          );\r\n          if (response.code === 200) {\r\n            message.success('创建复合尺码表成功');\r\n            fetchCompositeSizeChartList();\r\n          } else {\r\n            message.error(response.msg || '创建复合尺码表失败');\r\n          }\r\n        } else {\r\n          if (currentRecord) {\r\n            const response = await updateCompositeSizeChartSimple(\r\n              currentRecord.id,\r\n              values as UpdateCompositeSizeChartSimpleInput,\r\n            );\r\n            if (response.code === 200) {\r\n              message.success('更新复合尺码表成功');\r\n              fetchCompositeSizeChartList();\r\n            } else {\r\n              message.error(response.msg || '更新复合尺码表失败');\r\n            }\r\n          }\r\n        }\r\n      }\r\n      setModalVisible(false);\r\n      form.resetFields();\r\n      setCurrentRecord(null);\r\n    } catch (error) {\r\n      message.error('操作失败');\r\n      console.error('操作失败:', error);\r\n    }\r\n  };\r\n\r\n  // 处理删除\r\n  const handleDelete = async (\r\n    record: SizeChartSimple | CompositeSizeChartSimple,\r\n  ) => {\r\n    try {\r\n      let response;\r\n      if (activeTab === 'sizeChart') {\r\n        response = await deleteSizeChartSimple(record.id);\r\n      } else {\r\n        response = await deleteCompositeSizeChartSimple(record.id);\r\n      }\r\n\r\n      if (response.code === 200) {\r\n        message.success('删除成功');\r\n        if (activeTab === 'sizeChart') {\r\n          fetchSizeChartList();\r\n        } else {\r\n          fetchCompositeSizeChartList();\r\n        }\r\n      } else {\r\n        message.error(response.msg || '删除失败');\r\n      }\r\n    } catch (error) {\r\n      message.error('删除失败');\r\n      console.error('删除失败:', error);\r\n    }\r\n  };\r\n\r\n  // 简单尺码表表格列定义\r\n  const sizeChartColumns: ColumnsType<SizeChartSimple> = [\r\n    {\r\n      title: '序号',\r\n      key: 'index',\r\n      width: 80,\r\n      render: (_, __, index) =>\r\n        (sizeChartPage - 1) * sizeChartPageSize + index + 1,\r\n    },\r\n    {\r\n      title: '名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n      width: 180,\r\n    },\r\n    {\r\n      title: '尺码范围',\r\n      dataIndex: 'sizeRange',\r\n      key: 'sizeRange',\r\n      width: 400,\r\n      render: (text: string) => (\r\n        <div\r\n          style={{\r\n            whiteSpace: 'nowrap',\r\n            overflow: 'hidden',\r\n            textOverflow: 'ellipsis',\r\n          }}\r\n        >\r\n          {text}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '尺码值',\r\n      dataIndex: 'sizeValue',\r\n      key: 'sizeValue',\r\n      width: 200,\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'isDeleted',\r\n      key: 'isDeleted',\r\n      width: 80,\r\n      render: (isDeleted: number) => (\r\n        <Tag color={isDeleted === 0 ? 'green' : 'red'}>\r\n          {isDeleted === 0 ? '正常' : '已删除'}\r\n        </Tag>\r\n      ),\r\n    },\r\n    {\r\n      title: '创建时间',\r\n      dataIndex: 'createdAt',\r\n      key: 'createdAt',\r\n      width: 180,\r\n      render: (text: string) => new Date(text).toLocaleString(),\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      fixed: 'right',\r\n      width: 150,\r\n      render: (_, record) => (\r\n        <Space size=\"small\">\r\n          <Button\r\n            type=\"link\"\r\n            icon={<EditOutlined />}\r\n            onClick={() => {\r\n              setModalType('edit');\r\n              setCurrentRecord(record);\r\n              form.setFieldsValue(record);\r\n              setModalVisible(true);\r\n            }}\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Popconfirm\r\n            title=\"确定要删除这条记录吗？\"\r\n            onConfirm={() => handleDelete(record)}\r\n            okText=\"确定\"\r\n            cancelText=\"取消\"\r\n          >\r\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\r\n              删除\r\n            </Button>\r\n          </Popconfirm>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 复合尺码表表格列定义\r\n  const compositeSizeChartColumns: ColumnsType<CompositeSizeChartSimple> = [\r\n    {\r\n      title: '序号',\r\n      key: 'index',\r\n      width: 80,\r\n      render: (_, __, index) =>\r\n        (compositeSizeChartPage - 1) * compositeSizeChartPageSize + index + 1,\r\n    },\r\n    {\r\n      title: '名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n      width: 150,\r\n    },\r\n    {\r\n      title: '类型',\r\n      dataIndex: 'type',\r\n      key: 'type',\r\n      width: 180,\r\n      ellipsis: true,\r\n      render: (text: string) => (\r\n        <div style={{ whiteSpace: 'nowrap' }} title={text}>\r\n          {text}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: '尺码范围',\r\n      dataIndex: 'sizeRange',\r\n      key: 'sizeRange',\r\n      width: 450,\r\n      render: (text: string) => {\r\n        // 处理男女款的换行显示\r\n        const formattedText = text\r\n          .replace(/男款:/g, '男款:\\n')\r\n          .replace(/女款:/g, '\\n女款:\\n');\r\n\r\n        return (\r\n          <div style={{ whiteSpace: 'pre-line', lineHeight: '1.4' }}>\r\n            {formattedText}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: '尺码值',\r\n      dataIndex: 'sizeValue',\r\n      key: 'sizeValue',\r\n      width: 200,\r\n      render: (text: string) => {\r\n        // 处理男女款的换行显示\r\n        const formattedText = text\r\n          .replace(/男款:/g, '男款:\\n')\r\n          .replace(/女款:/g, '\\n女款:\\n');\r\n\r\n        return (\r\n          <div style={{ whiteSpace: 'pre-line', lineHeight: '1.4' }}>\r\n            {formattedText}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'isDeleted',\r\n      key: 'isDeleted',\r\n      width: 80,\r\n      render: (isDeleted: number) => (\r\n        <Tag color={isDeleted === 0 ? 'green' : 'red'}>\r\n          {isDeleted === 0 ? '正常' : '已删除'}\r\n        </Tag>\r\n      ),\r\n    },\r\n    {\r\n      title: '创建时间',\r\n      dataIndex: 'createdAt',\r\n      key: 'createdAt',\r\n      width: 180,\r\n      render: (text: string) => new Date(text).toLocaleString(),\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      fixed: 'right',\r\n      width: 150,\r\n      render: (_, record) => (\r\n        <Space size=\"small\">\r\n          <Button\r\n            type=\"link\"\r\n            icon={<EditOutlined />}\r\n            onClick={() => {\r\n              setModalType('edit');\r\n              setCurrentRecord(record);\r\n              form.setFieldsValue(record);\r\n              setModalVisible(true);\r\n            }}\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Popconfirm\r\n            title=\"确定要删除这条记录吗？\"\r\n            onConfirm={() => handleDelete(record)}\r\n            okText=\"确定\"\r\n            cancelText=\"取消\"\r\n          >\r\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\r\n              删除\r\n            </Button>\r\n          </Popconfirm>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 初始化数据\r\n  useEffect(() => {\r\n    if (activeTab === 'sizeChart') {\r\n      fetchSizeChartList();\r\n    } else {\r\n      fetchCompositeSizeChartList();\r\n    }\r\n  }, [\r\n    activeTab,\r\n    sizeChartPage,\r\n    sizeChartPageSize,\r\n    compositeSizeChartPage,\r\n    compositeSizeChartPageSize,\r\n  ]);\r\n\r\n  // 标签页切换\r\n  const handleTabChange = (key: string) => {\r\n    setActiveTab(key);\r\n  };\r\n\r\n  return (\r\n    <PageContainer\r\n      title=\"简单尺码表管理\"\r\n      content=\"管理商品的尺码表信息，包括简单尺码表和复合尺码表\"\r\n    >\r\n      <Card>\r\n        <Tabs activeKey={activeTab} onChange={handleTabChange}>\r\n          <TabPane tab=\"简单尺码表\" key=\"sizeChart\">\r\n            <div style={{ marginBottom: 16 }}>\r\n              <Space>\r\n                <Search\r\n                  placeholder=\"搜索尺码表名称\"\r\n                  allowClear\r\n                  style={{ width: 300 }}\r\n                  onSearch={(value) => {\r\n                    setSizeChartSearchName(value);\r\n                    setSizeChartPage(1);\r\n                  }}\r\n                />\r\n                <Button\r\n                  type=\"primary\"\r\n                  icon={<PlusOutlined />}\r\n                  onClick={() => {\r\n                    setModalType('create');\r\n                    setCurrentRecord(null);\r\n                    form.resetFields();\r\n                    setModalVisible(true);\r\n                  }}\r\n                >\r\n                  新增尺码表\r\n                </Button>\r\n                <Button icon={<ReloadOutlined />} onClick={fetchSizeChartList}>\r\n                  刷新\r\n                </Button>\r\n              </Space>\r\n            </div>\r\n\r\n            <Table\r\n              columns={sizeChartColumns}\r\n              dataSource={sizeChartList}\r\n              rowKey=\"id\"\r\n              loading={loading}\r\n              scroll={{ x: 'max-content', y: 'calc(100vh - 300px)' }}\r\n              pagination={{\r\n                current: sizeChartPage,\r\n                pageSize: sizeChartPageSize,\r\n                total: sizeChartTotal,\r\n                showSizeChanger: true,\r\n                showQuickJumper: true,\r\n                showTotal: (total, range) =>\r\n                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\r\n                onChange: (page, pageSize) => {\r\n                  setSizeChartPage(page);\r\n                  setSizeChartPageSize(pageSize || 10);\r\n                },\r\n              }}\r\n            />\r\n          </TabPane>\r\n\r\n          <TabPane tab=\"复合尺码表\" key=\"compositeSizeChart\">\r\n            <div style={{ marginBottom: 16 }}>\r\n              <Space>\r\n                <Search\r\n                  placeholder=\"搜索复合尺码表名称\"\r\n                  allowClear\r\n                  style={{ width: 300 }}\r\n                  onSearch={(value) => {\r\n                    setCompositeSizeChartSearchName(value);\r\n                    setCompositeSizeChartPage(1);\r\n                  }}\r\n                />\r\n                <Button\r\n                  type=\"primary\"\r\n                  icon={<PlusOutlined />}\r\n                  onClick={() => {\r\n                    setModalType('create');\r\n                    setCurrentRecord(null);\r\n                    form.resetFields();\r\n                    setModalVisible(true);\r\n                  }}\r\n                >\r\n                  新增复合尺码表\r\n                </Button>\r\n                <Button\r\n                  icon={<ReloadOutlined />}\r\n                  onClick={fetchCompositeSizeChartList}\r\n                >\r\n                  刷新\r\n                </Button>\r\n              </Space>\r\n            </div>\r\n\r\n            <Table\r\n              columns={compositeSizeChartColumns}\r\n              dataSource={compositeSizeChartList}\r\n              rowKey=\"id\"\r\n              loading={loading}\r\n              scroll={{ x: 'max-content', y: 'calc(100vh - 300px)' }}\r\n              pagination={{\r\n                current: compositeSizeChartPage,\r\n                pageSize: compositeSizeChartPageSize,\r\n                total: compositeSizeChartTotal,\r\n                showSizeChanger: true,\r\n                showQuickJumper: true,\r\n                showTotal: (total, range) =>\r\n                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\r\n                onChange: (page, pageSize) => {\r\n                  setCompositeSizeChartPage(page);\r\n                  setCompositeSizeChartPageSize(pageSize || 10);\r\n                },\r\n              }}\r\n            />\r\n          </TabPane>\r\n        </Tabs>\r\n      </Card>\r\n\r\n      {/* 创建/编辑弹窗 */}\r\n      <Modal\r\n        title={\r\n          modalType === 'create'\r\n            ? `新增${activeTab === 'sizeChart' ? '尺码表' : '复合尺码表'}`\r\n            : `编辑${activeTab === 'sizeChart' ? '尺码表' : '复合尺码表'}`\r\n        }\r\n        open={modalVisible}\r\n        onCancel={() => {\r\n          setModalVisible(false);\r\n          form.resetFields();\r\n          setCurrentRecord(null);\r\n        }}\r\n        onOk={() => form.submit()}\r\n        destroyOnHidden\r\n        width={600}\r\n      >\r\n        <Form form={form} layout=\"vertical\" onFinish={handleSubmit}>\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"名称\"\r\n            rules={[{ required: true, message: '请输入名称' }]}\r\n          >\r\n            <Input placeholder=\"请输入名称\" />\r\n          </Form.Item>\r\n\r\n          {activeTab === 'compositeSizeChart' && (\r\n            <Form.Item\r\n              name=\"type\"\r\n              label=\"类型\"\r\n              rules={[{ required: true, message: '请输入类型' }]}\r\n            >\r\n              <Input placeholder=\"请输入类型\" />\r\n            </Form.Item>\r\n          )}\r\n\r\n          <Form.Item\r\n            name=\"sizeRange\"\r\n            label=\"尺码范围\"\r\n            rules={[{ required: true, message: '请输入尺码范围' }]}\r\n          >\r\n            <Input placeholder=\"请输入尺码范围\" />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"sizeValue\"\r\n            label=\"尺码值\"\r\n            rules={[{ required: true, message: '请输入尺码值' }]}\r\n          >\r\n            <Input.TextArea\r\n              placeholder=\"请输入尺码值\"\r\n              rows={4}\r\n              showCount\r\n              maxLength={500}\r\n            />\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n    </PageContainer>\r\n  );\r\n};\r\n\r\nexport default SizeTableSimple;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Shop/Product/SizeTableSimple.tsx"}, "17": {"name": "智能跟单", "path": "", "parentId": "18", "id": "17", "absPath": "/shop/smart-orders", "originPath": "/shop/smart-orders"}, "18": {"path": "/shop/smart-orders", "isWrapper": true, "file": "@/components/ProtectedRoute.tsx", "parentId": "ant-design-pro-layout", "id": "18", "absPath": "/shop/smart-orders", "__content": "import { message } from 'antd';\r\nimport Cookies from 'js-cookie';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Navigate, Outlet, useLocation } from 'umi';\r\n\r\nconst ProtectedRoute: React.FC = () => {\r\n  const location = useLocation();\r\n  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);\r\n  const isLoginPage = location.pathname === '/login';\r\n\r\n  useEffect(() => {\r\n    const token = Cookies.get('access_token');\r\n    setIsTokenValid(!!token);\r\n  }, [location.pathname]);\r\n\r\n  useEffect(() => {\r\n    if (isTokenValid === false && !isLoginPage) {\r\n      Cookies.remove('access_token');\r\n      localStorage.removeItem('kywy');\r\n      message.error('请先登录系统');\r\n    }\r\n  }, [isTokenValid, isLoginPage]);\r\n\r\n  if (isTokenValid === null) return null;\r\n\r\n  if (!isTokenValid && !isLoginPage) {\r\n    return <Navigate to=\"/login\" replace state={{ from: location }} />;\r\n  }\r\n\r\n  return <Outlet />;\r\n};\r\n\r\nexport default ProtectedRoute;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/components/ProtectedRoute.tsx"}, "19": {"name": "跟单任务管理", "path": "/shop/smart-orders/order-management", "file": "@/pages/Shop/SmartOrders/OrderManagement.tsx", "parentId": "17", "id": "19", "absPath": "/shop/smart-orders/order-management", "__content": "import { SearchOutlined } from '@ant-design/icons';\r\nimport { Button, Input, Select, Table } from 'antd';\r\nimport React, { useState } from 'react';\r\n\r\nconst { Option } = Select;\r\n\r\ninterface OrderRecord {\r\n  key: string;\r\n  priority: string;\r\n  taskName: string;\r\n  productCode: string;\r\n  webDescription: string;\r\n  readStatus: boolean;\r\n  priorityLevel: number;\r\n}\r\n\r\nconst OrderManagement: React.FC = () => {\r\n  const [selectedCategory, setSelectedCategory] = useState('知识下发');\r\n  const [searchText, setSearchText] = useState('');\r\n\r\n  // 顶部按钮功能\r\n  const handleExportProcess = () => {\r\n    console.log('导出流程');\r\n    // 这里可以添加导出流程的逻辑\r\n  };\r\n\r\n  const handleExport = () => {\r\n    console.log('导出');\r\n    // 这里可以添加导出的逻辑\r\n  };\r\n\r\n  const handleCreate = () => {\r\n    console.log('创建');\r\n    // 这里可以添加创建的逻辑\r\n  };\r\n\r\n  // 左侧分类数据\r\n  const categories = [\r\n    { key: '知识下发', label: '知识下发', count: 1 },\r\n    { key: '数据管控', label: '数据管控', count: 1 },\r\n    { key: '上架删除', label: '上架删除', count: 1 },\r\n    { key: '门店配货', label: '门店配货', count: 1 },\r\n    { key: '客服出货', label: '客服出货', count: 1 },\r\n    { key: '已读服务', label: '已读服务', count: 1 },\r\n    { key: '行货数据', label: '行货数据', count: 1 },\r\n    { key: '门店比较', label: '门店比较', count: 1 },\r\n    { key: '交易识别', label: '交易识别', count: 1 },\r\n  ];\r\n\r\n  // 表格数据\r\n  const tableData: OrderRecord[] = [\r\n    {\r\n      key: '1',\r\n      priority: '1',\r\n      taskName: '知识下发',\r\n      productCode: '全部商品',\r\n      webDescription: '',\r\n      readStatus: false,\r\n      priorityLevel: 1,\r\n    },\r\n  ];\r\n\r\n  // 表格列定义\r\n  const columns = [\r\n    {\r\n      title: '优先级',\r\n      dataIndex: 'priority',\r\n      key: 'priority',\r\n      width: 80,\r\n      render: (text: string) => (\r\n        <span className=\"font-medium\" data-oid=\"nbfg21b\">\r\n          {text}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '任务名称',\r\n      dataIndex: 'taskName',\r\n      key: 'taskName',\r\n      width: 120,\r\n      render: (text: string) => (\r\n        <span className=\"text-blue-600\" data-oid=\"n0:_wxc\">\r\n          {text}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '商品编码',\r\n      dataIndex: 'productCode',\r\n      key: 'productCode',\r\n      width: 120,\r\n    },\r\n    {\r\n      title: '网页描述',\r\n      dataIndex: 'webDescription',\r\n      key: 'webDescription',\r\n      width: 120,\r\n      render: (text: string) => text || '-',\r\n    },\r\n    {\r\n      title: '已读状态',\r\n      dataIndex: 'readStatus',\r\n      key: 'readStatus',\r\n      width: 100,\r\n      render: (status: boolean) => (\r\n        <span\r\n          className={status ? 'text-green-500' : 'text-gray-500'}\r\n          data-oid=\"zpjco8h\"\r\n        >\r\n          {status ? '已读' : '未读'}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '优先级',\r\n      dataIndex: 'priorityLevel',\r\n      key: 'priorityLevel',\r\n      width: 80,\r\n      render: (level: number) => <span data-oid=\"-imyi52\">{level}</span>,\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      width: 200,\r\n      render: () => (\r\n        <div className=\"flex gap-2\" data-oid=\"-w.k16t\">\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            className=\"p-0 text-blue-600\"\r\n            data-oid=\"f5y2fk.\"\r\n          >\r\n            查看\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            className=\"p-0 text-blue-600\"\r\n            data-oid=\"ps1al_5\"\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            size=\"small\"\r\n            className=\"p-0 text-red-500\"\r\n            data-oid=\"9y34iz0\"\r\n          >\r\n            删除\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen bg-gray-100\" data-oid=\"kskii04\">\r\n      {/* 顶部横条 */}\r\n      <div\r\n        className=\"bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center min-h-16 shadow-sm\"\r\n        data-oid=\"g9_qd04\"\r\n      >\r\n        {/* 左侧标题 */}\r\n        <div className=\"text-lg font-semibold text-gray-800\" data-oid=\"fuyhffy\">\r\n          任务列表\r\n        </div>\r\n\r\n        {/* 右侧按钮组 */}\r\n        <div className=\"flex gap-3\" data-oid=\"wzt-jqe\">\r\n          <Button\r\n            onClick={handleExportProcess}\r\n            className=\"border-gray-300 text-gray-700\"\r\n            data-oid=\"-m:9d5s\"\r\n          >\r\n            同步配置\r\n          </Button>\r\n          <Button\r\n            onClick={handleExport}\r\n            className=\"border-gray-300 text-gray-700\"\r\n            data-oid=\"c19n7at\"\r\n          >\r\n            跟单规则设置\r\n          </Button>\r\n          <Button type=\"primary\" onClick={handleCreate} data-oid=\"j.j4d3-\">\r\n            创建跟单任务\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 主要内容区域 */}\r\n      <div className=\"flex flex-1\" data-oid=\"a.exn-w\">\r\n        {/* 左侧分类导航 */}\r\n        <div\r\n          className=\"w-70 bg-white border-r border-gray-200 py-4\"\r\n          data-oid=\"6bpoe1:\"\r\n        >\r\n          <div\r\n            className=\"px-4 mb-4 text-base font-semibold text-gray-800\"\r\n            data-oid=\"zhr_kd6\"\r\n          >\r\n            跟单场景\r\n          </div>\r\n\r\n          <div className=\"px-4\" data-oid=\"gm6abrl\">\r\n            <div\r\n              className=\"mb-4 text-sm text-gray-600 border-b border-gray-100 pb-2\"\r\n              data-oid=\"5r3td4:\"\r\n            >\r\n              类型统计\r\n            </div>\r\n\r\n            {categories.map((category) => (\r\n              <div\r\n                key={category.key}\r\n                className={`flex justify-between items-center px-3 py-2 my-1 cursor-pointer rounded-md transition-colors ${\r\n                  selectedCategory === category.key\r\n                    ? 'bg-blue-50 text-blue-600 border border-blue-200'\r\n                    : 'text-gray-800 border border-transparent hover:bg-gray-50'\r\n                }`}\r\n                onClick={() => setSelectedCategory(category.key)}\r\n                data-oid=\"49uz8ij\"\r\n              >\r\n                <span className=\"text-sm\" data-oid=\"231amb:\">\r\n                  {category.label}\r\n                </span>\r\n                <span\r\n                  className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\"\r\n                  data-oid=\"nh1l0ev\"\r\n                >\r\n                  {category.count}\r\n                </span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* 右侧主要内容区 */}\r\n        <div className=\"flex-1 p-6\" data-oid=\"udvua2p\">\r\n          {/* 页面标题 */}\r\n          <div\r\n            className=\"mb-6 text-xl font-semibold text-gray-800\"\r\n            data-oid=\"j5jemd5\"\r\n          >\r\n            日常咨询分析\r\n          </div>\r\n\r\n          {/* 搜索和筛选区域 */}\r\n          <div\r\n            className=\"mb-4 flex justify-between items-center\"\r\n            data-oid=\"o2m_xly\"\r\n          >\r\n            <div className=\"flex gap-4 items-center\" data-oid=\"fz_zt4o\">\r\n              <Input\r\n                placeholder=\"搜索关键词\"\r\n                prefix={<SearchOutlined data-oid=\"_9c:ahk\" />}\r\n                className=\"w-60\"\r\n                value={searchText}\r\n                onChange={(e) => setSearchText(e.target.value)}\r\n                data-oid=\"s1cmpmb\"\r\n              />\r\n\r\n              <Select\r\n                defaultValue=\"全部状态\"\r\n                className=\"w-30\"\r\n                data-oid=\"ltfh-wz\"\r\n              >\r\n                <Option value=\"all\" data-oid=\"uisuc-w\">\r\n                  全部状态\r\n                </Option>\r\n                <Option value=\"read\" data-oid=\"_m7gtbm\">\r\n                  已读\r\n                </Option>\r\n                <Option value=\"unread\" data-oid=\"h_.t-p-\">\r\n                  未读\r\n                </Option>\r\n              </Select>\r\n              <Select\r\n                defaultValue=\"全部优先级\"\r\n                className=\"w-30\"\r\n                data-oid=\"uxl6pxv\"\r\n              >\r\n                <Option value=\"all\" data-oid=\"6t6-jus\">\r\n                  全部优先级\r\n                </Option>\r\n                <Option value=\"high\" data-oid=\"wo8x74-\">\r\n                  高优先级\r\n                </Option>\r\n                <Option value=\"medium\" data-oid=\"7srjq7v\">\r\n                  中优先级\r\n                </Option>\r\n                <Option value=\"low\" data-oid=\"wvl6vxs\">\r\n                  低优先级\r\n                </Option>\r\n              </Select>\r\n            </div>\r\n\r\n            <Button type=\"primary\" data-oid=\"70009qw\">\r\n              新建工单\r\n            </Button>\r\n          </div>\r\n\r\n          {/* 表格区域 */}\r\n          <div className=\"bg-white rounded-lg\" data-oid=\"uh00udu\">\r\n            <Table\r\n              columns={columns}\r\n              dataSource={tableData}\r\n              pagination={{\r\n                total: 1,\r\n                pageSize: 10,\r\n                showTotal: (total) => `共 ${total} 条记录`,\r\n                showSizeChanger: true,\r\n                showQuickJumper: true,\r\n              }}\r\n              scroll={{ y: 'calc(100vh - 300px)' }}\r\n              className=\"p-6\"\r\n              size=\"middle\"\r\n              data-oid=\"bf6zx.a\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OrderManagement;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Shop/SmartOrders/OrderManagement.tsx"}, "20": {"name": "发货受限地址", "path": "/shop/smart-orders/shipping-restricted-addresses", "file": "@/pages/Shop/SmartOrders/ShippingRestrictedAddresses.tsx", "parentId": "17", "id": "20", "absPath": "/shop/smart-orders/shipping-restricted-addresses", "__content": "import ImportShippingRestrictedAreaModal from '@/components/ShippingRestrictedArea/ImportShippingRestrictedAreaModal';\r\nimport type {\r\n  ShippingRestrictedArea,\r\n  ShippingRestrictedAreaQueryParams,\r\n} from '@/models/shippingRestrictedArea';\r\nimport {\r\n  getShippingRestrictedAreaList,\r\n  syncShippingRestrictedAreaToRagflow,\r\n  updateShippingRestrictedAreaStatus,\r\n} from '@/services/shippingRestrictedArea';\r\nimport { InboxOutlined, SearchOutlined } from '@ant-design/icons';\r\nimport {\r\n  Button,\r\n  Input,\r\n  message,\r\n  Popconfirm,\r\n  Select,\r\n  Table,\r\n  Typography,\r\n} from 'antd';\r\nimport React, { useEffect, useState } from 'react';\r\n\r\nconst { Title, Text } = Typography;\r\n\r\nconst ShippingRestrictedAddresses: React.FC = () => {\r\n  const [searchText, setSearchText] = useState('');\r\n  const [restrictionStatus, setRestrictionStatus] = useState<\r\n    boolean | undefined\r\n  >(undefined);\r\n  const [dataSource, setDataSource] = useState<ShippingRestrictedArea[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [searchParams, setSearchParams] =\r\n    useState<ShippingRestrictedAreaQueryParams>({});\r\n  const [pagination, setPagination] = useState({\r\n    current: 1,\r\n    pageSize: 20,\r\n    total: 0,\r\n  });\r\n  const [importModalVisible, setImportModalVisible] = useState(false);\r\n\r\n  // 更新状态回调\r\n  const handleStatusChange = async (id: string, isActive: boolean) => {\r\n    try {\r\n      const response = await updateShippingRestrictedAreaStatus(id, isActive);\r\n      if (response.code === 200) {\r\n        message.success('状态更新成功');\r\n        // 更新本地数据，避免重新加载整个列表\r\n        setDataSource((prev) =>\r\n          prev.map((item) => (item.id === id ? { ...item, isActive } : item)),\r\n        );\r\n      } else {\r\n        message.error(response.msg || '状态更新失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('更新状态失败:', error);\r\n      message.error('状态更新失败，请稍后重试');\r\n    }\r\n  };\r\n\r\n  // 同步到知识库\r\n  const handleSyncToRagflow = async () => {\r\n    const hide = message.loading('正在同步发货受限地址数据到知识库...', 0);\r\n    try {\r\n      const res = await syncShippingRestrictedAreaToRagflow();\r\n      hide();\r\n      if (res.code === 0) {\r\n        message.success('发货受限地址数据同步成功');\r\n      } else {\r\n        message.error(res.msg || '发货受限地址数据同步失败');\r\n      }\r\n    } catch (error) {\r\n      message.error('发货受限地址数据同步失败，请稍后重试');\r\n      hide();\r\n    }\r\n  };\r\n\r\n  // 表格列定义\r\n  const columns = [\r\n    {\r\n      title: '省市/直辖市',\r\n      dataIndex: 'province',\r\n      key: 'province',\r\n      width: 150,\r\n      render: (text: string) => (\r\n        <span className=\"font-medium\" data-oid=\"-o02ypy\">\r\n          {text}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      title: '城市',\r\n      dataIndex: 'city',\r\n      key: 'city',\r\n      width: 200,\r\n    },\r\n    {\r\n      title: '县/区',\r\n      dataIndex: 'district',\r\n      key: 'district',\r\n      width: 180,\r\n    },\r\n    {\r\n      title: '发货受限程度',\r\n      dataIndex: 'isActive',\r\n      key: 'isActive',\r\n      width: 150,\r\n      render: (isActive: boolean, record: ShippingRestrictedArea) => (\r\n        <Select\r\n          value={isActive}\r\n          onChange={(value) => handleStatusChange(record.id, value)}\r\n          size=\"small\"\r\n          bordered={false}\r\n          style={{ width: 'auto', minWidth: '70px' }}\r\n          options={[\r\n            {\r\n              label: <span className=\"text-green-500\">不受限</span>,\r\n              value: false,\r\n            },\r\n            {\r\n              label: <span className=\"text-red-500\">受限</span>,\r\n              value: true,\r\n            },\r\n          ]}\r\n        />\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 加载数据\r\n  const loadData = async (params?: ShippingRestrictedAreaQueryParams) => {\r\n    setLoading(true);\r\n    try {\r\n      const queryParams = {\r\n        ...searchParams,\r\n        ...params,\r\n        current: params?.current || pagination.current,\r\n        pageSize: params?.pageSize || pagination.pageSize,\r\n      };\r\n\r\n      const response = await getShippingRestrictedAreaList(queryParams);\r\n\r\n      if (response.code === 200) {\r\n        setDataSource(response.data.items);\r\n        setPagination((prev) => ({\r\n          ...prev,\r\n          current: queryParams.current,\r\n          pageSize: queryParams.pageSize,\r\n          total: response.data.total,\r\n        }));\r\n      } else {\r\n        message.error(response.msg || '获取数据失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取发货受限地址列表失败:', error);\r\n      message.error('获取数据失败，请稍后重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 初始化加载数据\r\n  useEffect(() => {\r\n    loadData();\r\n  }, []);\r\n\r\n  // 搜索功能\r\n  const handleSearch = () => {\r\n    const newParams: ShippingRestrictedAreaQueryParams = {\r\n      ...searchParams,\r\n      searchText: searchText.trim() || undefined, // 使用searchText字段进行全文搜索\r\n      isActive: restrictionStatus,\r\n      current: 1,\r\n    };\r\n    setSearchParams(newParams);\r\n    loadData(newParams);\r\n  };\r\n\r\n  // 重置搜索\r\n  const handleReset = () => {\r\n    setSearchText('');\r\n    setRestrictionStatus(undefined);\r\n    setSearchParams({});\r\n    loadData({ current: 1, pageSize: pagination.pageSize });\r\n  };\r\n\r\n  // 打开导入模态框\r\n  const handleImport = () => {\r\n    setImportModalVisible(true);\r\n  };\r\n\r\n  // 导入成功回调\r\n  const handleImportSuccess = () => {\r\n    setImportModalVisible(false);\r\n    loadData(); // 刷新数据\r\n  };\r\n\r\n  // 表格分页变化处理\r\n  const handleTableChange = (page: number, pageSize: number) => {\r\n    const newParams = { ...searchParams, current: page, pageSize };\r\n    loadData(newParams);\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-6 bg-gray-100 min-h-screen\" data-oid=\"hwy2f.6\">\r\n      {/* 主要内容区域 */}\r\n      <div className=\"bg-white rounded-lg p-6 shadow-lg\" data-oid=\"h0-atrd\">\r\n        {/* 标题区域 */}\r\n        <div className=\"mb-6\" data-oid=\"irtr-a0\">\r\n          <Title\r\n            level={4}\r\n            className=\"mb-2 text-gray-800 text-lg font-semibold\"\r\n            data-oid=\"2q8erx5\"\r\n          >\r\n            交通管制、自然灾害等原因，部分地区网点收派件受限？\r\n          </Title>\r\n\r\n          <div className=\"text-gray-600 text-sm mb-5\" data-oid=\"v_x0c5_\">\r\n            <Text type=\"secondary\" data-oid=\"nh87m-7\">\r\n              &quot;地址管理&quot; 帮您搞定\r\n            </Text>\r\n            <Text className=\"text-blue-600\" data-oid=\"k6mzljh\">\r\n              跟单场景\r\n            </Text>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 搜索和操作区域 */}\r\n        <div\r\n          className=\"mb-5 flex justify-between items-center p-4 bg-gray-50 rounded-md border border-gray-200\"\r\n          data-oid=\":itjc:e\"\r\n        >\r\n          <div className=\"flex items-center gap-3\" data-oid=\"p0g0of.\">\r\n            <Input\r\n              placeholder=\"全国地址查询（省份/城市/县区）\"\r\n              prefix={<SearchOutlined data-oid=\"0w59_yl\" />}\r\n              className=\"w-70\"\r\n              value={searchText}\r\n              onChange={(e) => setSearchText(e.target.value)}\r\n              onPressEnter={handleSearch}\r\n              data-oid=\"j5cd9vg\"\r\n            />\r\n            <Select\r\n              placeholder=\"选择受限状态\"\r\n              className=\"w-40\"\r\n              value={restrictionStatus}\r\n              onChange={setRestrictionStatus}\r\n              allowClear\r\n              options={[\r\n                { label: '受限', value: true },\r\n                { label: '不受限', value: false },\r\n              ]}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex gap-3\" data-oid=\"hn:o:ho\">\r\n            <Button\r\n              onClick={handleReset}\r\n              className=\"border-gray-300 text-gray-700\"\r\n            >\r\n              重置\r\n            </Button>\r\n            <Button\r\n              icon={<InboxOutlined />}\r\n              onClick={handleImport}\r\n              className=\"border-gray-300 text-gray-700\"\r\n              data-oid=\"8157n-3\"\r\n            >\r\n              导入\r\n            </Button>\r\n            <Popconfirm\r\n              title=\"确定要同步发货受限地址数据到知识库吗？\"\r\n              description=\"此操作将把当前发货受限地址数据同步到知识库系统中，请确认是否继续。\"\r\n              onConfirm={handleSyncToRagflow}\r\n              okText=\"确定\"\r\n              cancelText=\"取消\"\r\n            >\r\n              <Button className=\"border-gray-300 text-gray-700\">\r\n                同步到知识库\r\n              </Button>\r\n            </Popconfirm>\r\n            <Button type=\"primary\" onClick={handleSearch} data-oid=\"26iruln\">\r\n              查询\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 表格区域 */}\r\n        <div data-oid=\"46.awvk\">\r\n          <Table\r\n            columns={columns}\r\n            dataSource={dataSource}\r\n            loading={loading}\r\n            rowKey=\"id\"\r\n            pagination={{\r\n              current: pagination.current,\r\n              pageSize: pagination.pageSize,\r\n              total: pagination.total,\r\n              showTotal: (total) => `共 ${total} 条记录`,\r\n              showSizeChanger: true,\r\n              showQuickJumper: true,\r\n              pageSizeOptions: ['10', '20', '50', '100'],\r\n              onChange: handleTableChange,\r\n              onShowSizeChange: handleTableChange,\r\n            }}\r\n            scroll={{ y: 'calc(100vh - 380px)' }}\r\n            size=\"middle\"\r\n            bordered={false}\r\n            className=\"bg-white\"\r\n            data-oid=\"e:cet52\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* 导入模态框 */}\r\n      <ImportShippingRestrictedAreaModal\r\n        visible={importModalVisible}\r\n        onCancel={() => setImportModalVisible(false)}\r\n        onSuccess={handleImportSuccess}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ShippingRestrictedAddresses;\r\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/pages/Shop/SmartOrders/ShippingRestrictedAddresses.tsx"}, "ant-design-pro-layout": {"id": "ant-design-pro-layout", "path": "/", "file": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/.umi/plugin-layout/Layout.tsx", "absPath": "/", "isLayout": true, "__absFile": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/src/.umi/plugin-layout/Layout.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "pnpm", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "@umijs/max", "cliName": "max"}, "bundleStatus": {"done": false}, "mfsuBundleStatus": {"done": false}, "react": {"version": "18.3.1", "path": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react"}, "react-dom": {"version": "18.3.1", "path": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\react-dom"}, "appJS": {"path": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\src\\app.tsx", "exports": ["getInitialState", "layout", "request", "rootContainer"]}, "locale": "en-US", "globalCSS": [], "globalJS": [], "overridesCSS": [], "bundler": "webpack", "git": {"originUrl": "**************:effortless-innovations/ykwy-admin.git"}, "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}, "faviconFiles": [], "port": 8000, "host": "0.0.0.0", "ip": "*************", "antd": {"pkgPath": "C:\\Users\\<USER>\\workspace\\ykwy-assistant-app\\ykwy-admin\\node_modules\\antd", "version": "5.26.6"}, "pluginLayout": {"pkgPath": "C:/Users/<USER>/workspace/ykwy-assistant-app/ykwy-admin/node_modules/@ant-design/pro-components", "version": "2.8.10"}}