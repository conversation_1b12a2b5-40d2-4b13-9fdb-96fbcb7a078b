#!/usr/bin/env python3
"""
测试所有修复后的千牛数据解析器
"""
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mock-production', 'src'))

from sales_agent.models.qianniu_models import QianniuDataParser
from sales_agent.utils.logger import setup_detailed_logging

def test_order_parser():
    """测试订单解析器"""
    print("\n🧪 测试订单解析器...")
    
    # 使用真实API返回的数据结构
    test_data = {
        "success": True,
        "message": "queryRecentOrders API调用完成",
        "data": {
            "requestId": "test_1753435352353_aeiz1ik",
            "success": True,
            "data": {
                "success": True,
                "data": {
                    "orders": [
                        {
                            "bizOrderId": "*******************",
                            "orderPrice": "0.10",
                            "cardTypeText": "已关闭",
                            "createTime": "2025-07-22 21:26:33",
                            "itemList": [
                                {
                                    "auctionId": "953394282130",
                                    "auctionTitle": "AI 智能写作助手｜高效产出各类文案",
                                    "price": "0.10"
                                }
                            ]
                        }
                    ],
                    "pageNum": 1
                }
            }
        }
    }
    
    try:
        result = QianniuDataParser.parse_orders(test_data)
        print(f"✅ 订单解析成功！找到 {len(result.orders)} 个订单")
        if result.orders:
            order = result.orders[0]
            print(f"   订单号: {order.biz_order_id}")
            print(f"   价格: ¥{order.order_price}")
    except Exception as e:
        print(f"❌ 订单解析失败: {e}")

def test_shop_items_parser():
    """测试商品解析器"""
    print("\n🧪 测试商品解析器...")
    
    # 使用真实API返回的数据结构
    test_data = {
        "success": True,
        "data": {
            "data": {
                "data": [
                    {
                        "itemId": "953394282130",
                        "title": "AI 智能写作助手｜高效产出各类文案",
                        "price": "0.10",
                        "pic": "//img.alicdn.com/bao/uploaded/i4/1938907789/O1CN01phC9Mh27PQ1xk21rz_!!1938907789.png",
                        "approveStatus": "onsale",
                        "soldQuantity": 100
                    }
                ]
            }
        }
    }
    
    try:
        result = QianniuDataParser.parse_shop_items(test_data)
        print(f"✅ 商品解析成功！找到 {len(result.items)} 个商品")
        if result.items:
            item = result.items[0]
            print(f"   商品ID: {item.item_id}")
            print(f"   标题: {item.title}")
    except Exception as e:
        print(f"❌ 商品解析失败: {e}")

def test_customer_info_parser():
    """测试客户信息解析器"""
    print("\n🧪 测试客户信息解析器...")
    
    # 使用真实API返回的数据结构
    test_data = {
        "success": True,
        "data": {
            "data": {
                "buyerCreditLevel": 8,
                "buyerCreditScore": 261,
                "sendGoodRate": "98.5%",
                "isNewCustomer": False,
                "isShopFans": True,
                "hasMembership": False
            }
        }
    }
    
    try:
        result = QianniuDataParser.parse_customer_info(test_data)
        print(f"✅ 客户信息解析成功！")
        print(f"   信用等级: {result.buyer_credit_level}")
        print(f"   信用分: {result.buyer_credit_score}")
        print(f"   是否粉丝: {result.is_shop_fans}")
    except Exception as e:
        print(f"❌ 客户信息解析失败: {e}")

def test_buyer_search_parser():
    """测试买家搜索解析器"""
    print("\n🧪 测试买家搜索解析器...")
    
    # 使用真实API返回的数据结构
    test_data = {
        "success": True,
        "data": {
            "data": {
                "data": [
                    {
                        "accountId": "*************",
                        "nick": "tb2494039180",
                        "displayNick": "tb2494039180",
                        "accountType": 3,
                        "searchType": "byNick"
                    }
                ]
            }
        }
    }
    
    try:
        result = QianniuDataParser.parse_buyer_search_result(test_data)
        print(f"✅ 买家搜索解析成功！找到 {len(result)} 个结果")
        if result:
            buyer = result[0]
            print(f"   账号ID: {buyer.account_id}")
            print(f"   昵称: {buyer.nick}")
    except Exception as e:
        print(f"❌ 买家搜索解析失败: {e}")

def test_coupons_parser():
    """测试优惠券解析器"""
    print("\n🧪 测试优惠券解析器...")
    
    # 使用真实API返回的数据结构
    test_data = {
        "success": True,
        "data": {
            "data": {
                "publicCouponList": [
                    {
                        "activityId": "acfb71cd53684077a439a1a2f99a3ce3",
                        "name": "商品立减券0723",
                        "description": "满1.01减1元",
                        "amount": "1",
                        "threshold": "1.01"
                    }
                ],
                "privateCouponList": []
            }
        }
    }
    
    try:
        result = QianniuDataParser.parse_shop_coupons(test_data)
        print(f"✅ 优惠券解析成功！找到 {len(result.coupons)} 个优惠券")
        if result.coupons:
            coupon = result.coupons[0]
            print(f"   优惠券名称: {coupon.name}")
            print(f"   描述: {coupon.description}")
    except Exception as e:
        print(f"❌ 优惠券解析失败: {e}")

def main():
    print("🚀 开始测试所有修复后的千牛数据解析器...")
    
    # 设置日志
    setup_detailed_logging()
    
    # 运行所有测试
    test_order_parser()
    test_shop_items_parser()
    test_customer_info_parser()
    test_buyer_search_parser()
    test_coupons_parser()
    
    print("\n✅ 所有解析器测试完成！")

if __name__ == "__main__":
    main()
