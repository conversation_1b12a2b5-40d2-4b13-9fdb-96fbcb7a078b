import { Button, Checkbox, Form, Form as FormNew, Input } from 'antd';
import React, { useEffect } from 'react';

const formItemMarginClass = 'mb-3 sm:mb-4 md:mb-6';
const inputFieldClass =
  'w-full h-8 sm:h-9 md:h-10 lg:h-10 transition-all duration-300';

interface LoginFormProps {
  onFinish: (values: {
    email: string;
    password: string;
    remember: boolean;
  }) => Promise<void>;
  buttonText: string;
  loading: boolean;
  initialValues?: { email: string; password: string; remember: boolean };
}

const LoginForm: React.FC<LoginFormProps> = ({
  onFinish,
  buttonText,
  loading,
  initialValues,
}) => {
  const [form] = FormNew.useForm();

  // 当initialValues变化时更新表单值
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  return (
    <Form
      form={form}
      onFinish={onFinish}
      layout="vertical"
      initialValues={initialValues || { remember: false }}
    >
      <Form.Item
        name="email"
        label="邮箱"
        rules={[{ required: true, message: '请输入邮箱' }]}
        className={formItemMarginClass}
      >
        <Input className={inputFieldClass} placeholder="请输入邮箱" />
      </Form.Item>
      <Form.Item
        name="password"
        label="密码"
        rules={[{ required: true, message: '请输入密码' }]}
        className={formItemMarginClass}
      >
        <Input.Password className={inputFieldClass} placeholder="请输入密码" />
      </Form.Item>
      <Form.Item
        name="remember"
        valuePropName="checked"
        className={formItemMarginClass}
      >
        <Checkbox>记住我</Checkbox>
      </Form.Item>
      <Form.Item className={formItemMarginClass}>
        <Button
          type="primary"
          htmlType="submit"
          block
          className={inputFieldClass}
          loading={loading}
        >
          {buttonText}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default LoginForm;
