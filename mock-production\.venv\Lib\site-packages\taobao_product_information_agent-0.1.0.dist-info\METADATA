Metadata-Version: 2.3
Name: taobao-product-information-agent
Version: 0.1.0
Summary: 基于LlamaIndex的淘宝产品信息智能销售代理
Author: AI Assistant
Author-email: <EMAIL>
Requires-Python: >=3.9.2,<4.0.0
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Dist: aiofiles (>=23.2.0,<24.0.0)
Requires-Dist: aiohttp (>=3.9.0,<4.0.0)
Requires-Dist: chromadb (>=1.0.15,<2.0.0)
Requires-Dist: fastapi (>=0.104.1,<0.105.0)
Requires-Dist: httpx (>=0.27.0,<0.28.0)
Requires-Dist: llama-index (>=0.12.50,<0.13.0)
Requires-Dist: llama-index-agent-openai (>=0.4.0,<0.5.0)
Requires-Dist: llama-index-embeddings-openai (>=0.3.1,<0.4.0)
Requires-Dist: llama-index-llms-openai (>=0.4.0,<0.5.0)
Requires-Dist: openai (>=1.3.0,<2.0.0)
Requires-Dist: openpyxl (>=3.1.5,<4.0.0)
Requires-Dist: pandas (>=2.1.0,<3.0.0)
Requires-Dist: pydantic (>=2.5.0,<3.0.0)
Requires-Dist: pydantic-settings (>=2.10.1,<3.0.0)
Requires-Dist: python-dotenv (>=1.0.0,<2.0.0)
Requires-Dist: python-multipart (>=0.0.6,<0.0.7)
Requires-Dist: requests (>=2.31.0,<3.0.0)
Requires-Dist: uvicorn (>=0.24.0,<0.25.0)
Description-Content-Type: text/markdown

# 无忧淘宝AI销售智能体

基于LlamaIndex和OpenAI构建的智能销售代理，专门用于淘宝AI产品的销售转化。通过向量检索、对话记忆和专业销售工具，提供类人化的销售服务体验。

## 功能特性

- 🤖 **智能对话**: 基于GPT-4的自然语言理解和生成，支持多轮对话记忆
- 📊 **产品咨询**: 专业的AI产品（PPT制作、视频创作、写作助手）咨询服务
- 🔍 **智能检索**: 基于向量数据库的知识检索，精准匹配QA和对话案例
- 🛠️ **销售工具**: 产品识别、策略分析、标准话术、相似对话检索等专业工具
- 📈 **销售转化**: 基于真实对话数据和销售阶段的智能策略推荐
- 🎯 **阶段识别**: 自动识别销售阶段，提供对应的专业话术和策略
- 🔗 **产品链接识别**: 智能识别用户发送的淘宝产品链接，提供针对性服务

## 产品列表

### 1. AI智能写作助手

- **品牌**: 无忧科技
- **定位**: 专业内容创作工具
- **核心功能**: 多场景写作、智能优化、原创度检测
- **目录**: `ai-writing-assistant/`

### 2. AI智能视频创作平台

- **品牌**: 无忧科技
- **定位**: 零基础视频制作工具
- **核心功能**: AI自动剪辑、智能配音、一键字幕
- **目录**: `ai-video-creator/`

### 3. AI智能PPT制作助手

- **品牌**: 无忧科技
- **定位**: 专业演示文稿制作工具
- **核心功能**: 一键生成PPT、智能排版、海量模板
- **目录**: `ai-ppt-maker/`

## 项目结构

```
├── src/sales_agent/          # 核心代码
│   ├── api/                  # FastAPI接口
│   ├── core/                 # 代理核心逻辑
│   ├── tools/                # 销售工具集
│   ├── data_loader/          # 数据加载器
│   └── utils/                # 工具函数
├── data/                     # 训练数据
│   ├── ai-ppt-maker/         # PPT制作产品数据
│   ├── ai-video-creator/     # 视频创作产品数据
│   ├── ai-writing-assistant/ # 写作助手产品数据
│   └── common/               # 通用销售策略
├── main.py                   # 启动入口
├── test_agent.py            # 测试脚本
└── pyproject.toml           # 项目配置
```

## 快速开始

### 1. 环境准备

```bash
# 安装Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 安装依赖
poetry install
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

必需的环境变量：
```bash
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=your_openai_base_url
```

> ⚠️ **安全提醒**：请勿在代码、文档或公共仓库中暴露真实的 API 密钥！

### 3. 索引构建

#### 手动构建索引
```bash
# 验证数据文件
python scripts/build_index.py --validate-only

# 构建索引
python scripts/build_index.py

# 强制重建索引
python scripts/build_index.py --force
```

### 4. 运行测试

```bash
# 激活虚拟环境
poetry shell

# 运行测试脚本
python test_agent.py
```

### 5. 启动API服务

```bash
# 启动服务
python main.py

# 或使用Poetry
poetry run python main.py
```

服务启动后访问: http://localhost:8000

## 🐳 Docker 部署

### 方式一：构建时预构建索引（推荐）

索引在 Docker 构建时预先构建并打包到镜像中，容器启动更快：

```bash
# 1. 构建镜像（预构建索引）
docker build \
  --build-arg OPENAI_API_KEY=your_openai_api_key \
  --build-arg OPENAI_API_BASE=https://api.openai.com/v1 \
  -t taobao-ai-sales-agent .

# 2. 运行容器（索引已在镜像中）
docker run -d \
  --name taobao-ai-sales-agent \
  --restart unless-stopped \
  -p 8001:8000 \
  -e OPENAI_API_KEY=your_openai_api_key \
  -e OPENAI_API_BASE=https://api.openai.com/v1 \
  taobao-ai-sales-agent

# 可选：如果需要持久化索引更新或日志（最好添加否则每次部署重新构建索引将花费大量token）
# -v $(pwd)/storage:/app/storage \
# -v $(pwd)/logs:/app/logs \
```

### 方式二：运行时构建索引

索引在容器首次启动时构建，需要数据卷持久化：

```bash
# 1. 构建镜像（不预构建索引）
docker build -t taobao-ai-sales-agent .

# 2. 运行容器（必须挂载 storage 卷）
docker run -d \
  --name taobao-ai-sales-agent \
  --restart unless-stopped \
  -p 8001:8000 \
  -e OPENAI_API_KEY=your_openai_api_key \
  -e OPENAI_API_BASE=https://api.openai.com/v1 \
  -v $(pwd)/storage:/app/storage \
  taobao-ai-sales-agent
```

### 📋 数据卷说明

| 卷挂载 | 用途 | 预构建索引时 | 运行时构建时 |
|--------|------|-------------|-------------|
| `storage:/app/storage` | 索引文件存储 | 可选（用于持久化更新） | 必需（避免重复构建） |
| `logs:/app/logs` | 日志文件 | 可选（可用 `docker logs` 查看） | 可选（可用 `docker logs` 查看） |

> 💡 **提示**：大多数情况下不需要挂载日志卷，使用 `docker logs -f taobao-ai-sales-agent` 即可查看日志。

### 🔧 常见问题处理

#### 1. 镜像已存在，需要重新构建

```bash
# 删除旧镜像
docker rmi taobao-ai-sales-agent

# 或者强制重新构建
docker build --no-cache -t taobao-ai-sales-agent .
```

#### 2. 容器已在运行，需要重启

```bash
# 停止并删除旧容器
docker stop taobao-ai-sales-agent
docker rm taobao-ai-sales-agent

# 或者一条命令搞定
docker rm -f taobao-ai-sales-agent

# 然后重新运行
docker run -d \
  --name taobao-ai-sales-agent \
  --restart unless-stopped \
  -p 8001:8000 \
  -e OPENAI_API_KEY=your_openai_api_key \
  -e OPENAI_API_BASE=https://api.openai.com/v1 \
  -v $(pwd)/storage:/app/storage \
  -v $(pwd)/logs:/app/logs \
  taobao-ai-sales-agent
```

#### 3. 端口被占用

```bash
# 查看端口占用情况
lsof -i :8001

# 使用其他端口
docker run -d \
  --name taobao-ai-sales-agent \
  -p 8002:8000 \
  # ... 其他参数
```

#### 4. 一键重新部署命令

```bash
# 设置环境变量
export OPENAI_API_KEY=your_key
export OPENAI_API_BASE=your_base_url

# 停止并删除旧容器，重新构建和运行
docker rm -f taobao-ai-sales-agent 2>/dev/null || true && \
docker rmi taobao-ai-sales-agent 2>/dev/null || true && \
docker build \
  --build-arg OPENAI_API_KEY=$OPENAI_API_KEY \
  --build-arg OPENAI_API_BASE=$OPENAI_API_BASE \
  -t taobao-ai-sales-agent . && \
docker run -d \
  --name taobao-ai-sales-agent \
  --restart unless-stopped \
  -p 8001:8000 \
  -v $(pwd)/storage:/app/storage \
  -v $(pwd)/logs:/app/logs \
  taobao-ai-sales-agent

echo "✅ 重新部署完成，访问地址: http://localhost:8001"
```

### 📊 容器管理命令

```bash
# 查看容器状态
docker ps -a | grep taobao-ai-sales-agent

# 查看实时日志
docker logs -f taobao-ai-sales-agent

# 查看最近100行日志
docker logs --tail 100 taobao-ai-sales-agent

# 进入容器
docker exec -it taobao-ai-sales-agent bash

# 查看容器资源使用情况
docker stats taobao-ai-sales-agent

# 重启容器
docker restart taobao-ai-sales-agent

# 停止容器
docker stop taobao-ai-sales-agent

# 删除容器
docker rm taobao-ai-sales-agent

# 删除镜像
docker rmi taobao-ai-sales-agent
```

### 🔍 健康检查

```bash
# 检查服务是否正常
curl http://localhost:8001/health

# 检查API文档
curl http://localhost:8001/docs
```

### 📋 环境变量说明

| 变量名 | 说明 | 是否必需 | 默认值 |
|--------|------|----------|--------|
| `OPENAI_API_KEY` | OpenAI API 密钥 | 是 | - |
| `OPENAI_API_BASE` | OpenAI API 基础URL | 否 | - |
| `DEBUG` | 调试模式 | 否 | `false` |
| `API_HOST` | API 服务主机 | 否 | `0.0.0.0` |
| `API_PORT` | API 服务端口 | 否 | `8000` |
| `CHUNK_SIZE` | 文档分块大小 | 否 | `1024` |
| `CHUNK_OVERLAP` | 文档分块重叠 | 否 | `200` |

## API接口

### 聊天接口
```http
POST /chat
Content-Type: application/json

{
    "message": "你好，我想了解AI PPT制作服务",
    "session_id": "optional_session_id"
}
```

### 重置对话
```http
POST /reset
```

### 获取对话历史
```http
GET /history
```

### 重建索引
```http
POST /rebuild-index
```

## 核心销售工具

无忧淘宝AI销售智能体配备了以下专业销售工具：

1. **产品链接识别工具** - 自动识别用户发送的淘宝产品链接，提供对应产品信息
2. **产品详情查询工具** - 查询AI产品的详细功能、特色和应用场景
3. **销售策略分析工具** - 智能分析客户意图和对话阶段，制定销售策略
4. **标准话术检索工具** - 基于销售阶段检索专业话术和回复模板
5. **相似对话检索工具** - 从真实对话数据中学习自然的沟通风格
6. **产品推荐工具** - 根据用户需求智能推荐合适的AI产品和服务

## 技术特色

### 数据结构化

- 所有数据采用JSON格式，便于程序处理
- 统一的数据结构，便于批量操作
- 丰富的元数据标签，支持多维度分析

### 内容专业化

- 每个产品都有独特的品牌定位和技术特色
- 价格策略符合市场规律
- 服务保障体系完整

### 场景真实化

- 基于真实的电商销售场景设计
- 涵盖客户购买决策的各个环节
- 考虑了不同用户群体的特点和需求

## 使用说明

1. **查看产品信息**: 进入对应产品目录，查看`product-description.json`了解产品详情
2. **学习问答数据**: 查看`qa-database.json`了解各种客户咨询场景和标准回复
3. **参考销售策略**: 查看`common/sales-strategies.json`学习专业的销售话术
4. **分析咨询场景**: 阅读`common/customer-consultation-scenarios.md`了解客户心理

## 项目价值

**无忧淘宝AI销售智能体** 展示了现代AI技术在电商销售场景中的应用价值：

- **类人化销售**: 通过向量检索和对话记忆，提供接近真人销售的服务体验
- **智能阶段识别**: 自动识别客户所处的销售阶段，提供针对性的话术和策略
- **专业产品咨询**: 针对复杂的AI产品功能提供专业、准确的解答
- **个性化推荐**: 根据用户需求和场景智能推荐最合适的产品方案
- **销售转化优化**: 运用专业销售技巧和真实对话数据提升转化率
- **异议处理能力**: 妥善处理客户疑虑，化解购买障碍

## 📝 开发说明

### 项目结构
- `src/sales_agent/`: 核心代码
  - `api/`: FastAPI接口
  - `core/`: 代理核心逻辑
  - `tools/`: 销售工具集
  - `data_loader/`: 数据加载器
  - `utils/`: 工具函数
- `data/`: 训练数据
- `storage/`: 索引存储
- `logs/`: 日志文件
- `scripts/`: 工具脚本

### 扩展开发
1. 添加新产品：在`data/`目录下创建对应文件夹
2. 添加新工具：在`src/sales_agent/tools/`中扩展
3. 修改提示词：在`src/sales_agent/core/agent.py`中调整

## 注意事项

本项目中的所有产品、品牌、价格等信息均为虚构，仅用于技术展示和学习目的。在实际应用中，请使用真实的产品信息和合规的销售策略。

## 📄 许可证

MIT License

