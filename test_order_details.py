#!/usr/bin/env python3
"""
测试订单详情功能
"""
import json
import urllib.request
import urllib.parse

# API配置
BASE_URL = "http://localhost:3002"
API_URL = f"{BASE_URL}/api/v1"

# 测试数据
CONNECTION_ID = "01984070-57c1-7353-b1eb-5aab4f0d0b42"
ORDER_ID = "4642312068661268604"

def test_decrypt_order():
    """测试订单解密API"""
    print("🧪 测试订单解密API...")
    
    url = f"{API_URL}/qianniu-api/decrypt-order"
    data = {
        "connectionId": CONNECTION_ID,
        "tid": ORDER_ID,
        "bizType": "qianniu",
        "queryByTid": True
    }
    
    try:
        json_data = json.dumps(data).encode('utf-8')
        req = urllib.request.Request(url, data=json_data)
        req.add_header('Content-Type', 'application/json')
        
        with urllib.request.urlopen(req, timeout=30) as response:
            status_code = response.getcode()
            response_data = response.read().decode('utf-8')
        
        print(f"📊 状态码: {status_code}")
        
        if status_code == 200:
            result = json.loads(response_data)
            print(f"✅ 请求成功")
            
            # 格式化输出响应数据
            formatted_result = json.dumps(result, ensure_ascii=False, indent=2)
            print(f"📦 响应数据:\n{formatted_result}")
            
            # 测试数据解析
            try:
                level1_data = result.get('data', {})
                level2_data = level1_data.get('data', {})
                level3_data = level2_data.get('data', {})
                order_data = level3_data.get('result', {})
                
                print(f"\n🔍 解析结果:")
                print(f"   收货人: {order_data.get('name', '未知')}")
                print(f"   手机号: {order_data.get('mobile', '未知')}")
                print(f"   地址: {order_data.get('fullAddress', '未知')}")
                print(f"   订单状态: {order_data.get('orderStatus', 0)}")
                
            except Exception as e:
                print(f"❌ 数据解析失败: {e}")
        else:
            print(f"❌ 请求失败: {response_data}")
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")

def main():
    print("🚀 开始测试订单详情功能...")
    test_decrypt_order()
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
