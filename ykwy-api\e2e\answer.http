### 创建答案
POST http://localhost:3009/api/v1/answer
Content-Type: application/json

{
  "content": "测试答案内容",
  "imageUrls": ["http://example.com/img1.jpg"],
  "industryQuestionId": "{{industryQuestionId}}",
  "preciseIntentId": "{{preciseIntentId}}",
  "productIds": ["productId1"],
  "categoryIds": ["cat1"],
  "orderStatus": "已支付",
  "replyRound": "首次回复",
  "effectiveTime": "2024-06-01T00:00:00Z",
  "sessionStage": "会话开始",
  "serviceGroup": "客服A组",
  "auxiliarySwitch": true,
  "unmannedSwitch": true,
  "autoReplySwitch": true,
  "preSaleStatus": "预售",
  "titleKeywords": "关键词",
  "comparisonGroup": "对比组A",
  "customerGroups": ["VIP"],
  "paymentTimeLimit": "24h"
}

### 获取答案列表
GET http://localhost:3009/api/v1/answers?skip=0&take=10

### 获取单个答案详情
GET http://localhost:3009/api/v1/answer/{{answerId}}

### 更新答案
POST http://localhost:3009/api/v1/answer
Content-Type: application/json

{
  "id": "{{answerId}}",
  "content": "测试答案内容-更新",
  "imageUrls": ["http://example.com/img2.jpg"],
  "industryQuestionId": "{{industryQuestionId}}",
  "preciseIntentId": "{{preciseIntentId}}",
  "productIds": ["productId2"],
  "categoryIds": ["cat2"],
  "orderStatus": "已发货",
  "replyRound": "二次回复",
  "effectiveTime": "2024-07-01T00:00:00Z",
  "sessionStage": "会话中",
  "serviceGroup": "客服B组",
  "auxiliarySwitch": false,
  "unmannedSwitch": false,
  "autoReplySwitch": false,
  "preSaleStatus": "非预售",
  "titleKeywords": "新关键词",
  "comparisonGroup": "对比组B",
  "customerGroups": ["普通用户"],
  "paymentTimeLimit": "48h"
}

### 软删除答案
DELETE http://localhost:3009/api/v1/answer/{{answerId}}

### 恢复已删除答案
POST http://localhost:3009/api/v1/answer/{{answerId}}/restore

### 批量软删除
POST http://localhost:3009/api/v1/answers/bulk-delete
Content-Type: application/json

{
  "ids": ["{{answerId}}", "{{answerId2}}"]
} 