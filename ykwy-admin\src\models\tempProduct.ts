/**
 * 临时商品相关的类型定义
 */

/** 临时商品基础信息 */
export interface TempProduct {
  /** 临时商品ID */
  id: string;
  /** 商品名称 */
  name: string;
  /** 商品链接或ID */
  linkOrId: string;
  /** 商品ID */
  productId: string;
  /** 商品状态 */
  status: string;
  /** 货号/款号 */
  styleNumber?: string | null;
  /** 商品图片链接 */
  imageUrl?: string | null;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 是否已删除 */
  isDeleted: number;
}

/** 临时商品列表返回数据 */
export interface TempProductListResponse {
  /** 临时商品列表 */
  items: TempProduct[];
  /** 总数 */
  total: number;
}

/** 查询参数 */
export interface TempProductQueryParams {
  /** 按商品名称模糊搜索 */
  name?: string;
  /** 按商品状态精确匹配 */
  status?: string;
  /** 按商品ID精确匹配 */
  productId?: string;
  /** 跳过条数（分页） */
  skip?: number;
  /** 获取条数（分页） */
  take?: number;
  /** 排序字段 */
  sortBy?: 'createdAt' | 'updatedAt' | 'name' | 'status';
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 是否包含已删除 */
  includeDeleted?: boolean;
  /** 当前页码 */
  current?: number;
  /** 每页大小 */
  pageSize?: number;
}

/** 创建/更新临时商品输入参数 */
export interface TempProductInput {
  /** 临时商品ID，更新时必传，创建时可选 */
  id?: string;
  /** 商品名称 */
  name: string;
  /** 商品链接或ID */
  linkOrId: string;
  /** 商品ID */
  productId: string;
  /** 商品状态 */
  status: string;
  /** 货号/款号 */
  styleNumber?: string;
  /** 商品图片链接 */
  imageUrl?: string;
}

/** 批量删除结果 */
export interface BulkDeleteResult {
  /** 实际删除的数量 */
  count: number;
}

/** 批量创建输入参数 */
export interface BulkCreateInput {
  /** 临时商品数据数组 */
  products: TempProductInput[];
}

/** 批量创建结果 */
export interface BulkCreateResult {
  /** 成功创建的数量 */
  count: number;
  /** 创建的商品列表 */
  products: TempProduct[];
}

/** 批量导入结果 */
export interface BatchImportResult {
  /** 成功处理的数量 */
  successCount: number;
  /** 失败的数量 */
  failCount: number;
  /** 新增的数量 */
  newCount: number;
  /** 更新的数量 */
  updatedCount: number;
  /** 错误信息列表 */
  errors?: string[];
}

/** API响应结构 */
export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/** 商品状态枚举 */
export const PRODUCT_STATUS = {
  ON_SHELF: '已上架',
  OFF_SHELF: '已下架',
  PENDING: '待上架',
  DRAFT: '草稿',
} as const;

/** 商品状态标签映射 */
export const PRODUCT_STATUS_LABELS = {
  已上架: { color: 'green', text: '已上架' },
  已下架: { color: 'red', text: '已下架' },
  待上架: { color: 'orange', text: '待上架' },
  草稿: { color: 'gray', text: '草稿' },
} as const;

/** 获取商品状态标签 */
export const getProductStatusLabel = (status: string) => {
  return (
    PRODUCT_STATUS_LABELS[status as keyof typeof PRODUCT_STATUS_LABELS] || {
      color: 'default',
      text: status,
    }
  );
};
