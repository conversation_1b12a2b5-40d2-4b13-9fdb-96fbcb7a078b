import type { Context } from 'hono';

import { ErrorCode, getErrorMessage } from './error-messages';
import { logger } from './logger';
import type { ApiResponse, PaginatedResponse } from './types';

// 响应工具函数
export function createResponse<T>(data: T, message?: string): ApiResponse<T> {
  return {
    success: true,
    data,
    message,
  };
}

export function createErrorResponse(error: string, code?: string, details?: unknown): ApiResponse {
  return {
    success: false,
    error,
    code,
    details,
  };
}

export function createPaginatedResponse<T>(data: T[], page: number, limit: number, total: number, message?: string): PaginatedResponse<T> {
  return {
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
    message,
  };
}

// 自定义错误类
export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string,
    public details?: unknown,
  ) {
    super(message);
    this.name = 'AppError';
  }
}

// 常见错误类型
export class ValidationError extends AppError {
  constructor(message: string, details?: unknown) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = '资源不存在') {
    super(message, 404, 'NOT_FOUND');
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = '未授权访问') {
    super(message, 401, 'UNAUTHORIZED');
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = '禁止访问') {
    super(message, 403, 'FORBIDDEN');
  }
}

export class ConflictError extends AppError {
  constructor(message: string = '资源冲突') {
    super(message, 409, 'CONFLICT');
  }
}

// 增强的错误处理工具
export function handleError(c: Context, error: unknown): Response {
  // 使用结构化日志记录错误
  const contextLogger = logger.withContext(c);

  if (error instanceof Error) {
    contextLogger.error(
      'API Error occurred',
      {
        errorName: error.name,
        url: c.req.url,
        method: c.req.method,
        userAgent: c.req.header('user-agent'),
        ip: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
      },
      error,
    );
  } else {
    contextLogger.error('Unknown API Error', {
      error: String(error),
      url: c.req.url,
      method: c.req.method,
      userAgent: c.req.header('user-agent'),
      ip: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
    });
  }

  // 处理自定义应用错误
  if (error instanceof AppError) {
    return c.json(createErrorResponse(error.message, error.code, error.details), error.statusCode as 400 | 401 | 403 | 404 | 500);
  }

  // 处理 Prisma 错误
  if (error && typeof error === 'object' && 'code' in error) {
    const prismaError = error as { code: string; message?: string };

    switch (prismaError.code) {
      case 'P2002':
        return c.json(createErrorResponse(getErrorMessage(ErrorCode.DUPLICATE_ERROR), ErrorCode.DUPLICATE_ERROR), 409);
      case 'P2025':
        return c.json(createErrorResponse(getErrorMessage(ErrorCode.NOT_FOUND), ErrorCode.NOT_FOUND), 404);
      case 'P2003':
        return c.json(createErrorResponse(getErrorMessage(ErrorCode.FOREIGN_KEY_ERROR), ErrorCode.FOREIGN_KEY_ERROR), 400);
      case 'P2014':
        return c.json(createErrorResponse(getErrorMessage(ErrorCode.RELATION_ERROR), ErrorCode.RELATION_ERROR), 400);
      default:
        contextLogger.error('Unhandled Prisma error', { prismaError });
        return c.json(createErrorResponse(getErrorMessage(ErrorCode.DATABASE_ERROR), ErrorCode.DATABASE_ERROR), 500);
    }
  }

  // 处理验证错误（Zod）
  if (error && typeof error === 'object' && 'issues' in error) {
    const zodError = error as { issues: Array<{ path: string[]; message: string }> };
    const validationErrors = zodError.issues.map((issue) => ({
      field: issue.path.join('.'),
      message: issue.message,
    }));

    return c.json(createErrorResponse(getErrorMessage(ErrorCode.VALIDATION_ERROR), ErrorCode.VALIDATION_ERROR, validationErrors), 400);
  }

  // 处理标准错误
  if (error instanceof Error) {
    // 开发环境返回详细错误信息
    if (Bun.env.NODE_ENV === 'development') {
      return c.json(createErrorResponse(error.message, ErrorCode.INTERNAL_ERROR, { stack: error.stack }), 500);
    }
    return c.json(createErrorResponse(getErrorMessage(ErrorCode.INTERNAL_ERROR), ErrorCode.INTERNAL_ERROR), 500);
  }

  // 未知错误
  return c.json(createErrorResponse(getErrorMessage(ErrorCode.UNKNOWN_ERROR), ErrorCode.UNKNOWN_ERROR), 500);
}

// 分页计算工具
export function calculatePagination(page: number, limit: number) {
  const skip = (page - 1) * limit;
  return { skip, take: limit };
}
