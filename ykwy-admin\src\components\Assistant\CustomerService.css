/* CustomerService.css */
.customer-service-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.floating-btn {
  background-color: #1a73e8;
  color: white;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 15%);
  cursor: pointer;
  transition: all 0.3s ease;
}

.floating-btn:hover {
  background-color: #0d47a1;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 20%);
}

.chat-box {
  width: 380px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 15%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 85vh;
  transition: all 0.3s ease;
}

.chat-header {
  background-color: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
}

.tabs {
  display: flex;
}

.tab {
  background: none;
  border: none;
  color: #1a73e8;
  font-weight: 600;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 2px solid #1a73e8;
}

.tab:not(.active) {
  color: #6c757d;
  border-bottom: none;
}

.close-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #333;
}

.content-area {
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.panel-section {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 5%);
}

.section-title {
  color: #1a73e8;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px;
  border-bottom: 1px solid #e9ecef;
  text-align: left; /* 明确设置居左（默认值，可省略） */
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 5%);
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.setting-label input[type='checkbox'] {
  width: 16px;
  height: 16px;
  accent-color: #1a73e8;
}

.setting-value {
  display: flex;
  align-items: center;
}

.setting-select {
  padding: 6px 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  color: #333;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 30px;
  cursor: pointer;
}

.search-container {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.search-container input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.search-btn {
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  width: 36px;
  font-size: 10px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  cursor: pointer;
  transition: color 0.3s;
}

.search-btn:hover {
  background-color: #f8f9fa;
  color: #000;
}

.subsection-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 12px 0 8px;
}

.product-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.product-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.2s;
}

.product-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 8%);
}

.product-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 6px;
}

.product-info {
  text-align: center;
}

.product-name {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.product-price {
  font-size: 12px;
  color: #6c757d;
}

.messages-container {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message {
  padding: 8px 12px;
  border-radius: 6px;
  max-width: 85%;
}

.message.user {
  background-color: #e8f0fe;
  align-self: flex-end;
}

.message.bot {
  background-color: #f1f3f4;
  align-self: flex-start;
}

.message-content {
  margin-bottom: 4px;
  font-size: 14px;
  color: #333;
}

.message-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.semantic-tag {
  font-size: 11px;
  color: #1a73e8;
  background-color: #e8f0fe;
  padding: 2px 6px;
  border-radius: 3px;
}

.report-btn {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 11px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.report-btn:hover {
  background-color: #f8d7da;
}

.message-time {
  font-size: 10px;
  color: #6c757d;
  text-align: right;
}

.input-container {
  display: flex;
  gap: 8px;
}

.input-container input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.send-btn {
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 4px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.send-btn:hover {
  background-color: #0d47a1;
}

/* 滚动条样式 */
.content-area::-webkit-scrollbar,
.messages-container::-webkit-scrollbar {
  width: 4px;
}

.content-area::-webkit-scrollbar-track,
.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.content-area::-webkit-scrollbar-thumb,
.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.content-area::-webkit-scrollbar-thumb:hover,
.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 客服助手标题样式 */
.assistant-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.assistant-main-header {
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex; /* 添加弹性布局 */
  justify-content: space-between; /* 两端对齐 */
  align-items: center; /* 垂直居中 */
}

.simple-conversation {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 12px;
  border: 1px solid red;
}

.user-msg {
  background-color: white;
  border: 1px solid #e0e8f2;
  align-self: flex-start;
  width: 90%;
  padding: 12px;
  border-radius: 6px;
}

.ai-msg {
  background-color: #f0f8ff;
  border: 1px solid #e0e8f2;
  align-self: flex-end;
  width: 80%;
}

.msg-tag {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  color: white;
  border-radius: 2px;
  margin-right: 8px;
  font-size: 14px;
  background-color: #007bff;
}

.ai-msg .msg-tag {
  background-color: #6c757d;
}

.msg-content {
  margin-bottom: 4px;
  font-size: 14px;
  color: #333;
  text-align: left;
  display: flex;
}

.msg-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.conversation-item {
  border: 1px solid #eaeaea;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  background-color: white;
  align-self: flex-start;
  width: 92%;
  padding: 12px;
}
