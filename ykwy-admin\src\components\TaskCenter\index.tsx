import { ControlOutlined } from '@ant-design/icons';
import { Drawer, Tabs } from 'antd';
import React, { useState } from 'react';
import ExportTaskList from './ExportTaskList';
import ImportTaskList from './ImportTaskList';

interface TaskCenterProps {
  visible: boolean;
  onClose: () => void;
  defaultActiveTab?: 'import' | 'export';
  refreshTrigger?: number; // 用于触发刷新的属性
}

const TaskCenter: React.FC<TaskCenterProps> = ({
  visible,
  onClose,
  defaultActiveTab = 'import',
  refreshTrigger,
}) => {
  const [activeTab, setActiveTab] = useState<string>(defaultActiveTab);

  const tabItems = [
    {
      key: 'import',
      label: '导入任务',
      children: (
        <ImportTaskList
          refreshTrigger={refreshTrigger}
          visible={visible && activeTab === 'import'}
        />
      ),
    },
    {
      key: 'export',
      label: '导出任务',
      children: (
        <ExportTaskList
          refreshTrigger={refreshTrigger}
          visible={visible && activeTab === 'export'}
        />
      ),
    },
  ];

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <ControlOutlined />
          <span>任务中心</span>
        </div>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={800}
      styles={{
        body: { padding: 0, overflow: 'hidden' },
      }}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        style={{ padding: '0 24px' }}
        tabBarStyle={{
          marginBottom: 0,
          borderBottom: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      />
    </Drawer>
  );
};

export default TaskCenter;
