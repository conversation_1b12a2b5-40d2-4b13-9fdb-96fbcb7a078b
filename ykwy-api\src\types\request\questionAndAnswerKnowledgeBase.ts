/**
 * 问答知识库查询请求对象
 */
export interface QuestionAndAnswerKnowledgeBaseQuery {
  /** 按问题类型模糊搜索 */
  questionType?: string;
  /** 按分类编码精确匹配 */
  categoryCode?: string;
  /** 按订单状态精确匹配 */
  orderStatus?: string;
  /** 按匹配方式精确匹配 */
  matchMode?: string;
  /** 按是否自定义过滤 */
  isCustom?: boolean;
  /** 按是否永久有效过滤 */
  isPermanent?: boolean;
  /** 按店铺ID过滤 */
  shopId?: string;
  /** 按商品ID过滤 */
  productId?: string;
  /** 按时效配置ID过滤 */
  timeValidityId?: string;
  /** 跳过条数（分页） */
  skip?: number;
  /** 获取条数（分页） */
  take?: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 是否包含已删除 */
  includeDeleted?: boolean;
}

/**
 * 搜索选项
 */
export interface QuestionAndAnswerKnowledgeBaseSearchOptions {
  /** 匹配方式 */
  matchMode?: string;
  /** 是否只搜索当前有效的问答 */
  onlyValid?: boolean;
}

/**
 * 统计查询选项
 */
export interface QuestionAndAnswerKnowledgeBaseStatsOptions {
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 分组方式 */
  groupBy?: string;
}
