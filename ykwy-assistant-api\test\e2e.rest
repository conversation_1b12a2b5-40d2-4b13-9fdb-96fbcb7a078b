### Variables
@baseUrl = http://localhost:3002
@apiUrl = {{baseUrl}}/api/v1

# Test variables (will be updated during testing)
@organizationId = cmbrnxouk0000f3pueml5pm8g
@platformId = cmbrnxovu0002f3puli5hao2q
@customerId = cmbstkgpo0005rsssetqmjoe8
@conversationId = cmbstlh9y0009rsssdqx30a0d
@messageId = cmbsujwd30002ceesf6jcolgt
@userId = cmbsugzmy0000b9ao1k82e698
@taskId = cmbsup818000bceesb05lv5om
@recommendationId = cmbsum7ct0007cees0m5it3f8
@memberId =
@activityId =
@statsId =

###############################################
# 0. Health Check & API Info
###############################################

### Health Check
GET {{baseUrl}}/health
Accept: application/json

###############################################
# 1. Organizations API Tests
###############################################

### Get Organizations List
GET {{apiUrl}}/organizations
?page=1&limit=10

### Create Organization
POST {{apiUrl}}/organizations
Content-Type: application/json

{
  "name": "测试科技有限公司",
  "description": "这是一个测试组织",
  "logoUrl": "https://example.com/logo.png",
  "website": "https://test-company.com",
  "contactEmail": "<EMAIL>"
}

### Get Organization Details (需要先运行上面的创建请求获取ID)
GET {{apiUrl}}/organizations/{{organizationId}}

### Update Organization
PUT {{apiUrl}}/organizations/{{organizationId}}
Content-Type: application/json

{
  "name": "更新后的测试科技公司",
  "description": "更新后的描述"
}

###############################################
# 2. Users API Tests
###############################################

### Get Users List
GET {{apiUrl}}/users
?page=1&limit=10&role=CUSTOMER_SERVICE

### Get User Details
GET {{apiUrl}}/users/{{userId}}

### Update User
PUT {{apiUrl}}/users/{{userId}}
Content-Type: application/json

{
  "name": "更新的用户名",
  "role": "CUSTOMER_SERVICE"
}

### Update User Role
PUT {{apiUrl}}/users/{{userId}}/role
Content-Type: application/json

{
  "role": "ORGANIZATION_ADMIN"
}

### Get User Stats
GET {{apiUrl}}/users/{{userId}}/stats
?days=30

### Get User Online Status
GET {{apiUrl}}/users/{{userId}}/online-status

###############################################
# 3. Organization Members API Tests
###############################################

### Get Organization Members
GET {{apiUrl}}/organization-members/organization/{{organizationId}}
?page=1&limit=10

### Add Organization Member
POST {{apiUrl}}/organization-members
Content-Type: application/json

{
  "organizationId": "{{organizationId}}",
  "userId": "{{userId}}",
  "role": "MEMBER"
}

### Update Member Role
PUT {{apiUrl}}/organization-members/{{memberId}}
Content-Type: application/json

{
  "role": "ADMIN"
}

### Remove Organization Member
DELETE {{apiUrl}}/organization-members/{{memberId}}

###############################################
# 4. Platforms API Tests
###############################################

### Get Platforms List
GET {{apiUrl}}/platforms
?page=1&limit=10&organizationId={{organizationId}}

### Create Platform
POST {{apiUrl}}/platforms
Content-Type: application/json

{
  "organizationId": "{{organizationId}}",
  "name": "测试淘宝店铺",
  "type": "TAOBAO",
  "config": {
    "appKey": "test_app_key",
    "appSecret": "test_app_secret",
    "sessionKey": "test_session_key"
  },
  "isActive": true
}

### Get Platform Details
GET {{apiUrl}}/platforms/{{platformId}}

### Update Platform
PUT {{apiUrl}}/platforms/{{platformId}}
Content-Type: application/json

{
  "name": "更新后的淘宝店铺",
  "config": {
    "appKey": "updated_app_key",
    "appSecret": "updated_app_secret"
  }
}

### Delete Platform
DELETE {{apiUrl}}/platforms/{{platformId}}

###############################################
# 5. Customers API Tests
###############################################

### Get Customers List
GET {{apiUrl}}/customers
?page=1&limit=10&platformId={{platformId}}

### Search Customers
GET {{apiUrl}}/customers
?search=测试用户&page=1&limit=10

### Create Customer
POST {{apiUrl}}/customers
Content-Type: application/json

{
  "platformId": "{{platformId}}",
  "platformCustomerId": "tb_customer_123456",
  "nickname": "测试用户小明",
  "avatar": "https://example.com/avatar.jpg",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "realName": "张小明",
  "gender": "男",
  "location": "北京市朝阳区",
  "vipLevel": "VIP1",
  "tags": ["老客户", "高价值"],
  "notes": "这是一个测试客户",
  "totalOrders": 10,
  "totalAmount": 1500.50
}

### Get Customer Details
GET {{apiUrl}}/customers/{{customerId}}

### Update Customer
PUT {{apiUrl}}/customers/{{customerId}}
Content-Type: application/json

{
  "nickname": "更新后的昵称",
  "vipLevel": "VIP2",
  "tags": ["老客户", "高价值", "活跃用户"],
  "notes": "更新后的备注信息"
}

### Delete Customer
DELETE {{apiUrl}}/customers/{{customerId}}

###############################################
# 6. Conversations API Tests
###############################################

### Get Conversations List
GET {{apiUrl}}/conversations
?page=1&limit=10&status=PENDING

### Get Conversations with Filters
GET {{apiUrl}}/conversations
?organizationId={{organizationId}}&priority=HIGH&page=1&limit=5

### Create Conversation
POST {{apiUrl}}/conversations
Content-Type: application/json

{
  "platformId": "{{platformId}}",
  "customerId": "{{customerId}}",
  "title": "客户咨询产品问题",
  "priority": "NORMAL",
  "tags": ["产品咨询", "售前"]
}

### Get Conversation Details
GET {{apiUrl}}/conversations/{{conversationId}}

### Update Conversation
PUT {{apiUrl}}/conversations/{{conversationId}}
Content-Type: application/json

{
  "title": "更新后的对话标题",
  "status": "IN_PROGRESS",
  "priority": "HIGH",
  "assignedUserId": "{{userId}}"
}

### Lock Conversation
POST {{apiUrl}}/conversations/{{conversationId}}/lock
Content-Type: application/json

{
  "userId": "{{userId}}"
}

### Unlock Conversation
POST {{apiUrl}}/conversations/{{conversationId}}/unlock

###############################################
# 7. Messages API Tests
###############################################

### Get Conversation Messages
GET {{apiUrl}}/messages/conversation/{{conversationId}}
?page=1&limit=20

### Send Message
POST {{apiUrl}}/messages
Content-Type: application/json

{
  "conversationId": "{{conversationId}}",
  "senderId": "{{userId}}",
  "senderType": "CUSTOMER_SERVICE",
  "content": "您好！我是客服小王，很高兴为您服务。",
  "messageType": "TEXT"
}

### Send Reply Message
POST {{apiUrl}}/messages
Content-Type: application/json

{
  "conversationId": "{{conversationId}}",
  "senderId": "{{userId}}",
  "senderType": "CUSTOMER_SERVICE",
  "content": "这是一条回复消息",
  "messageType": "TEXT",
  "parentMessageId": "{{messageId}}"
}

### Get Message Details
GET {{apiUrl}}/messages/{{messageId}}

### Recall Message
POST {{apiUrl}}/messages/{{messageId}}/recall
Content-Type: application/json

{
  "userId": "{{userId}}",
  "reason": "消息内容有误，需要撤回"
}

###############################################
# 8. AI Recommendations API Tests
###############################################

### Get AI Recommendations List
GET {{apiUrl}}/ai-recommendations
?page=1&limit=10&conversationId={{conversationId}}

### Get Latest AI Recommendations for Conversation
GET {{apiUrl}}/ai-recommendations/conversation/{{conversationId}}/latest
?limit=5

### Create AI Recommendation
POST {{apiUrl}}/ai-recommendations
Content-Type: application/json

{
  "messageId": "{{messageId}}",
  "conversationId": "{{conversationId}}",
  "query": "客户询问退货政策",
  "recommendation": "根据我们的退货政策，7天内可以无理由退货，需要保持商品完好...",
  "confidence": 0.85,
  "modelName": "gpt-3.5-turbo",
  "modelVersion": "2023-06-01",
  "strategyType": "DETAILED_ANSWER",
  "metadata": {
    "keywords": ["退货", "政策"],
    "category": "售后服务"
  }
}

### Mark AI Recommendation as Used
POST {{apiUrl}}/ai-recommendations/{{recommendationId}}/use

### Provide Feedback for AI Recommendation
POST {{apiUrl}}/ai-recommendations/{{recommendationId}}/feedback
Content-Type: application/json

{
  "feedback": "GOOD",
  "feedbackNote": "推荐内容很有帮助，但需要稍作调整"
}

###############################################
# 9. Customer Service Tasks API Tests
###############################################

### Get Tasks List
GET {{apiUrl}}/customer-service-tasks
?page=1&limit=10&status=TODO

### Get Tasks with Filters
GET {{apiUrl}}/customer-service-tasks
?organizationId={{organizationId}}&priority=HIGH&overdue=true

### Create Task
POST {{apiUrl}}/customer-service-tasks
Content-Type: application/json

{
  "organizationId": "{{organizationId}}",
  "title": "跟进重要客户咨询",
  "description": "客户对产品功能有疑问，需要详细解答",
  "taskType": "FOLLOW_UP",
  "priority": "HIGH",
  "assignedUserId": "{{userId}}",
  "dueDate": "2024-01-20T10:00:00.000Z"
}

### Get Task Details
GET {{apiUrl}}/customer-service-tasks/{{taskId}}

### Update Task
PUT {{apiUrl}}/customer-service-tasks/{{taskId}}
Content-Type: application/json

{
  "title": "更新后的任务标题",
  "status": "IN_PROGRESS",
  "priority": "URGENT",
  "description": "更新后的任务描述"
}

### Assign Task
POST {{apiUrl}}/customer-service-tasks/{{taskId}}/assign
Content-Type: application/json

{
  "assignedUserId": "{{userId}}"
}

### Delete Task
DELETE {{apiUrl}}/customer-service-tasks/{{taskId}}

###############################################
# 10. Conversation Activities API Tests
###############################################

### Get Conversation Activities
GET {{apiUrl}}/conversation-activities
?page=1&limit=10&conversationId={{conversationId}}

### Get Activities for Specific Conversation
GET {{apiUrl}}/conversation-activities/conversation/{{conversationId}}
?page=1&limit=20

### Create Activity Record
POST {{apiUrl}}/conversation-activities
Content-Type: application/json

{
  "conversationId": "{{conversationId}}",
  "userId": "{{userId}}",
  "action": "ENTER",
  "metadata": {
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "*************"
  }
}

### Send Heartbeat
POST {{apiUrl}}/conversation-activities/heartbeat
Content-Type: application/json

{
  "conversationId": "{{conversationId}}",
  "userId": "{{userId}}"
}

### Get Online Users in Conversation
GET {{apiUrl}}/conversation-activities/conversation/{{conversationId}}/online-users

###############################################
# 11. Daily Stats API Tests
###############################################

### Get Daily Stats List
GET {{apiUrl}}/daily-stats
?page=1&limit=10&organizationId={{organizationId}}

### Get Stats by Date Range
GET {{apiUrl}}/daily-stats
?organizationId={{organizationId}}&startDate=2024-01-01&endDate=2024-01-31

### Get Organization Overview
GET {{apiUrl}}/daily-stats/organization/{{organizationId}}/overview
?days=30

### Create Daily Stats
POST {{apiUrl}}/daily-stats
Content-Type: application/json

{
  "organizationId": "{{organizationId}}",
  "date": "2024-01-15",
  "totalConversations": 50,
  "newConversations": 15,
  "resolvedConversations": 12,
  "avgResponseTime": 3.5,
  "customerSatisfaction": 4.2
}

### Update Daily Stats
PUT {{apiUrl}}/daily-stats/{{statsId}}
Content-Type: application/json

{
  "totalConversations": 55,
  "resolvedConversations": 15,
  "avgResponseTime": 3.2,
  "customerSatisfaction": 4.5
}

### Generate Stats for Date
POST {{apiUrl}}/daily-stats/generate
Content-Type: application/json

{
  "organizationId": "{{organizationId}}",
  "date": "2024-01-16"
}

###############################################
# 12. Error Testing
###############################################

### Test 404 Error
GET {{apiUrl}}/non-existent-endpoint

### Test Invalid ID Format
GET {{apiUrl}}/organizations/invalid-id

### Test Invalid JSON
POST {{apiUrl}}/organizations
Content-Type: application/json

{
  "name": "",
  "invalidField": "test"
}

###############################################
# 13. Pagination and Search Testing
###############################################

### Test Large Page Size
GET {{apiUrl}}/conversations
?page=1&limit=101

### Test Invalid Page
GET {{apiUrl}}/conversations
?page=0&limit=10

### Test Search with Special Characters
GET {{apiUrl}}/customers
?search=测试@#$%

###############################################
# 14. Batch Operations Testing
###############################################

### Create Multiple Customers
POST {{apiUrl}}/customers
Content-Type: application/json

{
  "platformId": "{{platformId}}",
  "platformCustomerId": "batch_customer_001",
  "nickname": "批量测试用户1",
  "tags": ["批量创建"]
}

###

POST {{apiUrl}}/customers
Content-Type: application/json

{
  "platformId": "{{platformId}}",
  "platformCustomerId": "batch_customer_002",
  "nickname": "批量测试用户2",
  "tags": ["批量创建"]
}

### 删除用户
DELETE {{apiUrl}}/users/{{userId}}
Content-Type: application/json

###############################################
# 15. Clean Up (Delete Created Test Data)
###############################################

### Delete Test Organization (运行在最后)
# DELETE {{apiUrl}}/organizations/{{organizationId}}