import { z<PERSON>alida<PERSON> } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { prisma } from '../lib/db';
import { platformWsManager } from '../lib/platform-websocket';
import { createErrorResponse, createResponse, handleError } from '../lib/utils';

const app = new Hono();

// 验证Schema
const ClientIdSchema = z.object({
  clientId: z.string().uuid('无效的客户端ID'),
});

const UpdateClientStatusSchema = z.object({
  isOnline: z.boolean(),
  connectionId: z.string().optional(),
  clientInfo: z
    .object({
      ip: z.string().optional(),
      userAgent: z.string().optional(),
      version: z.string().optional(),
      macAddress: z.string().optional(),
    })
    .optional(),
});

const RegisterClientSchema = z.object({
  invitationId: z.string().uuid('无效的邀请ID'),
  name: z.string().min(1, '客户端名称不能为空'),
  description: z.string().optional(),
  clientInfo: z
    .object({
      ip: z.string().optional(),
      userAgent: z.string().optional(),
      version: z.string().optional(),
      macAddress: z.string().optional(),
    })
    .optional(),
});

/**
 * GET /qianniu-clients - 获取所有千牛客户端（包含实时连接状态）
 */
app.get('/', async (c) => {
  try {
    const clients = await prisma.qianniuClient.findMany({
      include: {
        organization: {
          select: { id: true, name: true },
        },
        team: {
          select: { id: true, name: true },
        },
        accounts: {
          select: {
            id: true,
            accountName: true,
            shopName: true,
            platformType: true,
            isActive: true,
            isLoggedIn: true,
          },
        },
        invitation: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        _count: {
          select: {
            accounts: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // 获取实时连接状态
    const clientsWithStatus = clients.map((client) => {
      const connections = platformWsManager.getTeamConnections(client.teamId);
      const isRealTimeOnline = connections.some(
        (conn) => conn.platformData?.['clientId'] === client.id && conn.ws.readyState === 1, // WebSocket.OPEN
      );

      return {
        ...client,
        realTimeStatus: {
          isOnline: isRealTimeOnline,
          lastSeen: isRealTimeOnline ? new Date() : client.lastOnlineAt,
          connectionCount: connections.filter((conn) => conn.platformData?.['clientId'] === client.id).length,
        },
      };
    });

    return c.json(createResponse(clientsWithStatus, '获取千牛客户端列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /qianniu-clients/:clientId - 获取特定千牛客户端详情
 */
app.get('/:clientId', zValidator('param', ClientIdSchema), async (c) => {
  try {
    const { clientId } = c.req.valid('param');

    const client = await prisma.qianniuClient.findUnique({
      where: { id: clientId },
      include: {
        organization: {
          select: { id: true, name: true },
        },
        team: {
          select: { id: true, name: true },
        },
        accounts: {
          include: {
            brand: {
              select: { id: true, name: true },
            },
          },
        },
        invitation: {
          select: {
            id: true,
            name: true,
            status: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            accounts: true,
          },
        },
      },
    });

    if (!client) {
      return c.json(createErrorResponse('千牛客户端不存在'), 404);
    }

    return c.json(createResponse(client, '获取千牛客户端详情成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /qianniu-clients/register - 千牛客户端注册（由注入脚本调用）
 */
app.post('/register', zValidator('json', RegisterClientSchema), async (c) => {
  try {
    const data = c.req.valid('json');

    // 验证邀请是否有效
    const invitation = await prisma.connectionInvitation.findUnique({
      where: { id: data.invitationId },
      include: {
        organization: true,
        team: true,
      },
    });

    if (!invitation) {
      return c.json(createErrorResponse('邀请不存在'), 404);
    }

    if (invitation.status !== 'PENDING') {
      return c.json(createErrorResponse('邀请已被使用或已过期'), 400);
    }

    if (invitation.expiresAt < new Date()) {
      return c.json(createErrorResponse('邀请已过期'), 400);
    }

    // 检查是否已经有客户端使用了这个邀请
    const existingClient = await prisma.qianniuClient.findFirst({
      where: { invitation: { id: data.invitationId } },
    });

    if (existingClient) {
      return c.json(createErrorResponse('该邀请已被使用'), 400);
    }

    // 创建千牛客户端
    const client = await prisma.qianniuClient.create({
      data: {
        name: data.name,
        description: data.description,
        organizationId: invitation.organizationId,
        teamId: invitation.teamId,
        clientInfo: data.clientInfo,
        connectionId: '', // 初始为空，连接时更新
        isOnline: true, // 注册时默认在线
        lastOnlineAt: new Date(),
      },
      include: {
        organization: {
          select: { id: true, name: true },
        },
        team: {
          select: { id: true, name: true },
        },
      },
    });

    // 更新邀请状态
    await prisma.connectionInvitation.update({
      where: { id: data.invitationId },
      data: {
        status: 'ACTIVATED',
        activatedAt: new Date(),
        qianniuClientId: client.id,
      },
    });

    return c.json(
      createResponse(
        {
          client,
          message: '千牛客户端注册成功',
        },
        '客户端注册成功',
      ),
      201,
    );
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * PUT /qianniu-clients/:clientId/status - 更新客户端状态（由WebSocket连接管理调用）
 */
app.put('/:clientId/status', zValidator('param', ClientIdSchema), zValidator('json', UpdateClientStatusSchema), async (c) => {
  try {
    const { clientId } = c.req.valid('param');
    const data = c.req.valid('json');

    const client = await prisma.qianniuClient.update({
      where: { id: clientId },
      data: {
        isOnline: data.isOnline,
        connectionId: data.connectionId,
        clientInfo: data.clientInfo,
        lastOnlineAt: data.isOnline ? new Date() : undefined,
        updatedAt: new Date(),
      },
      include: {
        organization: {
          select: { id: true, name: true },
        },
        team: {
          select: { id: true, name: true },
        },
      },
    });

    return c.json(createResponse(client, '客户端状态更新成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * DELETE /qianniu-clients/:clientId - 删除千牛客户端
 */
app.delete('/:clientId', zValidator('param', ClientIdSchema), async (c) => {
  try {
    const { clientId } = c.req.valid('param');

    // 检查客户端是否存在
    const client = await prisma.qianniuClient.findUnique({
      where: { id: clientId },
    });

    if (!client) {
      return c.json(createErrorResponse('千牛客户端不存在'), 404);
    }

    // 删除客户端（级联删除相关账号）
    await prisma.qianniuClient.delete({
      where: { id: clientId },
    });

    return c.json(createResponse(null, '千牛客户端删除成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /qianniu-clients/organization/:organizationId - 获取组织的千牛客户端
 */
app.get(
  '/organization/:organizationId',
  zValidator(
    'param',
    z.object({
      organizationId: z.string().uuid('无效的组织ID'),
    }),
  ),
  async (c) => {
    try {
      const { organizationId } = c.req.valid('param');

      const clients = await prisma.qianniuClient.findMany({
        where: { organizationId },
        include: {
          team: {
            select: { id: true, name: true },
          },
          accounts: {
            select: {
              id: true,
              accountName: true,
              shopName: true,
              platformType: true,
              isActive: true,
              isLoggedIn: true,
            },
          },
          _count: {
            select: {
              accounts: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return c.json(createResponse(clients, '获取组织千牛客户端成功'));
    } catch (error) {
      return handleError(c, error);
    }
  },
);

/**
 * GET /qianniu-clients/connection-stats - 获取连接状态统计
 */
app.get('/connection-stats', async (c) => {
  try {
    // 获取WebSocket连接统计
    const wsStats = platformWsManager.getStats();

    // 获取数据库中的客户端统计
    const dbStats = await prisma.qianniuClient.groupBy({
      by: ['isOnline', 'organizationId'],
      _count: {
        id: true,
      },
    });

    // 组织统计数据
    const stats = {
      websocket: wsStats,
      database: {
        total: await prisma.qianniuClient.count(),
        online: await prisma.qianniuClient.count({ where: { isOnline: true } }),
        offline: await prisma.qianniuClient.count({ where: { isOnline: false } }),
        byOrganization: dbStats.reduce(
          (acc, stat) => {
            const orgId = stat.organizationId;
            if (!acc[orgId]) {
              acc[orgId] = { total: 0, online: 0, offline: 0 };
            }
            acc[orgId].total += stat._count.id;
            if (stat.isOnline) {
              acc[orgId].online += stat._count.id;
            } else {
              acc[orgId].offline += stat._count.id;
            }
            return acc;
          },
          {} as Record<string, { total: number; online: number; offline: number }>,
        ),
      },
      realTime: {
        activeConnections: wsStats.totalConnections,
        qianniuConnections: wsStats.connectionsByType?.['QIANNIU'] || 0,
      },
    };

    return c.json(createResponse(stats, '获取连接状态统计成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

export default app;
