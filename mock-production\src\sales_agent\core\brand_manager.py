"""
品牌管理器 - 管理多个品牌的销售智能体
"""
import logging
from typing import Dict, Optional
from .agent import SalesAgent
from .qinggong_agent import QinggongSportsAgent

logger = logging.getLogger(__name__)


class BrandManager:
    """品牌管理器，负责管理不同品牌的销售智能体"""
    
    def __init__(self):
        self.agents: Dict[str, SalesAgent] = {}
        self._initialize_agents()
    
    def _initialize_agents(self):
        """初始化所有品牌的智能体"""
        try:
            # 初始化默认品牌（易客无忧）
            logger.info("🏢 初始化易客无忧品牌智能体...")
            self.agents['ykwy'] = SalesAgent()
            self.agents['ykwy'].build_index()
            self.agents['ykwy'].initialize_agent()
            logger.info("✅ 易客无忧品牌智能体初始化完成")
            
            # 初始化轻功体育品牌
            logger.info("🏃 初始化轻功体育品牌智能体...")
            self.agents['qinggong'] = QinggongSportsAgent()
            self.agents['qinggong'].build_index()
            self.agents['qinggong'].initialize_agent()
            logger.info("✅ 轻功体育品牌智能体初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 品牌智能体初始化失败: {e}")
            raise
    
    def get_agent(self, brand_id: str) -> Optional[SalesAgent]:
        """根据品牌ID获取对应的智能体"""
        if brand_id not in self.agents:
            logger.warning(f"⚠️ 未找到品牌ID为 {brand_id} 的智能体，使用默认智能体")
            return self.agents.get('ykwy')  # 默认返回易客无忧智能体
        
        return self.agents[brand_id]
    
    def get_available_brands(self) -> list:
        """获取所有可用的品牌ID"""
        return list(self.agents.keys())
    
    async def chat(self, brand_id: str, message: str, **kwargs) -> str:
        """统一的聊天接口"""
        agent = self.get_agent(brand_id)
        if not agent:
            raise ValueError(f"未找到品牌ID为 {brand_id} 的智能体")
        
        logger.info(f"🎯 使用品牌 {brand_id} 的智能体处理消息")
        return await agent.chat(message, **kwargs)
    
    def get_conversation_history(self, brand_id: str, conversation_id: str) -> list:
        """获取指定品牌的对话历史"""
        agent = self.get_agent(brand_id)
        if not agent:
            return []
        
        return agent.get_conversation_history(conversation_id)
    
    def reset_conversation(self, brand_id: str, conversation_id: str) -> bool:
        """重置指定品牌的对话"""
        agent = self.get_agent(brand_id)
        if not agent:
            return False
        
        return agent.reset_conversation(conversation_id)
    
    def get_brand_info(self, brand_id: str) -> dict:
        """获取品牌信息"""
        brand_info = {
            'ykwy': {
                'name': '易客无忧科技',
                'description': '专业的AI服务提供商',
                'products': ['AI智能PPT制作', 'AI智能视频创作', 'AI智能写作'],
                'agent_type': 'SalesAgent'
            },
            'qinggong': {
                'name': '轻功体育',
                'description': '专业的体育用品销售商',
                'products': ['运动鞋', '运动服装', '体育器材', '健身用品'],
                'agent_type': 'QinggongSportsAgent'
            }
        }
        
        return brand_info.get(brand_id, {
            'name': '未知品牌',
            'description': '未知品牌描述',
            'products': [],
            'agent_type': 'Unknown'
        })


# 全局品牌管理器实例
brand_manager: Optional[BrandManager] = None


def get_brand_manager() -> BrandManager:
    """获取全局品牌管理器实例"""
    global brand_manager
    if brand_manager is None:
        brand_manager = BrandManager()
    return brand_manager
