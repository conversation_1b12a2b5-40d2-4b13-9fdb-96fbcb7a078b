#!/usr/bin/env node

/**
 * TCP 连接测试脚本
 * 测试线上部署的 TCP 服务是否正常工作
 */

const net = require('net');
const { URL } = require('url');

// 配置
const config = {
  baseUrl: 'https://ykwy-assistant-api.wuyoutansuo.com',
  // 测试 TCP 端口配置
  tcpPorts: [
    9997,  // 默认的 TCP 端口
    3000,  // HTTP/WebSocket 端口（测试是否支持 TCP 复用）
    3001   // 备用 TCP 端口
  ],
  host: 'ykwy-assistant-api.wuyoutansuo.com',
  testTimeout: 10000, // 10秒超时
  messageTimeout: 5000, // 消息超时
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  const timestamp = new Date().toISOString();
  console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

// 测试单个 TCP 端口
async function testTcpPort(host, port) {
  return new Promise((resolve) => {
    logInfo(`测试 TCP 连接: ${host}:${port}`);

    const socket = new net.Socket();
    let isConnected = false;
    let dataReceived = false;
    let responseCount = 0;
    const responses = [];

    // 发送 YKWY 后端特定的测试数据
    const testMessages = [
      // 1. 基本连接测试
      JSON.stringify({
        type: 'ping',
        timestamp: Date.now(),
        message: 'YKWY TCP 连接测试'
      }) + '\n',

      // 2. 千牛协议测试（如果是千牛 TCP 服务）
      JSON.stringify({
        type: 'qianniu_test',
        action: 'heartbeat',
        data: {
          clientId: 'test_client_' + Date.now(),
          version: '1.0.0'
        }
      }) + '\n',

      // 3. 平台 WebSocket 协议测试
      JSON.stringify({
        type: 'platform_test',
        event: 'connection_test',
        payload: {
          source: 'tcp_test_script',
          timestamp: Date.now()
        }
      }) + '\n'
    ];

    // 设置超时
    const timeout = setTimeout(() => {
      if (!isConnected) {
        logWarning(`TCP 端口 ${port} 连接超时`);
        socket.destroy();
        resolve({ port, success: false, error: 'Connection timeout' });
      }
    }, config.testTimeout);

    // 连接成功
    socket.on('connect', () => {
      isConnected = true;
      clearTimeout(timeout);
      logSuccess(`TCP 端口 ${port} 连接成功`);



      let messageIndex = 0;

      function sendNextMessage() {
        if (messageIndex < testMessages.length) {
          const message = testMessages[messageIndex];
          logInfo(`向端口 ${port} 发送测试消息 ${messageIndex + 1}/${testMessages.length}...`);
          logInfo(`消息内容: ${message.trim()}`);
          socket.write(message);
          messageIndex++;

          // 等待一段时间再发送下一条消息
          setTimeout(sendNextMessage, 1000);
        }
      }

      // 开始发送测试消息
      sendNextMessage();

      // 等待响应
      const messageTimeout = setTimeout(() => {
        logInfo(`端口 ${port} 未收到响应，关闭连接`);
        socket.end();
        resolve({
          port,
          success: true,
          connected: true,
          dataReceived: false,
          message: 'Connected but no response'
        });
      }, config.messageTimeout);

      // 如果收到数据，清除消息超时
      socket.once('data', () => {
        clearTimeout(messageTimeout);
      });
    });

    // 接收数据
    socket.on('data', (data) => {
      dataReceived = true;
      responseCount++;
      const message = data.toString().trim();
      responses.push(message);

      logSuccess(`端口 ${port} 收到响应 ${responseCount}: ${message}`);

      // 分析响应内容，判断是否是 YKWY 后端服务
      let serviceType = 'unknown';
      let isYkwyBackend = false;

      try {
        const jsonResponse = JSON.parse(message);

        // 检查是否是千牛 TCP 服务的响应
        if (jsonResponse.type === 'qianniu_response' ||
            jsonResponse.source === 'qianniu_tcp_server' ||
            jsonResponse.service === 'qianniu') {
          serviceType = 'qianniu_tcp';
          isYkwyBackend = true;
          logSuccess(`🎯 确认连接到 YKWY 千牛 TCP 服务`);
        }

        // 检查是否是平台 WebSocket 服务的响应
        else if (jsonResponse.type === 'platform_response' ||
                 jsonResponse.source === 'platform_ws_server' ||
                 jsonResponse.service === 'platform') {
          serviceType = 'platform_ws';
          isYkwyBackend = true;
          logSuccess(`🎯 确认连接到 YKWY 平台 WebSocket 服务`);
        }

        // 检查是否包含 YKWY 相关标识
        else if (JSON.stringify(jsonResponse).toLowerCase().includes('ykwy') ||
                 JSON.stringify(jsonResponse).toLowerCase().includes('assistant')) {
          serviceType = 'ykwy_service';
          isYkwyBackend = true;
          logSuccess(`🎯 确认连接到 YKWY 相关服务`);
        }

        // 通用响应分析
        else if (jsonResponse.status || jsonResponse.success !== undefined) {
          serviceType = 'generic_api';
          logInfo(`📡 收到 API 响应，可能是 YKWY 后端服务`);
        }

        // 显示响应详情
        if (jsonResponse.timestamp) {
          logInfo(`⏰ 响应时间戳: ${new Date(jsonResponse.timestamp).toISOString()}`);
        }
        if (jsonResponse.version) {
          logInfo(`📦 服务版本: ${jsonResponse.version}`);
        }
        if (jsonResponse.clientId) {
          logInfo(`🆔 客户端ID: ${jsonResponse.clientId}`);
        }

      } catch (e) {
        // 非 JSON 响应
        if (message.toLowerCase().includes('ykwy') ||
            message.toLowerCase().includes('assistant') ||
            message.toLowerCase().includes('qianniu')) {
          isYkwyBackend = true;
          serviceType = 'text_response';
          logSuccess(`🎯 文本响应包含 YKWY 相关标识`);
        } else {
          logWarning(`📄 收到非 JSON 响应，无法确定服务类型`);
        }
      }

      // 如果收到多个响应，继续等待
      if (responseCount < testMessages.length) {
        logInfo(`⏳ 等待更多响应... (${responseCount}/${testMessages.length})`);
        return;
      }

      // 所有测试完成，关闭连接
      setTimeout(() => {
        socket.end();
        resolve({
          port,
          success: true,
          connected: true,
          dataReceived: true,
          responseCount,
          responses,
          serviceType,
          isYkwyBackend,
          summary: `收到 ${responseCount} 个响应，服务类型: ${serviceType}`
        });
      }, 500);
    });

    // 连接错误
    socket.on('error', (error) => {
      clearTimeout(timeout);
      logError(`TCP 端口 ${port} 连接错误: ${error.message}`);
      resolve({ port, success: false, error: error.message });
    });

    // 连接关闭
    socket.on('close', () => {
      if (isConnected && !dataReceived) {
        logInfo(`TCP 端口 ${port} 连接已关闭（无数据响应）`);
      }
    });

    // 开始连接
    socket.connect(port, host);
  });
}

// 测试所有 TCP 端口
async function testAllTcpPorts() {
  logInfo('开始测试所有可能的 TCP 端口...');

  const results = [];

  for (const port of config.tcpPorts) {
    const result = await testTcpPort(config.host, port);
    results.push(result);

    // 如果找到一个成功的连接，可以选择继续测试其他端口或停止
    if (result.success && result.connected) {
      logSuccess(`找到可用的 TCP 端口: ${port}`);
    }
  }

  return results;
}

// 测试 HTTP API 获取 TCP 信息
async function testTcpInfo() {
  return new Promise((resolve) => {
    logInfo('尝试从 API 获取 TCP 服务信息...');

    const endpoints = [
      '/api/v1/qianniu-tcp/connections',
      '/api/v1/platform-ws/connections',
      '/api/v1/qianniu-clients',
      '/api/v1/connection',
      '/health'
    ];

    let currentIndex = 0;

    function testNextEndpoint() {
      if (currentIndex >= endpoints.length) {
        resolve(null);
        return;
      }

      const endpoint = endpoints[currentIndex++];
      const url = new URL(config.baseUrl + endpoint);
      const isHttps = url.protocol === 'https:';
      const client = isHttps ? require('https') : require('http');
      const port = url.port || (isHttps ? 443 : 80);

      const options = {
        hostname: url.hostname,
        port: port,
        path: url.pathname,
        method: 'GET',
        timeout: 5000
      };

      const req = client.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          if (res.statusCode === 200) {
            try {
              const jsonData = JSON.parse(data);
              logSuccess(`API 端点 ${endpoint} 响应: ${JSON.stringify(jsonData, null, 2)}`);

              // 尝试从响应中提取 TCP 端口信息
              const extractedPorts = [];

              // 检查各种可能的端口字段
              if (jsonData.tcpPort) extractedPorts.push(jsonData.tcpPort);
              if (jsonData.port) extractedPorts.push(jsonData.port);
              if (jsonData.serverPort) extractedPorts.push(jsonData.serverPort);

              // 检查嵌套对象中的端口信息
              if (jsonData.server && jsonData.server.port) extractedPorts.push(jsonData.server.port);
              if (jsonData.config && jsonData.config.port) extractedPorts.push(jsonData.config.port);

              // 检查数组中的连接信息
              if (Array.isArray(jsonData.data)) {
                jsonData.data.forEach(item => {
                  if (item.port) extractedPorts.push(item.port);
                  if (item.tcpPort) extractedPorts.push(item.tcpPort);
                });
              }

              // 在响应文本中搜索端口号模式
              const portMatches = JSON.stringify(jsonData).match(/(?:port|Port)["']?\s*:\s*(\d+)/g);
              if (portMatches) {
                portMatches.forEach(match => {
                  const port = parseInt(match.match(/(\d+)/)[1]);
                  if (port > 1000 && port < 65536) {
                    extractedPorts.push(port);
                  }
                });
              }

              if (extractedPorts.length > 0) {
                const uniquePorts = [...new Set(extractedPorts)];
                logInfo(`从 API 发现 TCP 端口: ${uniquePorts.join(', ')}`);
                resolve(uniquePorts[0]); // 返回第一个端口
                return;
              }
            } catch (e) {
              logWarning(`API 端点 ${endpoint} 响应不是有效 JSON`);
            }
          } else {
            logWarning(`API 端点 ${endpoint} 返回状态: ${res.statusCode}`);
          }
          testNextEndpoint();
        });
      });

      req.on('error', (error) => {
        logWarning(`API 端点 ${endpoint} 请求失败: ${error.message}`);
        testNextEndpoint();
      });

      req.on('timeout', () => {
        logWarning(`API 端点 ${endpoint} 请求超时`);
        req.destroy();
        testNextEndpoint();
      });

      req.end();
    }

    testNextEndpoint();
  });
}

// 主测试函数
async function runTests() {
  console.log(`${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                      TCP 连接测试                            ║
║                                                              ║
║  测试主机: ${config.host}                        ║
║  测试端口: ${config.tcpPorts.join(', ')}                                    ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);

  try {
    // 1. 尝试从 API 获取 TCP 端口信息
    const apiTcpPort = await testTcpInfo();

    // 2. 如果从 API 获取到端口，优先测试该端口
    if (apiTcpPort && !config.tcpPorts.includes(apiTcpPort)) {
      config.tcpPorts.unshift(apiTcpPort);
      logInfo(`添加从 API 获取的 TCP 端口: ${apiTcpPort}`);
    }

    // 3. 测试所有 TCP 端口
    const results = await testAllTcpPorts();

    // 测试结果汇总
    console.log(`${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                        测试结果汇总                          ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}`);

    const successfulPorts = results.filter(r => r.success && r.connected);
    const responsivePorts = results.filter(r => r.success && r.dataReceived);

    logInfo(`测试端口总数: ${results.length}`);
    logInfo(`可连接端口: ${successfulPorts.length}`);
    logInfo(`有响应端口: ${responsivePorts.length}`);

    if (successfulPorts.length > 0) {
      logSuccess('找到可用的 TCP 端口:');

      // 分类显示结果
      const ykwyPorts = successfulPorts.filter(r => r.isYkwyBackend);
      const unknownPorts = successfulPorts.filter(r => !r.isYkwyBackend);

      if (ykwyPorts.length > 0) {
        logSuccess('🎯 确认的 YKWY 后端服务:');
        ykwyPorts.forEach(result => {
          const status = result.dataReceived ? '✅ 有响应' : '⚠️  仅连接';
          logInfo(`  端口 ${result.port}: ${status} (${result.serviceType})`);
          if (result.summary) {
            logInfo(`    ${result.summary}`);
          }
          if (result.responses && result.responses.length > 0) {
            logInfo(`    最新响应: ${result.responses[result.responses.length - 1].substring(0, 100)}...`);
          }
        });
      }

      if (unknownPorts.length > 0) {
        logWarning('❓ 未确认的服务:');
        unknownPorts.forEach(result => {
          const status = result.dataReceived ? '✅ 有响应' : '⚠️  仅连接';
          logInfo(`  端口 ${result.port}: ${status}`);
          if (result.summary) {
            logInfo(`    ${result.summary}`);
          }
        });
      }
    } else {
      logWarning('未找到可用的 TCP 端口');
      logInfo('可能的原因:');
      logInfo('  1. TCP 服务未启动');
      logInfo('  2. 端口被防火墙阻止');
      logInfo('  3. 使用了不同的端口号');
      logInfo('  4. TCP 服务仅限内网访问');
    }

    // 显示详细结果
    console.log('\n详细测试结果:');
    results.forEach(result => {
      const status = result.success ?
        (result.dataReceived ? '✅ 成功+响应' : '⚠️  仅连接') :
        '❌ 失败';
      console.log(`  端口 ${result.port}: ${status}`);
      if (result.error) {
        console.log(`    错误: ${result.error}`);
      }
    });

    process.exit(successfulPorts.length > 0 ? 0 : 1);

  } catch (error) {
    console.log(`${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                        测试失败                              ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}`);

    logError(`测试失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testTcpPort, testAllTcpPorts, testTcpInfo };
