import type { Context } from 'hono';
import type { ZodIssue } from 'zod';

import { ErrorCode } from '../constants/errorCodes.ts';
import { AppError, ValidationError } from '../errors/custom.error.ts';
import { default as sendLogToLoki, formatErrorLog, formatValidationErrorLog } from '../utils/logger.ts';
import { R } from '../utils/Response.ts';

export const errorHandler = async (error: Error, c: Context) => {
  const { method, url } = c.req;
  const service = 'ykwy-api';
  const module = 'api';

  if (error instanceof ValidationError) {
    const statusCode = error.statusCode;
    const context = {
      request: `${method} ${url}`,
      errorCode: error.errorCode,
      statusCode,
      ...error.context,
    };
    const validationIssues = (error.context?.['validationIssues'] as ZodIssue[]) || [];
    const detailedLog = formatValidationErrorLog(validationIssues, context);
    const prefix = `${method} ${url} ${statusCode} | `;
    const logMsg = prefix + detailedLog;
    console.warn(`[验证异常] >> ${logMsg}`);
    await sendLogToLoki({ service, level: 'warn', module }, logMsg);
    return R.fail(c, error.message, statusCode);
  }

  if (error instanceof AppError) {
    const statusCode = error.statusCode;
    const context = {
      request: `${method} ${url}`,
      errorCode: error.errorCode,
      statusCode,
      ...error.context,
    };
    const detailedLog = formatErrorLog(error, context);
    const prefix = `${method} ${url} ${statusCode} | `;
    const logMsg = prefix + detailedLog;
    console.warn(`[业务异常] >> ${logMsg}`);
    await sendLogToLoki({ service, level: 'error', module }, logMsg);
    return R.fail(c, error.message, statusCode);
  }

  // JSON解析异常
  if (error.message.includes('JSON') || error.message.includes('parse') || error.message.includes('position')) {
    const statusCode = 400;
    const context = {
      request: `${method} ${url}`,
      errorType: 'JSON解析异常',
    };
    const detailedLog = formatErrorLog(error, context);
    const prefix = `${method} ${url} ${statusCode} | `;
    const logMsg = prefix + detailedLog;
    console.warn(`[JSON解析异常] >> ${logMsg}`);
    await sendLogToLoki({ service, level: 'warn', module }, logMsg);
    return R.fail(c, '请求体JSON格式错误，请检查JSON语法', statusCode, null);
  }

  // 系统异常
  const statusCode = 500;
  const context = {
    request: `${method} ${url}`,
    errorType: '系统异常',
  };
  const detailedLog = formatErrorLog(error, context);
  const prefix = `${method} ${url} ${statusCode} | `;
  const logMsg = prefix + detailedLog;
  console.error('[系统异常] >> 发生未处理的错误:', logMsg);
  await sendLogToLoki({ service, level: 'error', module }, logMsg);
  const unexpectedError = ErrorCode.G001_UNEXPECTED_ERROR;
  return R.fail(c, unexpectedError.message, statusCode, null);
};
