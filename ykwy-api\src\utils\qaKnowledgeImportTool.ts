import prisma from '../client/prisma.ts';
import type { QuestionAndAnswerInput } from '../types/request/questionAndAnswer';

/**
 * 问答知识库导入结果接口
 */
export interface QAImportResult {
  successCount: number;
  failCount: number;
  newCount: number;
  updatedCount: number;
  errors?: string[];
}

/**
 * 现有记录接口
 */
interface ExistingRecord {
  id: string;
  isDeleted: number;
}

/**
 * 创建操作接口
 */
interface CreateOperation extends QuestionAndAnswerInput {
  createdAt: Date;
  updatedAt: Date;
  isDeleted: number;
}

/**
 * 更新操作接口
 */
interface UpdateOperation {
  where: { id: string };
  data: QuestionAndAnswerInput & { updatedAt: Date };
}

/**
 * 复活操作接口
 */
interface ReviveOperation {
  where: { id: string };
  data: QuestionAndAnswerInput & { isDeleted: number; updatedAt: Date };
}

/**
 * 数据验证结果接口
 */
interface ValidationResult {
  validItems: QuestionAndAnswerInput[];
  errors: string[];
  failCount: number;
}

/**
 * 操作准备结果接口
 */
interface OperationPrepareResult {
  createOperations: CreateOperation[];
  updateOperations: UpdateOperation[];
  reviveOperations: ReviveOperation[];
  newCount: number;
  updatedCount: number;
}

/**
 * 问答知识库导入工具类
 * 提供问答知识库批量导入的通用方法
 */
export class QAKnowledgeImportTool {
  /**
   * 验证导入数据
   * @param importList 导入数据数组
   * @returns 验证结果
   */
  static validateImportData(importList: QuestionAndAnswerInput[]): ValidationResult {
    const validItems: QuestionAndAnswerInput[] = [];
    const errors: string[] = [];
    let failCount = 0;

    for (const item of importList) {
      if (!item.questionType || !item.orderStatus || !item.answers?.length || !item.categoryCode) {
        failCount++;
        errors.push(`缺少必填字段: ${JSON.stringify(item)}`);
        continue;
      }
      validItems.push(item);
    }

    return { validItems, errors, failCount };
  }

  /**
   * 生成记录的唯一键
   * @param item 导入数据项
   * @returns 唯一键字符串
   */
  static generateRecordKey(item: QuestionAndAnswerInput): string {
    return `${item.questionType}|${item.orderStatus}|${item.categoryCode}`;
  }

  /**
   * 批量查询现有记录
   * @param validItems 有效的导入数据项
   * @returns 现有记录映射表
   */
  static async findExistingRecords(validItems: QuestionAndAnswerInput[]): Promise<Map<string, ExistingRecord>> {
    const existingRecords = await prisma.questionAndAnswer.findMany({
      where: {
        OR: validItems.map((item) => ({
          questionType: item.questionType,
          orderStatus: item.orderStatus,
          categoryCode: item.categoryCode,
        })),
      },
      select: {
        id: true,
        questionType: true,
        orderStatus: true,
        categoryCode: true,
        isDeleted: true,
      },
    });

    const existingRecordMap = new Map<string, ExistingRecord>();
    for (const record of existingRecords) {
      const key = this.generateRecordKey({
        questionType: record.questionType,
        orderStatus: record.orderStatus,
        categoryCode: record.categoryCode,
        commonQuestionSamples: [],
        answers: [],
      });
      existingRecordMap.set(key, { id: record.id, isDeleted: record.isDeleted });
    }

    return existingRecordMap;
  }

  /**
   * 准备数据库操作
   * @param validItems 有效的导入数据项
   * @param existingRecordMap 现有记录映射表
   * @returns 操作准备结果
   */
  static prepareOperations(validItems: QuestionAndAnswerInput[], existingRecordMap: Map<string, ExistingRecord>): OperationPrepareResult {
    const createOperations: CreateOperation[] = [];
    const updateOperations: UpdateOperation[] = [];
    const reviveOperations: ReviveOperation[] = [];
    let newCount = 0;
    let updatedCount = 0;

    for (const item of validItems) {
      const key = this.generateRecordKey(item);
      const existingRecord = existingRecordMap.get(key);

      if (existingRecord) {
        if (existingRecord.isDeleted === 1) {
          // 软删除记录需要复活
          reviveOperations.push({
            where: { id: existingRecord.id },
            data: {
              ...(item as QuestionAndAnswerInput),
              isDeleted: 0,
              updatedAt: new Date(),
            },
          });
          updatedCount++;
        } else {
          // 现有记录需要更新
          updateOperations.push({
            where: { id: existingRecord.id },
            data: {
              ...(item as QuestionAndAnswerInput),
              updatedAt: new Date(),
            },
          });
          updatedCount++;
        }
      } else {
        // 新记录需要创建
        createOperations.push({
          ...(item as QuestionAndAnswerInput),
          createdAt: new Date(),
          updatedAt: new Date(),
          isDeleted: 0,
        });
        newCount++;
      }
    }

    return { createOperations, updateOperations, reviveOperations, newCount, updatedCount };
  }

  /**
   * 执行批量数据库操作
   * @param createOperations 创建操作数组
   * @param updateOperations 更新操作数组
   * @param reviveOperations 复活操作数组
   */
  static async executeBatchOperations(createOperations: CreateOperation[], updateOperations: UpdateOperation[], reviveOperations: ReviveOperation[]): Promise<void> {
    const operations: Array<ReturnType<typeof prisma.questionAndAnswer.createMany> | ReturnType<typeof prisma.questionAndAnswer.update>> = [];

    // 添加创建操作
    if (createOperations.length > 0) {
      operations.push(
        prisma.questionAndAnswer.createMany({
          data: createOperations,
          skipDuplicates: true,
        }),
      );
    }

    // 添加更新操作
    for (const updateOp of updateOperations) {
      operations.push(prisma.questionAndAnswer.update(updateOp));
    }

    // 添加复活操作
    for (const reviveOp of reviveOperations) {
      operations.push(prisma.questionAndAnswer.update(reviveOp));
    }

    // 在事务中执行所有操作
    if (operations.length > 0) {
      await prisma.$transaction(operations);
    }
  }

  /**
   * 逐条处理数据（备用方案）
   * @param validItems 有效的导入数据项
   * @param upsertMethod 单条数据处理方法
   * @returns 处理结果
   */
  static async processItemsIndividually(
    validItems: QuestionAndAnswerInput[],
    upsertMethod: (item: QuestionAndAnswerInput) => Promise<unknown>,
  ): Promise<{ successCount: number; failCount: number; errors: string[] }> {
    let successCount = 0;
    let failCount = 0;
    const errors: string[] = [];

    for (const item of validItems) {
      try {
        await upsertMethod(item);
        successCount++;
      } catch (itemError: unknown) {
        failCount++;
        errors.push(`单条处理失败: ${JSON.stringify(item)} - ${itemError instanceof Error ? itemError.message : '未知错误'}`);
      }
    }

    return { successCount, failCount, errors };
  }

  /**
   * 批量导入问答知识库的完整流程
   * @param importList 导入数据数组
   * @param fallbackUpsertMethod 单条数据处理方法（仅在批量操作失败时作为备用方案使用）
   * @returns 导入结果
   */
  static async batchImport(importList: QuestionAndAnswerInput[], fallbackUpsertMethod: (item: QuestionAndAnswerInput) => Promise<unknown>): Promise<QAImportResult> {
    // 1. 验证输入数据
    const { validItems, errors, failCount } = this.validateImportData(importList);

    if (validItems.length === 0) {
      return { successCount: 0, failCount, newCount: 0, updatedCount: 0, errors };
    }

    try {
      // 2. 查询现有记录
      const existingRecordMap = await this.findExistingRecords(validItems);

      // 3. 准备数据库操作
      const { createOperations, updateOperations, reviveOperations, newCount, updatedCount } = this.prepareOperations(validItems, existingRecordMap);

      // 4. 执行批量操作
      await this.executeBatchOperations(createOperations, updateOperations, reviveOperations);

      return {
        successCount: validItems.length,
        failCount,
        newCount,
        updatedCount,
        errors,
      };
    } catch (e: unknown) {
      // 5. 批量操作失败，回退到逐条处理
      const batchError = `批量操作失败，尝试逐条处理: ${e instanceof Error ? e.message : '未知错误'}`;
      errors.push(batchError);

      const { successCount, failCount: individualFailCount, errors: individualErrors } = await this.processItemsIndividually(validItems, fallbackUpsertMethod);

      return {
        successCount,
        failCount: failCount + individualFailCount,
        newCount: 0, // 逐条处理时无法准确统计新增和更新数量
        updatedCount: 0,
        errors: [...errors, ...individualErrors],
      };
    }
  }
}
