#!/usr/bin/env python3
"""
测试千牛API端点并完善数据解析
"""
import json
import urllib.request
import urllib.parse
import urllib.error
from typing import Dict, Any

# API配置
BASE_URL = "http://localhost:3002"
API_URL = f"{BASE_URL}/api/v1"

# 测试数据
CONNECTION_ID = "********-57c1-7353-b1eb-5aab4f0d0b42"
CUSTOMER_ID = "********-a6a9-7630-b124-a4f2684cffa5"
BUYER_ID = "*************"
TO_ID = "*************"
CCODE = "*************.1-*************.1#11001@cntaobao"
GROUP_ID = "*********"
ACCOUNT_ID = "**********"
ORDER_ID = "4646134045163268604"
ITEM_IDS = "[************, ************, ************]"
SEARCH_QUERY = "tb2494039180"
ACTIVITY_ID = "acfb71cd53684077a439a1a2f99a3ce3"

class QianniuAPITester:
    def __init__(self):
        self.results = {}

    def test_api(self, name: str, method: str, endpoint: str, data: Dict = None):
        """测试单个API端点"""
        url = f"{API_URL}{endpoint}"
        print(f"\n🧪 测试 {name}")
        print(f"📍 {method} {url}")

        try:
            if method.upper() == "GET":
                # GET请求
                if data:
                    query_string = urllib.parse.urlencode(data)
                    url = f"{url}?{query_string}"

                req = urllib.request.Request(url)
                req.add_header('Content-Type', 'application/json')

                with urllib.request.urlopen(req, timeout=30) as response:
                    status_code = response.getcode()
                    response_data = response.read().decode('utf-8')
            else:
                # POST请求
                json_data = json.dumps(data).encode('utf-8') if data else b''

                req = urllib.request.Request(url, data=json_data)
                req.add_header('Content-Type', 'application/json')

                with urllib.request.urlopen(req, timeout=30) as response:
                    status_code = response.getcode()
                    response_data = response.read().decode('utf-8')

            print(f"📊 状态码: {status_code}")

            if status_code == 200:
                result = json.loads(response_data)
                print(f"✅ 请求成功")

                # 格式化输出响应数据
                formatted_result = json.dumps(result, ensure_ascii=False, indent=2)
                if len(formatted_result) > 1000:
                    print(f"📦 响应数据 (前1000字符):\n{formatted_result[:1000]}...")
                else:
                    print(f"📦 响应数据:\n{formatted_result}")

                self.results[name] = {
                    "success": True,
                    "status_code": status_code,
                    "data": result
                }
            else:
                print(f"❌ 请求失败: {response_data}")
                self.results[name] = {
                    "success": False,
                    "status_code": status_code,
                    "error": response_data
                }

        except urllib.error.HTTPError as e:
            error_msg = e.read().decode('utf-8') if e.fp else str(e)
            print(f"❌ HTTP错误 {e.code}: {error_msg}")
            self.results[name] = {
                "success": False,
                "status_code": e.code,
                "error": error_msg
            }
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
            self.results[name] = {
                "success": False,
                "error": str(e)
            }

    def run_all_tests(self):
        """运行所有API测试"""
        print("🚀 开始测试千牛API端点...")

        # 健康检查
        self.test_api("健康检查", "GET", "/health", None)

        # 连接测试
        self.test_api("获取连接", "GET", "/qianniu-api/connections", None)
        self.test_api("获取客户列表", "GET", "/qianniu-api/customers",
                     {"connectionId": CONNECTION_ID})

        # 订单管理
        self.test_api("查询近期订单", "POST", "/qianniu-api/query-recent-orders", {
            "connectionId": CONNECTION_ID,
            "customerId": CUSTOMER_ID,
            "orderStatus": ""
        })

        self.test_api("查询历史订单", "POST", "/qianniu-api/query-history-orders", {
            "connectionId": CONNECTION_ID,
            "customerId": CUSTOMER_ID,
            "pageNum": 1,
            "pageSize": 10
        })

        self.test_api("查询订单物流", "POST", "/qianniu-api/query-order-logistics", {
            "connectionId": CONNECTION_ID,
            "bizOrderId": ORDER_ID
        })

        self.test_api("订单解密", "POST", "/qianniu-api/decrypt-order", {
            "connectionId": CONNECTION_ID,
            "tid": ORDER_ID,
            "bizType": "qianniu",
            "queryByTid": True
        })

        # 商品管理
        self.test_api("发送商品卡片", "POST", "/qianniu-api/send-item-card", {
            "connectionId": CONNECTION_ID,
            "customerId": CUSTOMER_ID,
            "batchItemIds": ITEM_IDS,
            "type": -1
        })

        self.test_api("查询商品记录", "POST", "/qianniu-api/query-item-record", {
            "connectionId": CONNECTION_ID,
            "customerId": CUSTOMER_ID
        })

        self.test_api("搜索店铺商品", "POST", "/qianniu-api/search-shop-items", {
            "connectionId": CONNECTION_ID,
            "customerId": CUSTOMER_ID,
            "pageSize": 8,
            "pageNo": 1,
            "keyWord": "",
            "sortKey": "sold",
            "desc": True,
            "type": 0,
            "queryGift": False
        })

        # 用户管理
        self.test_api("查询客户信息", "POST", "/qianniu-api/query-customer-info", {
            "connectionId": CONNECTION_ID,
            "customerId": CUSTOMER_ID
        })

        self.test_api("搜索买家ID", "POST", "/qianniu-api/search-buyer-id", {
            "connectionId": CONNECTION_ID,
            "searchQuery": SEARCH_QUERY
        })

        # 优惠券管理
        self.test_api("查询店铺优惠券", "POST", "/qianniu-api/query-shop-coupons", {
            "connectionId": CONNECTION_ID
        })

        # 客服操作
        self.test_api("获取店铺客服", "POST", "/qianniu-api/get-shop-customer-service", {
            "connectionId": CONNECTION_ID,
            "pageSize": 100
        })

        self.test_api("获取客服分组", "POST", "/qianniu-api/get-dispatch-groups", {
            "connectionId": CONNECTION_ID,
            "loginDomain": "cntaobao"
        })

        # 输出测试总结
        self.print_summary()

    def print_summary(self):
        """打印测试总结"""
        print("\n" + "="*80)
        print("📊 测试总结")
        print("="*80)

        success_count = sum(1 for result in self.results.values() if result.get("success", False))
        total_count = len(self.results)

        print(f"✅ 成功: {success_count}/{total_count}")
        print(f"❌ 失败: {total_count - success_count}/{total_count}")

        print("\n📋 详细结果:")
        for name, result in self.results.items():
            status = "✅" if result.get("success", False) else "❌"
            print(f"{status} {name}")
            if not result.get("success", False):
                error = result.get("error", "未知错误")
                print(f"   错误: {error}")

def main():
    tester = QianniuAPITester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
