import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator';
import { Hono } from 'hono';

import { prisma } from '../lib/db';
import { calculatePagination, createErrorResponse, createPaginatedResponse, createResponse, handleError } from '../lib/utils';
import { ConversationFilterSchema, ConversationIdParamSchema, CreateConversationSchema, IdParamSchema, PaginationSchema, UpdateConversationSchema } from '../lib/validations';

const conversationsRoute = new Hono();

// 获取对话列表
conversationsRoute.get('/', zValidator('query', PaginationSchema.merge(ConversationFilterSchema)), async (c) => {
  try {
    const { page, limit, ...filters } = c.req.valid('query');
    const { skip, take } = calculatePagination(page, limit);

    const where: Record<string, unknown> = {};

    // 应用过滤器
    if (filters.status) where['status'] = filters.status;
    if (filters.priority) where['priority'] = filters.priority;
    if (filters.assignedUserId) where['assignedUserId'] = filters.assignedUserId;
    if (filters.organizationId) where['organizationId'] = filters.organizationId;
    if (filters.qianniuAccountId) where['qianniuAccountId'] = filters.qianniuAccountId;
    if (filters.customerId) where['customerId'] = filters.customerId;

    if (filters.startDate || filters.endDate) {
      where['createdAt'] = {};
      if (filters.startDate) {
        (where['createdAt'] as { gte?: Date }).gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        (where['createdAt'] as { lte?: Date }).lte = new Date(filters.endDate);
      }
    }

    const [conversations, total] = await Promise.all([
      prisma.conversation.findMany({
        where: {
          ...where,
          customer: {
            nickname: { contains: filters.search },
          },
        },
        skip,
        take,
        include: {
          customer: {
            select: {
              id: true,
              nickname: true,
              avatar: true,
              vipLevel: true,
            },
          },
          qianniuAccount: {
            select: {
              accountName: true,
              platformType: true,
            },
          },
          assignedUser: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          messages: {
            orderBy: {
              sentAt: 'desc',
            },
            take: 1,
            select: {
              id: true,
              content: true,
              sentAt: true,
              senderType: true,
            },
          },
          _count: {
            select: {
              messages: true,
            },
          },
        },
        orderBy: {
          lastActivity: 'desc',
        },
      }),
      prisma.conversation.count({ where }),
    ]);

    return c.json(createPaginatedResponse(conversations, page, limit, total, '获取对话列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

// 获取单个对话详情
conversationsRoute.get('/:id', zValidator('param', ConversationIdParamSchema), async (c) => {
  try {
    const { id } = c.req.valid('param');

    // 只通过UUID格式的conversationId查找
    const conversation = await prisma.conversation.findUnique({
      where: { id },
      include: {
        customer: true,
        qianniuAccount: true,
        assignedUser: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            parentMessage: {
              select: {
                id: true,
                content: true,
                senderType: true,
              },
            },
          },
          orderBy: {
            sentAt: 'asc',
          },
        },
      },
    });

    if (!conversation) {
      return c.json(createErrorResponse('对话不存在'), 404);
    }

    return c.json(createResponse(conversation, '获取对话详情成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

// 创建对话
conversationsRoute.post('/', zValidator('json', CreateConversationSchema), async (c) => {
  try {
    const data = c.req.valid('json');

    // 获取客户信息以确定千牛账号和组织
    const customer = await prisma.customer.findUnique({
      where: { id: data.customerId },
      include: {
        qianniuAccount: {
          select: {
            id: true,
            client: {
              select: {
                organizationId: true,
              },
            },
          },
        },
      },
    });

    if (!customer) {
      return c.json(createErrorResponse('客户不存在'), 400);
    }

    const conversation = await prisma.conversation.create({
      data: {
        ...data,
        qianniuAccountId: customer.qianniuAccount.id,
        organizationId: customer.qianniuAccount.client.organizationId,
      },
      include: {
        customer: true,
        qianniuAccount: true,
      },
    });

    return c.json(createResponse(conversation, '创建对话成功'), 201);
  } catch (error) {
    return handleError(c, error);
  }
});

// 更新对话
conversationsRoute.put('/:id', zValidator('param', IdParamSchema), zValidator('json', UpdateConversationSchema), async (c) => {
  try {
    const { id } = c.req.valid('param');
    const data = c.req.valid('json');

    const conversation = await prisma.conversation.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      include: {
        customer: true,
        qianniuAccount: true,
        assignedUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return c.json(createResponse(conversation, '更新对话成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

export default conversationsRoute;
