"""
FastAPI应用
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional
import logging
import os

from ..core.agent import SalesAgent
from ..core.brand_manager import get_brand_manager
from ..utils.config import settings
from ..utils.logger import setup_detailed_logging, log_user_interaction, unified_logger
from ..utils.loki import loki_service

# 设置详细日志
setup_detailed_logging()
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="淘宝产品信息智能销售代理",
    description="基于LlamaIndex的AI产品销售助手",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 全局品牌管理器实例
brand_manager = None


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str
    brand_id: Optional[str] = "ykwy"  # 品牌ID，默认为易客无忧
    session_id: Optional[str] = None
    conversation_id: Optional[str] = None
    connection_id: Optional[str] = None  # 千牛连接ID
    customer_id: Optional[str] = None    # 客户ID
    buyer_nick: Optional[str] = None  # 客户昵称


class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str
    session_id: Optional[str] = None


class ConversationMessage(BaseModel):
    """对话消息模型"""
    role: str  # "customer" 或 "agent"
    content: str
    timestamp: Optional[str] = None


class RecommendationRequest(BaseModel):
    """推荐请求模型"""
    conversation_history: List[ConversationMessage]
    conversation_id: Optional[str] = None


class RecommendationResponse(BaseModel):
    """推荐响应模型"""
    recommendations: List[str]
    conversation_id: Optional[str] = None


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global brand_manager

    try:
        # 打印环境变量配置（调试用）
        print("\n" + "="*60)
        print("🔧 应用启动 - 环境变量检查")
        print("="*60)
        settings.print_all_env_vars()

        # 记录Loki服务状态
        if loki_service.is_enabled():
            print(f"✅ Loki日志服务已启用")
            print(f"   URL: {loki_service.url}")
            print(f"   用户名: {loki_service.username}")
            unified_logger.info('Loki日志服务已启用', {
                'service': 'loki',
                'url': loki_service.url,
                'server_name': loki_service.server_name
            })
        else:
            print(f"❌ Loki日志服务未启用")
            print(f"   LOKI_URL: {os.environ.get('LOKI_URL', '未设置')}")
            print(f"   LOKI_USERNAME: {os.environ.get('LOKI_USERNAME', '未设置')}")
            print(f"   LOKI_PASSWORD: {'已设置' if os.environ.get('LOKI_PASSWORD') else '未设置'}")
            unified_logger.warn('Loki日志服务未启用，请检查环境变量配置', {
                'service': 'loki',
                'required_vars': ['LOKI_URL', 'LOKI_USERNAME', 'LOKI_PASSWORD']
            })

        unified_logger.info("正在初始化品牌管理器...", {'component': 'brand_manager'})
        logger.info("正在初始化品牌管理器...")
        brand_manager = get_brand_manager()

        unified_logger.info("品牌管理器初始化完成", {
            'component': 'brand_manager',
            'status': 'ready',
            'brands': brand_manager.get_available_brands()
        })
        logger.info(f"品牌管理器初始化完成，支持品牌: {brand_manager.get_available_brands()}")

    except Exception as e:
        unified_logger.error("初始化品牌管理器失败", {'component': 'brand_manager'}, e)
        logger.error(f"初始化品牌管理器失败: {e}")
        raise


@app.get("/")
async def root():
    """根路径 - 返回Web界面"""
    if os.path.exists("static/index.html"):
        return FileResponse("static/index.html")
    return {
        "message": "淘宝产品信息智能销售代理API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "brand_manager_ready": brand_manager is not None,
        "available_brands": brand_manager.get_available_brands() if brand_manager else []
    }


@app.get("/brands")
async def get_brands():
    """获取所有可用品牌信息"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    brands = []
    for brand_id in brand_manager.get_available_brands():
        brand_info = brand_manager.get_brand_info(brand_id)
        brands.append({
            "brand_id": brand_id,
            **brand_info
        })

    return {"brands": brands}


@app.get("/brands/{brand_id}")
async def get_brand_info(brand_id: str):
    """获取指定品牌信息"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    brand_info = brand_manager.get_brand_info(brand_id)
    if not brand_info or brand_info.get('name') == '未知品牌':
        raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

    return {
        "brand_id": brand_id,
        **brand_info
    }


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口（支持多品牌）"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        logger.info("🌐 [API] 收到聊天请求")
        logger.info(f"   🏢 品牌ID: {request.brand_id}")
        logger.info(f"   📝 消息: {request.message}")
        logger.info(f"   🆔 会话ID: {request.session_id}")
        logger.info(f"   🔗 连接ID: {request.connection_id}")
        logger.info(f"   👤 客户ID: {request.customer_id}")
        logger.info(f"   📛 客户昵称: {request.buyer_nick}")

        # 使用品牌管理器处理消息
        response = await brand_manager.chat(
            brand_id=request.brand_id,
            message=request.message,
            conversation_id=request.conversation_id,
            connection_id=request.connection_id,
            customer_id=request.customer_id,
            customer_nick=request.buyer_nick
        )

        # 记录用户交互
        log_user_interaction(f"[{request.brand_id}] {request.message}", response)

        return ChatResponse(
            response=response,
            session_id=request.session_id
        )

    except Exception as e:
        logger.error(f"❌ [API] 聊天处理错误: {e}")
        raise HTTPException(status_code=500, detail=f"处理聊天消息时出错: {str(e)}")


@app.get("/conversation/{conversation_id}/history")
async def get_conversation_history(conversation_id: str, brand_id: str = "ykwy"):
    """获取对话历史"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        history = brand_manager.get_conversation_history(brand_id, conversation_id)
        return {
            "conversation_id": conversation_id,
            "brand_id": brand_id,
            "message_count": len(history),
            "history": history
        }

    except Exception as e:
        logger.error(f"获取对话历史错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话历史时出错: {str(e)}")


@app.post("/conversation/{conversation_id}/reset")
async def reset_conversation(conversation_id: str, brand_id: str = "ykwy"):
    """重置对话记忆"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        success = brand_manager.reset_conversation(brand_id, conversation_id)
        if success:
            return {"message": f"品牌 {brand_id} 的对话 {conversation_id} 记忆已重置"}
        else:
            raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

    except Exception as e:
        logger.error(f"重置对话记忆错误: {e}")
        raise HTTPException(status_code=500, detail=f"重置对话记忆时出错: {str(e)}")


@app.post("/recommendations", response_model=RecommendationResponse)
async def generate_recommendations(request: RecommendationRequest):
    """生成客服推荐回复

    基于对话历史，生成3个推荐的客服回复，指导客服下一步应该说什么
    """
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        logger.info("🤖 [API] 收到推荐生成请求")
        logger.info(f"   🆔 会话ID: {request.conversation_id}")
        logger.info(f"   📝 对话历史长度: {len(request.conversation_history)}")

        # 构建对话上下文
        conversation_context = []
        for msg in request.conversation_history:
            role_prefix = "客户" if msg.role == "customer" else "客服"
            conversation_context.append(f"{role_prefix}: {msg.content}")

        context_text = "\n".join(conversation_context)

        # 构建推荐生成的提示词
        recommendation_prompt = f"""
基于以下对话历史，请生成3个不同的客服回复建议，指导客服下一步应该说什么。

对话历史：
{context_text}

请生成3个专业、有帮助的客服回复建议，每个建议应该：
1. 针对当前对话情况
2. 提供有价值的信息或解决方案
3. 保持友好专业的语调
4. 推进对话向积极方向发展

请直接返回3个建议，每个建议一行，不需要编号或其他格式。
"""

        # 生成3个不同的推荐
        recommendations = []
        for i in range(3):
            try:
                # 为每个推荐使用稍微不同的会话ID以获得多样性
                session_id = f"{request.conversation_id}_rec_{i}" if request.conversation_id else f"rec_{i}"
                # 使用默认品牌（易客无忧）生成推荐
                response = await brand_manager.chat("ykwy", recommendation_prompt, conversation_id=session_id)

                # 清理和处理响应
                cleaned_response = response.strip()
                if cleaned_response:
                    recommendations.append(cleaned_response)

            except Exception as e:
                logger.warning(f"生成推荐 {i+1} 失败: {e}")
                # 添加默认推荐
                default_recommendations = [
                    "感谢您的咨询，我来为您详细解答这个问题。",
                    "我理解您的关切，让我为您提供更多相关信息。",
                    "根据您的情况，我建议您可以考虑以下解决方案。"
                ]
                if i < len(default_recommendations):
                    recommendations.append(default_recommendations[i])

        # 确保至少有3个推荐
        while len(recommendations) < 3:
            recommendations.extend([
                "感谢您的咨询，我来为您详细解答这个问题。",
                "我理解您的关切，让我为您提供更多相关信息。",
                "根据您的情况，我建议您可以考虑以下解决方案。"
            ])

        # 只取前3个
        recommendations = recommendations[:3]

        logger.info(f"✅ [API] 成功生成 {len(recommendations)} 个推荐")

        return RecommendationResponse(
            recommendations=recommendations,
            conversation_id=request.conversation_id
        )

    except Exception as e:
        logger.error(f"❌ [API] 推荐生成错误: {e}")
        raise HTTPException(status_code=500, detail=f"生成推荐时出错: {str(e)}")


@app.post("/rebuild-index")
async def rebuild_index(brand_id: str = "ykwy"):
    """重建索引"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        logger.info(f"开始重建品牌 {brand_id} 的索引...")
        agent = brand_manager.get_agent(brand_id)
        if not agent:
            raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

        agent.build_index(force_rebuild=True)
        agent.initialize_agent()
        logger.info(f"品牌 {brand_id} 索引重建完成")

        return {"message": f"品牌 {brand_id} 索引重建完成"}

    except Exception as e:
        logger.error(f"重建索引错误: {e}")
        raise HTTPException(status_code=500, detail=f"重建索引时出错: {str(e)}")


@app.get("/debug/dialogues")
async def get_debug_dialogues():
    """获取所有对话数据用于调试"""
    try:
        import json
        from pathlib import Path

        dialogues = []
        data_dir = Path("data")

        # 遍历所有产品目录
        for product_dir in ["ai-ppt-maker", "ai-video-creator", "ai-writing-assistant"]:
            dialogue_file = data_dir / product_dir / "customer-dialogues.json"
            if dialogue_file.exists():
                with open(dialogue_file, 'r', encoding='utf-8') as f:
                    product_dialogues = json.load(f)

                for dialogue in product_dialogues:
                    dialogues.append({
                        "id": dialogue["dialogueId"],
                        "product": product_dir,
                        "customerType": dialogue["customerType"],
                        "dialogue": dialogue["dialogue"]
                    })

        return {"dialogues": dialogues}

    except Exception as e:
        logger.error(f"获取对话数据错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话数据时出错: {str(e)}")


class ResetRequest(BaseModel):
    """重置请求模型"""
    conversation_id: Optional[str] = None


@app.post("/reset")
async def reset_conversation(request: ResetRequest = None):
    """重置对话历史"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        conversation_id = request.conversation_id if request else None
        brand_id = "ykwy"  # 默认使用易客无忧品牌

        success = brand_manager.reset_conversation(brand_id, conversation_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

        if conversation_id:
            logger.info(f"🔄 对话历史已重置: {conversation_id}")
            return {"message": f"对话已重置: {conversation_id}"}
        else:
            logger.info("🔄 所有对话历史已重置")
            return {"message": "所有对话已重置"}

    except Exception as e:
        logger.error(f"重置对话错误: {e}")
        raise HTTPException(status_code=500, detail=f"重置对话时出错: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.sales_agent.api.app:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
