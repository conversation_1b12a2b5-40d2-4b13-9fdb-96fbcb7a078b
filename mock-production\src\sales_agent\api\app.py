"""
FastAPI应用
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional
import logging
import os

from ..core.agent import SalesAgent
from ..core.brand_manager import get_brand_manager
from ..utils.config import settings
from ..utils.logger import setup_detailed_logging, log_user_interaction, unified_logger
from ..utils.loki import loki_service

# 设置详细日志
setup_detailed_logging()
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="淘宝产品信息智能销售代理",
    description="基于LlamaIndex的AI产品销售助手",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 全局品牌管理器实例
brand_manager = None


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str
    brand_id: Optional[str] = "ykwy"  # 品牌ID，默认为易客无忧
    session_id: Optional[str] = None
    conversation_id: Optional[str] = None
    connection_id: Optional[str] = None  # 千牛连接ID
    customer_id: Optional[str] = None    # 客户ID
    buyer_nick: Optional[str] = None  # 客户昵称


class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str
    session_id: Optional[str] = None


class ConversationMessage(BaseModel):
    """对话消息模型"""
    role: str  # "customer" 或 "agent"
    content: str
    timestamp: Optional[str] = None


class RecommendationRequest(BaseModel):
    """推荐请求模型"""
    conversation_history: List[ConversationMessage]
    conversation_id: Optional[str] = None
    brand_id: Optional[str] = "ykwy"  # 品牌ID，默认为易客无忧


class RecommendationResponse(BaseModel):
    """推荐响应模型"""
    recommendations: List[str]
    conversation_id: Optional[str] = None


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global brand_manager

    try:
        # 打印环境变量配置（调试用）
        print("\n" + "="*60)
        print("🔧 应用启动 - 环境变量检查")
        print("="*60)
        settings.print_all_env_vars()

        # 记录Loki服务状态
        if loki_service.is_enabled():
            print(f"✅ Loki日志服务已启用")
            print(f"   URL: {loki_service.url}")
            print(f"   用户名: {loki_service.username}")
            unified_logger.info('Loki日志服务已启用', {
                'service': 'loki',
                'url': loki_service.url,
                'server_name': loki_service.server_name
            })
        else:
            print(f"❌ Loki日志服务未启用")
            print(f"   LOKI_URL: {os.environ.get('LOKI_URL', '未设置')}")
            print(f"   LOKI_USERNAME: {os.environ.get('LOKI_USERNAME', '未设置')}")
            print(f"   LOKI_PASSWORD: {'已设置' if os.environ.get('LOKI_PASSWORD') else '未设置'}")
            unified_logger.warn('Loki日志服务未启用，请检查环境变量配置', {
                'service': 'loki',
                'required_vars': ['LOKI_URL', 'LOKI_USERNAME', 'LOKI_PASSWORD']
            })

        unified_logger.info("正在初始化品牌管理器...", {'component': 'brand_manager'})
        logger.info("正在初始化品牌管理器...")
        brand_manager = get_brand_manager()

        unified_logger.info("品牌管理器初始化完成", {
            'component': 'brand_manager',
            'status': 'ready',
            'brands': brand_manager.get_available_brands()
        })
        logger.info(f"品牌管理器初始化完成，支持品牌: {brand_manager.get_available_brands()}")

    except Exception as e:
        unified_logger.error("初始化品牌管理器失败", {'component': 'brand_manager'}, e)
        logger.error(f"初始化品牌管理器失败: {e}")
        raise


@app.get("/")
async def root():
    """根路径 - 返回Web界面"""
    if os.path.exists("static/index.html"):
        return FileResponse("static/index.html")
    return {
        "message": "淘宝产品信息智能销售代理API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "brand_manager_ready": brand_manager is not None,
        "available_brands": brand_manager.get_available_brands() if brand_manager else []
    }


@app.get("/brands")
async def get_brands():
    """获取所有可用品牌信息"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    brands = []
    for brand_id in brand_manager.get_available_brands():
        brand_info = brand_manager.get_brand_info(brand_id)
        brands.append({
            "brand_id": brand_id,
            **brand_info
        })

    return {"brands": brands}


@app.get("/brands/{brand_id}")
async def get_brand_info(brand_id: str):
    """获取指定品牌信息"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    brand_info = brand_manager.get_brand_info(brand_id)
    if not brand_info or brand_info.get('name') == '未知品牌':
        raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

    return {
        "brand_id": brand_id,
        **brand_info
    }


@app.get("/cache/stats")
async def get_cache_stats():
    """获取缓存统计信息"""
    try:
        from ..utils.recommendation_cache import recommendation_cache
        stats = recommendation_cache.get_cache_stats()
        return {
            "cache_stats": stats,
            "status": "healthy"
        }
    except Exception as e:
        logger.error(f"❌ 获取缓存统计失败: {e}")
        return {
            "cache_stats": {},
            "status": "error",
            "error": str(e)
        }


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口（支持多品牌）"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        logger.info("🌐 [API] 收到聊天请求")
        logger.info(f"   🏢 品牌ID: {request.brand_id}")
        logger.info(f"   📝 消息: {request.message}")
        logger.info(f"   🆔 会话ID: {request.session_id}")
        logger.info(f"   🔗 连接ID: {request.connection_id}")
        logger.info(f"   👤 客户ID: {request.customer_id}")
        logger.info(f"   📛 客户昵称: {request.buyer_nick}")

        # 使用品牌管理器处理消息
        response = await brand_manager.chat(
            brand_id=request.brand_id,
            message=request.message,
            conversation_id=request.conversation_id,
            connection_id=request.connection_id,
            customer_id=request.customer_id,
            customer_nick=request.buyer_nick
        )

        # 记录用户交互
        log_user_interaction(f"[{request.brand_id}] {request.message}", response)

        return ChatResponse(
            response=response,
            session_id=request.session_id
        )

    except Exception as e:
        logger.error(f"❌ [API] 聊天处理错误: {e}")
        raise HTTPException(status_code=500, detail=f"处理聊天消息时出错: {str(e)}")


@app.get("/conversation/{conversation_id}/history")
async def get_conversation_history(conversation_id: str, brand_id: str = "ykwy"):
    """获取对话历史"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        history = brand_manager.get_conversation_history(brand_id, conversation_id)
        return {
            "conversation_id": conversation_id,
            "brand_id": brand_id,
            "message_count": len(history),
            "history": history
        }

    except Exception as e:
        logger.error(f"获取对话历史错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话历史时出错: {str(e)}")


@app.post("/conversation/{conversation_id}/reset")
async def reset_conversation(conversation_id: str, brand_id: str = "ykwy"):
    """重置对话记忆"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        success = brand_manager.reset_conversation(brand_id, conversation_id)
        if success:
            return {"message": f"品牌 {brand_id} 的对话 {conversation_id} 记忆已重置"}
        else:
            raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

    except Exception as e:
        logger.error(f"重置对话记忆错误: {e}")
        raise HTTPException(status_code=500, detail=f"重置对话记忆时出错: {str(e)}")


@app.post("/recommendations", response_model=RecommendationResponse)
async def generate_recommendations(request: RecommendationRequest):
    """生成客服推荐回复

    基于对话历史，调用智能体生成3个推荐的客服回复，指导客服下一步应该说什么
    """
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        import time

        start_time = time.time()
        logger.info("🤖 [API] 收到推荐生成请求")
        logger.info(f"   🏢 品牌ID: {request.brand_id}")
        logger.info(f"   🆔 会话ID: {request.conversation_id}")
        logger.info(f"   📝 对话历史长度: {len(request.conversation_history)}")

        # 如果对话历史为空，返回默认推荐
        if not request.conversation_history:
            logger.info("📋 [API] 对话历史为空，返回默认推荐")
            return RecommendationResponse(
                recommendations=[
                    "您好！有什么可以帮助您的吗？",
                    "欢迎咨询，我来为您详细介绍我们的产品。",
                    "感谢您的关注，请告诉我您的具体需求。"
                ],
                conversation_id=request.conversation_id
            )

        # 构建对话上下文，只取最近的几条消息
        recent_messages = request.conversation_history[-6:] if len(request.conversation_history) > 6 else request.conversation_history

        # 获取最后一条客户消息作为当前用户输入
        last_customer_message = None
        for msg in reversed(recent_messages):
            if msg.role == "customer":
                last_customer_message = msg.content
                break

        if not last_customer_message:
            logger.info("📋 [API] 未找到客户消息，返回默认推荐")
            return RecommendationResponse(
                recommendations=[
                    "感谢您的咨询，我来为您详细解答这个问题。",
                    "我理解您的关切，让我为您提供更多相关信息。",
                    "根据您的情况，我建议您可以考虑以下解决方案。"
                ],
                conversation_id=request.conversation_id
            )

        # 构建推荐生成的特殊提示词
        recommendation_prompt = f"""
请基于客户的这条消息生成3个不同的客服回复建议：

客户消息：{last_customer_message}

要求：
1. 生成3个不同角度的专业客服回复
2. 每个回复要简洁实用，1-2句话
3. 要体现专业性和服务意识
4. 直接返回3个建议，用换行分隔，不要编号

请现在生成3个推荐回复：
"""

        try:
            # 使用临时会话ID，不保存记忆
            temp_session_id = f"recommendation_temp_{int(time.time())}"

            # 调用指定品牌的智能体生成推荐
            response = await brand_manager.chat(
                brand_id=request.brand_id,
                message=recommendation_prompt,
                conversation_id=temp_session_id
            )

            # 解析响应，提取3个推荐
            recommendations = []
            if response and response.strip():
                lines = [line.strip() for line in response.strip().split('\n') if line.strip()]
                # 过滤掉可能的编号或格式符号
                for line in lines:
                    clean_line = line
                    # 移除可能的编号前缀
                    if clean_line.startswith(('1.', '2.', '3.', '1、', '2、', '3、', '- ', '• ')):
                        clean_line = clean_line[2:].strip()
                    elif clean_line.startswith(('1)', '2)', '3)')):
                        clean_line = clean_line[2:].strip()

                    if clean_line and len(clean_line) > 5:  # 过滤太短的回复
                        recommendations.append(clean_line)

                    if len(recommendations) >= 3:
                        break

            # 如果生成的推荐不足3个，用默认推荐补充
            default_recommendations = [
                "感谢您的咨询，我来为您详细解答这个问题。",
                "我理解您的关切，让我为您提供更多相关信息。",
                "根据您的情况，我建议您可以考虑以下解决方案。"
            ]

            while len(recommendations) < 3:
                for default in default_recommendations:
                    if default not in recommendations:
                        recommendations.append(default)
                        if len(recommendations) >= 3:
                            break

            recommendations = recommendations[:3]

            elapsed_time = time.time() - start_time
            logger.info(f"✅ [API] 智能推荐生成完成，耗时: {elapsed_time:.3f}s")

            return RecommendationResponse(
                recommendations=recommendations,
                conversation_id=request.conversation_id
            )

        except Exception as e:
            logger.warning(f"⚠️ [API] 智能推荐生成失败: {e}，返回默认推荐")
            return RecommendationResponse(
                recommendations=[
                    "感谢您的咨询，我来为您详细解答这个问题。",
                    "我理解您的关切，让我为您提供更多相关信息。",
                    "根据您的情况，我建议您可以考虑以下解决方案。"
                ],
                conversation_id=request.conversation_id
            )

    except Exception as e:
        logger.error(f"❌ [API] 推荐生成错误: {e}")
        raise HTTPException(status_code=500, detail=f"生成推荐时出错: {str(e)}")


@app.post("/rebuild-index")
async def rebuild_index(brand_id: str = "ykwy"):
    """重建索引"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        logger.info(f"开始重建品牌 {brand_id} 的索引...")
        agent = brand_manager.get_agent(brand_id)
        if not agent:
            raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

        agent.build_index(force_rebuild=True)
        agent.initialize_agent()
        logger.info(f"品牌 {brand_id} 索引重建完成")

        return {"message": f"品牌 {brand_id} 索引重建完成"}

    except Exception as e:
        logger.error(f"重建索引错误: {e}")
        raise HTTPException(status_code=500, detail=f"重建索引时出错: {str(e)}")


@app.get("/debug/dialogues")
async def get_debug_dialogues():
    """获取所有对话数据用于调试"""
    try:
        import json
        from pathlib import Path

        dialogues = []
        data_dir = Path("data")

        # 遍历所有产品目录
        for product_dir in ["ai-ppt-maker", "ai-video-creator", "ai-writing-assistant"]:
            dialogue_file = data_dir / product_dir / "customer-dialogues.json"
            if dialogue_file.exists():
                with open(dialogue_file, 'r', encoding='utf-8') as f:
                    product_dialogues = json.load(f)

                for dialogue in product_dialogues:
                    dialogues.append({
                        "id": dialogue["dialogueId"],
                        "product": product_dir,
                        "customerType": dialogue["customerType"],
                        "dialogue": dialogue["dialogue"]
                    })

        return {"dialogues": dialogues}

    except Exception as e:
        logger.error(f"获取对话数据错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话数据时出错: {str(e)}")


class ResetRequest(BaseModel):
    """重置请求模型"""
    conversation_id: Optional[str] = None


@app.post("/reset")
async def reset_conversation(request: ResetRequest = None):
    """重置对话历史"""
    if not brand_manager:
        raise HTTPException(status_code=500, detail="品牌管理器未初始化")

    try:
        conversation_id = request.conversation_id if request else None
        brand_id = "ykwy"  # 默认使用易客无忧品牌

        success = brand_manager.reset_conversation(brand_id, conversation_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"未找到品牌 {brand_id}")

        if conversation_id:
            logger.info(f"🔄 对话历史已重置: {conversation_id}")
            return {"message": f"对话已重置: {conversation_id}"}
        else:
            logger.info("🔄 所有对话历史已重置")
            return {"message": "所有对话已重置"}

    except Exception as e:
        logger.error(f"重置对话错误: {e}")
        raise HTTPException(status_code=500, detail=f"重置对话时出错: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.sales_agent.api.app:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
