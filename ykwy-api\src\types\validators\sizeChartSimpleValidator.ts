import { z } from 'zod';

/**
 * SizeChartSimple 验证器
 */

export const createSizeChartSimpleSchema = z.object({
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100个字符'),
  sizeRange: z.string().min(1, '尺码范围不能为空').max(200, '尺码范围长度不能超过200个字符'),
  sizeValue: z.string().min(1, '尺码值不能为空').max(200, '尺码值长度不能超过200个字符'),
});

export const updateSizeChartSimpleSchema = z.object({
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100个字符').optional(),
  sizeRange: z.string().min(1, '尺码范围不能为空').max(200, '尺码范围长度不能超过200个字符').optional(),
  sizeValue: z.string().min(1, '尺码值不能为空').max(200, '尺码值长度不能超过200个字符').optional(),
});

export const getSizeChartSimpleListSchema = z.object({
  page: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 1)),
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 10)),
  name: z.string().optional(),
  sizeRange: z.string().optional(),
  sizeValue: z.string().optional(),
});

export const getSizeChartSimpleByIdSchema = z.object({
  id: z.string().uuid('无效的ID格式'),
});

export const deleteSizeChartSimpleSchema = z.object({
  id: z.string().uuid('无效的ID格式'),
});

export const checkNameUniqueSchema = z.object({
  name: z.string().min(1, '名称不能为空'),
  excludeId: z.string().uuid().optional(),
});

export type CreateSizeChartSimpleInput = z.infer<typeof createSizeChartSimpleSchema>;
export type UpdateSizeChartSimpleInput = z.infer<typeof updateSizeChartSimpleSchema>;
export type GetSizeChartSimpleListInput = z.infer<typeof getSizeChartSimpleListSchema>;
export type GetSizeChartSimpleByIdInput = z.infer<typeof getSizeChartSimpleByIdSchema>;
export type DeleteSizeChartSimpleInput = z.infer<typeof deleteSizeChartSimpleSchema>;
export type CheckNameUniqueInput = z.infer<typeof checkNameUniqueSchema>;
