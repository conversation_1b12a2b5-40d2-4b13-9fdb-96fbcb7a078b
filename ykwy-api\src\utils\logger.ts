import Bun from 'bun';
import type { ZodIssue } from 'zod';

const lokiUrl = Bun.env['LOKI_URL']; // 存放到.env 文件中
const username = Bun.env['LOKI_USERNAME'];
const password = Bun.env['LOKI_PASSWORD'];
// const sererName = 'ykwy-api'; // 替换为你 package.json 中的 name

type StreamOptions = {
  service: string;
  level: 'info' | 'debug' | 'warn' | 'error';
  [key: string]: string | number | boolean; // 其他可选字段
};

/**
 * 格式化错误日志，使其更易读
 */
export function formatErrorLog(error: unknown, context?: Record<string, unknown>): string {
  const timestamp = new Date().toISOString();

  let name = 'UnknownError';
  let message: string;
  let stack: string | undefined = undefined;

  if (error instanceof Error) {
    name = error.name;
    message = error.message;
    stack = error.stack;
  } else {
    message = typeof error === 'string' ? error : JSON.stringify(error);
  }

  const errorInfo = {
    timestamp,
    error: { name, message, stack },
    context: context ?? {},
  };

  return JSON.stringify(errorInfo, null, 2);
}

/**
 * 格式化验证错误日志
 */
export function formatValidationErrorLog(validationIssues: ZodIssue[], context?: Record<string, unknown>): string {
  const validationDetails = validationIssues.map((issue) => ({
    field: issue.path.join('.'),
    message: issue.message,
    code: issue.code,
  }));

  const errorInfo = {
    timestamp: new Date().toISOString(),
    errorType: 'ValidationError',
    validationIssues: validationDetails,
    context: context || {},
  };

  return JSON.stringify(errorInfo, null, 2);
}

async function sendLogToLoki(streamData: StreamOptions, logMessage: string | string[]) {
  if (!lokiUrl || !username || !password) {
    console.error('Loki URL, username, or password is not set in environment variables.');
    return;
  }

  const timestamp = (Date.now() * 1000000).toString(); // 纳秒时间戳

  let streamValues = [[timestamp, logMessage]];
  if (Array.isArray(logMessage)) {
    streamValues = [];
    for (const msg of logMessage) {
      streamValues.push([timestamp, msg]);
    }
  }

  const logData = { streams: [{ stream: streamData, values: streamValues }] };

  try {
    const authorization = `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`;
    const response = await fetch(lokiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'X-Scope-OrgID': 'fake', Authorization: authorization },
      body: JSON.stringify(logData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  } catch (error) {
    console.error('发送日志时出错:', error);
  }
}

export default sendLogToLoki;

// sendLogToLoki(
//   {
//     service: serverName,
//     level: 'info',
//     module: 'user-behavior',
//     // "request_id": "req-7a3b4c5d"
//     // "env": "production",
//     // "region": "us-west-2"
//   },
//   '第一行错误消息\n第二行堆栈跟踪\n第三行详细信息',
// );
//
// sendLogToLoki(
//   {
//     service: serverName,
//     level: 'warn',
//     module: 'user-behavior',
//     request_id: 'req-7a3b4c5d',
//     env: 'production',
//     region: 'us-west-2',
//   },
//   ['第一行错误消息', '第二条日志', '第二条日志的详细信息'],
// );
