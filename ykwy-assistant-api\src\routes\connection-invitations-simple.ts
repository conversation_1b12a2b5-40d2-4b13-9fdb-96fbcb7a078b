import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { canManageTeam, checkPermission, getCurrentUser, getUserOrganizationId } from '../lib/auth-utils';
import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { createErrorResponse, createResponse, handleError } from '../lib/utils';
import { cdnService } from '../services/cdn.service';
import { generateInjectionScript } from './cdn-inject';

const app = new Hono();

// 验证Schema
const CreateInvitationSchema = z.object({
  name: z.string().min(1, '连接名称不能为空'),
  description: z.string().optional(),
  teamId: z.string().uuid('无效的团队ID'),
  expiresInDays: z.number().min(1).max(30).default(7), // 默认7天过期
});

// 绑定用户Schema
const BindUserSchema = z.object({
  userId: z.string().uuid('无效的用户ID'),
});

const IdParamSchema = z.object({
  id: z.string().uuid('无效的邀请ID'),
});

const RegenerateInvitationSchema = z.object({
  expiresInDays: z.number().min(1).max(30).default(7),
});

/**
 * 创建连接邀请
 */
app.post('/', zValidator('json', CreateInvitationSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：团队管理员及以上可以创建邀请
    if (!checkPermission(user.role, 'TEAM_MANAGER')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    const data = c.req.valid('json');

    // 验证团队是否属于用户的组织
    const team = await prisma.team.findFirst({
      where: {
        id: data.teamId,
        organizationId: organizationId,
      },
    });

    if (!team) {
      return c.json(createErrorResponse('团队不存在或无权限'), 404);
    }

    // 如果是团队管理员，检查是否管理该团队
    if (user.role === 'TEAM_MANAGER') {
      const canManage = await canManageTeam(user.id, data.teamId);
      if (!canManage) {
        return c.json(createErrorResponse('无权限管理该团队'), 403);
      }
    }

    // 创建连接邀请
    const invitation = await prisma.connectionInvitation.create({
      data: {
        name: data.name,
        description: data.description,
        teamId: data.teamId,
        organizationId: organizationId as string, // 已经检查过非空
        expiresAt: new Date(Date.now() + data.expiresInDays * 24 * 60 * 60 * 1000),
        status: 'PENDING',
      },
      include: {
        team: {
          select: {
            id: true,
            name: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // 生成CDN注入脚本
    logger.info('开始生成连接邀请的注入脚本', {
      invitationId: invitation.id,
      organizationId: invitation.organizationId,
      teamId: invitation.teamId,
      invitationName: invitation.name,
      scriptType: 'qianniu_injection',
    });

    const scriptGenerationStartTime = Date.now();
    const injectionScript = generateInjectionScript(invitation);
    const scriptGenerationDuration = Date.now() - scriptGenerationStartTime;

    logger.info('注入脚本生成完成', {
      invitationId: invitation.id,
      scriptLength: injectionScript.length,
      scriptLengthKB: Math.round((injectionScript.length / 1024) * 100) / 100,
      generationDuration: `${scriptGenerationDuration}ms`,
      scriptPreview: injectionScript.substring(0, 200) + '...',
      containsInvitationId: injectionScript.includes(invitation.id),
      containsWebSocket: injectionScript.includes('WebSocket') || injectionScript.includes('ws://') || injectionScript.includes('wss://'),
    });

    let cdnUrl: string | undefined;

    try {
      logger.info('开始上传脚本到 CDN', {
        invitationId: invitation.id,
        scriptSize: injectionScript.length,
        uploadStartTime: new Date().toISOString(),
      });

      const cdnUploadStartTime = Date.now();
      const cdnResult = await cdnService.uploadScript(invitation.id, injectionScript);
      const cdnUploadDuration = Date.now() - cdnUploadStartTime;

      cdnUrl = cdnResult.cdnUrl;

      logger.info('CDN 脚本上传成功', {
        invitationId: invitation.id,
        cdnUrl,
        uploadDuration: `${cdnUploadDuration}ms`,
        uploadedSize: cdnResult.size,
        uploadTime: cdnResult.uploadTime.toISOString(),
        nextStep: '脚本现在可通过 CDN URL 访问',
      });
    } catch (error) {
      const errorDetails = {
        invitationId: invitation.id,
        organizationId: invitation.organizationId,
        teamId: invitation.teamId,
        scriptSize: injectionScript.length,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : String(error),
        fallbackPlan: '将使用备用方案，通过直接 API 访问提供脚本',
        impact: 'CDN 加速不可用，但功能仍然正常',
      };

      logger.error('CDN 脚本上传失败，启用备用方案', errorDetails, error instanceof Error ? error : new Error(String(error)));

      // 记录备用方案的使用
      logger.warn('启用脚本访问备用方案', {
        invitationId: invitation.id,
        backupMethod: 'direct_api_access',
        backupUrl: `/cdn-inject/qianniu-inject/${invitation.id}.js`,
        recommendation: '建议检查 CDN 配置和网络连接',
      });
    }

    const response = {
      invitation,
      cdnUrl,
      deploymentInstructions: {
        method1: {
          title: '方法1: 一键注入（推荐）',
          command: `curl -fsSL ${Bun.env['CDN_BASE_URL']}/qianniu-tools/qianniu-runner.js | node - inject --url "${cdnUrl}"`,
          description: '使用CDN工具一键注入连接脚本到千牛客户端',
        },
        method2: {
          title: '方法2: 下载工具后执行',
          steps: [
            `1. 下载千牛工具：curl -o qianniu-runner.js ${Bun.env['CDN_BASE_URL']}/qianniu-tools/qianniu-runner.js`,
            `2. 注入连接脚本：node qianniu-runner.js inject --url "${cdnUrl}"`,
            '3. 重启千牛客户端完成连接',
          ],
        },
        notes: [
          '注入前请确保千牛客户端已完全关闭',
          '工具会自动备份原始文件，支持一键恢复',
          '注入成功后千牛将自动连接到您的系统',
          `如需恢复：curl -fsSL ${Bun.env['CDN_BASE_URL']}/qianniu-tools/qianniu-runner.js | node - restore`,
        ],
      },
    };

    return c.json(createResponse(response, '连接邀请创建成功'), 201);
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * 获取连接邀请列表
 */
app.get('/', async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 获取用户的组织ID
    const organizationId = await getUserOrganizationId(user.id);
    logger.debug('连接邀请调试信息', {
      userId: user.id,
      userRole: user.role,
      organizationId: organizationId,
    });

    if (!user || !organizationId) {
      return c.json(createErrorResponse('未登录或缺少组织信息'), 401);
    }

    const invitations = await prisma.connectionInvitation.findMany({
      where: {
        organizationId: organizationId,
      },
      include: {
        team: {
          select: {
            id: true,
            name: true,
          },
        },
        qianniuClient: {
          select: {
            id: true,
            name: true,
            isOnline: true,
          },
        },
        boundUser: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    logger.debug('连接邀请查询结果', {
      organizationId: organizationId,
      invitationsCount: invitations.length,
      invitations: invitations.map((inv) => ({ id: inv.id, name: inv.name, teamId: inv.teamId })),
    });

    return c.json(createResponse(invitations, '获取连接邀请列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * 获取单个连接邀请详情
 */
app.get('/:id', zValidator('param', IdParamSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    const { id } = c.req.valid('param');
    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    const invitation = await prisma.connectionInvitation.findFirst({
      where: {
        id,
        organizationId: organizationId,
      },
      include: {
        team: {
          select: {
            id: true,
            name: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        qianniuClient: {
          select: {
            id: true,
            name: true,
            isOnline: true,
          },
        },
      },
    });

    if (!invitation) {
      return c.json(createErrorResponse('连接邀请不存在'), 404);
    }

    // 生成CDN注入脚本URL（如果需要）
    let cdnUrl = invitation.cdnUrl;
    if (!cdnUrl) {
      // 使用CDN服务生成公开访问URL（无过期时间）
      cdnUrl = cdnService.getPublicUrl(invitation.id);
    }

    const response = {
      invitation,
      cdnUrl,
      deploymentInstructions: {
        method1: {
          title: '方法1: 一键注入（推荐）',
          command: `curl -fsSL ${Bun.env['CDN_BASE_URL']}/qianniu-tools/qianniu-runner.js | node - inject --url "${cdnUrl}"`,
          description: '使用CDN工具一键注入连接脚本到千牛客户端',
        },
        method2: {
          title: '方法2: 下载工具后执行',
          steps: [
            `1. 下载千牛工具：curl -o qianniu-runner.js ${Bun.env['CDN_BASE_URL']}/qianniu-tools/qianniu-runner.js`,
            `2. 注入连接脚本：node qianniu-runner.js inject --url "${cdnUrl}"`,
            '3. 重启千牛客户端完成连接',
          ],
        },
        notes: [
          '注入前请确保千牛客户端已完全关闭',
          '工具会自动备份原始文件，支持一键恢复',
          '注入成功后千牛将自动连接到您的系统',
          `如需恢复：curl -fsSL ${Bun.env['CDN_BASE_URL']}/qianniu-tools/qianniu-runner.js | node - restore`,
        ],
      },
    };

    return c.json(createResponse(response, '获取连接邀请详情成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * 删除连接邀请
 */
app.delete('/:id', zValidator('param', IdParamSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：团队管理员及以上可以删除邀请
    if (!checkPermission(user.role, 'TEAM_MANAGER')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const { id } = c.req.valid('param');
    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    // 查找邀请
    const invitation = await prisma.connectionInvitation.findFirst({
      where: {
        id,
        organizationId: organizationId,
      },
      include: {
        team: true,
        qianniuClient: true,
      },
    });

    if (!invitation) {
      return c.json(createErrorResponse('连接邀请不存在'), 404);
    }

    // 如果是团队管理员，检查是否管理该团队
    if (user.role === 'TEAM_MANAGER') {
      const canManage = await canManageTeam(user.id, invitation.teamId);
      if (!canManage) {
        return c.json(createErrorResponse('无权限管理该团队'), 403);
      }
    }

    // 检查是否可以删除
    if (invitation.status === 'ACTIVATED' && invitation.qianniuClient) {
      return c.json(createErrorResponse('邀请已激活且有客户端连接，请先断开连接后再删除'), 400);
    }

    // 删除邀请（如果有关联的千牛客户端，也一并删除）
    await prisma.$transaction(async (tx) => {
      // 如果有关联的千牛客户端，先删除
      if (invitation.qianniuClientId) {
        await tx.qianniuClient.delete({
          where: { id: invitation.qianniuClientId },
        });
      }

      // 删除邀请
      await tx.connectionInvitation.delete({
        where: { id },
      });
    });

    logger.info('连接邀请已删除', {
      invitationId: id,
      invitationName: invitation.name,
      deletedBy: user.id,
      organizationId,
    });

    return c.json(createResponse({ message: '邀请已删除' }, '邀请删除成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * 撤销连接邀请（设置为过期状态）
 */
app.patch('/:id/revoke', zValidator('param', IdParamSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：团队管理员及以上可以撤销邀请
    if (!checkPermission(user.role, 'TEAM_MANAGER')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const { id } = c.req.valid('param');
    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    // 查找邀请
    const invitation = await prisma.connectionInvitation.findFirst({
      where: {
        id,
        organizationId: organizationId,
      },
      include: {
        team: true,
      },
    });

    if (!invitation) {
      return c.json(createErrorResponse('连接邀请不存在'), 404);
    }

    // 如果是团队管理员，检查是否管理该团队
    if (user.role === 'TEAM_MANAGER') {
      const canManage = await canManageTeam(user.id, invitation.teamId);
      if (!canManage) {
        return c.json(createErrorResponse('无权限管理该团队'), 403);
      }
    }

    // 检查邀请状态
    if (invitation.status === 'EXPIRED') {
      return c.json(createErrorResponse('邀请已过期'), 400);
    }

    // 撤销邀请（设置为过期）
    await prisma.connectionInvitation.update({
      where: { id },
      data: {
        status: 'EXPIRED',
        expiresAt: new Date().toISOString(), // 立即过期
      },
    });

    logger.info('连接邀请已撤销', {
      invitationId: id,
      invitationName: invitation.name,
      revokedBy: user.id,
      organizationId,
    });

    return c.json(createResponse({ message: '邀请已撤销' }, '邀请撤销成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * 重新生成连接邀请
 */
app.post('/:id/regenerate', zValidator('param', IdParamSchema), zValidator('json', RegenerateInvitationSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：团队管理员及以上可以重新生成邀请
    if (!checkPermission(user.role, 'TEAM_MANAGER')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const { id } = c.req.valid('param');
    const { expiresInDays } = c.req.valid('json');
    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    // 查找邀请
    const invitation = await prisma.connectionInvitation.findFirst({
      where: {
        id,
        organizationId: organizationId,
      },
      include: {
        team: true,
        organization: true,
      },
    });

    if (!invitation) {
      return c.json(createErrorResponse('连接邀请不存在'), 404);
    }

    // 如果是团队管理员，检查是否管理该团队
    if (user.role === 'TEAM_MANAGER') {
      const canManage = await canManageTeam(user.id, invitation.teamId);
      if (!canManage) {
        return c.json(createErrorResponse('无权限管理该团队'), 403);
      }
    }

    // 允许重新生成已激活的邀请（相当于软件更新）
    // 不再限制已激活的邀请，支持对已连接的千牛客户端进行更新

    // 更新邀请（支持已激活邀请的重新生成）
    const updatedInvitation = await prisma.connectionInvitation.update({
      where: { id },
      data: {
        status: 'PENDING',
        expiresAt: new Date(Date.now() + expiresInDays * 24 * 60 * 60 * 1000),
        // 注意：不清空 activatedAt 和 qianniuClientId，保留激活历史
        // 这样可以支持对已连接客户端的更新，而不是完全重置
      },
      include: {
        team: {
          select: {
            id: true,
            name: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // 重新生成CDN注入脚本
    logger.info('开始重新生成连接邀请脚本', {
      invitationId: updatedInvitation.id,
      organizationId: updatedInvitation.organizationId,
      teamId: updatedInvitation.teamId,
      invitationName: updatedInvitation.name,
      previousStatus: invitation.status,
      newStatus: updatedInvitation.status,
      regenerationType: 'manual_regeneration',
    });

    const regenerationStartTime = Date.now();
    const injectionScript = generateInjectionScript(updatedInvitation);
    const scriptGenerationDuration = Date.now() - regenerationStartTime;

    logger.info('脚本重新生成完成', {
      invitationId: updatedInvitation.id,
      scriptLength: injectionScript.length,
      scriptLengthKB: Math.round((injectionScript.length / 1024) * 100) / 100,
      generationDuration: `${scriptGenerationDuration}ms`,
      scriptType: 'regenerated',
    });

    try {
      logger.info('开始重新上传脚本到 CDN', {
        invitationId: updatedInvitation.id,
        uploadType: 'regeneration',
        scriptSize: injectionScript.length,
      });

      const cdnUploadStartTime = Date.now();
      const cdnResult = await cdnService.uploadScript(updatedInvitation.id, injectionScript);
      const cdnUploadDuration = Date.now() - cdnUploadStartTime;
      const cdnUrl = cdnResult.cdnUrl;

      logger.info('脚本重新上传到 CDN 成功', {
        invitationId: updatedInvitation.id,
        cdnUrl,
        uploadDuration: `${cdnUploadDuration}ms`,
        uploadedSize: cdnResult.size,
        totalRegenerationDuration: `${Date.now() - regenerationStartTime}ms`,
        result: 'success',
      });

      // 更新CDN URL
      await prisma.connectionInvitation.update({
        where: { id },
        data: { cdnUrl },
      });

      const response = {
        invitation: { ...updatedInvitation, cdnUrl },
        cdnUrl,
        deploymentInstructions: {
          method1: {
            title: '方法1: 一键注入（推荐）',
            command: `curl -fsSL ${Bun.env['CDN_BASE_URL']}/qianniu-tools/qianniu-runner.js | node - inject --url "${cdnUrl}"`,
            description: '使用CDN工具一键注入连接脚本到千牛客户端',
          },
          method2: {
            title: '方法2: 下载工具后执行',
            steps: [
              `1. 下载千牛工具：curl -o qianniu-runner.js ${Bun.env['CDN_BASE_URL']}/qianniu-tools/qianniu-runner.js`,
              `2. 注入连接脚本：node qianniu-runner.js inject --url "${cdnUrl}"`,
              '3. 重启千牛客户端完成连接',
            ],
          },
          notes: [
            '注入前请确保千牛客户端已完全关闭',
            '工具会自动备份原始文件，支持一键恢复',
            '注入成功后千牛将自动连接到您的系统',
            `如需恢复：curl -fsSL ${Bun.env['CDN_BASE_URL']}/qianniu-tools/qianniu-runner.js | node - restore`,
          ],
        },
      };

      return c.json(createResponse(response, '邀请重新生成成功'));
    } catch (error) {
      return handleError(c, error);
    }
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /:id/bind-user - 绑定用户到连接邀请
 */
app.post('/:id/bind-user', zValidator('param', z.object({ id: z.string().uuid() })), zValidator('json', BindUserSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：组织管理员及以上可以绑定用户
    if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    const { id } = c.req.valid('param');
    const { userId } = c.req.valid('json');

    // 验证连接邀请是否存在且属于当前组织
    const invitation = await prisma.connectionInvitation.findFirst({
      where: {
        id,
        organizationId: organizationId,
      },
      include: {
        qianniuClient: {
          select: {
            id: true,
            name: true,
            isOnline: true,
          },
        },
      },
    });

    if (!invitation) {
      return c.json(createErrorResponse('连接邀请不存在或无权限'), 404);
    }

    // 验证连接是否已激活
    if (invitation.status !== 'ACTIVATED') {
      return c.json(createErrorResponse('连接尚未激活，无法绑定用户'), 400);
    }

    // 验证用户是否属于同一组织
    const targetUser = await prisma.user.findFirst({
      where: {
        id: userId,
        members: {
          some: {
            organizationId: organizationId,
          },
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    if (!targetUser) {
      return c.json(createErrorResponse('用户不存在或不属于同一组织'), 404);
    }

    // 检查用户是否已绑定其他连接
    const existingBinding = await prisma.connectionInvitation.findFirst({
      where: {
        boundUserId: userId,
        organizationId: organizationId,
        status: 'ACTIVATED',
      },
    });

    if (existingBinding && existingBinding.id !== id) {
      return c.json(createErrorResponse('该用户已绑定其他连接'), 400);
    }

    // 检查连接是否已绑定其他用户
    if (invitation.boundUserId && invitation.boundUserId !== userId) {
      return c.json(createErrorResponse('该连接已绑定其他用户'), 400);
    }

    // 执行绑定
    const updatedInvitation = await prisma.connectionInvitation.update({
      where: { id },
      data: {
        boundUserId: userId,
        boundAt: new Date(),
      },
      include: {
        boundUser: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        qianniuClient: {
          select: {
            id: true,
            name: true,
            isOnline: true,
          },
        },
        team: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    logger.info('连接用户绑定成功', {
      invitationId: id,
      userId: userId,
      userName: targetUser.name,
      boundBy: user.id,
      organizationId: organizationId,
    });

    return c.json(createResponse(updatedInvitation, '用户绑定成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * DELETE /:id/bind-user - 解绑用户
 */
app.delete('/:id/bind-user', zValidator('param', z.object({ id: z.string().uuid() })), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：组织管理员及以上可以解绑用户
    if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    const { id } = c.req.valid('param');

    // 验证连接邀请是否存在且属于当前组织
    const invitation = await prisma.connectionInvitation.findFirst({
      where: {
        id,
        organizationId: organizationId,
      },
    });

    if (!invitation) {
      return c.json(createErrorResponse('连接邀请不存在或无权限'), 404);
    }

    if (!invitation.boundUserId) {
      return c.json(createErrorResponse('该连接未绑定任何用户'), 400);
    }

    // 执行解绑
    const updatedInvitation = await prisma.connectionInvitation.update({
      where: { id },
      data: {
        boundUserId: null,
        boundAt: null,
      },
      include: {
        qianniuClient: {
          select: {
            id: true,
            name: true,
            isOnline: true,
          },
        },
        team: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    logger.info('连接用户解绑成功', {
      invitationId: id,
      previousUserId: invitation.boundUserId,
      unboundBy: user.id,
      organizationId: organizationId,
    });

    return c.json(createResponse(updatedInvitation, '用户解绑成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

export default app;
