### 新尺码表系统 API 完整测试

@baseUrl = http://localhost:3009/api/v1

# ============ 尺码表类型管理 ============

### 1. 创建尺码表类型
POST {{baseUrl}}/size-chart-types

{
  "name": "女士内衣尺码",
  "xAxis": "胸围",
  "yAxis": "腰围",
  "desc": "女士内衣尺码表类型，支持胸围和腰围双轴"
}

### 2. 获取尺码表类型列表
GET {{baseUrl}}/size-chart-types?skip=0&take=10

### 3. 按名称搜索尺码表类型
GET {{baseUrl}}/size-chart-types?name=女士&skip=0&take=10

### 4. 获取单个尺码表类型详情（需要替换ID）
# 从上面的响应中复制实际的ID
GET {{baseUrl}}/size-chart-types/dc944b9f-604a-4015-886b-a657e607b777

### 5. 更新尺码表类型
POST {{baseUrl}}/size-chart-types

{
  "id": "dc944b9f-604a-4015-886b-a657e607b777",
  "name": "女士内衣尺码更新版",
  "xAxis": "胸围",
  "yAxis": "腰围",
  "desc": "更新后的女士内衣尺码表类型"
}



### 6. 创建附表结构定义 - 罩杯对照
POST {{baseUrl}}/sub-table-defs
Content-Type: application/json

{
  "typeId": "dc944b9f-604a-4015-886b-a657e607b777",
  "name": "罩杯对照表v2",
  "key": "cup_mapping",
  "schema": {
    "type": "object",
    "properties": {
      "cup": {"type": "string", "description": "罩杯型号"},
      "description": {"type": "string", "description": "罩杯描述"}
    },
    "required": ["cup", "description"]
  }
}

### 7. 创建附表结构定义 - 尺寸指导
POST {{baseUrl}}/sub-table-defs
Content-Type: application/json

{
  "typeId": "387554c8-6126-4f95-a808-e0a368ca554c",
  "name": "尺寸指导v2",
  "key": "size_guide_v2",
  "schema": {
    "type": "object",
    "properties": {
      "size": {"type": "string"},
      "recommendation": {"type": "string"}
    }
  }
}

### 8. 获取指定类型的附表定义
GET {{baseUrl}}/size-chart-types/387554c8-6126-4f95-a808-e0a368ca554c/sub-table-defs

# ============ 尺码表管理 ============

### 9. 创建基础尺码表
POST {{baseUrl}}/size-charts
Content-Type: application/json

{
  "name": "女士内衣A款尺码表",
  "typeId": "387554c8-6126-4f95-a808-e0a368ca554c",
  "isComposite": false,
  "thresholdRecommendation": "推大",
  "sortOrder": 1
}

### 10. 创建完整尺码表（包含条目和附表数据）
POST {{baseUrl}}/size-charts/full
Content-Type: application/json

{
  "chart": {
    "name": "女士内衣B款完整尺码表",
    "typeId": "387554c8-6126-4f95-a808-e0a368ca554c",
    "isComposite": false,
    "thresholdRecommendation": "推小",
    "sortOrder": 2
  },
  "entries": [
    {
      "xValue": "80",
      "yValue": "65",
      "size": "32A"
    },
    {
      "xValue": "85",
      "yValue": "70",
      "size": "34A"
    },
    {
      "xValue": "90",
      "yValue": "75",
      "size": "36A"
    },
    {
      "xValue": "95",
      "yValue": "80",
      "size": "38A"
    }
  ],
  "subEntries": [
    {
      "subTableKey": "cup_mapping_v2",
      "data": {
        "A": "小罩杯，适合A杯女性",
        "B": "中等罩杯，适合B杯女性",
        "C": "大罩杯，适合C杯女性"
      }
    },
    {
      "subTableKey": "size_guide_v2",
      "data": {
        "32A": "适合胸围80cm以下，较瘦体型",
        "34A": "适合胸围80-85cm，标准体型",
        "36A": "适合胸围85-90cm，微胖体型"
      }
    }
  ]
}

### 11. 获取尺码表列表
GET {{baseUrl}}/size-charts?skip=0&take=10

### 12. 按类型ID过滤尺码表
GET {{baseUrl}}/size-charts?typeId=387554c8-6126-4f95-a808-e0a368ca554c

### 13. 按名称搜索尺码表
GET {{baseUrl}}/size-charts?name=女士&skip=0&take=10

### 14. 获取复合尺码表
GET {{baseUrl}}/size-charts?isComposite=false

### 15. 获取单个尺码表详情
# 使用上面创建的尺码表ID，需要先运行测试9或10获取实际ID
GET {{baseUrl}}/size-charts/00000000-0000-0000-0000-000000000001

### 16. 更新尺码表
POST {{baseUrl}}/size-charts

{
  "id": "00000000-0000-0000-0000-000000000001",
  "name": "女士内衣A款尺码表（更新版）",
  "typeId": "387554c8-6126-4f95-a808-e0a368ca554c",
  "isComposite": false,
  "thresholdRecommendation": "推大",
  "sortOrder": 1
}

# ============ 尺码表条目管理 ============

### 17. 添加单个尺码表条目
POST {{baseUrl}}/size-chart-entries

{
  "chartId": "00000000-0000-0000-0000-000000000001",
  "xValue": "100",
  "yValue": "85",
  "size": "40A"
}

### 18. 批量添加尺码表条目
POST {{baseUrl}}/size-charts/00000000-0000-0000-0000-000000000001/entries/batch

{
  "entries": [
    {
      "xValue": "105",
      "yValue": "90",
      "size": "42A"
    },
    {
      "xValue": "110",
      "yValue": "95",
      "size": "44A"
    }
  ]
}

### 19. 更新尺码表条目
POST {{baseUrl}}/size-chart-entries

{
  "id": "00000000-0000-0000-0000-000000000011",
  "chartId": "00000000-0000-0000-0000-000000000001",
  "xValue": "100",
  "yValue": "85",
  "size": "40B"
}

# ============ 尺码表附表条目管理 ============

### 20. 添加尺码表附表条目
POST {{baseUrl}}/size-chart-sub-entries

{
  "chartId": "00000000-0000-0000-0000-000000000001",
  "subTableKey": "care_instructions",
  "data": {
    "wash": "手洗或机洗温柔模式",
    "temperature": "水温不超过30度",
    "dry": "平铺阴干，避免暴晒",
    "iron": "低温熨烫"
  }
}

### 21. 批量添加附表条目
POST {{baseUrl}}/size-charts/00000000-0000-0000-0000-000000000001/sub-entries/batch

{
  "subEntries": [
    {
      "subTableKey": "material_info",
      "data": {
        "fabric": "95%精梳棉 + 5%氨纶",
        "lining": "100%纯棉",
        "feature": "透气舒适，亲肤柔软"
      }
    },
    {
      "subTableKey": "brand_info",
      "data": {
        "brand": "测试品牌",
        "origin": "中国制造",
        "quality": "A级品质"
      }
    }
  ]
}

### 22. 更新附表条目
POST {{baseUrl}}/size-chart-sub-entries

{
  "id": "00000000-0000-0000-0000-000000000021",
  "chartId": "00000000-0000-0000-0000-000000000001",
  "subTableKey": "care_instructions",
  "data": {
    "wash": "建议手洗",
    "temperature": "冷水洗涤",
    "dry": "自然风干",
    "iron": "不需要熨烫"
  }
}

# ============ 产品关联管理 ============

### 23. 为产品分配尺码表
POST {{baseUrl}}/products/00000000-0000-0000-0000-000000000099/size-charts

{
  "sizeChartIds": ["00000000-0000-0000-0000-000000000001", "00000000-0000-0000-0000-000000000002"]
}

### 24. 获取产品关联的尺码表
GET {{baseUrl}}/products/00000000-0000-0000-0000-000000000099/size-charts

### 25. 清空产品的尺码表关联
POST {{baseUrl}}/products/实际的产品ID/size-charts

{
  "sizeChartIds": []
}

# ============ 高级功能 ============

### 26. 复制尺码表
POST {{baseUrl}}/size-charts/实际的尺码表ID/clone

{
  "newName": "女士内衣A款尺码表（副本）"
}

### 27. 恢复已删除的尺码表
POST {{baseUrl}}/size-charts/实际的尺码表ID/restore

### 28. 批量删除尺码表
POST {{baseUrl}}/size-charts/bulk-delete

{
  "ids": ["实际的尺码表ID1", "实际的尺码表ID2"]
}

# ============ 删除操作 ============

### 29. 删除尺码表条目
DELETE {{baseUrl}}/size-chart-entries/实际的条目ID

### 30. 删除尺码表附表条目
DELETE {{baseUrl}}/size-chart-sub-entries/实际的附表条目ID

### 31. 删除尺码表
DELETE {{baseUrl}}/size-charts/实际的尺码表ID

### 32. 删除附表结构定义
DELETE {{baseUrl}}/sub-table-defs/实际的附表定义ID

### 33. 删除尺码表类型
DELETE {{baseUrl}}/size-chart-types/实际的类型ID

# ============ 错误场景测试 ============

### 34. 创建无效的尺码表类型（缺少必填字段）
POST {{baseUrl}}/size-chart-types

{
  "name": "",
  "xAxis": ""
}

### 35. 获取不存在的尺码表
GET {{baseUrl}}/size-charts/00000000-0000-0000-0000-000000000000

### 36. 删除被产品使用的尺码表（应该失败）
DELETE {{baseUrl}}/size-charts/实际的被使用的尺码表ID

### 37. 创建重复坐标的条目（应该失败）
POST {{baseUrl}}/size-chart-entries

{
  "chartId": "实际的尺码表ID",
  "xValue": "80",
  "yValue": "65",
  "size": "32A"
}

### 38. 为不存在的类型创建附表定义（应该失败）
POST {{baseUrl}}/sub-table-defs

{
  "typeId": "00000000-0000-0000-0000-000000000000",
  "name": "测试附表",
  "key": "test_key",
  "schema": {}
}

# ============ JSON格式错误测试 ============

### 39. 测试JSON格式错误 - 缺少引号（应该返回400错误）
POST {{baseUrl}}/size-chart-types

{
  name: "测试尺码类型",
  "xAxis": "胸围"
}

### 40. 测试JSON格式错误 - 缺少逗号（应该返回400错误）
POST {{baseUrl}}/size-chart-types

{
  "name": "测试尺码类型"
  "xAxis": "胸围"
}

### 41. 测试JSON格式错误 - 多余的逗号（应该返回400错误）
POST {{baseUrl}}/size-chart-types

{
  "name": "测试尺码类型",
  "xAxis": "胸围",
}

### 42. 测试完全无效的JSON（应该返回400错误）
POST {{baseUrl}}/size-chart-types

这不是JSON格式的内容

