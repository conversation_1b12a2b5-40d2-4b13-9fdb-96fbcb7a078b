import { ReactNode } from 'react';
import { useLocation } from 'react-router-dom';

import { useAppStore } from '../../lib/store';
import { getPageLayoutMode, type LayoutMode } from '../../types/layout';

import { cn } from '@/lib/utils';

interface PageContainerProps {
  children: ReactNode;
  mode?: LayoutMode;
  className?: string;
}

export default function PageContainer({ children, mode: propMode, className = '' }: PageContainerProps) {
  const location = useLocation();
  const mode = propMode || getPageLayoutMode(location.pathname);
  const isMobileView = useAppStore((state) => state.isMobileView);

  // 基于布局模式和移动视图状态设置类名
  const containerClasses = cn(
    'w-full h-full',
    mode === 'fixed' ? 'overflow-hidden' : 'overflow-y-auto',
    // 移动视图添加额外的内边距和响应式调整
    isMobileView ? 'px-4' : 'px-6',
    className,
  );

  return (
    <div className="py-4 h-full">
      <div className={containerClasses}>{children}</div>
    </div>
  );
}
