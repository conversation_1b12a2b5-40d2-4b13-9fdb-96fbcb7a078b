import { Hono } from 'hono';

import { ProductController } from '../controller/product';

const router = new Hono();
const product = new ProductController();

// 创建/更新商品
// POST /api/v1/product
router.post('/product', product.upsert);

// 获取商品列表（支持分页、过滤）
// GET /api/v1/products
router.get('/products', product.findMany);

// 获取单个商品详情
// GET /api/v1/product/:id
router.get('/product/:id', product.findById);

// 软删除单个商品
// DELETE /api/v1/product/:id
router.delete('/product/:id', product.delete);

// 恢复已删除商品
// POST /api/v1/product/:id/restore
router.post('/product/:id/restore', product.restore);

// 批量软删除
// POST /api/v1/products/bulk-delete
router.post('/products/bulk-delete', product.bulkDelete);

export default router;
