import type { ServerWebSocket } from 'bun';

import { connectionBasedNotificationService } from '../services/connectionBasedNotificationService';
import { logger } from './logger';

/**
 * 扩展Bun的WebSocket类型，以附加我们自己的上下文数据
 */
export interface WebSocketContext {
  ws: ServerWebSocket<{
    userId: string;
    organizationId: string;
    socketId: string;
  }>;
}

/**
 * WebSocket管理器，用于跟踪所有活动的客户端连接
 */
class WebSocketManager {
  // 使用 Map 存储组织，每个组织下再用 Map 存储用户，一个用户可能有多个连接
  // Map<organizationId, Map<userId, Set<socketId>>>
  private organizations = new Map<string, Map<string, Set<string>>>();
  // 直接通过 socketId 查找 WebSocket 连接实例
  // Map<socketId, WebSocketContext['ws']>
  private sockets = new Map<string, WebSocketContext['ws']>();

  /**
   * 添加一个新客户端
   * @param orgId - 组织ID
   * @param userId - 用户ID
   * @param ws - WebSocket实例
   */
  addClient(orgId: string, userId: string, ws: WebSocketContext['ws']) {
    const { socketId } = ws.data;

    // 存储socket实例
    this.sockets.set(socketId, ws);

    // 如果组织不存在，则创建
    if (!this.organizations.has(orgId)) {
      this.organizations.set(orgId, new Map<string, Set<string>>());
    }
    const orgUsers = this.organizations.get(orgId)!;

    // 如果用户不存在，则创建
    if (!orgUsers.has(userId)) {
      orgUsers.set(userId, new Set<string>());
    }
    const userSockets = orgUsers.get(userId)!;

    // 添加当前socketId
    userSockets.add(socketId);

    // 通知连接绑定服务用户上线
    connectionBasedNotificationService.userOnline(userId, orgId);

    logger.debug('WebSocket客户端连接', { orgId, userId, socketId });
  }

  /**
   * 移除一个客户端
   * @param orgId - 组织ID
   * @param userId - 用户ID
   * @param socketId - Socket ID
   */
  removeClient(orgId: string, userId: string, socketId: string) {
    this.sockets.delete(socketId);

    const orgUsers = this.organizations.get(orgId);
    if (!orgUsers) return;

    const userSockets = orgUsers.get(userId);
    if (!userSockets) return;

    userSockets.delete(socketId);

    // 如果用户的所有连接都已关闭，则从Map中移除该用户
    if (userSockets.size === 0) {
      orgUsers.delete(userId);
      // 通知连接绑定服务用户离线
      connectionBasedNotificationService.userOffline(userId, orgId);
    }

    // 如果组织的所有用户都已离线，则从Map中移除该组织
    if (orgUsers.size === 0) {
      this.organizations.delete(orgId);
    }

    logger.debug('WebSocket客户端断开连接', { orgId, userId, socketId });
  }

  /**
   * 向特定组织广播消息
   * @param orgId - 目标组织ID
   * @param message - 要发送的消息 (JSON字符串)
   * @param options - 广播选项
   *   - to: 指定接收消息的用户ID数组
   *   - except: 从广播中排除的用户ID数组
   */
  broadcast(orgId: string, message: string, options: { to?: string[]; except?: string[] } = {}) {
    const orgUsers = this.organizations.get(orgId);
    if (!orgUsers) {
      logger.warn('WebSocket广播失败：组织未找到或无客户端', { orgId });
      return;
    }

    let targetUserIds = Array.from(orgUsers.keys());

    // 如果指定了接收者
    if (options.to) {
      targetUserIds = options.to.filter((userId) => targetUserIds.includes(userId));
    }

    // 如果指定了排除者
    if (options.except) {
      const excludedSet = new Set(options.except);
      targetUserIds = targetUserIds.filter((userId) => !excludedSet.has(userId));
    }

    logger.debug('WebSocket广播消息', { orgId, targetUsers: targetUserIds.length, messageType: JSON.parse(message).type });
    for (const userId of targetUserIds) {
      const userSockets = orgUsers.get(userId);
      if (userSockets) {
        for (const socketId of userSockets) {
          const ws = this.sockets.get(socketId);
          ws?.send(message);
        }
      }
    }

    logger.debug('WebSocket广播完成', { orgId, targetUsers: targetUserIds.length });
  }
}

// 创建并导出一个单例
export const wsManager = new WebSocketManager();
