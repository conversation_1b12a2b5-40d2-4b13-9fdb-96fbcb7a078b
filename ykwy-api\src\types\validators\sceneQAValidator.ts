import { z } from 'zod';

/**
 * 场景Q&A ID参数校验
 */
export const sceneQAParamIdSchema = z.object({
  /** 场景Q&A ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

/**
 * 场景Q&A输入参数校验 Schema
 */
export const sceneQAInputSchema = z.object({
  /** 问题 (必须字段) */
  question: z.string().min(1, { message: '问题为必填项' }),
  /** 答案 (必须字段) */
  answer: z.string().min(1, { message: '答案为必填项' }),
  /** 关键词数组 (必须字段) */
  keywords: z.array(z.string().min(1, { message: '关键词不能为空' })).min(1, { message: '至少需要一个关键词' }),
  /** 意图 (必须字段) */
  intent: z.string().min(1, { message: '意图为必填项' }),
  /** 商品链接 (可选字段) */
  productUrl: z.string().url({ message: '商品链接格式不正确' }).optional(),
  /** 店铺ID (可选字段) */
  shopId: z.string().optional(),
});

/**
 * 场景Q&A查询参数校验
 */
export const sceneQAQuerySchema = z.object({
  /** 按问题模糊搜索 */
  question: z.string().optional(),
  /** 按答案模糊搜索 */
  answer: z.string().optional(),
  /** 按关键词搜索 */
  keyword: z.string().optional(),
  /** 按意图搜索 */
  intent: z.string().optional(),
  /** 按商品链接搜索 */
  productUrl: z.string().optional(),
  /** 按店铺ID搜索 */
  shopId: z.string().optional(),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10)),
});

/**
 * 批量删除参数校验
 */
export const sceneQABulkDeleteSchema = z.object({
  /** 场景Q&A ID数组 */
  ids: z.array(z.string().uuid()),
});

/**
 * 场景Q&A搜索参数校验 Schema
 * 用于V2版本的搜索接口
 */
export const sceneQASearchSchema = z.object({
  /** 查询字符串，用于匹配问题、答案、关键词、意图字段 */
  query: z.string().max(200, { message: '查询字符串不能超过200个字符' }).optional(),
});

/**
 * 场景Q&A按店铺搜索参数校验 Schema
 * shopId为必须字段，其他字段可选用于组合查询
 */
export const sceneQASearchByQuerySchema = z.object({
  /** 店铺ID（必须字段） */
  shopId: z.string().min(1, { message: '店铺ID为必填项' }),
  /** 按问题精确搜索 */
  question: z.string().optional(),
  /** 按答案精确搜索 */
  answer: z.string().optional(),
  /** 按关键词精确搜索 */
  keyword: z.string().optional(),
  /** 按意图精确搜索 */
  intent: z.string().optional(),
  /** 按商品链接精确搜索 */
  productUrl: z.string().optional(),
  /** 关键词反向匹配查询：检查query字符串是否包含数据库中关键词数组的任一元素 */
  query: z.string().max(200, { message: '查询字符串不能超过200个字符' }).optional(),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10)),
});

// ========== 类型定义 ==========

/**
 * 场景Q&A输入类型
 */
export type SceneQAInput = z.infer<typeof sceneQAInputSchema>;

/**
 * 场景Q&A查询参数类型
 */
export type SceneQAQuery = z.infer<typeof sceneQAQuerySchema>;

/**
 * 场景Q&A ID参数类型
 */
export type SceneQAIdParam = z.infer<typeof sceneQAParamIdSchema>;

/**
 * 批量删除参数类型
 */
export type SceneQABulkDelete = z.infer<typeof sceneQABulkDeleteSchema>;

/**
 * 搜索参数类型
 */
export type SceneQASearch = z.infer<typeof sceneQASearchSchema>;

/**
 * 按店铺搜索参数类型
 */
export type SceneQASearchByQuery = z.infer<typeof sceneQASearchByQuerySchema>;
