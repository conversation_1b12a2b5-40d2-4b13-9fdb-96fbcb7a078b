import pluginJs from '@eslint/js';
import stylistic from '@stylistic/eslint-plugin';
import { defineConfig, globalIgnores } from 'eslint/config';
import prettier from 'eslint-plugin-prettier';
import simpleImportSort from 'eslint-plugin-simple-import-sort';
import unusedImports from 'eslint-plugin-unused-imports';
import globals from 'globals';
import tseslint from 'typescript-eslint';

export default defineConfig([
  globalIgnores(['src/generated/*', 'prisma/*']),
  { files: ['**/*.{js,mjs,cjs,ts}'] },
  { languageOptions: { globals: globals.node } },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  {
    plugins: {
      '@stylistic': stylistic,
      'simple-import-sort': simpleImportSort,
      'unused-imports': unusedImports,
      prettier,
    },
    rules: {
      // 使用prettier配置文件中的配置
      // 'prettier/prettier': ['error', {}, { usePrettierrc: true }],
      '@stylistic/arrow-spacing': 'warn',
      '@stylistic/comma-spacing': ['warn', { before: false, after: true }],
      '@stylistic/indent': ['warn', 2],
      '@stylistic/no-multiple-empty-lines': ['warn', { max: 2, maxEOF: 0, maxBOF: 0 }],
      '@stylistic/no-multi-spaces': 'warn',
      '@stylistic/semi': 'warn',
      '@stylistic/semi-spacing': 'warn',
      '@stylistic/space-in-parens': ['warn', 'never'],
      '@stylistic/object-curly-spacing': ['warn', 'always'],
      eqeqeq: ['error', 'always', { null: 'ignore' }],
      'max-lines': ['warn', 400],
      'max-len': ['warn', 200, 2, { ignoreComments: true, ignoreUrls: true, ignoreStrings: true, ignoreTemplateLiterals: true, ignoreRegExpLiterals: true }],
      'no-undef': 'warn',
      'no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-require-imports': 'warn',
      '@typescript-eslint/no-unused-vars': 'warn',
      'simple-import-sort/imports': ['error', { groups: [['^\\u0000'], ['^@?\\w'], ['^\\./(?=.*/)', '^\\.'], ['^.+\\.s?css$']] }],
      'simple-import-sort/exports': 'error',
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': ['warn', { vars: 'all', varsIgnorePattern: '^_', args: 'after-used', argsIgnorePattern: '^_' }],
    },
  },
]);
