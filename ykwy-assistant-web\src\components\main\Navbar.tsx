import { ChevronDown, LogOut, Menu, MessageCircle, PanelLeftOpen, PanelRightOpen, User } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import type { WSStatus } from '../../hooks/useWebSocket';
import { authClient } from '../../lib/auth-client';
import { useAppStore } from '../../lib/store';

import { Button } from '@/components/ui/button';

interface NavbarProps {
  wsStatus?: WSStatus;
  isSmallScreen?: boolean;
}
export default function Navbar({ wsStatus = 'idle', isSmallScreen = false }: NavbarProps) {
  const navigate = useNavigate();
  const { data: session } = authClient.useSession();
  const user = session?.user;
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 获取侧边栏状态
  const sidebarCollapsed = useAppStore((state) => state.sidebarCollapsed);
  const toggleSidebar = useAppStore((state) => state.toggleSidebar);
  const isMobileView = useAppStore((state) => state.isMobileView);
  const toggleMobileSidebar = useAppStore((state) => state.toggleMobileSidebar);

  const handleLogout = async () => {
    try {
      await authClient.signOut();
      navigate('/auth/login');
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 h-16 bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between h-full px-6">
        {/* 移动端侧边栏切换按钮 */}
        {isMobileView && (
          <Button variant="ghost" size="icon" className="mr-2" onClick={toggleMobileSidebar}>
            <Menu className="h-5 w-5" />
            <span className="sr-only">切换菜单</span>
          </Button>
        )}

        {/* Logo 和侧边栏切换按钮 */}
        <div className="flex items-center flex-1">
          <div className="flex items-center gap-3 cursor-pointer" onClick={() => navigate('/')}>
            <MessageCircle className="w-6 h-6 text-indigo-600" />
            <span className="text-xl font-bold text-gray-900">客服中心</span>
          </div>

          {/* 桌面端侧边栏切换按钮 */}
          {!isMobileView && !isSmallScreen && (
            <Button variant="ghost" size="sm" onClick={toggleSidebar} className="ml-4">
              {sidebarCollapsed ? <PanelLeftOpen /> : <PanelRightOpen />}
            </Button>
          )}
        </div>

        {/* 右侧用户信息 */}
        <div className="relative" ref={dropdownRef}>
          <button onClick={() => setIsDropdownOpen(!isDropdownOpen)} className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="text-right hidden sm:block">
              <div className="text-sm font-medium text-gray-900">{user?.name || user?.email || '张三'}</div>
              <div className="text-xs text-gray-500">客服主管</div>
            </div>
            {/* 头像 + 在线状态 */}
            <div className="relative w-8 h-8">
              <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-indigo-600" />
              </div>
              <span className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${wsStatus === 'open' ? 'bg-emerald-500' : 'bg-gray-300'}`} />
            </div>
            <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

          {/* 下拉菜单 */}
          {isDropdownOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
              <div className="px-4 py-2 border-b border-gray-100">
                <div className="text-sm font-medium text-gray-900">{user?.name || user?.email || '张三'}</div>
                <div className="text-xs text-gray-500">{user?.email || '<EMAIL>'}</div>
              </div>

              <button
                onClick={() => {
                  setIsDropdownOpen(false);
                  // 这里可以添加个人资料页面的导航
                }}
                className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <User className="w-4 h-4" />
                个人资料
              </button>

              <button
                onClick={() => {
                  setIsDropdownOpen(false);
                  handleLogout();
                }}
                className="w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                退出登录
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}
