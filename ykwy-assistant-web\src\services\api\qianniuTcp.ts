// 千牛TCP相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse, RegisterTcpConnectionRequest, TcpConnectionInfo, TcpConnectionMapping, TestSendRequest, TestSendResponse } from '../types';

// TCP连接状态查询选项
export const tcpConnectionsQueryOptions = () => ({
  queryKey: queryKeys.tcpConnections(),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<TcpConnectionInfo>>('qianniu-tcp/connections');
    return (
      response.data || {
        tcpConnections: [],
        connectionMappings: {},
        totalConnections: 0,
        onlineClients: [],
        unmappedTcpConnections: [],
      }
    );
  },
});

// TCP连接映射列表查询选项 - 注意：后端没有单独的mappings端点，映射信息包含在connections响应中

// 注册TCP连接映射
export const registerTcpConnectionMutation = {
  mutationFn: async (data: RegisterTcpConnectionRequest) => {
    const response = await apiClient.post<ApiResponse<TcpConnectionMapping>>('qianniu-tcp/register', data as unknown as Record<string, unknown>);
    return response.data;
  },
};

// 删除TCP连接映射
export const deleteTcpConnectionMutation = {
  mutationFn: async (connectionId: string) => {
    const response = await apiClient.delete<ApiResponse<null>>(`qianniu-tcp/connections/${connectionId}`);
    return response.data;
  },
};

// 测试发送消息
export const testSendMessageMutation = {
  mutationFn: async (data: TestSendRequest) => {
    const response = await apiClient.post<TestSendResponse>('qianniu-tcp/test-send', data as unknown as Record<string, unknown>);
    return response;
  },
};
