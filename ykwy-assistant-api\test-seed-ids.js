#!/usr/bin/env node
/**
 * 测试seed文件中的固定ID
 */

// 从seed文件中提取的固定ID（UUIDv7格式）
const FIXED_ORGANIZATION_ID = '01936b2e-8f4a-7000-8000-000000000001';
const FIXED_TEAM_ID = '01936b2e-8f4a-7000-8000-000000000002';

console.log('🔧 测试seed文件中的固定ID');
console.log('=' * 50);
console.log(`组织ID: ${FIXED_ORGANIZATION_ID}`);
console.log(`团队ID: ${FIXED_TEAM_ID}`);
console.log('');
console.log('✅ 这些ID将在数据库seed时被使用');
console.log('✅ 所有用户、邀请、客户端都将关联到这个组织和团队');
console.log('');
console.log('📋 运行seed命令:');
console.log('cd ykwy-assistant-api');
console.log('npx prisma migrate reset --force');
console.log('npx prisma db seed');
