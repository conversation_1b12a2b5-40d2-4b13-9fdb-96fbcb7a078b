import logger from './logger';

// 存储请求开始时间
const requestStartTimes = new Map<string, number>();

// 生成请求唯一标识
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 解析请求体
function parseRequestBody(
  body: any,
): Record<string, unknown> | string | null | undefined {
  if (!body) return undefined;

  try {
    if (typeof body === 'string') {
      return JSON.parse(body);
    }
    if (body instanceof FormData) {
      const formObj: Record<string, unknown> = {};
      body.forEach((value, key) => {
        formObj[key] = value;
      });
      return formObj;
    }
    if (body instanceof URLSearchParams) {
      const params: Record<string, unknown> = {};
      body.forEach((value, key) => {
        params[key] = value;
      });
      return params;
    }
    return body;
  } catch {
    return body?.toString() || body;
  }
}

// 解析响应数据
async function parseResponseData(
  response: Response,
): Promise<Record<string, unknown> | string | null> {
  try {
    const contentType = response.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      return await response.json();
    }
    return await response.text();
  } catch {
    return null;
  }
}

// 请求拦截器
export function setupRequestInterceptor() {
  // 重写 fetch 函数
  if (typeof window !== 'undefined' && window.fetch) {
    const originalFetch = window.fetch;

    window.fetch = async function (
      input: RequestInfo | URL,
      init?: RequestInit,
    ): Promise<Response> {
      const requestId = generateRequestId();
      const startTime = Date.now();
      const url =
        typeof input === 'string'
          ? input
          : input instanceof URL
          ? input.href
          : input.url;
      const method = init?.method || 'GET';

      requestStartTimes.set(requestId, startTime);

      try {
        const response = await originalFetch(input, init);
        const endTime = Date.now();
        const duration = endTime - startTime;

        // 只记录失败的请求
        if (!response.ok) {
          const responseData = await parseResponseData(response.clone());

          logger.logRequest(
            method,
            url,
            response.status,
            duration,
            init?.body ? await parseRequestBody(init.body) : undefined,
            responseData,
          );
        }

        return response;
      } catch (error) {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // 记录请求错误
        const errorMessage =
          error instanceof Error ? error.message : '网络请求失败';
        logger.logError(
          error instanceof Error ? error : new Error(errorMessage),
          'request',
          {
            method,
            requestUrl: url,
            duration,
          },
        );

        throw error;
      } finally {
        requestStartTimes.delete(requestId);
      }
    };
  }

  // 重写 XMLHttpRequest
  if (typeof window !== 'undefined' && window.XMLHttpRequest) {
    const originalXHR = window.XMLHttpRequest;

    (window.XMLHttpRequest as any) = function () {
      const xhr = new originalXHR();
      const requestId = generateRequestId();
      let startTime: number;
      let method: string;
      let url: string;
      let requestData: any;

      // 保存原始方法
      const originalOpen = xhr.open;
      const originalSend = xhr.send;

      // 拦截 open 方法
      xhr.open = function (
        method_: string,
        url_: string | URL,
        async?: boolean,
        user?: string | null,
        password?: string | null,
      ) {
        method = method_;
        url = typeof url_ === 'string' ? url_ : url_.href;
        return originalOpen.call(
          this,
          method_,
          url_,
          async ?? true,
          user ?? null,
          password ?? null,
        );
      };

      // 拦截 send 方法
      xhr.send = function (body?: Document | XMLHttpRequestBodyInit | null) {
        startTime = Date.now();
        requestData = body;

        return originalSend.call(this, body);
      };

      // 监听状态变化
      xhr.addEventListener('loadend', () => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        try {
          let responseData: any;
          try {
            responseData = xhr.responseText
              ? JSON.parse(xhr.responseText)
              : null;
          } catch {
            responseData = xhr.responseText;
          }

          // 只记录失败的请求
          if (xhr.status >= 400) {
            logger.logRequest(
              method,
              url,
              xhr.status,
              duration,
              parseRequestBody(requestData),
              responseData,
            );
          }
        } catch (error) {
          logger.logError(
            error instanceof Error ? error : new Error('XHR响应处理失败'),
            'request',
            {
              method,
              requestUrl: url,
              duration,
            },
          );
        } finally {
          requestStartTimes.delete(requestId);
        }
      });

      // 监听错误
      xhr.addEventListener('error', () => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        const errorMessage = 'XHR网络请求失败';
        logger.error(errorMessage, 'request', {
          method,
          requestUrl: url,
          duration,
        });

        requestStartTimes.delete(requestId);
      });

      // 监听超时
      xhr.addEventListener('timeout', () => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        const errorMessage = 'XHR请求超时';
        logger.error(errorMessage, 'request', {
          method,
          requestUrl: url,
          duration,
        });

        requestStartTimes.delete(requestId);
      });

      return xhr;
    };

    // 复制原型方法
    window.XMLHttpRequest.prototype = originalXHR.prototype;
  }
}
