"""
策略工具类 - 提供销售策略分析工具
"""
import logging
from typing import List, Dict, Any
from .base_tools import BaseTools

logger = logging.getLogger(__name__)

class StrategyTools(BaseTools):
    """策略工具类 - 提供销售策略分析功能"""

    def decide_sales_strategy(self, user_message: str) -> str:
        """智能销售策略决策工具 - 使用LLM分析"""
        logger.info(f"🧠 [工具调用] decide_sales_strategy")
        logger.info(f"   📥 用户消息: '{user_message}'")

        try:
            # 获取当前对话历史
            conversation_history = self._get_current_conversation_history()
            logger.info(f"   📝 对话历史长度: {len(conversation_history)} 字符")

            if conversation_history:
                logger.info(f"   📜 对话历史内容:")
                lines = conversation_history.split('\n')
                for line in lines[:6]:  # 只显示前6行
                    logger.info(f"      {line}")
                if len(lines) > 6:
                    logger.info(f"      ... (还有 {len(lines) - 6} 行)")
            else:
                logger.info(f"   📜 对话历史: 空（首次对话）")

            # 使用LLM进行智能分析
            logger.info(f"   🤖 开始LLM策略分析...")
            analysis_result = self._llm_analyze_sales_strategy(user_message, conversation_history)

            logger.info(f"   ✅ LLM策略分析完成")
            logger.info(f"   📊 分析结果:")
            for line in analysis_result.split('\n')[:10]:  # 显示前10行结果
                if line.strip():
                    logger.info(f"      {line}")

            return analysis_result

        except Exception as e:
            logger.error(f"   ❌ 策略决策出错: {str(e)}")
            return "策略分析暂时不可用，请按常规方式回复"

    def _llm_analyze_sales_strategy(self, user_message: str, conversation_history: str) -> str:
        """使用LLM分析销售策略"""
        from llama_index.core import Settings

        # 构建分析提示
        analysis_prompt = f"""你是一个专业的销售策略分析师，请分析以下销售对话情况：

【对话历史】
{conversation_history if conversation_history else "（这是第一次对话）"}

【客户最新消息】
{user_message}

请从以下几个维度进行分析：

1. **客户合作意向分析**：
   - 客户是否明确表达了合作需求？（如"你们能帮我做吗"、"我需要你们帮忙"、"怎么合作"）
   - 客户是否主动询问联系方式？（如"微信多少"、"怎么联系"）
   - 客户是否询问具体合作细节？（如价格、时间、流程）

2. **对话阶段判断**：
   - 当前处于什么阶段？（初步了解/需求确认/合作意向/成交引导）
   - 客户对我们的服务是否已经有基本认知？
   - 是否已经建立了初步信任？

3. **加微信时机判断**（严格标准）：
   - **绝对不能引导加微信的情况**：
     * 客户只是点击产品链接了解（绝对不能推微信！）
     * 客户只是一般性咨询产品功能
     * 客户还在犹豫或比较阶段
     * 还没有明确表达合作意向
     * 第一次接触或初步了解阶段

   - **只有在以下情况才可以建议引导加微信**：
     * 客户主动询问"微信多少"、"怎么联系"
     * 客户询问具体合作细节（价格、流程、时间）
     * 客户表达明确的合作意向（用户主动询问合作方式）

4. **销售策略建议**：
   - 下一步应该采取什么策略？
   - 应该重点强调什么？
   - 推荐的话术风格和回复方向

**重要提醒**：
- 分析结果仅供内部参考，不要在回复中暴露分析过程
- 不要告诉客户"您在什么阶段"、"根据分析"等内部判断
- 回复建议要自然，像真人客服一样
- **特别注意**：如果客户只是点击产品链接，绝对不能建议加微信！

请给出简洁明确的分析结果，特别注意加微信时机的判断要严格按照上述标准。"""

        try:
            # 使用当前的LLM进行分析
            llm = Settings.llm
            logger.info(f"   🔍 发送提示给LLM，提示长度: {len(analysis_prompt)} 字符")

            response = llm.complete(analysis_prompt)
            result = str(response)

            logger.info(f"   📝 LLM原始回复长度: {len(result)} 字符")
            logger.info(f"   📝 LLM回复前200字符: {result[:200]}...")

            return result
        except Exception as e:
            logger.error(f"   ❌ LLM分析失败: {e}")
            return "LLM分析暂时不可用，建议按常规销售流程进行"
def _get_current_conversation_history(self) -> str:
        """获取当前对话的历史记录（从Memory对象中获取）"""
        if not self.agent or not self.current_conversation_id:
            return ""

        if self.current_conversation_id not in self.agent.conversation_memories:
            return ""

        # 从Memory对象获取聊天历史
        memory = self.agent.conversation_memories[self.current_conversation_id]
        chat_history = memory.get()

        if not chat_history:
            return ""

        # 格式化对话历史
        formatted_history = []
        for message in chat_history:
            if message.role == "user":
                formatted_history.append(f"用户: {message.content}")
            elif message.role == "assistant":
                formatted_history.append(f"客服: {message.content}")

        return "\n".join(formatted_history)

