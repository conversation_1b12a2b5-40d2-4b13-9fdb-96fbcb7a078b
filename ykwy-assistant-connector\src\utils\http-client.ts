import http from 'node:http';
import https from 'node:https';

/**
 * HTTP客户端工具
 * 提供HTTP请求功能
 */
class HttpClient {
  /**
   * 发送HTTP请求
   * @param {Object} options - 请求选项
   * @param {string} postData - POST数据
   * @returns {Promise<Object>} 响应数据
   */
  static async request(options: import('http').RequestOptions, postData: string | null = null) {
    return new Promise((resolve, reject) => {
      const client = options.protocol === 'https:' ? https : http;

      const req = client.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => (data += chunk));
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(result);
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${result.message || data}`));
            }
          } catch {
            reject(new Error(`解析响应失败: ${data}`));
          }
        });
      });

      req.on('error', reject);

      if (postData) {
        req.write(postData);
      }

      req.end();
    });
  }

  /**
   * 发送GET请求
   * @param {string} url - 请求URL
   * @param {Object} headers - 请求头
   * @returns {Promise<Object>} 响应数据
   */
  static async get(url: string, headers: Record<string, string> = {}) {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      protocol: urlObj.protocol,
      headers,
    };

    return this.request(options);
  }

  /**
   * 发送POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} headers - 请求头
   * @returns {Promise<Object>} 响应数据
   */
  static async post(url, data, headers = {}) {
    const postData = JSON.stringify(data);
    const urlObj = new URL(url);

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'POST',
      protocol: urlObj.protocol,
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        ...headers,
      },
    };

    return this.request(options, postData);
  }
}

export default HttpClient;
