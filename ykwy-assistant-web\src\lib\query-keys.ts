export const queryKeys = {
  // 所有查询的根键
  all: ['app'] as const,
  // 对话相关查询
  conversations: (params?: Record<string, unknown>) => [...queryKeys.all, 'conversations', params] as const,
  conversation: (id: string) => [...queryKeys.all, 'conversations', id] as const,
  conversationMessages: (conversationId: string) => [...queryKeys.all, 'conversations', conversationId, 'messages'] as const,
  conversationAutoReplyStatus: (conversationId: string) => [...queryKeys.all, 'conversations', conversationId, 'auto-reply-status'] as const,

  // 客户相关查询
  customers: (params?: Record<string, unknown>) => [...queryKeys.all, 'customers', params] as const,
  customer: (id: string) => [...queryKeys.all, 'customers', id] as const,

  // AI推荐相关查询 - 简化queryKey，只使用conversationId保持稳定
  aiRecommendations: (conversationId: string) => [...queryKeys.all, 'ai-recommendations', conversationId] as const,

  // 任务相关查询
  tasks: (params?: Record<string, unknown>) => [...queryKeys.all, 'tasks', params] as const,
  task: (id: string) => [...queryKeys.all, 'tasks', id] as const,

  // 用户相关查询
  users: () => [...queryKeys.all, 'users'] as const,
  user: (id: string) => [...queryKeys.all, 'users', id] as const,

  // 团队相关查询
  teams: (params?: Record<string, unknown>) => [...queryKeys.all, 'teams', params] as const,
  team: (id: string) => [...queryKeys.all, 'teams', id] as const,
  teamMembers: (teamId: string, params?: Record<string, unknown>) => [...queryKeys.all, 'teams', teamId, 'members', params] as const,

  // 连接邀请相关查询
  connectionInvitations: (params?: Record<string, unknown>) => [...queryKeys.all, 'connection-invitations', params] as const,
  connectionInvitation: (id: string) => [...queryKeys.all, 'connection-invitations', id] as const,

  // 千牛客户端相关查询
  qianniuClients: (params?: Record<string, unknown>) => [...queryKeys.all, 'qianniu-clients', params] as const,
  qianniuClient: (id: string) => [...queryKeys.all, 'qianniu-clients', id] as const,
  organizationQianniuClients: (organizationId: string) => [...queryKeys.all, 'qianniu-clients', 'organization', organizationId] as const,

  // 千牛账号相关查询
  qianniuAccounts: (clientId: string) => [...queryKeys.all, 'qianniu-accounts', 'client', clientId] as const,
  qianniuAccount: (accountId: string) => [...queryKeys.all, 'qianniu-accounts', accountId] as const,

  // 品牌相关查询
  brands: () => [...queryKeys.all, 'brands'] as const,
  organizationBrands: (organizationId: string) => [...queryKeys.all, 'brands', 'organization', organizationId] as const,

  // 千牛TCP相关查询
  tcpConnections: () => [...queryKeys.all, 'tcp-connections'] as const,

  // 千牛监控相关查询
  connectionStats: () => [...queryKeys.all, 'connection-stats'] as const,
  activeConnections: () => [...queryKeys.all, 'active-connections'] as const,
  messageStats: () => [...queryKeys.all, 'message-stats'] as const,
} as const;

// 类型安全的查询键类型
export type QueryKeys = typeof queryKeys;
