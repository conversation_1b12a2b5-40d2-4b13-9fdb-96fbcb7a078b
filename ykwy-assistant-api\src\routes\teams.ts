import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { canManageTeam, checkPermission, getCurrentUser, getUserOrganizationId, requireOrganization } from '../lib/auth-utils';
import { prisma } from '../lib/db';
import { calculatePagination, createErrorResponse, createPaginatedResponse, createResponse, handleError } from '../lib/utils';
import { CreateTeamSchema, IdParamSchema, PaginationSchema, UpdateTeamSchema } from '../lib/validations';

const teamsRoute = new Hono();

// 团队查询过滤器
const TeamFilterSchema = z.object({
  search: z.string().optional(),
  organizationId: z.string().uuid().optional(),
});

/**
 * GET /teams - 获取团队列表
 */
teamsRoute.get('/', zValidator('query', PaginationSchema.merge(TeamFilterSchema)), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    const { page, limit, search } = c.req.valid('query');
    const { skip, take } = calculatePagination(page, limit);

    const where = {
      organizationId,
    } as Record<string, unknown>;

    // 应用过滤器
    if (search) {
      where['OR'] = [{ name: { contains: search, mode: 'insensitive' } }];
    }

    const [teams, total] = await Promise.all([
      prisma.team.findMany({
        where,
        skip,
        take,
        include: {
          _count: {
            select: {
              members: true,
              qianniuClients: true,
              connectionInvitations: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.team.count({ where }),
    ]);

    return c.json(createPaginatedResponse(teams, page, limit, total, '获取团队列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /teams/:id - 获取团队详情
 */
teamsRoute.get('/:id', zValidator('param', IdParamSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    const { id } = c.req.valid('param');
    const organizationId = await getUserOrganizationId(user.id);

    const team = await prisma.team.findFirst({
      where: {
        id,
        organizationId: organizationId || undefined,
      },
      include: {
        members: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            image: true,
            createdAt: true,
          },
        },
        qianniuClients: {
          select: {
            id: true,
            name: true,
            isOnline: true,
            lastOnlineAt: true,
          },
        },
        connectionInvitations: {
          select: {
            id: true,
            name: true,
            status: true,
            createdAt: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!team) {
      return c.json(createErrorResponse('团队不存在'), 404);
    }

    return c.json(createResponse(team, '获取团队详情成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /teams - 创建团队
 */
teamsRoute.post('/', zValidator('json', CreateTeamSchema), async (c) => {
  try {
    const user = await requireOrganization(c);

    // 检查权限：组织管理员及以上可以创建团队
    if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const data = c.req.valid('json');

    const team = await prisma.team.create({
      data: {
        ...data,
        organizationId: user.organizationId,
      },
      include: {
        _count: {
          select: {
            members: true,
            qianniuClients: true,
            connectionInvitations: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return c.json(createResponse(team, '团队创建成功'), 201);
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * PUT /teams/:id - 更新团队
 */
teamsRoute.put('/:id', zValidator('param', IdParamSchema), zValidator('json', UpdateTeamSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    const { id } = c.req.valid('param');
    const data = c.req.valid('json');

    // 检查权限：组织管理员或团队管理员可以更新团队
    if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
      const canManage = await canManageTeam(user.id, id);
      if (!canManage) {
        return c.json(createErrorResponse('权限不足'), 403);
      }
    }

    const organizationId = await getUserOrganizationId(user.id);

    const team = await prisma.team.update({
      where: {
        id,
        organizationId: organizationId || undefined,
      },
      data,
      include: {
        _count: {
          select: {
            members: true,
            qianniuClients: true,
            connectionInvitations: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return c.json(createResponse(team, '团队更新成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * DELETE /teams/:id - 删除团队
 */
teamsRoute.delete('/:id', zValidator('param', IdParamSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：只有组织管理员可以删除团队
    if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const { id } = c.req.valid('param');
    const organizationId = await getUserOrganizationId(user.id);

    // 检查团队是否存在成员
    const team = await prisma.team.findFirst({
      where: {
        id,
        organizationId: organizationId || undefined,
      },
      include: {
        _count: {
          select: {
            members: true,
          },
        },
      },
    });

    if (!team) {
      return c.json(createErrorResponse('团队不存在'), 404);
    }

    if (team._count.members > 0) {
      return c.json(createErrorResponse('团队还有成员，无法删除'), 400);
    }

    await prisma.team.delete({
      where: { id },
    });

    return c.json(createResponse({ message: '团队删除成功' }, '团队删除成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /teams/:id/members - 获取团队成员列表
 */
teamsRoute.get('/:id/members', zValidator('param', IdParamSchema), zValidator('query', PaginationSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    const { id } = c.req.valid('param');
    const { page, limit } = c.req.valid('query');
    const { skip, take } = calculatePagination(page, limit);

    const organizationId = await getUserOrganizationId(user.id);

    // 验证团队是否属于用户的组织
    const team = await prisma.team.findFirst({
      where: {
        id,
        organizationId: organizationId || undefined,
      },
    });

    if (!team) {
      return c.json(createErrorResponse('团队不存在或无权限访问'), 404);
    }

    const [members, total] = await Promise.all([
      prisma.user.findMany({
        where: {
          teamId: id,
        },
        skip,
        take,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          image: true,
          createdAt: true,
          permissionLevel: true,
          canManageTeams: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.user.count({
        where: {
          teamId: id,
        },
      }),
    ]);

    return c.json(createPaginatedResponse(members, page, limit, total, '获取团队成员列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /teams/:id/members - 添加团队成员
 */
teamsRoute.post(
  '/:id/members',
  zValidator('param', IdParamSchema),
  zValidator(
    'json',
    z.object({
      userId: z.string().uuid('无效的用户ID'),
    }),
  ),
  async (c) => {
    try {
      const user = await getCurrentUser(c);
      if (!user) {
        return c.json(createErrorResponse('未登录'), 401);
      }

      const { id } = c.req.valid('param');
      const { userId } = c.req.valid('json');

      // 检查权限：组织管理员或团队管理员可以添加成员
      if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
        const canManage = await canManageTeam(user.id, id);
        if (!canManage) {
          return c.json(createErrorResponse('权限不足'), 403);
        }
      }

      const organizationId = await getUserOrganizationId(user.id);

      // 验证团队是否属于用户的组织
      const team = await prisma.team.findFirst({
        where: {
          id,
          organizationId: organizationId || undefined,
        },
      });

      if (!team) {
        return c.json(createErrorResponse('团队不存在或无权限访问'), 404);
      }

      // 验证要添加的用户是否属于同一组织
      const targetUserOrganizationId = await getUserOrganizationId(userId);
      if (targetUserOrganizationId !== organizationId) {
        return c.json(createErrorResponse('用户不属于同一组织'), 400);
      }

      // 检查用户是否已在其他团队
      const existingUser = await prisma.user.findUnique({
        where: { id: userId },
        select: { teamId: true, name: true, email: true },
      });

      if (!existingUser) {
        return c.json(createErrorResponse('用户不存在'), 404);
      }

      if (existingUser.teamId && existingUser.teamId !== id) {
        return c.json(createErrorResponse('用户已属于其他团队'), 400);
      }

      // 添加用户到团队
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { teamId: id },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          image: true,
          createdAt: true,
        },
      });

      return c.json(createResponse(updatedUser, '成员添加成功'), 201);
    } catch (error) {
      return handleError(c, error);
    }
  },
);

/**
 * DELETE /teams/:id/members/:userId - 移除团队成员
 */
teamsRoute.delete(
  '/:id/members/:userId',
  zValidator(
    'param',
    z.object({
      id: z.string().uuid('无效的团队ID'),
      userId: z.string().uuid('无效的用户ID'),
    }),
  ),
  async (c) => {
    try {
      const user = await getCurrentUser(c);
      if (!user) {
        return c.json(createErrorResponse('未登录'), 401);
      }

      const { id, userId } = c.req.valid('param');

      // 检查权限：组织管理员或团队管理员可以移除成员
      if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
        const canManage = await canManageTeam(user.id, id);
        if (!canManage) {
          return c.json(createErrorResponse('权限不足'), 403);
        }
      }

      const organizationId = await getUserOrganizationId(user.id);

      // 验证团队是否属于用户的组织
      const team = await prisma.team.findFirst({
        where: {
          id,
          organizationId: organizationId || undefined,
        },
      });

      if (!team) {
        return c.json(createErrorResponse('团队不存在或无权限访问'), 404);
      }

      // 验证用户是否在该团队
      const member = await prisma.user.findFirst({
        where: {
          id: userId,
          teamId: id,
        },
      });

      if (!member) {
        return c.json(createErrorResponse('用户不在该团队中'), 404);
      }

      // 移除用户从团队
      await prisma.user.update({
        where: { id: userId },
        data: { teamId: null },
      });

      return c.json(createResponse({ message: '成员移除成功' }, '成员移除成功'));
    } catch (error) {
      return handleError(c, error);
    }
  },
);

export default teamsRoute;
