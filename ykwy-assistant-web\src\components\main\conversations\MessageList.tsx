import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { forwardRef, useEffect, useMemo, useRef, useState } from 'react';

import { useAutoScroll } from '../../../hooks/useAutoScroll';
import { Message } from '../../../services';

interface MessageListProps {
  messages: Message[];
  onScrollToBottom?: () => void;
}

interface MessageItemProps {
  message: Message;
  isLast?: boolean;
  isSmallScreen?: boolean;
}

function MessageItem({ message, isLast = false, isSmallScreen = false }: MessageItemProps) {
  const isCustomer = message.senderType === 'CUSTOMER';
  const isSystem = message.senderType === 'SYSTEM';
  // 通过metadata识别AI消息（AI消息现在保存为CUSTOMER_SERVICE类型）
  const metadata = message.metadata as Record<string, unknown> | undefined;
  const isAI = message.senderType === 'CUSTOMER_SERVICE' && metadata?.source === 'ragflow_auto_reply';

  // 格式化时间
  const formattedTime = format(new Date(message.sentAt), 'HH:mm', {
    locale: zhCN,
  });

  if (isSystem) {
    return <div className="text-center text-xs text-gray-500 my-2">{message.content}</div>;
  }

  return (
    <div className={`flex ${isCustomer ? '' : 'justify-end'} ${isLast ? 'mb-4' : ''}`} id={isLast ? 'last-message' : undefined}>
      <div className={`${isSmallScreen ? 'max-w-[85%]' : 'max-w-[70%]'}`}>
        {/* 头像和发送者信息 */}
        <div className={`flex items-center mb-1 ${isCustomer ? '' : 'justify-end'}`}>
          {isCustomer ? (
            <>
              <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-2">
                <span className="text-xs font-medium text-gray-600">{message.sender?.name?.charAt(0) || '客'}</span>
              </div>
              <span className="text-xs font-medium">{message.sender?.name || '客户'}</span>
              <span className="text-xs text-gray-500 ml-2">{formattedTime}</span>
            </>
          ) : (
            <>
              <span className="text-xs text-gray-500 mr-2">{formattedTime}</span>
              <span className="text-xs font-medium">{isAI ? 'AI助手' : message.sender?.name || '客服'}</span>
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center ml-2">
                <span className="text-xs font-medium text-white">{isAI ? 'AI' : message.sender?.name?.charAt(0) || '客'}</span>
              </div>
            </>
          )}
        </div>

        {/* 消息内容 */}
        <div
          className={`p-3 rounded-lg shadow-sm transition-all duration-200 ${
            isCustomer ? 'bg-gray-100 text-gray-800 rounded-tl-sm' : isAI ? 'bg-purple-100 text-gray-800 rounded-tr-sm border-l-2 border-purple-300' : 'bg-blue-500 text-white rounded-tr-sm'
          }`}
        >
          <div className="whitespace-pre-wrap break-words">{message.content}</div>

          {/* AI消息标识 */}
          {isAI && <div className="text-xs text-purple-600 mt-1 opacity-75">AI生成回复</div>}
        </div>

        {/* 消息状态指示器（可选） */}
        {isLast && !isCustomer && (
          <div className="flex justify-end mt-1">
            <span className="text-xs text-gray-400">已发送</span>
          </div>
        )}
      </div>
    </div>
  );
}

const MessageList = forwardRef<HTMLDivElement, MessageListProps>(({ messages, onScrollToBottom }, ref) => {
  // 检测是否为小屏幕PC
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      // 768px 以上 1024px 以下为小屏幕PC
      setIsSmallScreen(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 按时间排序消息
  const sortedMessages = useMemo(() => [...messages].sort((a, b) => new Date(a.sentAt).getTime() - new Date(b.sentAt).getTime()), [messages]);

  // 使用增强的自动滚动钩子
  const { scrollRef, scrollToBottom, isAtBottom, setShouldAutoScroll } = useAutoScroll({
    dependencies: [sortedMessages.length, sortedMessages[sortedMessages.length - 1]?.id],
    delay: 50,
    threshold: 150,
  });

  // 使用消息变化来检测新消息
  const prevMessagesLengthRef = useRef(messages.length);
  useEffect(() => {
    const messagesAdded = messages.length > prevMessagesLengthRef.current;
    prevMessagesLengthRef.current = messages.length;

    if (messagesAdded) {
      // 100ms延迟确保DOM已更新
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [messages.length, scrollToBottom]);

  // 首次渲染时滚动到底部
  useEffect(() => {
    if (sortedMessages.length > 0) {
      // 立即滚动到底部
      scrollToBottom();
    }
  }, []); // 只在组件挂载时执行

  // 暴露滚动方法给父组件
  useEffect(() => {
    if (onScrollToBottom) {
      onScrollToBottom();
    }
  }, [onScrollToBottom]);

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500 p-8">
        <div className="text-center">
          <div className="mb-4">
            <svg className="w-12 h-12 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
          </div>
          <p className="text-sm">暂无消息</p>
          <p className="text-xs text-gray-400 mt-1">开始对话吧</p>
        </div>
      </div>
    );
  }

  return (
    <div ref={ref} className="relative flex-1 flex flex-col min-h-0">
      {/* 消息列表容器 */}
      <div ref={scrollRef} className={`flex-1 overflow-y-auto p-4 space-y-4 ${isSmallScreen ? 'px-2' : ''}`} style={{ minHeight: 0 }}>
        {sortedMessages.map((message, index) => (
          <MessageItem key={message.id} message={message} isLast={index === sortedMessages.length - 1} isSmallScreen={isSmallScreen} />
        ))}
        {/* 添加一个不可见的元素作为滚动目标 */}
        <div id="scroll-bottom-anchor" className="h-1"></div>
      </div>

      {/* 滚动到底部按钮 */}
      {!isAtBottom && (
        <button
          onClick={() => {
            setShouldAutoScroll(true);
            scrollToBottom();
          }}
          className="absolute bottom-4 right-4 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-2 shadow-lg transition-all duration-200 z-10"
          title="滚动到底部"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      )}
    </div>
  );
});

MessageList.displayName = 'MessageList';

export default MessageList;
