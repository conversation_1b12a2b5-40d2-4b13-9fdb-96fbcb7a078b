import { Layout, Row, Typography } from 'antd';
import React from 'react';

interface Props {
  name: string;
}

// 脚手架示例组件
const Guide: React.FC<Props> = (props) => {
  const { name } = props;
  return (
    <Layout data-oid="ebbk2zs">
      <Row data-oid=".fx.bw2">
        <Typography.Title
          level={3}
          className={'mx-auto font-light'}
          data-oid="369fkrz"
        >
          欢迎使用 <strong data-oid="hgfh89.">{name}</strong> ！
        </Typography.Title>
      </Row>
    </Layout>
  );
};

export default Guide;
