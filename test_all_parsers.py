#!/usr/bin/env python3
"""
测试所有千牛数据解析器
"""
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mock-production', 'src'))

from sales_agent.models.qianniu_models import QianniuDataParser
from sales_agent.utils.logger import setup_detailed_logging

def test_order_parser():
    """测试订单解析器"""
    print("\n🧪 测试订单解析器...")

    # 测试数据（从E2E测试结果复制）
    test_data = {
        "success": True,
        "message": "queryRecentOrders API调用完成",
        "data": {
            "requestId": "test_1753431347751_4mjkoxp",
            "success": True,
            "data": {
                "success": True,
                "data": {
                    "orders": [
                        {
                            "adjustFee": "0.00",
                            "afterSaleText": "售后完成",
                            "bizOrderId": "*******************",
                            "buyAmount": 1,
                            "cardTypeText": "已关闭",
                            "category": "已关闭",
                            "createTime": "2025-07-22 21:26:33",
                            "endTime": "2025-07-25 10:12:17",
                            "itemList": [
                                {
                                    "auctionId": "953394282130",
                                    "auctionPrice": "0.10",
                                    "auctionTitle": "AI 智能写作助手｜高效产出各类文案",
                                    "price": "0.10",
                                    "buyAmount": 1
                                }
                            ],
                            "orderPrice": "0.10",
                            "payTime": "2025-07-22 21:26:33"
                        }
                    ],
                    "pageNum": 1,
                    "useNewChangeRefund": True,
                    "useNewInsteadRefund": True
                }
            }
        }
    }

    try:
        result = QianniuDataParser.parse_orders(test_data)
        print(f"✅ 订单解析成功！找到 {len(result.orders)} 个订单")
        if result.orders:
            order = result.orders[0]
            print(f"   订单号: {order.biz_order_id}")
            print(f"   价格: ¥{order.order_price}")
            print(f"   状态: {order.card_type_text}")
    except Exception as e:
        print(f"❌ 订单解析失败: {e}")

def test_logistics_parser():
    """测试物流解析器"""
    print("\n🧪 测试物流解析器...")

    # 模拟物流数据
    test_data = {
        "success": True,
        "data": {
            "data": {
                "data": {
                    "logisticsInfoList": [
                        {
                            "logisticsDetailList": [
                                {
                                    "time": "2025-07-20 15:00:00",
                                    "title": "已发货",
                                    "desc": "商品已从仓库发出"
                                }
                            ]
                        }
                    ],
                    "orderDetailList": [
                        {
                            "mainBizOrderId": "*******************",
                            "price": 0.20,
                            "itemDetailInfoList": [
                                {
                                    "itemTitle": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本",
                                    "buyAmount": 1,
                                    "actualPayAmount": "0.20"
                                }
                            ]
                        }
                    ],
                    "receiver": {
                        "name": "宫**",
                        "mobile": "***********",
                        "fullAddress": "湖北省武汉市武昌区***********"
                    }
                }
            }
        }
    }

    try:
        result = QianniuDataParser.parse_logistics_info(test_data, "*******************")
        print(f"✅ 物流解析成功！")
        print(f"   订单号: {result.order_id}")
        print(f"   状态: {result.status}")
        print(f"   物流信息数量: {len(result.logistics_info_list or [])}")
        if result.receiver:
            print(f"   收货人: {result.receiver.name}")
    except Exception as e:
        print(f"❌ 物流解析失败: {e}")

def test_shop_items_parser():
    """测试商品解析器"""
    print("\n🧪 测试商品解析器...")

    # 模拟商品数据
    test_data = {
        "success": True,
        "data": {
            "data": {
                "data": {
                    "items": [
                        {
                            "itemId": "953394282130",
                            "title": "AI 智能写作助手｜高效产出各类文案",
                            "price": "0.10",
                            "pic": "//img.alicdn.com/bao/uploaded/i4/1938907789/O1CN01phC9Mh27PQ1xk21rz_!!1938907789.png",
                            "soldQuantity": 100,
                            "quantity": 999
                        }
                    ],
                    "page": 1,
                    "pageSize": 8,
                    "totalCount": 1
                }
            }
        }
    }

    try:
        result = QianniuDataParser.parse_shop_items(test_data)
        print(f"✅ 商品解析成功！找到 {len(result.items)} 个商品")
        if result.items:
            item = result.items[0]
            print(f"   商品ID: {item.item_id}")
            print(f"   标题: {item.title}")
            print(f"   价格: ¥{item.price}")
    except Exception as e:
        print(f"❌ 商品解析失败: {e}")

def test_customer_info_parser():
    """测试客户信息解析器"""
    print("\n🧪 测试客户信息解析器...")

    # 模拟客户信息数据
    test_data = {
        "success": True,
        "data": {
            "data": {
                "data": {
                    "buyerCreditLevel": 5,
                    "buyerCreditScore": 1200,
                    "sendGoodRate": "98.5%",
                    "isNewCustomer": False,
                    "isShopFans": True,
                    "hasMembership": True,
                    "vipLevel": 3
                }
            }
        }
    }

    try:
        result = QianniuDataParser.parse_customer_info(test_data)
        print(f"✅ 客户信息解析成功！")
        print(f"   信用等级: {result.buyer_credit_level}")
        print(f"   信用分: {result.buyer_credit_score}")
        print(f"   好评率: {result.send_good_rate}")
        print(f"   是否新客户: {result.is_new_customer}")
        print(f"   是否店铺粉丝: {result.is_shop_fans}")
    except Exception as e:
        print(f"❌ 客户信息解析失败: {e}")

def main():
    print("🚀 开始测试所有千牛数据解析器...")

    # 设置日志
    setup_detailed_logging()

    # 运行所有测试
    test_order_parser()
    test_logistics_parser()
    test_shop_items_parser()
    test_customer_info_parser()

    print("\n✅ 所有解析器测试完成！")

if __name__ == "__main__":
    main()
