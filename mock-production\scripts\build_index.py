#!/usr/bin/env python3
"""
索引构建脚本
用于在 Docker 构建时预构建索引
"""
import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.sales_agent.core.agent import SalesAgent
from src.sales_agent.utils.config import settings

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def validate_environment():
    """验证环境变量"""
    logger.info("🔍 验证环境变量...")

    if not settings.openai_api_key:
        logger.error("❌ OPENAI_API_KEY 未设置")
        return False

    if not settings.openai_base_url:
        logger.error("❌ OPENAI_API_BASE 未设置")
        return False

    logger.info("✅ 环境变量验证通过")
    return True


def build_index(force_rebuild=False):
    """构建索引"""
    try:
        logger.info("🚀 开始构建索引...")

        # 创建销售代理实例
        agent = SalesAgent()

        # 构建索引
        agent.build_index(force_rebuild=force_rebuild)

        logger.info("✅ 索引构建完成")
        return True

    except Exception as e:
        logger.error(f"❌ 索引构建失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🔧 开始索引构建流程...")

    # 验证环境
    if not validate_environment():
        sys.exit(1)

    # 构建索引
    force_rebuild = '--force' in sys.argv
    if not build_index(force_rebuild=force_rebuild):
        sys.exit(1)

    logger.info("🎉 索引构建流程完成")


if __name__ == "__main__":
    main()