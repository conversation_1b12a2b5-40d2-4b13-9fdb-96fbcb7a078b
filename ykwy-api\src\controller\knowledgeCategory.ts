import type { Context } from 'hono';

import { ErrorCode } from '../constants/errorCodes';
import { AppError } from '../errors/custom.error';
import { KnowledgeCategoryService } from '../services/knowledgeCategoryService';
import { knowledgeCategoryCodesQuerySchema, knowledgeCategoryParamCodeSchema, knowledgeCategoryParamIdSchema, knowledgeCategoryQuerySchema } from '../types/request/knowledgeCategory';
import { R } from '../utils/Response';

const knowledgeCategoryService = new KnowledgeCategoryService();

/**
 * 获取知识库分类列表
 */
export const getKnowledgeCategories = async (c: Context) => {
  const query = c.req.query();
  // 参数校验
  const validation = knowledgeCategoryQuerySchema.safeParse(query);
  if (!validation.success) {
    throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
  }

  const result = await knowledgeCategoryService.findMany(validation.data);
  return R.success(c, result);
};

/**
 * 根据ID获取分类详情
 */
export const getKnowledgeCategoryById = async (c: Context) => {
  const params = c.req.param();
  // 参数校验
  const validation = knowledgeCategoryParamIdSchema.safeParse(params);
  if (!validation.success) {
    throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
  }

  const category = await knowledgeCategoryService.findById(validation.data.id);
  if (!category) {
    throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, { message: '分类不存在' });
  }

  return R.success(c, category);
};

/**
 * 根据编码获取分类详情
 */
export const getKnowledgeCategoryByCode = async (c: Context) => {
  const params = c.req.param();
  // 参数校验
  const validation = knowledgeCategoryParamCodeSchema.safeParse(params);
  if (!validation.success) {
    throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
  }

  const category = await knowledgeCategoryService.findByCode(validation.data.code);
  if (!category) {
    throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, { message: '分类不存在' });
  }

  return R.success(c, category);
};

/**
 * 获取所有启用的分类（用于下拉选择）
 */
export const getActiveKnowledgeCategories = async (c: Context) => {
  const categories = await knowledgeCategoryService.findAllActive();
  return R.success(c, categories);
};

/**
 * 获取分类树形结构
 */
export const getKnowledgeCategoryTree = async (c: Context) => {
  const tree = await knowledgeCategoryService.getTree();
  return R.success(c, tree);
};

/**
 * 获取编码到名称的映射
 */
export const getKnowledgeCategoryMap = async (c: Context) => {
  const map = await knowledgeCategoryService.getCodeNameMap();
  return R.success(c, map);
};

/**
 * 根据编码列表批量获取分类信息
 */
export const getKnowledgeCategoriesByCodes = async (c: Context) => {
  const query = c.req.query();
  // 参数校验
  const validation = knowledgeCategoryCodesQuerySchema.safeParse(query);
  if (!validation.success) {
    throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
  }

  const codeArray = validation.data.codes
    .split(',')
    .map((code) => code.trim())
    .filter(Boolean);

  const categories = await knowledgeCategoryService.findByCodes(codeArray);
  return R.success(c, categories);
};
