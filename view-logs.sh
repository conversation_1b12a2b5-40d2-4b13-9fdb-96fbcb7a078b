#!/bin/bash

# 销售智能体日志查看脚本

set -e

CONTAINER_NAME="ykwy-sales-agent"
LOG_DIR="$(pwd)/logs/sales-agent"
LOG_FILE="$LOG_DIR/sales_agent.log"

# 显示帮助信息
show_help() {
    echo "📋 销售智能体日志查看工具"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -f, --follow     实时跟踪日志"
    echo "  -n, --lines N    显示最后N行日志 (默认: 100)"
    echo "  -c, --container  查看Docker容器日志"
    echo "  -h, --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 显示最后100行文件日志"
    echo "  $0 -f                 # 实时跟踪文件日志"
    echo "  $0 -n 50              # 显示最后50行文件日志"
    echo "  $0 -c                 # 查看Docker容器日志"
    echo "  $0 -c -f              # 实时跟踪Docker容器日志"
}

# 默认参数
FOLLOW=false
LINES=100
CONTAINER_LOG=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        -c|--container)
            CONTAINER_LOG=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "❌ 未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查容器是否运行
if ! docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    echo "⚠️  容器 $CONTAINER_NAME 未运行"
    echo "📋 可用容器:"
    docker ps --format 'table {{.Names}}\t{{.Status}}'
    exit 1
fi

echo "📊 销售智能体日志查看"
echo "容器: $CONTAINER_NAME"
echo "日志目录: $LOG_DIR"
echo ""

if [ "$CONTAINER_LOG" = true ]; then
    # 查看Docker容器日志
    echo "📋 Docker容器日志:"
    if [ "$FOLLOW" = true ]; then
        echo "🔄 实时跟踪容器日志 (Ctrl+C 退出)..."
        docker logs -f "$CONTAINER_NAME"
    else
        echo "📄 显示最后 $LINES 行容器日志:"
        docker logs --tail "$LINES" "$CONTAINER_NAME"
    fi
else
    # 查看文件日志
    if [ ! -f "$LOG_FILE" ]; then
        echo "⚠️  日志文件不存在: $LOG_FILE"
        echo "💡 提示: 使用 -c 选项查看Docker容器日志"
        exit 1
    fi
    
    echo "📋 文件日志: $LOG_FILE"
    if [ "$FOLLOW" = true ]; then
        echo "🔄 实时跟踪文件日志 (Ctrl+C 退出)..."
        tail -f "$LOG_FILE"
    else
        echo "📄 显示最后 $LINES 行文件日志:"
        tail -n "$LINES" "$LOG_FILE"
    fi
fi
