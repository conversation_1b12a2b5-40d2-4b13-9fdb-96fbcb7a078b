import CategorySelector from '@/components/Question/Q&A/CategoryCascader';
import type { KnowledgeCategory } from '@/models/questionAndAnswer';
import { getKnowledgeCategoryTree } from '@/services/knowledgeCategory';
import { getKnowledgeBaseList } from '@/services/questionAndAnswerKnowledgeBase';
import type {
  ActionType,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import React, { useEffect, useRef, useState } from 'react';

const Knowledge: React.FC = () => {
  const [categoryTree, setCategoryTree] = useState<KnowledgeCategory[]>([]);
  const [activeTab, setActiveTab] = useState('list');

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    getKnowledgeCategoryTree().then((res) => {
      if (res.code === 200 && Array.isArray(res.data)) {
        setCategoryTree(res.data);
      }
    });
  }, []);

  const columns: ProColumns<any>[] = [
    {
      title: '问题分类',
      dataIndex: 'categoryId',
      hideInTable: true,
      colSize: 24,
      order: 0,
      renderFormItem: (_, __, form) => (
        <div style={{ width: '100%', paddingTop: 5 }}>
          <CategorySelector
            categoryTree={categoryTree}
            onChange={(id, code) => {
              form.setFieldsValue({ categoryId: code });
              setTimeout(() => {
                formRef.current?.submit(); // 自动查询
              });
            }}
          />
        </div>
      ),
    },
    {
      title: '问题类型',
      dataIndex: 'questionType',
      valueType: 'text',
      search: true,
    },
    {
      title: '7日咨询量',
      dataIndex: 'weeklyConsultationVolume',
      valueType: 'text',
      search: false,
    },
    {
      title: '关联商品数量',
      dataIndex: 'numberOfRelatedProducts',
      valueType: 'text',
      search: false,
    },
    {
      title: '答案数量',
      dataIndex: 'numberOfAnswers',
      valueType: 'text',
      search: false,
    },
  ];

  return (
    <PageContainer
      header={{
        title: '问答知识库',
      }}
      tabList={[
        {
          tab: '行业场景',
          key: 'list',
        },
        {
          tab: '自定义问题',
          key: 'custom',
        },
      ]}
      breadcrumbRender={false}
      tabActiveKey={activeTab}
      onTabChange={setActiveTab}
    >
      {activeTab === 'list' && (
        <ProTable
          actionRef={actionRef}
          formRef={formRef}
          rowKey="id"
          columns={columns}
          request={async (params) => {
            const res = await getKnowledgeBaseList(params);

            const mockData = [
              {
                id: 'mock-id-1',
                categoryId: 'mock-cat-001',
                questionType: '发货时效',
                weeklyConsultationVolume: '120',
                numberOfRelatedProducts: '12',
                numberOfAnswers: '2',
              },
              {
                id: 'mock-id-2',
                categoryId: 'mock-cat-002',
                questionType: '售后服务',
                weeklyConsultationVolume: '75',
                numberOfRelatedProducts: '5',
                numberOfAnswers: '1',
              },
              {
                id: 'mock-id-3',
                categoryId: 'mock-cat-003',
                questionType: '支付问题',
                weeklyConsultationVolume: '98',
                numberOfRelatedProducts: '7',
                numberOfAnswers: '3',
              },
            ];

            const realData = Array.isArray(res.data?.data) ? res.data.data : [];

            return {
              data: [...mockData, ...realData],
              total: (res.data?.total || 0) + mockData.length,
              success: true,
            };
          }}
          pagination={{ pageSize: 10 }}
          search={{
            labelWidth: 100,
            defaultCollapsed: false,
          }}
          cardBordered
        />
      )}
    </PageContainer>
  );
};

export default Knowledge;
