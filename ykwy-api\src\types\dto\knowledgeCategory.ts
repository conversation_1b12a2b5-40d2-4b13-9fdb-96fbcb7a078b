/**
 * 知识库分类 DTO - 基本信息
 */
export interface KnowledgeCategoryDto {
  /** 分类ID */
  id: string;
  /** 分类名称 */
  name: string;
  /** 分类编码 */
  code: string;
  /** 分类级别：1=大分类，2=子分类，3=子子分类 */
  level: number;
  /** 排序号 */
  sortOrder: number;
  /** 分类描述 */
  description?: string | null;
  /** 是否启用 */
  isActive: boolean;
  /** 父分类ID */
  parentId?: string | null;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 是否已删除 */
  isDeleted: number;
}

/**
 * 知识库分类树形结构 DTO
 */
export interface KnowledgeCategoryTreeDto extends KnowledgeCategoryDto {
  /** 子分类列表 */
  children?: KnowledgeCategoryTreeDto[];
  /** 父分类信息 */
  parent?: {
    id: string;
    name: string;
    code: string;
  } | null;
}

/**
 * 知识库分类列表 DTO
 */
export interface KnowledgeCategoryListDto {
  /** 分类列表 */
  items: KnowledgeCategoryDto[];
  /** 总数 */
  total: number;
}

/**
 * 知识库分类简化信息 DTO - 用于下拉选择
 */
export interface KnowledgeCategorySimpleDto {
  /** 分类编码 */
  code: string;
  /** 分类名称 */
  name: string;
  /** 分类级别 */
  level: number;
  /** 父分类编码 */
  parentCode?: string | null;
}
