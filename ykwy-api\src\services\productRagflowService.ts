import type { TempProductDto } from '../types/dto/tempProduct.ts';
import { getFormattedDateTimeFileName } from '../utils/dateTimeTool.ts';
import { RagflowUploader } from '../utils/ragflowUploader.ts';
import { TempProductService } from './tempProductService.ts';

const tempProductService = new TempProductService();
const ragflowUploader = new RagflowUploader();

export class ProductRagflowService {
  async syncToRagflow() {
    // 获取所有商品数据（循环分页查询）
    const allProducts = await tempProductService.findAllFromTempProduct({
      includeDeleted: false,
    });

    // 构建TXT内容
    const lines = allProducts.map((product: TempProductDto) => {
      const productId = sanitizeSimpleText(product.productId);
      const name = sanitizeSimpleText(product.name);
      const linkOrId = sanitizeSimpleText(product.linkOrId);
      const status = sanitizeSimpleText(product.status);
      const imageUrl = sanitizeSimpleText(product.imageUrl);
      const styleNumber = sanitizeSimpleText(product.styleNumber);

      return [
        `商品id: ${productId}`,
        `商品名称: ${name}`,
        `商品链接或id: ${linkOrId}`,
        `状态: ${status}`,
        `图片链接: ${imageUrl}`,
        `货号/款号: ${styleNumber}`,
        '=============', // 商品数据分隔符
      ].join('\n');
    });

    const fileContent = lines.join('\n');
    const fileName = getFormattedDateTimeFileName('商品知识库');
    const delimiter = '=============';

    // 完整调用Ragflow工具链
    await ragflowUploader.clearDocumentsByPrefix('商品知识库');
    const newDoc = await ragflowUploader.uploadFileFromStream(fileContent, fileName);
    await ragflowUploader.updateDocumentParserConfig(newDoc.id, delimiter);
    await ragflowUploader.parseDocuments([newDoc.id]);

    return newDoc.id;
  }
}

function sanitizeSimpleText(field: string | null | undefined): string {
  return typeof field === 'string' ? field.trim() : '无';
}
