// 千牛账号相关 hooks

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { queryKeys } from '../../lib/query-keys';
import { createQianniuAccountMutation, deleteQianniuAccountMutation, qianniuAccountQueryOptions, qianniuAccountsQueryOptions, updateQianniuAccountMutation } from '../api/qianniuAccounts';

// 获取客户端的千牛账号列表
export function useQianniuAccounts(clientId: string) {
  return useQuery(qianniuAccountsQueryOptions(clientId));
}

// 获取单个千牛账号详情
export function useQianniuAccount(accountId: string) {
  return useQuery(qianniuAccountQueryOptions(accountId));
}

// 创建千牛账号
export function useCreateQianniuAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    ...createQianniuAccountMutation,
    onSuccess: (data) => {
      // 刷新客户端账号列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.qianniuAccounts(data.clientId),
      });
      // 刷新客户端详情（包含账号数量）
      queryClient.invalidateQueries({
        queryKey: queryKeys.qianniuClient(data.clientId),
      });
    },
  });
}

// 更新千牛账号
export function useUpdateQianniuAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    ...updateQianniuAccountMutation,
    onSuccess: (data) => {
      // 更新账号详情缓存
      queryClient.setQueryData(queryKeys.qianniuAccount(data.id), data);
      // 刷新客户端账号列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.qianniuAccounts(data.clientId),
      });
      // 刷新客户端详情
      queryClient.invalidateQueries({
        queryKey: queryKeys.qianniuClient(data.clientId),
      });
    },
  });
}

// 删除千牛账号
export function useDeleteQianniuAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    ...deleteQianniuAccountMutation,
    onSuccess: (deletedAccount) => {
      // 移除账号详情缓存
      queryClient.removeQueries({
        queryKey: queryKeys.qianniuAccount(deletedAccount.id),
      });
      // 刷新客户端账号列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.qianniuAccounts(deletedAccount.clientId),
      });
      // 刷新客户端详情
      queryClient.invalidateQueries({
        queryKey: queryKeys.qianniuClient(deletedAccount.clientId),
      });
    },
  });
}
