version: '3.8'

services:
  # 后端API服务
  ykwy-assistant-api:
    build: 
      context: ./ykwy-assistant-api
      dockerfile: Dockerfile
    ports:
      - "3000:3000"      # HTTP API端口
      - "3001:3001"      # WebSocket端口
    environment:
      - NODE_ENV=production
      - DATABASE_URL=***************************************/ykwy_assistant
      - SALES_AGENT_URL=http://sales-agent:8000
      - WEBSOCKET_PORT=3001
    volumes:
      - ./logs/api:/app/logs                    # API日志挂载
    depends_on:
      - postgres
      - sales-agent
    networks:
      - ykwy-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 销售代理服务
  sales-agent:
    build:
      context: ./mock-production
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - LOG_DIR=/app/logs                       # 日志目录
      - PYTHONPATH=/app/src
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
    volumes:
      - ./logs/sales-agent:/app/logs            # 销售代理日志挂载
      - ./mock-production/data:/app/data        # 数据目录挂载
    networks:
      - ykwy-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 数据库服务
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=ykwy_assistant
      - POSTGRES_USER=ykwy
      - POSTGRES_PASSWORD=ykwy123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./logs/postgres:/var/log/postgresql     # 数据库日志挂载
    ports:
      - "5432:5432"
    networks:
      - ykwy-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 日志收集服务（可选）
  log-viewer:
    image: amir20/dozzle:latest
    ports:
      - "9999:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - ykwy-network

networks:
  ykwy-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
