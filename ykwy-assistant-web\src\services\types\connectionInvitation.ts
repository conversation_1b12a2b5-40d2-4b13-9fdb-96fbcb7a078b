// 连接邀请相关类型定义

export interface ConnectionInvitation {
  id: string;
  name: string;
  description?: string;
  status: 'PENDING' | 'ACTIVATED' | 'EXPIRED' | 'REVOKED';
  expiresAt: string;
  createdAt: string;
  activatedAt?: string;
  organization: {
    id: string;
    name: string;
  };
  team: {
    id: string;
    name: string;
  };
  qianniuClient?: {
    id: string;
    name: string;
    isOnline: boolean;
  };
}

export interface CreateConnectionInvitationRequest extends Record<string, unknown> {
  name: string;
  description?: string;
  teamId: string;
  expiresInDays?: number;
}

export interface ConnectionInvitationResponse {
  invitation: ConnectionInvitation;
  cdnUrl: string;
  deploymentInstructions: {
    method1: {
      title: string;
      command: string;
      description: string;
    };
    method2: {
      title: string;
      steps: string[];
    };
    notes: string[];
  };
}

export interface ConnectionInvitationFilters extends Record<string, unknown> {
  organizationId?: string;
  status?: ConnectionInvitation['status'];
  teamId?: string;
}

export interface RegenerateConnectionInvitationData extends Record<string, unknown> {
  expiresInDays?: number;
}
