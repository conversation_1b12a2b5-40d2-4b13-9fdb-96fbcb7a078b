### 场景Q&A API测试

@baseUrl = http://localhost:3009
@accessToken = PoNyP4f0zYDWuXg8NytR

### ================================
### 场景Q&A创建和查询测试
### ================================

### 1. 创建场景Q&A - 成功场景
POST {{baseUrl}}/api/v2/scene-qa
Content-Type: application/json
Access-Token: {{accessToken}}

{
  "question": "你们的AI写作工具是正品吗？有保障吗？",
  "answer": "我们是智创科技官方旗舰店，提供100%正品保障。产品基于GPT-4 Turbo架构，拥有软件著作权和相关资质证明。我们承诺99.9%系统稳定性，30天无理由退款，让您放心使用。",
  "keywords": ["正品", "保障", "官方"],
  "intent": "商品信息类",
  "productUrl": "http://item.taobao.com/item.htm?id=953394282130",
  "shopId": "wyts666"
}

### 2. 更新场景Q&A - 基于question+intent组合唯一键
POST {{baseUrl}}/api/v2/scene-qa
Content-Type: application/json
Access-Token: {{accessToken}}

{
  "question": "你们的AI写作工具是正品吗？有保障吗？",
  "answer": "我们是智创科技官方旗舰店，提供100%正品保障。产品基于GPT-4 Turbo架构，拥有软件著作权和相关资质证明。我们承诺99.9%系统稳定性，30天无理由退款，让您放心使用。如有任何问题，请随时联系我们的客服团队。",
  "keywords": ["正品", "保障", "官方", "客服"],
  "intent": "商品信息类",
  "productUrl": "http://item.taobao.com/item.htm?id=953394282130",
  "shopId": "wyts666"
}

### 2.1. 创建相同问题但不同意图的Q&A - 验证组合唯一键
POST {{baseUrl}}/api/v2/scene-qa
Content-Type: application/json
Access-Token: {{accessToken}}

{
  "question": "你们的AI写作工具是正品吗？有保障吗？",
  "answer": "我们支持7天无理由退换货，如果对产品质量有疑问，可以申请退款。我们的售后服务团队会及时处理您的退款申请。",
  "keywords": ["退款", "售后", "服务"],
  "intent": "售后服务类",
  "productUrl": "http://item.taobao.com/item.htm?id=953394282130",
  "shopId": "wyts666"
}

### 3. 获取场景Q&A详情
GET {{baseUrl}}/api/v2/scene-qa/{{sceneQAId}}
Access-Token: {{accessToken}}

### 4. 搜索场景Q&A - 按关键词搜索 (需要shopId)
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666&query=正品
Access-Token: {{accessToken}}

### 5. 搜索场景Q&A - 全文搜索 (使用新的 searchByQuery，需要 shopId)
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666&query=AI写作
Access-Token: {{accessToken}}

### 5.1. 搜索场景Q&A - 按店铺ID查询所有
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666
Access-Token: {{accessToken}}

### 5.2. 搜索场景Q&A - 按店铺ID + 意图精确查询  
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666&intent=商品信息类
Access-Token: {{accessToken}}

### 5.3. 搜索场景Q&A - 按店铺ID + 问题精确查询
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666&question=你们的AI写作工具是正品吗？有保障吗？
Access-Token: {{accessToken}}

### 5.4. 搜索场景Q&A - 按店铺ID + 关键词精确查询
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666&keyword=正品
Access-Token: {{accessToken}}

### 5.5. 搜索场景Q&A - 关键词反向匹配测试 (query包含关键词)
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666&query=请问你们的产品是正品授权的吗我想确认一下
Access-Token: {{accessToken}}

### 5.6. 搜索场景Q&A - 按店铺ID + 商品链接精确查询
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666&productUrl=http://item.taobao.com/item.htm?id=953394282130
Access-Token: {{accessToken}}

### 5.7. 搜索场景Q&A - 多字段组合查询
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666&intent=商品信息类&keyword=正品
Access-Token: {{accessToken}}

### 5.7. 搜索场景Q&A - 分页查询
GET {{baseUrl}}/api/v2/scene-qa/search?shopId=wyts666&skip=0&take=5
Access-Token: {{accessToken}}

### 6. 搜索场景Q&A - 缺少必须的 shopId 参数（应该报错）
GET {{baseUrl}}/api/v2/scene-qa/search?query=查询
Access-Token: {{accessToken}}

### 7. 获取场景Q&A列表 - 分页查询
GET {{baseUrl}}/api/v2/scene-qa?skip=0&take=10
Access-Token: {{accessToken}}

### 8. 获取场景Q&A列表 - 按问题过滤
GET {{baseUrl}}/api/v2/scene-qa?question=订单&skip=0&take=5
Access-Token: {{accessToken}}

### 9. 获取场景Q&A列表 - 按意图过滤
GET {{baseUrl}}/api/v2/scene-qa?intent=查询&skip=0&take=10
Access-Token: {{accessToken}}

### 9.1. 按商品链接过滤
GET {{baseUrl}}/api/v2/scene-qa?productUrl=taobao.com&skip=0&take=10
Access-Token: {{accessToken}}

### 9.2. 按店铺ID过滤
GET {{baseUrl}}/api/v2/scene-qa?shopId=shop123&skip=0&take=10
Access-Token: {{accessToken}}

### 9.3. 组合过滤（意图+店铺ID）
GET {{baseUrl}}/api/v2/scene-qa?intent=订单查询&shopId=shop123&skip=0&take=10
Access-Token: {{accessToken}}

### 10. 获取场景Q&A列表 - 按关键词过滤
GET {{baseUrl}}/api/v2/scene-qa?keyword=物流&skip=0&take=10
Access-Token: {{accessToken}}

### ================================
### 批量操作测试
### ================================

### 11. 批量创建场景Q&A
POST {{baseUrl}}/api/v2/scene-qa/bulk-create
Content-Type: application/json
Access-Token: {{accessToken}}

[
  {
    "question": "如何申请退款？",
    "answer": "退款申请步骤：1. 进入订单详情；2. 点击申请退款；3. 填写退款原因；4. 等待审核处理。",
    "keywords": ["退款", "申请退款", "退货"],
    "intent": "售后服务",
    "productUrl": "https://item.taobao.com/item.htm?id=111222333",
    "shopId": "shop001"
  },
  {
    "question": "如何联系客服？",
    "answer": "您可以通过以下方式联系客服：1. 在线客服聊天；2. 拨打客服热线；3. 发送邮件至客服邮箱。",
    "keywords": ["联系客服", "客服电话", "在线客服"],
    "intent": "客服咨询",
    "shopId": "shop002"
  },
  {
    "question": "支付方式有哪些？",
    "answer": "我们支持多种支付方式：微信支付、支付宝、银行卡支付、信用卡支付等。",
    "keywords": ["支付方式", "付款", "支付"],
    "intent": "支付咨询",
    "productUrl": "https://item.taobao.com/item.htm?id=444555666"
  }
]

### 12. 批量删除场景Q&A
POST {{baseUrl}}/api/v2/scene-qa/bulk-delete
Content-Type: application/json
Access-Token: {{accessToken}}

{
  "ids": ["id1", "id2", "id3"]
}

### ================================
### 删除操作测试
### ================================

### 13. 删除单个场景Q&A
DELETE {{baseUrl}}/api/v2/scene-qa/{{sceneQAId}}
Access-Token: {{accessToken}}

### 14. 清空所有场景Q&A（谨慎使用）
DELETE {{baseUrl}}/api/v2/scene-qa/truncate
Access-Token: {{accessToken}}

### ================================
### 错误测试
### ================================

### 15. 创建场景Q&A - 缺少必填字段
// 缺少 answer, keywords, intent
POST {{baseUrl}}/api/v2/scene-qa
Content-Type: application/json
Access-Token: {{accessToken}}

{
  "question": "测试问题"
}

### 16. 创建场景Q&A - 商品链接格式错误
POST {{baseUrl}}/api/v2/scene-qa
Content-Type: application/json
Access-Token: {{accessToken}}

{
  "question": "测试问题？",
  "answer": "测试答案",
  "keywords": ["测试"],
  "intent": "测试意图",
  "productUrl": "invalid-url",
  "shopId": "shop123"
}

### 17. 获取不存在的场景Q&A
GET {{baseUrl}}/api/v2/scene-qa/nonexistent-id
Access-Token: {{accessToken}}

### 18. 无效的分页参数
GET {{baseUrl}}/api/v2/scene-qa?skip=-1&take=0
Access-Token: {{accessToken}}

### 18. 批量删除 - 空ID列表
POST {{baseUrl}}/api/v2/scene-qa/bulk-delete
Content-Type: application/json
Access-Token: {{accessToken}}

{
  "ids": ["30a04f63-57eb-4fd6-8faf-bc80eb8e8e3a"]
} 