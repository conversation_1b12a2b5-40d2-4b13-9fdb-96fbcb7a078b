import { Plus } from 'lucide-react';
import { useState } from 'react';

import { ConnectionInvitationList } from '../../components/qianniu/ConnectionInvitationList';
import { CreateConnectionInvitationDialog } from '../../components/qianniu/CreateConnectionInvitationDialog';
import { QianniuClientList } from '../../components/qianniu/QianniuClientList';
import { QianniuMonitorDashboard } from '../../components/qianniu/QianniuMonitorDashboard';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function ConnectionManagePage() {
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  return (
    <div className="p-6 space-y-6 min-h-full max-w-6xl mx-auto">
      {/* 页面标题和操作 */}
      <div className="flex justify-between gap-4 items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">连接管理</h1>
          <p className="text-gray-600">管理千牛客户端连接邀请、在线状态和TCP连接</p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="w-4 h-4 mr-2" />
          创建连接邀请
        </Button>
      </div>

      {/* 标签页内容 */}
      <Tabs defaultValue="invitations" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 h-12 p-1 bg-gray-100 rounded-lg">
          <TabsTrigger value="invitations" className="h-10 px-6 text-sm font-medium rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">
            连接邀请
          </TabsTrigger>
          <TabsTrigger value="clients" className="h-10 px-6 text-sm font-medium rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">
            在线客户端
          </TabsTrigger>
          <TabsTrigger value="monitor" className="h-10 px-6 text-sm font-medium rounded-md data-[state=active]:bg-white data-[state=active]:shadow-sm">
            监控面板
          </TabsTrigger>
        </TabsList>

        <TabsContent value="invitations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>连接邀请</CardTitle>
            </CardHeader>
            <CardContent>
              <ConnectionInvitationList />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="clients" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>在线客户端</CardTitle>
            </CardHeader>
            <CardContent>
              <QianniuClientList />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitor" className="space-y-4">
          <QianniuMonitorDashboard />
        </TabsContent>
      </Tabs>

      {/* 创建邀请对话框 */}
      <CreateConnectionInvitationDialog open={showCreateDialog} onOpenChange={setShowCreateDialog} />
    </div>
  );
}
