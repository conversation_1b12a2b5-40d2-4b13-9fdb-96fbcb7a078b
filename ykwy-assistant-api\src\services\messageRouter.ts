import crypto from 'crypto';

import { qianniuAdapter } from '../adapters/qianniuAdapter';
import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { normalizeConversationCode } from '../lib/utils';
import { broadcastConversationSwitch } from '../routes/qianniu-tcp';
import type { PlatformAdapter, PlatformType, StandardMessage } from '../types/platform';
import { connectionBasedNotificationService } from './connectionBasedNotificationService';
import { notifyConversationListChanged, notifyCustomerMessage, notifyMessageListChanged } from './eventService';
import { salesAgentIntegrationService } from './salesAgentIntegrationService';

// 平台连接数据接口
interface PlatformConnection {
  connectionId: string;
  platformType: PlatformType;
  organizationId: string;
  teamId: string;
  platformData: Record<string, unknown>; // 平台特定数据
}

/**
 * 消息路由服务
 * 处理平台消息与内部消息的转换和路由
 */
class MessageRouter {
  /**
   * 统一的平台消息路由入口
   */
  async routePlatformMessage(connection: PlatformConnection, rawMessage: Record<string, unknown>): Promise<void> {
    const routingId = `route_${Date.now()}_${crypto.randomUUID()}`;
    const startTime = Date.now();
    let conversation: any = null;
    let savedMessage: any = null;

    try {
      logger.info('🎯 [MessageRouter] 开始处理平台消息', {
        routingId,
        platformType: connection.platformType,
        connectionId: connection.connectionId,
        organizationId: connection.organizationId,
        teamId: connection.teamId,
        messageKeys: Object.keys(rawMessage),
      });

      logger.debug('📋 [MessageRouter] 原始消息详情', {
        routingId,
        platformType: connection.platformType,
        rawMessage: JSON.stringify(rawMessage, null, 2),
      });
      // 1. 获取平台适配器
      logger.debug('🔧 [MessageRouter] 获取平台适配器', {
        routingId,
        platformType: connection.platformType,
      });
      const adapter = this.getPlatformAdapter(connection.platformType);

      // 2. 转换为标准消息格式
      logger.debug('🔄 [MessageRouter] 开始消息转换', {
        routingId,
        platformType: connection.platformType,
        adapterName: adapter.constructor.name,
      });

      const standardMessage = adapter.transformInbound(connection.platformData, rawMessage);
      if (!standardMessage) {
        logger.warn('⚠️ [MessageRouter] 平台消息转换失败', {
          routingId,
          platformType: connection.platformType,
          connectionId: connection.connectionId,
          rawMessageType: rawMessage['type'] || rawMessage['eventType'],
          processingTime: Date.now() - startTime,
        });
        return;
      }

      logger.info('✅ [MessageRouter] 消息转换成功', {
        routingId,
        standardMessageType: standardMessage.messageType,
        senderType: standardMessage.senderType,
        contentLength: standardMessage.content?.length || 0,
        platformMessageId: standardMessage.platformMessageId,
      });

      // 3. 查找或创建平台账号（基于消息中的loginID）
      logger.debug('👤 [MessageRouter] 查找或创建平台账号', {
        routingId,
        platformType: connection.platformType,
        loginID: rawMessage['loginID'],
      });

      const account = await this.findOrCreatePlatformAccount(connection, rawMessage);
      if (!account) {
        logger.warn('⚠️ [MessageRouter] 查找或创建平台账号失败', {
          routingId,
          platformType: connection.platformType,
          connectionId: connection.connectionId,
          loginID: rawMessage['loginID'],
          processingTime: Date.now() - startTime,
        });
        return;
      }

      logger.debug('✅ [MessageRouter] 平台账号处理成功', {
        routingId,
        accountId: account.id,
        platformAccountId: (account as any).platformAccountId,
      });

      // 4. 查找或创建客户
      logger.debug('🧑‍💼 [MessageRouter] 查找或创建客户', {
        routingId,
        accountId: account.id,
        customerInfo: (standardMessage as any).customer,
      });

      const customer = await this.findOrCreateCustomer(account, standardMessage);

      logger.debug('✅ [MessageRouter] 客户处理成功', {
        routingId,
        customerId: customer.id,
        customerName: customer.nickname,
      });

      // 5. 查找或创建对话
      logger.debug('💬 [MessageRouter] 查找或创建对话', {
        routingId,
        accountId: account.id,
        customerId: customer.id,
      });

      // 从消息metadata中提取conversationCode
      const conversationCode = standardMessage.metadata?.['conversationCode'] as string;

      conversation = await this.findOrCreateConversation(account, customer.id, conversationCode);

      logger.debug('✅ [MessageRouter] 对话处理成功', {
        routingId,
        conversationId: conversation.id,
        conversationStatus: conversation.status,
        conversationCode,
      });

      // 6. 检查消息是否已存在（去重）
      if (standardMessage.platformMessageId) {
        logger.debug('🔍 [MessageRouter] 检查消息重复性', {
          routingId,
          platformMessageId: standardMessage.platformMessageId,
          conversationId: conversation.id,
        });

        const existingMessage = await prisma.message.findFirst({
          where: {
            conversationId: conversation.id,
            platformMessageId: standardMessage.platformMessageId,
          },
        });

        if (existingMessage) {
          logger.warn('⚠️ [MessageRouter] 检测到重复消息，跳过处理', {
            routingId,
            platformMessageId: standardMessage.platformMessageId,
            existingMessageId: existingMessage.id,
            processingTime: Date.now() - startTime,
          });
          return;
        }

        logger.debug('✅ [MessageRouter] 消息重复性检查通过', {
          routingId,
          platformMessageId: standardMessage.platformMessageId,
        });
      }

      // 7. 保存消息
      logger.debug('💾 [MessageRouter] 开始保存消息', {
        routingId,
        conversationId: conversation.id,
        messageType: standardMessage.messageType,
        senderType: standardMessage.senderType,
      });

      savedMessage = await this.saveMessage(conversation.id, standardMessage);

      // 🔧 优化：移除不可达的 !savedMessage 检查
      // saveMessage 要么返回消息对象，要么抛出异常（已被 try-catch 处理）
      logger.info('✅ [MessageRouter] 消息保存成功', {
        routingId,
        messageId: savedMessage.id,
        conversationId: conversation.id,
      });

      // 8. 通知团队成员
      logger.debug('📢 [MessageRouter] 开始团队成员通知', {
        routingId,
        teamId: connection.teamId,
        messageId: savedMessage.id,
      });

      await this.notifyTeamMembers(account, savedMessage, conversation, customer);

      logger.debug('✅ [MessageRouter] 团队成员通知完成', {
        routingId,
        messageId: savedMessage.id,
      });

      // 9. 检查是否为会话切换事件
      if (standardMessage.metadata?.['isConversationSwitch']) {
        logger.info('🔄 [MessageRouter] 检测到会话切换事件，开始广播', {
          routingId,
          conversationId: conversation.id,
          organizationId: connection.organizationId,
        });

        await this.notifyConversationSwitch(connection, standardMessage, conversation);
      }

      // 10. 通知组织级别
      logger.debug('📡 [MessageRouter] 开始组织级别通知', {
        routingId,
        organizationId: connection.organizationId,
        conversationId: conversation.id,
      });

      this.notifyCustomerService(connection.organizationId, conversation.id);
    } catch (error) {
      logger.debug('✅ [MessageRouter] 组织级别通知完成', {
        routingId,
        organizationId: connection.organizationId,
      });

      logger.debug('✅ [MessageRouter] 组织级别通知完成', {
        routingId,
        organizationId: connection.organizationId,
      });

      logger.info('🎉 [MessageRouter] 平台消息处理成功', {
        routingId,
        platformType: connection.platformType,
        connectionId: connection.connectionId,
        conversationId: conversation.id,
        messageId: savedMessage.id,
        processingTime: Date.now() - startTime,
        totalSteps: 9,
      });

      logger.error(
        '💥 [MessageRouter] 平台消息处理失败',
        {
          routingId,
          platformType: connection.platformType,
          connectionId: connection.connectionId,
          organizationId: connection.organizationId,
          teamId: connection.teamId,
          processingTime: Date.now() - startTime,
          errorName: error instanceof Error ? error.name : 'Unknown',
          errorMessage: error instanceof Error ? error.message : String(error),
          errorStack: error instanceof Error ? error.stack : undefined,
          rawMessageType: rawMessage['type'] || rawMessage['eventType'],
        },
        error instanceof Error ? error : new Error(String(error)),
      );
    }
  }

  // 废弃的方法已删除，只保留统一的平台架构

  /**
   * 获取平台适配器（目前只支持千牛）
   */
  private getPlatformAdapter(platformType: PlatformType): PlatformAdapter {
    if (platformType === 'QIANNIU') {
      return qianniuAdapter;
    }
    throw new Error(`Unsupported platform type: ${platformType}. Only QIANNIU is supported.`);
  }

  /**
   * 查找或创建平台账号（目前只支持千牛）
   */
  private async findOrCreatePlatformAccount(connection: PlatformConnection, rawMessage: Record<string, unknown>) {
    if (connection.platformType === 'QIANNIU') {
      return await this.findOrCreateQianniuAccount(connection, rawMessage);
    }
    throw new Error(`Unsupported platform type: ${connection.platformType}. Only QIANNIU is supported.`);
  }

  /**
   * 查找或创建千牛账号（基于消息中的loginID）
   */
  private async findOrCreateQianniuAccount(connection: PlatformConnection, rawMessage: Record<string, unknown>) {
    const { clientId } = connection.platformData;

    // 统一从 response 中解析 loginID (支持多种位置)
    let loginID: { nick?: string; uid?: string } | null = null;
    const msg = rawMessage as { response?: string };

    if (msg.response) {
      try {
        const responseData = JSON.parse(msg.response);

        // 方法1: 直接在根级别查找 (onShopRobotReceriveNewMsgs 格式)
        if (responseData.loginID) {
          loginID = responseData.loginID;
        }
        // 方法2: 在 result[0].loginid 中查找 (receiveNewMsg 格式)
        else if (responseData.result?.[0]?.loginid) {
          loginID = responseData.result[0].loginid;
        }

        console.log('[MessageRouter] 解析到 loginID:', loginID?.nick);
      } catch (error) {
        console.warn('[MessageRouter] 解析 response 失败:', error);
      }
    }

    if (!loginID?.nick) {
      console.error('[MessageRouter] 找不到 loginID，无法识别账号');
      console.error('[MessageRouter] 消息内容:', JSON.stringify(rawMessage, null, 2).substring(0, 300));
      return null;
    }

    console.log('[MessageRouter] 找到账号:', loginID.nick);

    console.log(`[MessageRouter] Looking for account: clientId=${clientId}, accountName=${loginID.nick}`);

    // 查找现有账号
    let account = await prisma.qianniuAccount.findFirst({
      where: {
        clientId: clientId as string,
        accountName: loginID.nick,
      },
      include: {
        client: {
          include: {
            team: {
              include: {
                members: true,
              },
            },
          },
        },
        brand: true,
      },
    });

    // 如果账号不存在，尝试自动创建
    if (!account) {
      console.log(`[MessageRouter] Account not found, attempting to create: ${loginID.nick}`);
      const newAccount = await this.createQianniuAccountIfPossible(clientId as string, loginID);
      if (newAccount) {
        // 重新查询以获得一致的include结构
        account = await prisma.qianniuAccount.findUnique({
          where: { id: newAccount.id },
          include: {
            client: {
              include: {
                team: {
                  include: {
                    members: true,
                  },
                },
              },
            },
            brand: true,
          },
        });
      }
    }

    return account;
  }

  /**
   * 查找或创建千牛账号（如果有默认品牌）
   */
  private async createQianniuAccountIfPossible(clientId: string, loginID: Record<string, unknown>) {
    try {
      // 查找客户端信息
      const client = await prisma.qianniuClient.findUnique({
        where: { id: clientId },
        include: {
          team: true,
          organization: true,
        },
      });

      if (!client) {
        console.warn(`[MessageRouter] Client not found: ${clientId}`);
        return null;
      }

      // 查找该组织下的品牌
      const defaultBrand = await prisma.brand.findFirst({
        where: {
          organizationId: client.organizationId,
          isActive: true,
        },
        orderBy: {
          createdAt: 'asc', // 使用最早创建的品牌作为默认
        },
      });

      if (!defaultBrand) {
        console.warn(`[MessageRouter] No brand found for organization: ${client.organizationId}`);
        return null;
      }

      // 先检查是否已存在该品牌在该平台的账号
      const existingAccount = await prisma.qianniuAccount.findFirst({
        where: {
          brandId: defaultBrand.id,
          platformType: 'TAOBAO',
        },
        include: {
          client: {
            include: {
              team: {
                include: {
                  members: true,
                },
              },
            },
          },
          brand: true,
        },
      });

      if (existingAccount) {
        console.log(`[MessageRouter] Found existing account for brand ${defaultBrand.name}: ${existingAccount.accountName}`);

        // 更新现有账号的客户端关联和登录状态
        const updatedAccount = await prisma.qianniuAccount.update({
          where: { id: existingAccount.id },
          data: {
            clientId: clientId, // 更新到当前客户端
            accountName: loginID['nick'] as string, // 更新账号名
            accountId: (loginID['uid'] as string) || (loginID['nick'] as string), // 更新账号ID
            isLoggedIn: true,
            lastLoginAt: new Date(),
          },
          include: {
            client: {
              include: {
                team: {
                  include: {
                    members: true,
                  },
                },
              },
            },
            brand: true,
          },
        });

        return updatedAccount;
      }

      // 如果不存在，则创建新的千牛账号
      const newAccount = await prisma.qianniuAccount.create({
        data: {
          clientId: clientId,
          brandId: defaultBrand.id,
          accountName: loginID['nick'] as string,
          accountId: (loginID['uid'] as string) || (loginID['nick'] as string),
          platformType: 'TAOBAO', // 默认淘宝平台
          isActive: true,
          isLoggedIn: true,
          lastLoginAt: new Date(),
        },
        include: {
          client: {
            include: {
              team: {
                include: {
                  members: true,
                },
              },
            },
          },
          brand: true,
        },
      });

      console.log(`[MessageRouter] Created new account: ${newAccount.accountName} for brand: ${defaultBrand.name}`);
      return newAccount;
    } catch (error) {
      console.error('[MessageRouter] Failed to find or create account:', error);
      return null;
    }
  }

  /**
   * 查找或创建客户（统一接口）
   */
  private async findOrCreateCustomer(account: Record<string, unknown>, standardMessage: StandardMessage) {
    // 查找现有客户
    const existingCustomer = await prisma.customer.findFirst({
      where: {
        qianniuAccountId: account['id'] as string,
        platformCustomerId: standardMessage.senderId,
      },
    });

    if (existingCustomer) {
      return existingCustomer;
    }

    // 创建新客户
    return await prisma.customer.create({
      data: {
        qianniuAccountId: account['id'] as string,
        platformCustomerId: standardMessage.senderId,
        nickname: standardMessage.senderName || `客户_${standardMessage.senderId}`,
      },
    });
  }

  /**
   * 查找或创建对话（支持千牛conversationCode）
   */
  private async findOrCreateConversation(
    account: Record<string, unknown>,
    customerId: string,
    conversationCode?: string,
  ): Promise<{ id: string; conversationCode: string | null; [key: string]: any }> {
    if (conversationCode) {
      // 标准化conversationCode格式，确保一致性
      const normalizedCode = normalizeConversationCode(conversationCode);

      if (!normalizedCode) {
        logger.warn('conversationCode标准化后为空，使用原始值', { originalCode: conversationCode });
        // 如果标准化失败，回退到不使用conversationCode的逻辑
        return this.findOrCreateConversation(account, customerId);
      }

      logger.debug('查找现有会话', {
        originalCode: conversationCode,
        normalizedCode,
        customerId,
      });

      // 使用标准化的conversationCode进行查找
      const existingConversation = await prisma.conversation.findFirst({
        where: {
          qianniuAccountId: account['id'] as string,
          customerId,
          status: { not: 'CLOSED' },
          OR: [
            {
              conversationCode: normalizedCode, // 精确匹配标准化格式
            },
            {
              conversationCode: { startsWith: `${normalizedCode}#` }, // 前缀匹配，兼容性处理
            },
          ],
        },
        orderBy: [
          {
            // 使用消息数量作为主要排序依据，优先选择有消息的对话
            messages: { _count: 'desc' },
          },
          { lastActivity: 'desc' }, // 其次选择最近活跃的对话
          { createdAt: 'desc' }, // 最后选择最新创建的
        ],
      });

      if (existingConversation) {
        logger.info('✅ 找到现有会话，复用对话', {
          conversationId: existingConversation.id,
          existingConversationCode: existingConversation.conversationCode,
          searchNormalizedCode: normalizedCode,
          customerId,
        });

        // 如果找到的对话使用的是非标准化格式，更新为标准化格式
        if (existingConversation.conversationCode !== normalizedCode) {
          logger.info('🔄 将对话conversationCode更新为标准化格式', {
            conversationId: existingConversation.id,
            从: existingConversation.conversationCode,
            到: normalizedCode,
          });

          await prisma.conversation.update({
            where: { id: existingConversation.id },
            data: {
              conversationCode: normalizedCode, // 更新为标准化格式
              lastActivity: new Date(),
            },
          });
        } else {
          // 只更新活动时间
          await prisma.conversation.update({
            where: { id: existingConversation.id },
            data: { lastActivity: new Date() },
          });
        }

        return existingConversation;
      }

      // 兼容性处理：如果新字段中没找到，查找旧的 title 字段
      const legacyConversation = await prisma.conversation.findFirst({
        where: {
          qianniuAccountId: account['id'] as string,
          customerId,
          status: { not: 'CLOSED' },
          OR: [
            { title: normalizedCode }, // 使用标准化格式匹配旧格式
            { title: { startsWith: `${normalizedCode}#` } }, // 前缀匹配旧格式
          ],
          conversationCode: null, // 确保是旧数据
        },
        orderBy: [
          {
            // 使用消息数量作为主要排序依据，优先选择有消息的对话
            messages: { _count: 'desc' },
          },
          { lastActivity: 'desc' }, // 其次选择最近活跃的对话
          { createdAt: 'desc' }, // 最后选择最新创建的
        ],
      });

      if (legacyConversation) {
        logger.info('✅ 找到旧格式会话，迁移到新字段', {
          conversationId: legacyConversation.id,
          existingTitle: legacyConversation.title,
          normalizedCode,
          customerId,
        });

        // 迁移到新字段并使用标准化格式
        await prisma.conversation.update({
          where: { id: legacyConversation.id },
          data: {
            conversationCode: normalizedCode, // 使用标准化格式
            title: null, // 清理旧的 title 字段
            lastActivity: new Date(),
          },
        });

        return legacyConversation;
      }

      // 如果没找到，创建新对话并使用标准化的conversationCode
      logger.info('🆕 创建新会话使用标准化conversationCode', {
        normalizedCode,
        originalCode: conversationCode,
        customerId,
      });
      return await prisma.conversation.create({
        data: {
          qianniuAccountId: account['id'] as string,
          customerId,
          organizationId: (account['client'] as Record<string, unknown>)['organizationId'] as string,
          status: 'PENDING',
          conversationCode: normalizedCode, // 使用标准化格式
        },
      });
    }

    // 原有逻辑：根据账号和客户ID查找（无conversationCode的情况）
    const existingConversation = await prisma.conversation.findFirst({
      where: {
        qianniuAccountId: account['id'] as string,
        customerId,
        status: { not: 'CLOSED' },
      },
    });

    if (existingConversation) {
      logger.debug('找到现有会话通过账号和客户ID', { conversationId: existingConversation.id });
      return existingConversation;
    }

    // 创建新对话
    logger.info('🆕 创建新会话（无conversationCode）', { customerId });
    return await prisma.conversation.create({
      data: {
        qianniuAccountId: account['id'] as string,
        customerId,
        organizationId: (account['client'] as Record<string, unknown>)['organizationId'] as string,
        status: 'PENDING',
      },
    });
  }

  /**
   * 通知团队成员（统一接口）
   */
  private async notifyTeamMembers(account: Record<string, unknown>, message: Record<string, unknown>, conversation: Record<string, unknown>, customer?: Record<string, unknown>) {
    const client = account['client'] as Record<string, unknown>;
    const team = client['team'] as Record<string, unknown>;
    const brand = account['brand'] as Record<string, unknown>;
    const organizationId = client['organizationId'] as string;
    const connectionId = client['connectionId'] as string;

    console.log(`[MessageRouter] Notifying team ${team['name']} about new message from brand ${brand['name']}`);

    // 通过WebSocket广播新消息通知给组织内的所有在线客服
    // 这样团队成员就能实时看到新消息
    const conversationId = message['conversationId'] as string;

    // 使用连接绑定的通知服务发送精确通知
    if (connectionId) {
      // 向绑定了该连接的在线用户发送消息列表和对话列表更新通知
      const messageListNotification = JSON.stringify({
        type: 'MESSAGE_LIST_CHANGED',
        data: { conversationId },
      });
      const conversationListNotification = JSON.stringify({
        type: 'CONVERSATION_LIST_CHANGED',
        data: {},
      });

      await connectionBasedNotificationService.notifyBoundUsers(connectionId, organizationId, messageListNotification);
      await connectionBasedNotificationService.notifyBoundUsers(connectionId, organizationId, conversationListNotification);

      // 发送客户消息通知（如果是客户发送的消息）
      if (message['senderType'] === 'CUSTOMER') {
        // 获取自动回复状态
        const isAutoReplied = conversation['autoReplyEnabled'] === true;

        // 获取客户名称
        let customerName = '未知客户';
        if (customer && typeof customer === 'object' && 'nickname' in customer) {
          customerName = customer['nickname'] as string;
        }

        // 发送客户消息通知
        const customerMessageNotification = JSON.stringify({
          type: 'CUSTOMER_MESSAGE',
          data: {
            messageId: message['id'] as string,
            conversationId,
            customerName,
            isAutoReplied,
          },
        });

        await connectionBasedNotificationService.notifyBoundUsers(connectionId, organizationId, customerMessageNotification);
      }
    } else {
      // 回退到原有的广播通知方式（用于未绑定连接的情况）
      notifyMessageListChanged(organizationId, conversationId);
      notifyConversationListChanged(organizationId);

      // 发送客户消息通知（如果是客户发送的消息）
      if (message['senderType'] === 'CUSTOMER') {
        // 获取自动回复状态
        const isAutoReplied = conversation['autoReplyEnabled'] === true;

        // 获取客户名称
        let customerName = '未知客户';
        if (customer && typeof customer === 'object' && 'nickname' in customer) {
          customerName = customer['nickname'] as string;
        }

        // 发送客户消息通知
        notifyCustomerMessage(organizationId, message['id'] as string, conversationId, customerName, isAutoReplied);
      }
    }

    // 触发销售智能体自动回复处理（异步执行，不阻塞主流程）
    const messageForSalesAgent = {
      id: message['id'] as string,
      conversationId: message['conversationId'] as string,
      content: message['content'] as string,
      senderType: message['senderType'] as 'CUSTOMER' | 'CUSTOMER_SERVICE' | 'SYSTEM' | 'AI',
      messageType: message['messageType'] as string,
      createdAt: message['createdAt'] as Date,
      metadata: message['metadata'],
    };

    logger.info('🚀 [MessageRouter] 触发销售智能体处理', {
      messageId: messageForSalesAgent.id,
      conversationId: messageForSalesAgent.conversationId,
      senderType: messageForSalesAgent.senderType,
      contentPreview: messageForSalesAgent.content.substring(0, 100) + (messageForSalesAgent.content.length > 100 ? '...' : ''),
      organizationId,
    });

    salesAgentIntegrationService.handleNewMessage(messageForSalesAgent).catch((error: unknown) => {
      logger.error(
        '❌ [MessageRouter] Sales Agent auto reply error',
        {
          messageId: messageForSalesAgent.id,
          conversationId: messageForSalesAgent.conversationId,
          organizationId,
        },
        error instanceof Error ? error : new Error(String(error)),
      );
    });

    logger.debug('[MessageRouter] Notifications sent for conversation', { conversationId, organizationId });
  }

  /**
   * 保存消息（解决并发竞态条件）
   */
  private async saveMessage(conversationId: string, standardMessage: StandardMessage, retryCount = 5) {
    // 转换枚举值为数据库格式
    const messageType = standardMessage.messageType.toUpperCase() as 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
    const senderType = standardMessage.senderType.toUpperCase() as 'CUSTOMER' | 'CUSTOMER_SERVICE' | 'SYSTEM' | 'AI';

    for (let attempt = 1; attempt <= retryCount; attempt++) {
      try {
        // 使用事务确保原子性
        return await prisma.$transaction(async (tx) => {
          // 在事务中获取最新的序列号（带锁）
          const lastMessage = await tx.message.findFirst({
            where: { conversationId },
            orderBy: { sequenceNumber: 'desc' },
            select: { sequenceNumber: true },
          });

          const nextSequenceNumber = (lastMessage?.sequenceNumber || 0) + 1;

          // 查询对话信息不再需要存储在变量中，因为目前没有使用这些数据
          // 如果将来需要这些信息，可以取消下面的注释
          /*
          const autoReplyConfig = await tx.conversation.findUnique({
            where: { id: conversationId },
            select: { autoReplyEnabled: true },
          });
          */

          // 在同一事务中创建消息
          return await tx.message.create({
            data: {
              conversationId,
              content: standardMessage.content,
              messageType,
              senderType,
              platformMessageId: standardMessage.platformMessageId,
              metadata: (standardMessage.metadata as never) ?? undefined,
              createdAt: standardMessage.timestamp,
              sequenceNumber: nextSequenceNumber,
            },
            include: {
              conversation: true,
            },
          });
        });
      } catch (error: unknown) {
        // 如果是唯一约束冲突，进行重试
        if (
          error &&
          typeof error === 'object' &&
          'code' in error &&
          error.code === 'P2002' &&
          'meta' in error &&
          error.meta &&
          typeof error.meta === 'object' &&
          'target' in error.meta &&
          Array.isArray(error.meta.target) &&
          error.meta.target.includes('sequenceNumber')
        ) {
          console.log(`[MessageRouter] Sequence number conflict, retrying... (attempt ${attempt}/${retryCount})`);

          if (attempt === retryCount) {
            // 最后一次重试仍失败，抛出错误
            throw new Error(`Failed to save message after ${retryCount} attempts due to sequence number conflicts`);
          }

          // 指数退避延迟后重试，避免持续冲突
          const delay = Math.min(100 * Math.pow(2, attempt - 1) + Math.random() * 50, 1000);
          await new Promise((resolve) => setTimeout(resolve, delay));
          continue;
        }

        // 其他错误直接抛出
        throw error;
      }
    }

    // 添加这一行返回值，确保所有代码路径都有返回值
    throw new Error(`Failed to save message after ${retryCount} attempts`);
  }

  /**
   * 通知客服系统
   */
  private notifyCustomerService(organizationId: string, conversationId: string) {
    // 通知对话列表更新
    notifyConversationListChanged(organizationId);

    // 通知消息列表更新
    notifyMessageListChanged(organizationId, conversationId);
  }

  /**
   * 通知会话切换事件
   */
  private async notifyConversationSwitch(connection: PlatformConnection, standardMessage: StandardMessage, conversation: Record<string, unknown>) {
    try {
      const organizationId = connection.organizationId;
      const connectionId = connection.connectionId;

      // 从standardMessage的metadata中获取客户信息和conversationCode
      const customerInfo = standardMessage.metadata?.['customerInfo'] as { id: string; nickname: string; avatar?: string };
      const rawConversationCode = standardMessage.metadata?.['conversationCode'] as string;

      if (!customerInfo) {
        logger.warn('会话切换事件缺少客户信息', { conversationId: conversation['id'] });
        return;
      }

      // 标准化conversationCode，确保格式一致
      const normalizedConversationCode = normalizeConversationCode(rawConversationCode);

      if (!normalizedConversationCode) {
        logger.warn('会话切换事件conversationCode标准化失败', {
          rawCode: rawConversationCode,
          conversationId: conversation['id'],
        });
        return;
      }

      logger.info('🔄 会话切换conversationCode标准化', {
        原始: rawConversationCode,
        标准化: normalizedConversationCode,
      });

      // 构建会话切换数据 - 使用标准化的conversationCode作为conversationId
      const conversationData = {
        currentConversationId: normalizedConversationCode, // 使用标准化格式
        customerInfo: {
          id: customerInfo.id || standardMessage.senderId,
          nickname: customerInfo.nickname || standardMessage.senderName || '未知客户',
          avatar: customerInfo.avatar,
        },
        connectionId,
        timestamp: new Date().toISOString(),
        previousConversationId: undefined, // 暂时不跟踪之前的会话ID
      };

      logger.info('广播会话切换事件', {
        organizationId,
        原始conversationCode: rawConversationCode,
        标准化conversationCode: normalizedConversationCode,
        conversationData,
      });

      // 广播会话切换事件
      await broadcastConversationSwitch(organizationId, conversationData);
    } catch (error) {
      logger.error('会话切换通知失败', {}, error instanceof Error ? error : new Error(String(error)));
    }
  }
}

// 创建并导出单例
export const messageRouter = new MessageRouter();
