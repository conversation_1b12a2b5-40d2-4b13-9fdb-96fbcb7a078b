#!/usr/bin/env node

/**
 * 千牛TCP代理 - 增强版
 *
 * 使用方法:
 * node index.js [后端地址] [后端端口]
 *
 * 示例:
 * node index.js                           # 默认转发到 localhost:9997
 * node index.js api.example.com           # 转发到 api.example.com:9997
 * node index.js api.example.com 8080      # 转发到 api.example.com:8080
 */

import net from 'net';

// 解析命令行参数
const args = process.argv.slice(2);
const targetHost = args[0] || '127.0.0.1'; // 强制使用IPv4地址
const targetPort = parseInt(args[1]) || 9997;
const proxyPort = 9996;

console.log(`🚀 千牛TCP代理启动 - 增强版`);
console.log(`📡 监听端口: ${proxyPort} (千牛客户端连接这里)`);
console.log(`🎯 转发到: ${targetHost}:${targetPort}`);
console.log(`🔍 启用详细日志模式`);
console.log(``);

let connectionCount = 0;

// 创建TCP服务器
const server = net.createServer((clientSocket) => {
  connectionCount++;
  const connId = connectionCount;
  const startTime = Date.now();

  console.log(`✅ 新连接 #${connId} 来自 ${clientSocket.remoteAddress}:${clientSocket.remotePort}`);
  console.log(`📊 连接详情 #${connId}:`, {
    localAddress: clientSocket.localAddress,
    localPort: clientSocket.localPort,
    remoteAddress: clientSocket.remoteAddress,
    remotePort: clientSocket.remotePort,
    family: clientSocket.remoteFamily,
  });

  // 创建到后端服务器的连接
  const targetSocket = new net.Socket();
  let targetConnected = false;
  let dataTransferred = { clientToTarget: 0, targetToClient: 0 };

  // 连接到后端服务器
  console.log(`🔄 连接 #${connId} 正在连接后端服务器 ${targetHost}:${targetPort}...`);

  targetSocket.connect(targetPort, targetHost, () => {
    targetConnected = true;
    console.log(`🔗 连接 #${connId} 已成功连接到后端服务器`);
    console.log(`📊 后端连接详情 #${connId}:`, {
      localAddress: targetSocket.localAddress,
      localPort: targetSocket.localPort,
      remoteAddress: targetSocket.remoteAddress,
      remotePort: targetSocket.remotePort,
    });

    // 双向数据转发 - 使用手动转发以便记录数据
    clientSocket.on('data', (data) => {
      dataTransferred.clientToTarget += data.length;
      console.log(`📤 连接 #${connId} 客户端->后端: ${data.length} 字节 (总计: ${dataTransferred.clientToTarget})`);
      console.log(`📝 数据内容 #${connId}:`, data.toString('utf8').substring(0, 200));
      targetSocket.write(data);
    });

    targetSocket.on('data', (data) => {
      dataTransferred.targetToClient += data.length;
      console.log(`📥 连接 #${connId} 后端->客户端: ${data.length} 字节 (总计: ${dataTransferred.targetToClient})`);
      console.log(`📝 响应内容 #${connId}:`, data.toString('utf8').substring(0, 200));
      clientSocket.write(data);
    });
  });

  // 错误处理
  targetSocket.on('error', (err) => {
    const duration = Date.now() - startTime;
    console.log(`❌ 连接 #${connId} 后端错误 (持续${duration}ms):`, {
      error: err.message,
      code: err.code,
      errno: err.errno,
      syscall: err.syscall,
      address: err.address,
      port: err.port,
      targetConnected,
      dataTransferred,
    });
    if (!clientSocket.destroyed) {
      clientSocket.destroy();
    }
  });

  clientSocket.on('error', (err) => {
    const duration = Date.now() - startTime;
    console.log(`❌ 连接 #${connId} 客户端错误 (持续${duration}ms):`, {
      error: err.message,
      code: err.code,
      errno: err.errno,
      syscall: err.syscall,
      targetConnected,
      dataTransferred,
    });
    if (!targetSocket.destroyed) {
      targetSocket.destroy();
    }
  });

  // 连接关闭
  clientSocket.on('close', (hadError) => {
    const duration = Date.now() - startTime;
    console.log(`🔌 连接 #${connId} 客户端断开 (持续${duration}ms):`, {
      hadError,
      targetConnected,
      dataTransferred,
    });
    if (!targetSocket.destroyed) {
      targetSocket.destroy();
    }
  });

  targetSocket.on('close', (hadError) => {
    const duration = Date.now() - startTime;
    console.log(`🔌 连接 #${connId} 后端断开 (持续${duration}ms):`, {
      hadError,
      targetConnected,
      dataTransferred,
    });
    if (!clientSocket.destroyed) {
      clientSocket.destroy();
    }
  });

  // 连接超时处理
  clientSocket.setTimeout(30000); // 30秒超时
  clientSocket.on('timeout', () => {
    console.log(`⏰ 连接 #${connId} 客户端超时`);
    clientSocket.destroy();
  });

  targetSocket.setTimeout(30000); // 30秒超时
  targetSocket.on('timeout', () => {
    console.log(`⏰ 连接 #${connId} 后端超时`);
    targetSocket.destroy();
  });
});

// 启动服务器
server.listen(proxyPort, '0.0.0.0', () => {
  console.log(`✨ 代理服务器运行中...`);
  console.log(`💡 千牛客户端请连接: localhost:${proxyPort}`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log(`\n🛑 正在关闭代理服务器...`);
  server.close(() => {
    console.log(`👋 代理服务器已关闭`);
    process.exit(0);
  });
});
