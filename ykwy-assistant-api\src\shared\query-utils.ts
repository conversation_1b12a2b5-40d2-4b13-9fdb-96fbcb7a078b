import { logger } from '../lib/logger';

export type Key = readonly unknown[];
export type Pattern = readonly (string | number | '*' | Record<string, unknown>)[];

interface MatchOpt {
  exact?: boolean; // true ⇢ 必须等长（默认 true）
}

export const Q = {
  conv: {
    list: (params: Record<string, unknown> = {}) => ['conversations', params] as const,
    detail: (id: string) => ['conversations', id] as const,
    msgs: {
      list: (cid: string, p: Record<string, unknown> = {}) => ['conversations', cid, 'messages', p] as const,
      detail: (cid: string, mid: string) => ['conversations', cid, 'messages', mid] as const,
    },
  },
  user: {
    list: (p: Record<string, unknown> = {}) => ['users', p] as const,
    detail: (uid: string) => ['users', uid] as const,
  },
} as const;

/**
 * matches(key, pattern, { exact })
 *
 * - '*'  : 该位可为任意 **非对象值**（string | number）——常用于跨 id 批量匹配
 * - {}   : 该位必须是对象（分页 / 过滤占位），用于只命中列表键
 * - exact:
 *     true  (默认)  → key.length === pattern.length
 *     false         → 允许 key 比 pattern 更长（即前缀匹配）
 */
export function matches(key: Key, pattern: Pattern, opt: MatchOpt = { exact: true }): boolean {
  if (opt.exact ?? true) {
    if (key.length !== pattern.length) return false;
  } else {
    if (key.length < pattern.length) return false;
  }

  const matched = pattern.every((p, i) => {
    if (p === '*') return typeof key[i] === 'string' || typeof key[i] === 'number';
    if (typeof p === 'object') return typeof key[i] === 'object';
    return p === key[i];
  });

  // 命中时打印调试信息
  if (matched) {
    logger.debug('查询匹配命中', { key, pattern, opt });
  }

  return matched;
}
