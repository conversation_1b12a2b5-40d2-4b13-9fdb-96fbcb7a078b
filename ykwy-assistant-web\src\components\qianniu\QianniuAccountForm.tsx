import React, { useState } from 'react';

import { useBrands, useCreateQianniuAccount, useUpdateQianniuAccount } from '../../services/hooks';
import type { PlatformType, QianniuAccount } from '../../services/types';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface QianniuAccountFormProps {
  clientId: string;
  account?: QianniuAccount; // 如果提供则为编辑模式
  onSuccess: () => void;
  onCancel: () => void;
}

const platformOptions: { value: PlatformType; label: string }[] = [
  { value: 'TAOBAO', label: '淘宝' },
  { value: 'TMALL', label: '天猫' },
  { value: 'JD', label: '京东' },
  { value: 'PINDUODUO', label: '拼多多' },
  { value: 'WECHAT', label: '微信' },
  { value: 'DOUYIN', label: '抖音' },
  { value: 'XIAOHONGSHU', label: '小红书' },
  { value: 'OTHER', label: '其他' },
];

export function QianniuAccountForm({ clientId, account, onSuccess, onCancel }: QianniuAccountFormProps) {
  const [formData, setFormData] = useState({
    brandId: account?.brandId || '',
    accountName: account?.accountName || '',
    accountId: account?.accountId || '',
    shopName: account?.shopName || '',
    shopId: account?.shopId || '',
    platformType: account?.platformType || ('TAOBAO' as PlatformType),
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 获取品牌列表
  const { data: brandsResponse, isLoading: brandsLoading } = useBrands();
  const brands = brandsResponse?.data || [];

  // 创建和更新mutations
  const createMutation = useCreateQianniuAccount();
  const updateMutation = useUpdateQianniuAccount();

  const isEditing = !!account;
  const isLoading = createMutation.isPending || updateMutation.isPending;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.brandId) {
      newErrors.brandId = '请选择品牌';
    }
    if (!formData.accountName.trim()) {
      newErrors.accountName = '请输入账号名称';
    }
    if (!formData.accountId.trim()) {
      newErrors.accountId = '请输入账号ID';
    }
    if (!formData.platformType) {
      newErrors.platformType = '请选择平台类型';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (isEditing) {
        await updateMutation.mutateAsync({
          accountId: account.id,
          data: {
            accountName: formData.accountName,
            shopName: formData.shopName || undefined,
            shopId: formData.shopId || undefined,
            platformType: formData.platformType,
          },
        });
      } else {
        await createMutation.mutateAsync({
          clientId,
          brandId: formData.brandId,
          accountName: formData.accountName,
          accountId: formData.accountId,
          shopName: formData.shopName || undefined,
          shopId: formData.shopId || undefined,
          platformType: formData.platformType,
        });
      }
      onSuccess();
    } catch (error) {
      console.error('保存账号失败:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* 品牌选择 */}
      {!isEditing && (
        <div className="space-y-2">
          <Label htmlFor="brandId">品牌 *</Label>
          <Select value={formData.brandId} onValueChange={(value: string) => handleInputChange('brandId', value)} disabled={brandsLoading}>
            <SelectTrigger>
              <SelectValue placeholder={brandsLoading ? '加载中...' : '选择品牌'} />
            </SelectTrigger>
            <SelectContent>
              {brands.map((brand) => (
                <SelectItem key={brand.id} value={brand.id}>
                  {brand.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.brandId && <p className="text-sm text-red-500">{errors.brandId}</p>}
        </div>
      )}

      {/* 账号名称 */}
      <div className="space-y-2">
        <Label htmlFor="accountName">账号名称 *</Label>
        <Input id="accountName" value={formData.accountName} onChange={(e) => handleInputChange('accountName', e.target.value)} placeholder="输入千牛账号名称" />
        {errors.accountName && <p className="text-sm text-red-500">{errors.accountName}</p>}
      </div>

      {/* 账号ID */}
      {!isEditing && (
        <div className="space-y-2">
          <Label htmlFor="accountId">账号ID *</Label>
          <Input id="accountId" value={formData.accountId} onChange={(e) => handleInputChange('accountId', e.target.value)} placeholder="输入千牛账号ID" />
          {errors.accountId && <p className="text-sm text-red-500">{errors.accountId}</p>}
        </div>
      )}

      {/* 店铺名称 */}
      <div className="space-y-2">
        <Label htmlFor="shopName">店铺名称</Label>
        <Input id="shopName" value={formData.shopName} onChange={(e) => handleInputChange('shopName', e.target.value)} placeholder="输入店铺名称（可选）" />
      </div>

      {/* 店铺ID */}
      <div className="space-y-2">
        <Label htmlFor="shopId">店铺ID</Label>
        <Input id="shopId" value={formData.shopId} onChange={(e) => handleInputChange('shopId', e.target.value)} placeholder="输入店铺ID（可选）" />
      </div>

      {/* 平台类型 */}
      <div className="space-y-2">
        <Label htmlFor="platformType">平台类型 *</Label>
        <Select value={formData.platformType} onValueChange={(value: string) => handleInputChange('platformType', value)}>
          <SelectTrigger>
            <SelectValue placeholder="选择平台类型" />
          </SelectTrigger>
          <SelectContent>
            {platformOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.platformType && <p className="text-sm text-red-500">{errors.platformType}</p>}
      </div>

      {/* 按钮 */}
      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? '保存中...' : isEditing ? '更新' : '创建'}
        </Button>
      </div>
    </form>
  );
}
