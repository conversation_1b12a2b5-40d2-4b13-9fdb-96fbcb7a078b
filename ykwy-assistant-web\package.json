{"name": "ykwy-assistant-web", "version": "0.0.4", "description": "易康无忧客服助手", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint \"{src,test}/**/*.ts*\" --fix --cache --cache-location node_modules/.cache/eslint/.eslint-cache", "lint:fix": "eslint src --ext ts,tsx --fix", "prepare": "husky", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.62.0", "@tanstack/react-query-devtools": "^5.62.0", "better-auth": "^1.2.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.475.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-router-dom": "^7.1.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@stylistic/eslint-plugin": "^5.1.0", "@tailwindcss/postcss": "^4.0.8", "@tanstack/react-query-devtools": "^5.62.0", "@types/bun": "latest", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "esbuild": "0.21", "eslint": "^9.0.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.3.0", "husky": "^9.1.7", "postcss": "^8.5.0", "prettier": "^3.5.1", "tailwindcss": "^4.0.8", "typescript": "^5.7.3", "typescript-eslint": "^8.35.1", "vite": "^6.0.0"}}