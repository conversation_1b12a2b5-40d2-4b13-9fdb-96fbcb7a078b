import { Prisma } from '@prisma/client';
import fs from 'fs/promises';
import type { Context } from 'hono';
import Papa from 'papapar<PERSON>';
import path from 'path';
import * as XLSX from 'xlsx';

import prisma from '../client/prisma.ts';
import redis from '../client/redis.js';
import { ErrorCode } from '../constants/errorCodes.ts';
import { AppError } from '../errors/custom.error.ts';
import { ImportExportTaskService } from '../services/importExportTaskService';
import { TempProductService } from '../services/tempProductService';
import { tempProductExportSchema } from '../types/validators/tempProductValidator';
import { removeScanKeys } from '../utils/cache.js';
import { R } from '../utils/Response.ts';

const tempProductService = new TempProductService();
const importExportTaskService = new ImportExportTaskService();

/**
 * 临时商品文件操作控制器
 * 处理导入、导出、下载、清理等文件相关操作
 */
export class TempProductFileController {
  /**
   * 批量导入临时商品（Excel文件）
   * @param c Hono Context
   */
  public async batchImport(c: Context) {
    let taskId: string | null = null;

    try {
      // 获取上传的文件
      const formData = await c.req.formData();
      const file = formData.get('file') as File;

      if (!file) {
        throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { message: '请选择要上传的文件' });
      }

      // 验证文件类型
      const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'text/csv', 'application/csv'];

      if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.xlsx') && !file.name.toLowerCase().endsWith('.xls') && !file.name.toLowerCase().endsWith('.csv')) {
        throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { message: '文件格式不支持，请上传Excel或CSV文件' });
      }

      // 验证文件大小（10MB限制）
      if (file.size > 10 * 1024 * 1024) {
        throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { message: '文件大小不能超过10MB' });
      }

      // 1. 创建任务记录
      const task = await importExportTaskService.upsert({
        taskName: `商品批量导入 - ${file.name}`,
        taskType: 'import',
        taskTime: new Date(),
        fileName: file.name,
        status: 'pending',
      });

      taskId = task.id;

      // 2. 执行实际导入操作
      const arrayBuffer = await file.arrayBuffer();
      const buffer = new Uint8Array(arrayBuffer);

      const result = await tempProductService.batchImport(buffer, file.name);

      // 3. 更新任务状态
      await importExportTaskService.updateStatus(taskId, {
        status: 'success',
        result: {
          successCount: result.successCount,
          failCount: result.failCount,
          newCount: result.newCount,
          updatedCount: result.updatedCount,
          errors: result.errors,
          totalProcessed: result.successCount + result.failCount,
        },
      });

      // 清除相关缓存
      try {
        await removeScanKeys(redis, 'temp-product:list');
      } catch (cacheError) {
        console.error('缓存清理失败:', cacheError);
      }

      return R.success(c, result);
    } catch (error) {
      // 如果有任务ID，更新任务状态为失败
      if (taskId) {
        try {
          await importExportTaskService.updateStatus(taskId, {
            status: 'failed',
            result: {
              error: error instanceof Error ? error.message : '未知错误',
              failedAt: new Date().toISOString(),
            },
          });
        } catch (updateError) {
          console.error('更新任务状态失败:', updateError);
        }
      }

      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, { message: '文件导入失败', originalError: error });
    }
  }

  /**
   * 下载导入模版
   * @param c Hono Context
   */
  public async downloadTemplate(c: Context) {
    const query = c.req.query();
    const format = (query['format'] as 'excel' | 'csv') || 'excel';

    try {
      // 创建示例数据
      const templateData = [
        {
          商品名称: '示例商品名称',
          商品链接或id: 'https://example.com/product/123',
          商品ID: '123456789',
          商品状态: '已上架',
          '货号/款号': 'M250001',
          商品图片链接: 'https://example.com/image.jpg',
        },
        {
          商品名称: 'QINKUNG轻功运动T恤',
          商品链接或id: 'https://item.taobao.com/item.htm?id=911683882205',
          商品ID: '911683882205',
          商品状态: '已上架',
          '货号/款号': 'F250009',
          商品图片链接: 'https://gw.alicdn.com/imgextra/example.jpg',
        },
      ];

      let buffer: Buffer;
      let filename: string;

      if (format === 'csv') {
        // 生成CSV文件
        // 为CSV添加BOM头以确保Excel正确识别UTF-8编码
        const csv = Papa.unparse(templateData, {
          header: true,
          delimiter: ',',
          quotes: true,
        });
        buffer = Buffer.from('\uFEFF' + csv, 'utf-8');
        filename = `临时商品导入模版_${new Date().toISOString().slice(0, 10)}.csv`;
      } else {
        // 生成Excel文件
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(templateData);

        // 设置列宽
        const columnWidths = [
          { wch: 30 }, // 商品名称
          { wch: 40 }, // 商品链接或id
          { wch: 15 }, // 商品ID
          { wch: 10 }, // 商品状态
          { wch: 15 }, // 货号/款号
          { wch: 40 }, // 商品图片链接
        ];
        worksheet['!cols'] = columnWidths;

        XLSX.utils.book_append_sheet(workbook, worksheet, '临时商品数据');
        const xlsxBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        buffer = Buffer.from(xlsxBuffer);
        filename = `临时商品导入模版_${new Date().toISOString().slice(0, 10)}.xlsx`;
      }

      // 设置响应头
      c.header('Content-Type', format === 'excel' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 'text/csv; charset=utf-8');
      c.header('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);

      return c.body(buffer);
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, { message: '模版生成失败', originalError: error });
    }
  }

  /**
   * 导出临时商品数据 - 集成任务表记录
   * @param c Hono Context
   */
  public async exportTempProducts(c: Context) {
    let taskId: string | null = null;

    try {
      console.log('导出开始...');
      const body = await c.req.json();
      console.log('请求体:', body);

      // 参数校验
      const validation = tempProductExportSchema.safeParse(body);
      if (!validation.success) {
        console.error('参数校验失败:', validation.error);
        throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
      }
      console.log('参数校验通过');

      const { format, fields, filters } = validation.data;

      // 生成当前时间字符串
      const currentTime = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const fileName = `导出商品列表-${currentTime}.${format === 'excel' ? 'xlsx' : 'csv'}`;

      // 1. 创建任务记录
      const task = await importExportTaskService.upsert({
        taskName: `商品批量导出 - ${fileName}`,
        taskType: 'export',
        taskTime: new Date(),
        fileName: fileName,
        filePath: '', // 先设置为空，导出完成后更新
        status: 'pending',
      });

      taskId = task.id;

      // 2. 执行实际导出操作
      console.log('开始数据导出...');
      const where: Prisma.TempProductWhereInput = {};
      if (!filters?.includeDeleted) where.isDeleted = 0;
      if (filters?.name) where.name = { contains: filters.name, mode: 'insensitive' };
      if (filters?.status) where.status = { contains: filters.status, mode: 'insensitive' };
      if (filters?.productId) where.productId = { contains: filters.productId, mode: 'insensitive' };
      console.log('查询条件:', where);

      const products = await prisma.tempProduct.findMany({ where, orderBy: { createdAt: 'desc' } });
      console.log('查询到商品数量:', products.length);

      const fieldMap: Record<string, string> = {
        name: '商品名称',
        linkOrId: '商品链接或ID',
        productId: '商品ID',
        status: '商品状态',
        styleNumber: '货号/款号',
        imageUrl: '商品图片链接',
        createdAt: '创建时间',
        updatedAt: '更新时间',
      };
      const exportData = products.map((product) => {
        const item: Record<string, string> = {};
        fields.forEach((field) => {
          const title = fieldMap[field] || field;
          const rawValue = product[field as keyof typeof product];
          let value = '';
          if (rawValue !== null && rawValue !== undefined) {
            value = (field === 'createdAt' || field === 'updatedAt') && rawValue instanceof Date ? rawValue.toLocaleString('zh-CN') : String(rawValue);
          }
          item[title] = value;
        });
        return item;
      });

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const filename = `导出商品列表-${timestamp}.${format === 'excel' ? 'xlsx' : 'csv'}`;

      let buffer: Buffer;
      if (format === 'csv') {
        buffer = Buffer.from(Papa.unparse(exportData, { header: true, delimiter: ',', quotes: true }), 'utf-8');
      } else {
        const ws = XLSX.utils.json_to_sheet(exportData);
        ws['!cols'] = fields.map(() => ({ wch: 20 }));
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, '商品数据');
        buffer = Buffer.from(XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' }));
      }
      const result = { successCount: products.length, failCount: 0, buffer, filename };

      // 3. 保存文件到 temp_files 目录
      const tempFilesDir = path.join(process.cwd(), 'temp_files');

      // 确保目录存在
      try {
        await fs.access(tempFilesDir);
      } catch {
        await fs.mkdir(tempFilesDir, { recursive: true });
      }

      // 保存文件
      const filePath = path.join(tempFilesDir, result.filename);
      await fs.writeFile(filePath, result.buffer);

      // 4. 更新任务记录，添加文件路径和结果
      await importExportTaskService.updateStatus(taskId, {
        status: 'success',
        filePath: filePath,
        result: `成功${result.successCount}条，失败${result.failCount}条`,
      });

      // 清除相关缓存
      try {
        await removeScanKeys(redis, 'temp-product:list');
      } catch (cacheError) {
        console.error('缓存清理失败:', cacheError);
      }

      return R.success(c, {
        message: '导出完成',
        filename: result.filename,
        successCount: result.successCount,
        failCount: result.failCount,
        filePath: filePath,
      });
    } catch (error) {
      // 如果有任务ID，更新任务状态为失败
      if (taskId) {
        try {
          await importExportTaskService.updateStatus(taskId, {
            status: 'failed',
            result: `导出失败：${error instanceof Error ? error.message : '未知错误'}`,
          });
        } catch (updateError) {
          console.error('更新任务状态失败:', updateError);
        }
      }

      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, { message: '文件导出失败', originalError: error });
    }
  }

  public async downloadExportFile(c: Context) {
    try {
      const { filename } = c.req.param();
      if (!filename) throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { message: '文件名不能为空' });
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { message: '无效的文件名' });
      }
      const filePath = path.join(process.cwd(), 'temp_files', filename);
      try {
        await fs.access(filePath);
      } catch {
        throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { message: '文件不存在或已被删除' });
      }
      const fileBuffer = await fs.readFile(filePath);
      const isExcel = filename.toLowerCase().endsWith('.xlsx');
      const contentType = isExcel ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : 'text/csv';
      c.header('Content-Type', contentType);
      c.header('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
      c.header('Content-Length', fileBuffer.length.toString());
      return c.body(fileBuffer);
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, { message: '文件下载失败', originalError: error });
    }
  }

  public async cleanupExpiredFiles(c: Context) {
    try {
      const { SchedulerService } = await import('../services/schedulerService');
      await new SchedulerService().manualCleanupExpiredFiles();
      return R.success(c, { message: '过期文件清理完成', cleanupTime: new Date().toISOString() });
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, { message: '清理过期文件失败', originalError: error });
    }
  }
}
