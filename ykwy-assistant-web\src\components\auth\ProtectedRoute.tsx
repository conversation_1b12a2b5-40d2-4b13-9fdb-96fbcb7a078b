import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';

import { authClient } from '../../lib/auth-client';

interface ProtectedRouteProps {
  children?: React.ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { data: session, isPending } = authClient.useSession();
  const location = useLocation();

  // 如果正在加载会话信息，显示加载状态
  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  // 如果用户未登录，重定向到登录页面，并保存当前位置用于登录后返回
  if (!session?.user) {
    return <Navigate to={`/auth/login?redirect=${encodeURIComponent(location.pathname)}`} state={{ from: location }} replace />;
  }

  // 如果用户已登录，渲染子组件或使用 Outlet
  return children ? <>{children}</> : <Outlet />;
}
