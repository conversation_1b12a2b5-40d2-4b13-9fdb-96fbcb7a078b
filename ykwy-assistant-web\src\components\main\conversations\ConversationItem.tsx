import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useEffect, useRef } from 'react';

import { Conversation } from '../../../services';

import { useAppStore } from '@/lib/store';

interface ConversationItemProps {
  conversation: Conversation;
  isSelected: boolean;
  onClick: () => void;
}

export default function ConversationItem({ conversation, isSelected, onClick }: ConversationItemProps) {
  // 获取最后一条消息
  const lastMessage = conversation.messages?.[0];

  // 格式化时间
  const formattedTime = lastMessage?.sentAt ? formatDistanceToNow(new Date(lastMessage.sentAt), { addSuffix: true, locale: zhCN }) : '';

  // 优先级颜色
  const priorityColors = {
    LOW: 'bg-gray-200',
    NORMAL: 'bg-blue-200',
    HIGH: 'bg-orange-200',
    URGENT: 'bg-red-200',
  };

  // 从store中获取通知相关函数
  const getUnreadMessageCount = useAppStore((state) => state.getUnreadMessageCount);

  const markConversationAsRead = useAppStore((state) => state.markConversationAsRead);

  // 获取这个会话的未读消息数
  const unreadCount = getUnreadMessageCount(conversation.id);

  // 引用标记，确保只执行一次标记已读
  const hasMarkedRef = useRef(false);

  // 当选中时标记为已读
  useEffect(() => {
    if (isSelected && unreadCount > 0 && !hasMarkedRef.current) {
      // 标记为已处理过，避免重复执行
      hasMarkedRef.current = true;

      // 立即标记为已读
      markConversationAsRead(conversation.id);
    } else if (!isSelected) {
      // 重置标记，以便下次选中时能重新执行
      hasMarkedRef.current = false;
    }
  }, [isSelected, conversation.id, unreadCount, markConversationAsRead]);

  // 处理点击事件，确保点击时也会标记为已读
  const handleClick = () => {
    // 如果有未读消息，立即标记为已读
    if (unreadCount > 0) {
      markConversationAsRead(conversation.id);
    }
    // 调用原始的点击处理函数
    onClick();
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    // 防止点击对话项时搜索框失去焦点
    e.preventDefault();
  };

  return (
    <div className={`p-3 border-b cursor-pointer hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''} relative`} onClick={handleClick} onMouseDown={handleMouseDown}>
      <div className="flex justify-between items-start mb-1">
        <div className="font-medium flex items-center">
          {conversation.customer?.nickname || '未知客户'}

          {/* 未读消息徽章 - 只在未被自动回复且有未读消息时显示 */}
          {unreadCount > 0 && !conversation.autoReplyEnabled && (
            <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full">{unreadCount}</span>
          )}
        </div>
        <div className="text-xs text-gray-500">{formattedTime}</div>
      </div>

      <div className="text-sm text-gray-600 mb-2 line-clamp-1">{lastMessage?.content || '暂无消息'}</div>

      <div className="flex items-center gap-2">
        <span className={`text-xs px-1.5 py-0.5 rounded ${priorityColors[conversation.priority]}`}>
          {conversation.priority === 'LOW' && '低'}
          {conversation.priority === 'NORMAL' && '中'}
          {conversation.priority === 'HIGH' && '高'}
          {conversation.priority === 'URGENT' && '紧急'}
        </span>

        <span className="text-xs px-1.5 py-0.5 bg-gray-100 rounded">{conversation.platform?.name || '未知平台'}</span>

        {/* 自动回复状态标签 */}
        <span className={`text-xs px-1.5 py-0.5 ${conversation.autoReplyEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} rounded`}>
          {conversation.autoReplyEnabled ? 'AI回复' : '手动回复'}
        </span>
      </div>
    </div>
  );
}
