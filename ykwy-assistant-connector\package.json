{"name": "ykwy-assistant-connector", "version": "2.0.0", "description": "千牛连接一键启动脚本 - 自动化千牛消息接收和发送的完整流程", "main": "src/index.ts", "type": "module", "scripts": {"start": "bun run src/index.ts", "build": "bun run lint && bun build ./src/index.ts --compile --outfile dist/qianniu-connector.exe --minify", "lint": "eslint \"{src,test}/**/*.ts\" --fix --cache --cache-location node_modules/.cache/eslint/.eslint-cache", "clean": "<PERSON><PERSON><PERSON> dist"}, "bin": {"qianniu-connector": "./src/index.ts"}, "keywords": ["<PERSON>ian<PERSON>u", "千牛", "automation", "connector", "tcp-proxy", "one-click"], "author": "YKWY Assistant API", "license": "MIT", "devDependencies": {"@stylistic/eslint-plugin": "^5.2.0", "@types/node": "^24.0.14", "eslint": "^9.31.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.3.0", "rimraf": "^5.0.5", "typescript-eslint": "^8.37.0"}, "engines": {"node": ">=16.0.0"}, "os": ["win32", "linux", "darwin"]}