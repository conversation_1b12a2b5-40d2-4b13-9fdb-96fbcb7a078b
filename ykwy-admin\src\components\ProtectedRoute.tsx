import { message } from 'antd';
import Cookies from 'js-cookie';
import React, { useEffect, useState } from 'react';
import { Navigate, Outlet, useLocation } from 'umi';

const ProtectedRoute: React.FC = () => {
  const location = useLocation();
  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);
  const isLoginPage = location.pathname === '/login';

  useEffect(() => {
    const token = Cookies.get('access_token');
    setIsTokenValid(!!token);
  }, [location.pathname]);

  useEffect(() => {
    if (isTokenValid === false && !isLoginPage) {
      Cookies.remove('access_token');
      localStorage.removeItem('kywy');
      message.error('请先登录系统');
    }
  }, [isTokenValid, isLoginPage]);

  if (isTokenValid === null) return null;

  if (!isTokenValid && !isLoginPage) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
