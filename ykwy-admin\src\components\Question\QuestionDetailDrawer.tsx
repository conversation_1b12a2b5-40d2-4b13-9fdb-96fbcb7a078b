import type { QuestionAndAnswer } from '@/models/questionAndAnswer';
import { Card, Col, Drawer, Row, Tag } from 'antd';
import React from 'react';

interface QuestionDetailDrawerProps {
  visible: boolean;
  currentRecord: QuestionAndAnswer | null;
  categoryMap: Record<string, string>;
  onClose: () => void;
}

const QuestionDetailDrawer: React.FC<QuestionDetailDrawerProps> = ({
  visible,
  currentRecord,
  categoryMap,
  onClose,
}) => {
  return (
    <Drawer title="问答详情" open={visible} onClose={onClose} width={600}>
      {currentRecord && (
        <div>
          <Card title="基本信息" className="mb-4">
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <strong className="font-bold">问题类型：</strong>
                <span>{currentRecord.questionType}</span>
              </Col>
              <Col span={24}>
                <strong className="font-bold">分类：</strong>
                <Tag
                  color="green"
                  className="px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 border-none"
                >
                  {categoryMap[currentRecord.categoryCode] ||
                    currentRecord.categoryCode}
                </Tag>
              </Col>
              <Col span={24}>
                <strong className="font-bold">订单状态：</strong>
                {(() => {
                  const statusArr = currentRecord.orderStatus?.split('') || [];
                  const labels = ['售前', '发货前', '发货后', '售后'];
                  return labels.map((label, idx) => (
                    <Tag
                      key={label}
                      color={statusArr[idx] === '1' ? '#52c41a' : '#d9d9d9'}
                      className={`mr-1 mb-1 px-2 py-0.5 rounded text-xs font-medium ${
                        statusArr[idx] === '1'
                          ? 'bg-green-100 text-green-800 border-none'
                          : 'bg-gray-200 text-gray-500 border-none'
                      }`}
                    >
                      {label}
                    </Tag>
                  ));
                })()}
              </Col>
              {currentRecord.productName && (
                <Col span={24}>
                  <strong className="font-bold">商品名称：</strong>
                  <span>{currentRecord.productName}</span>
                </Col>
              )}
              {currentRecord.productUrl && (
                <Col span={24}>
                  <strong className="font-bold">商品链接：</strong>
                  <a
                    href={currentRecord.productUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline break-all"
                  >
                    {currentRecord.productUrl}
                  </a>
                </Col>
              )}
            </Row>
          </Card>

          <Card title="常见问法样本" className="mb-4">
            {currentRecord.commonQuestionSamples?.length > 0 ? (
              <ol className="list-decimal list-inside space-y-1">
                {currentRecord.commonQuestionSamples.map((sample, index) => (
                  <li key={index}>{sample}</li>
                ))}
              </ol>
            ) : (
              <span className="text-gray-400">暂无样本</span>
            )}
          </Card>

          <Card title="回答内容">
            {currentRecord.answers?.length > 0 ? (
              <div>
                {currentRecord.answers.map((answer, index) => (
                  <div key={index} className="mb-4">
                    <div className="font-bold mb-2">回答 {index + 1}:</div>
                    <div className="bg-gray-100 p-3 rounded whitespace-pre-wrap">
                      {answer}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <span className="text-gray-400">暂无回答</span>
            )}
          </Card>
        </div>
      )}
    </Drawer>
  );
};

export default QuestionDetailDrawer;
