import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { checkPermission, getCurrentUser, getUserOrganizationId, requireOrganization } from '../lib/auth-utils';
import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { calculatePagination, createErrorResponse, createPaginatedResponse, createResponse, handleError } from '../lib/utils';
import { IdParamSchema, PaginationSchema } from '../lib/validations';

const usersRoute = new Hono();

// 用户查询过滤器
const UserFilterSchema = z.object({
  search: z.string().optional(),
  role: z.enum(['SYSTEM_ADMIN', 'ORGANIZATION_ADMIN', 'TEAM_MANAGER', 'CUSTOMER_SERVICE']).optional(),
  teamId: z.string().uuid().optional(),
});

// 创建用户Schema
const CreateUserSchema = z.object({
  name: z.string().min(1, '用户名不能为空'),
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6位'),
  role: z.enum(['ORGANIZATION_ADMIN', 'TEAM_MANAGER', 'CUSTOMER_SERVICE']).default('CUSTOMER_SERVICE'),
  teamId: z.string().uuid('无效的团队ID').optional(),
});

// 更新用户Schema
const UpdateUserSchema = z.object({
  name: z.string().min(1, '用户名不能为空').optional(),
  email: z.string().email('邮箱格式不正确').optional(),
  role: z.enum(['ORGANIZATION_ADMIN', 'TEAM_MANAGER', 'CUSTOMER_SERVICE']).optional(),
  teamId: z.string().uuid('无效的团队ID').optional().nullable(),
});

/**
 * GET /users - 获取组织用户列表
 */
usersRoute.get('/', zValidator('query', PaginationSchema.merge(UserFilterSchema)), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：组织管理员及以上可以查看用户列表
    if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    const { page, limit, search, role, teamId } = c.req.valid('query');
    const { skip, take } = calculatePagination(page, limit);

    // 构建查询条件
    const where: Record<string, unknown> = {
      members: {
        some: {
          organizationId: organizationId,
        },
      },
    };

    // 应用过滤器
    if (search) {
      where['OR'] = [{ name: { contains: search, mode: 'insensitive' } }, { email: { contains: search, mode: 'insensitive' } }];
    }

    if (role) {
      where['role'] = role;
    }

    if (teamId) {
      where['teamId'] = teamId;
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          image: true,
          createdAt: true,
          teamId: true,
          team: {
            select: {
              id: true,
              name: true,
            },
          },
          members: {
            where: {
              organizationId: organizationId,
            },
            select: {
              role: true,
              createdAt: true,
            },
          },
          boundConnectionInvitations: {
            where: {
              organizationId: organizationId,
              status: 'ACTIVATED',
            },
            select: {
              id: true,
              name: true,
              status: true,
              activatedAt: true,
              qianniuClient: {
                select: {
                  id: true,
                  name: true,
                  isOnline: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.user.count({
        where,
      }),
    ]);

    return c.json(createPaginatedResponse(users, page, limit, total, '获取用户列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /users/:id - 获取用户详情
 */
usersRoute.get('/:id', zValidator('param', IdParamSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：组织管理员及以上可以查看用户详情
    if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const { id } = c.req.valid('param');
    const organizationId = await getUserOrganizationId(user.id);

    const targetUser = await prisma.user.findFirst({
      where: {
        id,
        members: {
          some: {
            organizationId: organizationId || undefined,
          },
        },
      },
      include: {
        team: {
          select: {
            id: true,
            name: true,
          },
        },
        members: {
          where: {
            organizationId: organizationId || undefined,
          },
          select: {
            role: true,
            createdAt: true,
          },
        },
        boundConnectionInvitations: {
          select: {
            id: true,
            name: true,
            status: true,
            activatedAt: true,
            qianniuClient: {
              select: {
                id: true,
                name: true,
                isOnline: true,
              },
            },
          },
        },
      },
    });

    if (!targetUser) {
      return c.json(createErrorResponse('用户不存在或无权限访问'), 404);
    }

    return c.json(createResponse(targetUser, '获取用户详情成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /users - 创建组织成员
 */
usersRoute.post('/', zValidator('json', CreateUserSchema), async (c) => {
  try {
    const user = await requireOrganization(c);

    // 检查权限：组织管理员及以上可以创建用户
    if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const data = c.req.valid('json');

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return c.json(createErrorResponse('邮箱已被使用'), 400);
    }

    // 如果指定了团队，验证团队是否属于当前组织
    if (data.teamId) {
      const team = await prisma.team.findFirst({
        where: {
          id: data.teamId,
          organizationId: user.organizationId,
        },
      });

      if (!team) {
        return c.json(createErrorResponse('团队不存在或无权限'), 404);
      }
    }

    // 加密密码
    const hashedPassword = await Bun.password.hash(data.password);

    const newUser = await prisma.$transaction(async (tx) => {
      // 创建用户
      const createdUser = await tx.user.create({
        data: {
          name: data.name,
          email: data.email,
          emailVerified: true, // 管理员创建的用户自动验证邮箱
          role: data.role,
          teamId: data.teamId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // 创建账户记录
      await tx.account.create({
        data: {
          userId: createdUser.id,
          accountId: createdUser.id,
          providerId: 'credential',
          password: hashedPassword,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // 创建 Member 记录
      await tx.member.create({
        data: {
          organizationId: user.organizationId,
          userId: createdUser.id,
          role: data.role,
        },
      });

      return createdUser;
    });

    logger.info('组织成员创建成功', {
      userId: newUser.id,
      email: newUser.email,
      role: newUser.role,
      organizationId: user.organizationId,
      createdBy: user.id,
    });

    return c.json(
      createResponse(
        {
          id: newUser.id,
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
          teamId: newUser.teamId,
          createdAt: newUser.createdAt,
        },
        '用户创建成功',
      ),
      201,
    );
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * PUT /users/:id - 更新用户信息
 */
usersRoute.put('/:id', zValidator('param', IdParamSchema), zValidator('json', UpdateUserSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    // 检查权限：组织管理员及以上可以更新用户
    if (!checkPermission(user.role, 'ORGANIZATION_ADMIN')) {
      return c.json(createErrorResponse('权限不足'), 403);
    }

    const { id } = c.req.valid('param');
    const data = c.req.valid('json');
    const organizationId = await getUserOrganizationId(user.id);

    // 验证目标用户是否属于同一组织
    const targetUser = await prisma.user.findFirst({
      where: {
        id,
        members: {
          some: {
            organizationId: organizationId || undefined,
          },
        },
      },
    });

    if (!targetUser) {
      return c.json(createErrorResponse('用户不存在或无权限访问'), 404);
    }

    // 如果更新邮箱，检查是否已被使用
    if (data.email && data.email !== targetUser.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email },
      });

      if (existingUser) {
        return c.json(createErrorResponse('邮箱已被使用'), 400);
      }
    }

    // 如果指定了团队，验证团队是否属于当前组织
    if (data.teamId) {
      const team = await prisma.team.findFirst({
        where: {
          id: data.teamId,
          organizationId: organizationId || undefined,
        },
      });

      if (!team) {
        return c.json(createErrorResponse('团队不存在或无权限'), 404);
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        teamId: true,
        updatedAt: true,
        team: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    logger.info('用户信息更新成功', {
      userId: id,
      updatedBy: user.id,
      changes: data,
    });

    return c.json(createResponse(updatedUser, '用户信息更新成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

export default usersRoute;
