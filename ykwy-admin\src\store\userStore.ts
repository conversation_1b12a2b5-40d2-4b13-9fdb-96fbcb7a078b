import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 用户信息类型定义
interface UserInfo {
  id?: string;
  email?: string;
  [key: string]: unknown;
}

// Store状态类型定义
interface UserStore {
  userInfo: UserInfo | null;
  setUserInfo: (info: UserInfo) => void;
  clearUser: () => void;
}

// 创建持久化store
const useStore = create<UserStore>()(
  persist(
    (set) => ({
      userInfo: null,
      setUserInfo: (info) => set({ userInfo: info }),
      clearUser: () => set({ userInfo: null }),
    }),
    {
      name: 'kywy',
    },
  ),
);

export default useStore;
export type { UserStore };
