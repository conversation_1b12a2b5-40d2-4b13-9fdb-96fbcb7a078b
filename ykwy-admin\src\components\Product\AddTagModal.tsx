import { Button, Form, Input, Modal } from 'antd';
import React from 'react';

interface AddTagModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (tagName: string) => void;
}

const AddTagModal: React.FC<AddTagModalProps> = ({
  visible,
  onCancel,
  onSave,
}) => {
  const [form] = Form.useForm();

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      onSave(values.tagName);
      form.resetFields();
    } catch (error) {
      // 校验失败，不处理
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="添加商品标签"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={500}
      destroyOnHidden
      data-oid="_caogrs"
    >
      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
        data-oid="wv0dgit"
      >
        <Form.Item
          label={
            <span data-oid="-aole.3">
              <span style={{ color: 'red' }} data-oid="saiu24t">
                *
              </span>{' '}
              标签名称
            </span>
          }
          name="tagName"
          rules={[{ required: true, message: '请输入标签名称' }]}
          data-oid="a6muwav"
        >
          <Input placeholder="请输入标签名称" data-oid="wzl70q." />
        </Form.Item>
        <div className="text-right" data-oid="iwkvntu">
          <Button
            onClick={handleCancel}
            style={{ marginRight: 8 }}
            data-oid="po.x24s"
          >
            取消
          </Button>
          <Button type="primary" onClick={handleSave} data-oid="6o2:5g4">
            保存
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default AddTagModal;
