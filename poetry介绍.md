解决凭据失效问题
poetry config keyring.enabled false
poetry install

解决代理问题
# 设置环境变量
$env:HTTP_PROXY="http://127.0.0.1:7890"
$env:HTTPS_PROXY="http://127.0.0.1:7890"

# 重新安装
poetry install

# 配置清华大学镜像源
poetry config repositories.tsinghua https://pypi.tuna.tsinghua.edu.cn/simple/
poetry source add --priority=primary tsinghua https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者配置阿里云镜像源
poetry source add --priority=primary aliyun https://mirrors.aliyun.com/pypi/simple/

# 或者配置豆瓣镜像源
poetry source add --priority=primary douban https://pypi.douban.com/simple/

修改远程仓库从http改到ssh 以解决
git remote set-<NAME_EMAIL>:effortless-innovations/ykwy-assistant-web.git