# 千牛连接链路日志追踪系统

## 🎯 系统架构概览

你的系统确实非常复杂，涉及多个连接环节：

```
前端生成注入脚本 → 千牛WebSocket连接 → 数据库状态更新 → TCP客户端连接 → 连接匹配 → 收发同步
```

## 📊 当前日志追踪能力

### ✅ 已实现的端到端追踪

#### 1️⃣ **注入脚本生成阶段**
```typescript
// 📍 位置: cdn-inject.ts
🎯 [连接追踪] 注入脚本已生成 {
  "invitationId": "inv-123",
  "organizationId": "org-456", 
  "teamId": "team-789",
  "userAgent": "Mozilla/5.0...",
  "ip": "*************",
  "organizationName": "测试公司",
  "teamName": "客服团队",
  "invitationName": "千牛连接-001",
  "phase": "script_generated"
}
```

#### 2️⃣ **WebSocket连接建立阶段**
```typescript
// 📍 位置: index.ts - WebSocket处理
🔗 [连接追踪] WebSocket连接已建立 {
  "invitationId": "inv-123",
  "connectionId": "conn-abc",
  "organizationId": "org-456",
  "teamId": "team-789", 
  "clientInfo": {
    "name": "千牛连接-001",
    "userAgent": "...",
    "qianniuVersion": "9.12.05N",
    "loginID": "seller123",
    "timestamp": 1641900000000
  },
  "phase": "websocket_connected"
}
```

#### 3️⃣ **WebSocket激活阶段**
```typescript
// 📍 位置: index.ts - handleQianniuConnectionOpen
✅ [连接追踪] WebSocket连接已激活 {
  "invitationId": "inv-123",
  "connectionId": "conn-abc",
  "organizationId": "org-456",
  "teamId": "team-789",
  "clientsUpdated": 1,
  "newClientCreated": false,
  "phase": "websocket_activated"
}
```

#### 4️⃣ **TCP客户端连接阶段**
```typescript
// 📍 位置: qianniuTcpServer.ts
🔌 [连接追踪] TCP客户端已连接 {
  "tcpClientId": "*************:12345",
  "username": "seller123",
  "loginID": "seller123", 
  "connectionId": "conn-abc",
  "phase": "tcp_connected"
}
```

#### 5️⃣ **完整链路建立**
```typescript
// 📍 位置: connection-tracker.ts
🎉 [连接追踪] 连接链路完全建立 {
  "invitationId": "inv-123",
  "connectionId": "conn-abc", 
  "tcpClientId": "*************:12345",
  "organizationId": "org-456",
  "teamId": "team-789",
  "username": "seller123",
  "loginID": "seller123",
  "phase": "fully_linked",
  "totalDuration": "2.35s"
}
```

### ❌ **连接匹配失败追踪**
```typescript
❌ [连接追踪] TCP连接匹配失败 {
  "tcpClientId": "*************:12345",
  "username": "seller123",
  "loginID": "seller123",
  "diagnostics": {
    "onlineClientsCount": 2,
    "accountsChecked": 3,
    "availableConnectionIds": ["conn-abc", "conn-def"]
  },
  "phase": "tcp_matching_failed"
}
```

## 🔍 详细的连接匹配日志

### TCP连接匹配过程
```typescript
// 1. 开始查找
[千牛TCP] 开始查找connectionId: username=seller123, loginID=seller123

// 2. 优先通过loginID查找
[千牛TCP] 尝试通过loginID查找: seller123
[千牛TCP] loginID查找结果: {
  "accountName": "seller123",
  "clientId": "client-456", 
  "connectionId": "conn-abc",
  "isOnline": true
}

// 3. 匹配成功
[千牛TCP] 通过loginID找到connectionId: seller123 -> conn-abc
[千牛TCP] 通过用户信息找到connectionId: *************:12345 -> conn-abc

// 4. 备用方案（如果loginID失败）
[千牛TCP] 尝试通过username查找: seller123
[千牛TCP] 只有一个在线客户端，使用其connectionId: conn-abc

// 5. 匹配失败情况
[千牛TCP] 无法找到匹配的connectionId: username=seller123, loginID=seller123
[千牛TCP] 在线客户端数量: 0
```

## 🛠️ 新增的诊断工具

### API端点
```bash
# 获取所有连接概览
GET /api/v1/connection/diagnostics

# 获取特定邀请的连接状态  
GET /api/v1/connection/diagnostics/{invitationId}
```

### 诊断响应示例
```json
{
  "success": true,
  "data": {
    "totalConnections": 5,
    "byPhase": {
      "script_generated": 2,
      "websocket_connected": 1, 
      "websocket_activated": 1,
      "fully_linked": 1
    },
    "activeConnections": [
      {
        "invitationId": "inv-123",
        "connectionId": "conn-abc",
        "tcpClientId": "*************:12345", 
        "duration": "120.5s"
      }
    ]
  }
}
```

## 📈 日志分析场景

### 🔍 **场景1：连接建立成功**
```
🎯 [连接追踪] 注入脚本已生成 (invitationId: inv-123)
    ↓ 2.1s
🔗 [连接追踪] WebSocket连接已建立 (connectionId: conn-abc)  
    ↓ 0.2s
✅ [连接追踪] WebSocket连接已激活 (clientsUpdated: 1)
    ↓ 15.3s  
🔌 [连接追踪] TCP客户端已连接 (tcpClientId: *************:12345)
    ↓ 0.1s
🎉 [连接追踪] 连接链路完全建立 (totalDuration: 17.7s)
```

### ❌ **场景2：TCP连接匹配失败**
```
🎯 [连接追踪] 注入脚本已生成 (invitationId: inv-123)
    ↓ 2.1s
🔗 [连接追踪] WebSocket连接已建立 (connectionId: conn-abc)
    ↓ 0.2s  
✅ [连接追踪] WebSocket连接已激活 (clientsUpdated: 1)
    ↓ 15.3s
🔌 [连接追踪] TCP客户端已连接 (tcpClientId: *************:12345)
    ↓ 0.1s
❌ [连接追踪] TCP连接匹配失败 (onlineClientsCount: 0)
```

### ⚠️ **场景3：WebSocket连接但未激活**
```
🎯 [连接追踪] 注入脚本已生成 (invitationId: inv-123)
    ↓ 2.1s
🔗 [连接追踪] WebSocket连接已建立 (connectionId: conn-abc)
    ↓ 超时...
⚠️ [连接追踪] WebSocket连接建立但未找到对应的邀请记录
```

## 🚨 故障排查指南

### 1. **脚本生成失败**
- 检查邀请状态（PENDING/ACTIVATED）
- 验证邀请是否过期
- 确认组织和团队信息

### 2. **WebSocket连接失败**  
- 检查网络连接
- 验证WebSocket URL配置
- 确认防火墙设置

### 3. **TCP连接匹配失败**
- 检查千牛账号信息（username/loginID）
- 验证数据库中的账号记录
- 确认客户端在线状态

### 4. **连接断开**
```typescript
🔌 [连接追踪] 连接已断开 {
  "identifier": "conn-abc",
  "type": "websocket", 
  "reason": "client_disconnect"
}

📊 [连接追踪] 连接会话结束 {
  "invitationId": "inv-123",
  "finalPhase": "fully_linked",
  "sessionDuration": "300.5s"
}
```

## 🎯 关键优势

1. **端到端追踪** - 从脚本生成到TCP连接的完整链路
2. **统一标识符** - 使用invitationId串联整个流程  
3. **详细诊断** - 连接失败时提供具体的诊断信息
4. **性能监控** - 记录每个阶段的耗时
5. **实时状态** - 可通过API实时查看连接状态

这套日志系统让你能够：
- 🔍 **快速定位问题** - 通过invitationId追踪整个连接过程
- 📊 **监控连接质量** - 统计连接成功率和耗时
- 🛠️ **优化连接逻辑** - 基于日志数据改进匹配算法
- 🚨 **及时发现异常** - 自动识别连接异常模式
