"""
轻功体育数据加载器
"""
import os
import logging
from typing import List
from llama_index.core import Document

logger = logging.getLogger(__name__)


class QinggongDataLoader:
    """轻功体育数据加载器"""
    
    def __init__(self):
        self.data_dir = "data_qinggong"  # 轻功体育专用数据目录
        self.products = self._get_default_products()
    
    def _get_default_products(self) -> List[dict]:
        """获取轻功体育的默认产品数据"""
        return [
            {
                "id": "qg_shoes_001",
                "name": "轻功专业跑鞋",
                "category": "运动鞋",
                "price": 299.0,
                "description": "采用轻量化设计，透气网面材质，专业缓震技术，适合日常跑步和健身训练。",
                "features": ["轻量化", "透气", "缓震", "防滑"],
                "sizes": ["36", "37", "38", "39", "40", "41", "42", "43", "44"],
                "colors": ["黑色", "白色", "蓝色", "红色"],
                "stock": 100,
                "brand": "轻功体育"
            },
            {
                "id": "qg_clothes_001", 
                "name": "轻功运动T恤",
                "category": "运动服装",
                "price": 89.0,
                "description": "采用速干面料，吸湿排汗，舒适透气，适合各种运动场景。",
                "features": ["速干", "透气", "舒适", "耐洗"],
                "sizes": ["S", "M", "L", "XL", "XXL"],
                "colors": ["黑色", "白色", "灰色", "蓝色"],
                "stock": 200,
                "brand": "轻功体育"
            },
            {
                "id": "qg_equipment_001",
                "name": "轻功哑铃套装",
                "category": "体育器材", 
                "price": 199.0,
                "description": "可调节重量哑铃，适合家庭健身，包含多种重量片，满足不同训练需求。",
                "features": ["可调节", "安全", "耐用", "多功能"],
                "weights": ["5kg", "10kg", "15kg", "20kg"],
                "colors": ["黑色"],
                "stock": 50,
                "brand": "轻功体育"
            },
            {
                "id": "qg_fitness_001",
                "name": "轻功瑜伽垫",
                "category": "健身用品",
                "price": 59.0,
                "description": "环保TPE材质，防滑耐用，适合瑜伽、普拉提等运动，便于携带和清洁。",
                "features": ["环保", "防滑", "耐用", "便携"],
                "sizes": ["183cm×61cm×6mm"],
                "colors": ["紫色", "蓝色", "粉色", "绿色"],
                "stock": 150,
                "brand": "轻功体育"
            },
            {
                "id": "qg_shoes_002",
                "name": "轻功篮球鞋",
                "category": "运动鞋",
                "price": 399.0,
                "description": "专业篮球鞋，高帮设计保护脚踝，优质橡胶大底提供出色抓地力。",
                "features": ["高帮保护", "抓地力强", "耐磨", "支撑性好"],
                "sizes": ["36", "37", "38", "39", "40", "41", "42", "43", "44"],
                "colors": ["黑红", "白蓝", "全黑"],
                "stock": 80,
                "brand": "轻功体育"
            },
            {
                "id": "qg_clothes_002",
                "name": "轻功运动套装",
                "category": "运动服装",
                "price": 159.0,
                "description": "运动上衣+运动裤套装，弹性面料，修身版型，适合健身房训练。",
                "features": ["弹性面料", "修身版型", "透气", "时尚"],
                "sizes": ["S", "M", "L", "XL", "XXL"],
                "colors": ["黑色", "深蓝", "灰色"],
                "stock": 120,
                "brand": "轻功体育"
            }
        ]
    
    def load_all_data(self) -> List[Document]:
        """加载所有轻功体育数据"""
        documents = []
        
        try:
            # 加载产品数据
            product_docs = self._load_product_data()
            documents.extend(product_docs)
            
            # 加载品牌介绍
            brand_docs = self._load_brand_data()
            documents.extend(brand_docs)
            
            # 加载销售策略
            strategy_docs = self._load_strategy_data()
            documents.extend(strategy_docs)
            
            logger.info(f"✅ 轻功体育数据加载完成，共 {len(documents)} 个文档")
            
        except Exception as e:
            logger.error(f"❌ 加载轻功体育数据失败: {e}")
            
        return documents
    
    def _load_product_data(self) -> List[Document]:
        """加载产品数据"""
        documents = []
        
        for product in self.products:
            # 创建产品文档
            product_text = f"""
产品名称: {product['name']}
产品ID: {product['id']}
分类: {product['category']}
价格: ¥{product['price']}
描述: {product['description']}
特点: {', '.join(product['features'])}
库存: {product['stock']}
品牌: {product['brand']}
"""
            
            # 添加尺寸信息
            if 'sizes' in product:
                product_text += f"可选尺寸: {', '.join(product['sizes'])}\n"
            if 'weights' in product:
                product_text += f"可选重量: {', '.join(product['weights'])}\n"
            if 'colors' in product:
                product_text += f"可选颜色: {', '.join(product['colors'])}\n"
            
            doc = Document(
                text=product_text,
                metadata={
                    "product_id": product['id'],
                    "product_name": product['name'],
                    "category": product['category'],
                    "price": product['price'],
                    "brand": "轻功体育",
                    "type": "product"
                }
            )
            documents.append(doc)
        
        return documents
    
    def _load_brand_data(self) -> List[Document]:
        """加载品牌介绍数据"""
        brand_text = """
轻功体育品牌介绍

轻功体育是一家专业的体育用品销售商，致力于为运动爱好者提供优质的运动装备和专业的服务。

品牌理念：让运动更轻松，让功夫更精进

主营产品：
1. 运动鞋类：跑鞋、篮球鞋、训练鞋等
2. 运动服装：T恤、运动套装、运动裤等  
3. 体育器材：哑铃、杠铃、健身器械等
4. 健身用品：瑜伽垫、拉力带、护具等

服务特色：
- 专业推荐：根据客户需求推荐合适产品
- 品质保证：所有产品均经过严格质检
- 售后无忧：提供完善的售后服务体系
- 价格优势：厂家直销，性价比高

目标客户：
- 运动爱好者
- 健身人群  
- 体育专业人士
- 学生群体

品牌价值观：专业、品质、服务、创新
"""
        
        return [Document(
            text=brand_text,
            metadata={
                "brand": "轻功体育",
                "type": "brand_info"
            }
        )]
    
    def _load_strategy_data(self) -> List[Document]:
        """加载销售策略数据"""
        strategy_text = """
轻功体育销售策略

1. 需求分析策略
- 了解客户运动类型（跑步、健身、球类等）
- 询问使用场景（日常训练、比赛、休闲等）
- 确认预算范围和品质要求

2. 产品推荐策略
- 根据运动类型推荐专业产品
- 强调产品的专业性和性价比
- 提供多个价位选择

3. 促销策略
- 新客户首单优惠
- 满额包邮活动
- 会员积分制度
- 节日特价活动

4. 售后服务策略
- 7天无理由退换
- 质量问题免费换货
- 专业使用指导
- 定期回访客户满意度

5. 客户维护策略
- 建立客户档案
- 定期推送新品信息
- 提供运动建议和指导
- 组织线下运动活动
"""
        
        return [Document(
            text=strategy_text,
            metadata={
                "brand": "轻功体育",
                "type": "sales_strategy"
            }
        )]
