import type { Context } from 'hono';
import type { ContentfulStatusCode } from 'hono/utils/http-status';

/**
 * 最终的 API 响应工具类
 * 旨在提供极其简洁的控制器返回体验
 */
export class R {
  // ===================================================================
  // 成功响应
  // ===================================================================

  /**
   * 响应成功 (200 OK)
   * @param c Hono 上下文
   * @param data 要返回的数据，默认为 null
   * @param message 成功消息，默认为 '操作成功'
   */
  public static success(c: Context, data: unknown, message = '操作成功') {
    return c.json(
      {
        code: 200,
        message,
        data,
      },
      200,
    );
  }

  /**
   * 响应无内容 (204 No Content)
   * @param c Hono 上下文
   */
  public static noContent(c: Context) {
    return c.newResponse(null, 204);
  }

  // ===================================================================
  // 失败响应
  // ===================================================================

  /**
   * 通用失败响应方法
   * @param c Hono 上下文
   * @param message 错误消息
   * @param statusCode HTTP 状态码，默认为 400
   * @param data 额外的错误信息（可选）
   */
  public static fail(c: Context, message = '操作失败', statusCode: ContentfulStatusCode = 400, data = null) {
    return c.json({ code: statusCode, message, data }, statusCode);
  }
  /**
   * 响应未授权 (401 Unauthorized)
   * @description 通常用于需要登录但未登录的场景
   * @param c Hono 上下文
   * @param message 错误消息，默认为 '身份验证失败，请重新登录'
   */
  public static unauthorized(c: Context, message = '身份验证失败，请重新登录') {
    return this.fail(c, message, 401, null);
  }

  /**
   * 响应禁止访问 (403 Forbidden)
   * @description 通常用于已登录但权限不足的场景
   * @param c Hono 上下文
   * @param message 错误消息，默认为 '权限不足，禁止访问'
   */
  public static forbidden(c: Context, message = '权限不足，禁止访问') {
    return this.fail(c, message, 403, null);
  }

  /**
   * 响应未找到 (404 Not Found)
   * @param c Hono 上下文
   * @param message 错误消息，默认为 '请求的资源未找到'
   */
  public static notFound(c: Context, message = '请求的资源未找到') {
    return this.fail(c, message, 404, null);
  }
}
