FROM oven/bun:1.2.18-slim
RUN apt-get update -y && apt-get install -y --no-install-recommends openssl && rm -rf /var/lib/apt/lists/*
WORKDIR /app

COPY .env package.json bun.lock ./
COPY prisma ./prisma
COPY src ./src

RUN bun install --frozen-lockfile --production --ignore-scripts && \
    bunx prisma generate && \
    bun run build && \
    rm .env bun.lock package.json && rm -rf prisma src

USER bun
EXPOSE 3000
EXPOSE 9997

ENTRYPOINT [ "bun", "run", "dist/index.js" ]
