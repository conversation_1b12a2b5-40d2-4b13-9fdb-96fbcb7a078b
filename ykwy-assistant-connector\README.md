# 千牛连接一键启动工具

基于 TypeScript + Bun 构建的现代化千牛消息接收和发送自动化工具，支持一键启动，提供独立可执行文件。

## �️ 技术栈

- **语言**: TypeScript + ES Module
- **运行时**: Bun (开发) / 独立可执行文件 (生产)
- **架构**: 模块化设计，类型安全
- **构建**: Bun 原生编译，生成独立 exe
- **特性**: 零依赖运行，完全独立部署

## �🚀 主要功能

- ✅ **TypeScript 架构** - 现代化 TypeScript + ES Module 架构，类型安全
- ✅ **独立可执行文件** - 使用 Bun 编译为独立 exe，无需安装运行时
- ✅ **自动调用后端API** - 自动创建连接邀请获取脚本地址
- ✅ **智能状态检测** - 自动检测千牛客户端状态并引导操作
- ✅ **自动脚本注入** - 自动注入连接脚本到千牛客户端
- ✅ **自动服务启动** - 自动启动TCP代理和千牛助手服务
- ✅ **用户友好界面** - 清晰的状态提示和错误处理

## 📋 使用方法

### 方式一：独立可执行文件（推荐）

**直接运行编译后的可执行文件：**

```bash
# 一键启动
./dist/qianniu-connector.exe

# 重新配置
./dist/qianniu-connector.exe config

# 显示帮助
./dist/qianniu-connector.exe help
```

### 方式二：开发环境运行

**需要安装 Bun 运行时：**

```bash
# 一键启动
bun run start

# 重新配置
bun run src/index.ts config

# 显示帮助
bun run src/index.ts help
```

### 方式三：构建可执行文件

```bash
# 安装依赖
bun install

# 构建独立可执行文件
bun run build

# 清理构建文件
bun run clean
```

## 🔄 操作流程

1. **首次配置** - 首次运行会提示输入：
   - API服务器地址和端口
   - 登录邮箱和密码
   - 团队ID
2. **自动登录** - 使用配置的邮箱密码自动登录系统
3. **获取脚本** - 自动调用后端API创建连接邀请获取脚本地址
4. **状态检测** - 智能检测千牛客户端状态：
   - 如果千牛正在运行 → 提示关闭 → 自动注入脚本
   - 如果千牛未运行 → 直接注入脚本
5. **启动千牛** - 注入完成后提示启动千牛客户端
6. **启动服务** - 检测到千牛启动后自动启动代理服务
7. **完成** - 千牛现在已连接到您的系统

## ⚙️ 配置文件

配置文件：`config.json`（首次运行自动创建）

```json
{
  "apiHost": "localhost",
  "apiPort": 3002,
  "email": "your-email",
  "password": "your-password",
  "teamId": "your-team-id-here",
  "connectionName": "千牛连接",
  "connectionDescription": "自动创建的千牛连接",
  "expiresInDays": 7,
  "tcpProxy": {
    "backendHost": "127.0.0.1",
    "backendPort": 9997
  }
}
```

### 配置说明

- `apiHost` - API服务器地址
- `apiPort` - API服务器端口
- `email` - 登录邮箱（必填）
- `password` - 登录密码（必填）
- `teamId` - 团队ID（必填）
- `connectionName` - 连接名称
- `expiresInDays` - 连接过期天数
- `tcpProxy.backendHost` - TCP代理后端服务器地址
- `tcpProxy.backendPort` - TCP代理后端服务器端口

## 📁 项目结构

```
qianniu-connector/
├── src/                          # TypeScript 源代码
│   ├── index.ts                  # 主入口文件
│   ├── core/                     # 核心功能模块
│   │   └── qianniu-connector.ts  # 千牛连接器核心类
│   ├── config/                   # 配置管理
│   │   └── config-manager.ts     # 配置管理器
│   ├── services/                 # 服务层
│   │   ├── api-service.ts        # API 服务
│   │   ├── auth-service.ts       # 认证服务
│   │   └── one-click-service.ts  # 一键启动服务
│   └── utils/                    # 工具类
│       ├── help.ts               # 帮助信息
│       ├── http-client.ts        # HTTP 客户端
│       └── process-manager.ts    # 进程管理器
├── tools/                        # 外部工具脚本
│   ├── qianniu-runner.js         # 千牛脚本注入工具
│   └── tcp-proxy.js              # TCP代理服务
├── bin/                          # 二进制文件
│   ├── QNQtHelp64.exe           # 千牛助手程序
│   └── QnQtMsg64.dll            # 千牛消息库
├── dist/                         # 构建输出
│   └── qianniu-connector.exe     # 独立可执行文件
├── config.json                   # 配置文件（运行时生成）
├── config.example.json           # 配置文件示例
├── package.json                  # 项目配置
└── tsconfig.json                 # TypeScript 配置
```

## ⚠️ 注意事项

1. **管理员权限** - Windows系统建议以管理员权限运行
2. **API服务器** - 确保后端API服务器正常运行
3. **网络连接** - 确保网络连接正常
4. **团队权限** - 确保有正确的团队管理权限
5. **千牛版本** - 支持主流千牛客户端版本

## 🔧 故障排除

### 常见问题

**Q: 程序启动后立即闪退**
A: 这通常是由于以下原因：

- 缺少必要的文件（tools/tcp-proxy.cjs, tools/qianniu-runner.cjs）
- 配置文件格式错误
- 权限不足
- 解决方法：以管理员权限运行，检查配置文件，确保所有文件完整

**Q: 提示"团队ID不能为空"**
A: 请确保在配置中输入了正确的团队ID，或运行 `./dist/qianniu-connector.exe config` 重新配置

**Q: API调用失败**
A: 请检查：

- API服务器是否正常运行
- 网络连接是否正常
- 团队ID是否正确
- 是否有足够的权限

**Q: 千牛进程检测失败**
A: 请确保：

- 千牛客户端（AliWorkbench.exe）已正确安装
- 以管理员权限运行脚本
- 防火墙未阻止脚本运行

**Q: 脚本注入失败**
A: 请确保：

- 千牛客户端已完全关闭
- 以管理员权限运行
- 千牛安装目录可写

## 📞 技术支持

如遇到问题，请检查：

1. 控制台错误信息
2. 配置文件是否正确
3. API服务器状态
4. 网络连接状态

## 🔄 版本更新

脚本支持自动更新，会从CDN获取最新的注入工具。
