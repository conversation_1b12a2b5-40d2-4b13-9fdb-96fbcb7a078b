import { request } from '@umijs/max';
import type {
  LogData,
  LogDetails,
  LogLevel,
  LogModule,
  LogQueue,
} from '../types/logger';

const API_BASE_URL =
  (process.env.UMI_APP_API_URL || 'http://localhost:3009') + '/api/v1';

class Logger {
  private queue: LogQueue;
  private sessionId: string;
  private userId?: string;
  private flushTimer?: NodeJS.Timeout;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.queue = {
      logs: [],
      maxSize: 10, // 最多缓存10条日志（仅错误和警告）
      flushInterval: 30000, // 30秒自动发送一次（仅警告日志）
      sending: false,
    };

    this.startAutoFlush();
    this.setupBeforeUnload();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public setUserId(userId: string) {
    this.userId = userId;
  }

  public clearUserId() {
    this.userId = undefined;
  }

  private startAutoFlush() {
    this.flushTimer = setInterval(() => {
      // 只有警告日志才需要定时发送，错误日志立即发送
      if (this.queue.logs.length > 0) {
        const hasWarningOnly = this.queue.logs.every(
          (log) => log.level === 'warn',
        );
        if (hasWarningOnly) {
          this.flush();
        }
      }
    }, this.queue.flushInterval);
  }

  private setupBeforeUnload() {
    if (typeof window !== 'undefined') {
      // 页面卸载前发送日志
      window.addEventListener('beforeunload', () => {
        if (this.queue.logs.length > 0) {
          // 使用sendBeacon确保日志能发送出去
          this.sendLogsSync();
        }
      });
    }
  }

  private createLogData(
    level: LogLevel,
    message: string,
    module: LogModule,
    details?: LogDetails,
  ): LogData {
    return {
      level,
      message,
      timestamp: Date.now(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent:
        typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      userId: this.userId,
      sessionId: this.sessionId,
      module,
      details,
    };
  }

  private addToQueue(logData: LogData) {
    // 只收集错误和警告日志
    if (logData.level !== 'error' && logData.level !== 'warn') {
      return;
    }

    this.queue.logs.push(logData);

    // 立即发送错误日志，延迟发送警告日志
    if (logData.level === 'error') {
      this.flush();
    } else if (this.queue.logs.length >= this.queue.maxSize) {
      this.flush();
    }
  }

  public async flush() {
    if (this.queue.sending || this.queue.logs.length === 0) {
      return;
    }

    const logsToSend = [...this.queue.logs];
    this.queue.logs = [];
    this.queue.sending = true;

    try {
      await Promise.all(logsToSend.map((log) => this.sendLog(log)));
    } catch (error) {
      console.error('发送日志失败:', error);
      // 如果发送失败，将日志重新加入队列
      this.queue.logs.unshift(...logsToSend);
    } finally {
      this.queue.sending = false;
    }
  }

  private sendLogsSync() {
    if (
      typeof navigator !== 'undefined' &&
      navigator.sendBeacon &&
      this.queue.logs.length > 0
    ) {
      try {
        this.queue.logs.forEach((log) => {
          const payload = JSON.stringify(log);
          navigator.sendBeacon(
            `${API_BASE_URL}/frontend-logs`,
            new Blob([payload], { type: 'application/json' }),
          );
        });
        this.queue.logs = []; // 发送后清空队列
      } catch (error) {
        console.error('同步发送日志失败:', error);
      }
    }
  }

  private async sendLog(logData: LogData) {
    try {
      await request(`${API_BASE_URL}/frontend-logs`, {
        method: 'POST',
        data: logData,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 3000, // 3秒超时
      });
    } catch (error) {
      // 静默处理发送失败，避免日志系统本身产生干扰
      console.warn('日志发送失败:', error);
      throw error;
    }
  }

  // 公共方法
  public info(
    message: string,
    module: LogModule = 'page',
    details?: LogDetails,
  ) {
    this.addToQueue(this.createLogData('info', message, module, details));
  }

  public warn(
    message: string,
    module: LogModule = 'page',
    details?: LogDetails,
  ) {
    this.addToQueue(this.createLogData('warn', message, module, details));
  }

  public error(
    message: string,
    module: LogModule = 'error',
    details?: LogDetails,
  ) {
    this.addToQueue(this.createLogData('error', message, module, details));
  }

  public debug(
    message: string,
    module: LogModule = 'page',
    details?: LogDetails,
  ) {
    this.addToQueue(this.createLogData('debug', message, module, details));
  }

  // 请求日志专用方法
  public logRequest(
    method: string,
    url: string,
    status: number,
    duration: number,
    requestData?: any,
    responseData?: any,
  ) {
    const level: LogLevel =
      status >= 500 ? 'error' : status >= 400 ? 'warn' : 'info';
    const message = `${method} ${url} ${status}`;

    this.addToQueue(
      this.createLogData(level, message, 'request', {
        method,
        requestUrl: url,
        requestData,
        responseStatus: status,
        responseData,
        duration,
      }),
    );
  }

  // 错误日志专用方法
  public logError(
    error: Error,
    module: LogModule = 'error',
    details?: Partial<LogDetails>,
  ) {
    this.addToQueue(
      this.createLogData('error', error.message, module, {
        stack: error.stack,
        errorType: error.name,
        ...details,
      }),
    );
  }

  public destroy() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flush(); // 最后发送一次
  }
}

// 创建全局单例
export const logger = new Logger();
export default logger;
