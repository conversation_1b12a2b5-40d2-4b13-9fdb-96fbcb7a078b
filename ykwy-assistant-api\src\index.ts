import type { Server, ServerWebSocket } from 'bun';
import Bun from 'bun';
import { randomUUID } from 'crypto';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger as honoLogger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';

import { connectionTracker } from './lib/connection-tracker';
import { prisma } from './lib/db';
import { logger } from './lib/logger';
import { lokiService } from './lib/loki';
import { platformWsManager, type UnifiedPlatformConnection } from './lib/platform-websocket';
import { wsManager } from './lib/websocket';
import { errorContextEnhancement, performanceMonitoring, requestTracking } from './middleware/request-tracking';
import { cdnService } from './services/cdn.service';
import { connectionBasedNotificationService } from './services/connectionBasedNotificationService';
import { notifyConnectionInvitationListChanged, notifyQianniuClientListChanged } from './services/eventService';
import { messageRouter } from './services/messageRouter';
import { qianniuTcpServer } from './services/qianniuTcpServer';
import type { PlatformType } from './types/platform';
import routes from './routes';

const app = new Hono();

// 客服WebSocket 附带的数据结构
type WSData = {
  userId: string;
  organizationId: string;
  socketId: string;
};

// 平台WebSocket数据结构已统一到Record<string, unknown>

// 全局中间件
app.use('*', honoLogger());
app.use('*', requestTracking);
app.use('*', performanceMonitoring);
app.use('*', errorContextEnhancement);
app.use('*', prettyJSON());
app.use(
  '*',
  cors({
    origin: [
      'http://localhost:5173',
      'https://localhost:5173',
      'http://localhost:5174', // 桌面端Vite开发服务器
      'http://localhost:5175', // 桌面端备用端口
      'http://localhost:3000', // 桌面端备用端口
      ...(Bun.env['YKWY_ASSISTANT_WEB_URL'] ? [Bun.env['YKWY_ASSISTANT_WEB_URL']] : []),
    ],
    allowHeaders: ['Content-Type', 'Authorization', 'Cache-Control'],
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    credentials: true,
  }),
);

// 根路径
app.get('/', (c) => {
  return c.json({
    message: 'Hono API Server with WebSocket Support',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      api: '/api/v1',
      ws: '/ws',
    },
  });
});

// 挂载所有路由
app.route('/', routes);

// 404 处理
app.notFound((c) => {
  return c.json({ error: 'Not Found', path: c.req.url }, 404);
});

// 错误处理
app.onError((err, c) => {
  // 使用统一的错误处理函数
  const { handleError } = require('./lib/utils');
  return handleError(c, err);
});

const port = Number(Bun.env['PORT']) || 3002;
const host = Bun.env['HOST'] || '0.0.0.0';

// 处理客服WebSocket连接
async function handleCustomerServiceWS(req: Request, server: { upgrade: (req: Request, options: { data: WSData }) => boolean }, url: URL) {
  const userId = url.searchParams.get('userId');
  const organizationId = url.searchParams.get('organizationId');
  const token = url.searchParams.get('token'); // 添加 token 参数

  logger.info('客服WebSocket连接请求', {
    url: url.toString(),
    userId,
    organizationId,
    hasToken: !!token,
    hasUserId: !!userId,
    hasOrganizationId: !!organizationId,
    timestamp: new Date().toISOString(),
  });

  if (!userId || !organizationId) {
    logger.error('WebSocket连接缺少必要参数', { userId, organizationId });
    return new Response('Missing userId or organizationId', { status: 400 });
  }

  // 验证 JWT token（如果提供了的话）
  if (token) {
    try {
      const { verifyToken } = await import('./lib/jwt');
      const payload = await verifyToken(token);

      if (!payload || payload.type !== 'access') {
        logger.error('WebSocket连接无效的访问令牌');
        return new Response('Invalid access token', { status: 401 });
      }

      // 验证 token 中的用户ID是否与请求的用户ID匹配
      if (payload.userId !== userId) {
        logger.error('WebSocket连接用户ID不匹配', { tokenUserId: payload.userId, requestUserId: userId });
        return new Response('User ID mismatch', { status: 403 });
      }

      logger.info('JWT令牌验证成功', { userId });
    } catch (error) {
      logger.error('JWT令牌验证失败', { userId }, error instanceof Error ? error : new Error(String(error)));
      return new Response('Token verification failed', { status: 401 });
    }
  } else {
    logger.warn('WebSocket连接未提供认证令牌', { userId, organizationId });
  }

  logger.info('尝试升级到WebSocket连接', { userId, organizationId });
  const success = server.upgrade(req, {
    data: {
      userId,
      organizationId,
      socketId: randomUUID(),
    } as WSData,
  });

  if (success) {
    logger.info('WebSocket升级成功', { userId, organizationId });
    return new Response(null, { status: 101 }); // 101 Switching Protocols
  } else {
    logger.error('WebSocket升级失败', { userId, organizationId });
    return new Response('WebSocket upgrade error', { status: 400 });
  }
}

// 处理千牛平台WebSocket连接（仅支持邀请模式）
function handlePlatformWS(req: Request, server: { upgrade: (req: Request, options: { data: Record<string, unknown> }) => boolean }, url: URL) {
  const pathParts = url.pathname.split('/');
  const platformType = pathParts[2]; // /platform-ws/{platformType}

  // 只支持千牛平台
  if (platformType !== 'qianniu') {
    return new Response('Only Qianniu platform is supported', { status: 400 });
  }

  // 只支持邀请码连接模式
  const invitationId = url.searchParams.get('invitationId');
  if (!invitationId) {
    return new Response('Missing invitationId parameter. Please use invitation-based connection.', { status: 400 });
  }

  return handleQianniuConnection(req, server, invitationId);
}

// 处理千牛连接
function handleQianniuConnection(req: Request, server: { upgrade: (req: Request, options: { data: Record<string, unknown> }) => boolean }, invitationId: string) {
  // 直接使用邀请ID作为连接ID，简化逻辑
  const success = server.upgrade(req, {
    data: {
      connectionId: invitationId, // 使用邀请ID作为连接ID - 一个邀请对应一台机器
      type: 'invitation_pending',
      platformType: 'QIANNIU',
      invitationId,
      connectedAt: new Date(),
    },
  });

  return success ? new Response(null, { status: 101 }) : new Response('Invitation WebSocket upgrade error', { status: 400 });
}

/**
 * 处理千牛WebSocket连接建立时的状态更新
 */
async function handleQianniuConnectionOpen(_ws: ServerWebSocket<Record<string, unknown>>, connectionId: string) {
  try {
    logger.info('千牛连接建立', { connectionId });

    // 查找对应的客户端并更新为在线状态
    const updatedClients = await prisma.qianniuClient.updateMany({
      where: {
        connectionId: connectionId,
      },
      data: {
        isOnline: true,
        lastOnlineAt: new Date(),
      },
    });

    if (updatedClients.count > 0) {
      logger.info('千牛客户端状态更新成功', {
        connectionId,
        clientsUpdated: updatedClients.count,
      });

      // 记录WebSocket激活追踪
      // 注意：这里我们需要从connectionId反向查找invitationId
      const client = await prisma.qianniuClient.findFirst({
        where: { connectionId },
        include: { invitation: true },
      });

      if (client?.invitation) {
        connectionTracker.trackWebSocketActivation(client.invitation.id, {
          clientsUpdated: updatedClients.count,
          newClientCreated: false,
        });
      }
    } else {
      logger.info('未找到对应connectionId的客户端，等待激活', { connectionId });
    }
  } catch (error) {
    logger.error('千牛连接建立处理失败', { connectionId }, error instanceof Error ? error : new Error(String(error)));
  }
}

/**
 * 处理千牛WebSocket连接关闭时的状态更新
 */
async function handleQianniuConnectionClose(connectionId: string) {
  try {
    logger.info('千牛连接关闭，更新客户端状态', { connectionId });

    // 查找对应的客户端并设置为离线
    const updatedClients = await prisma.qianniuClient.updateMany({
      where: {
        connectionId: connectionId,
        isOnline: true,
      },
      data: {
        isOnline: false,
        lastOnlineAt: new Date(),
      },
    });

    if (updatedClients.count > 0) {
      logger.info('千牛客户端状态更新为离线', {
        connectionId,
        clientsUpdated: updatedClients.count,
      });
    } else {
      logger.info('未找到需要更新的在线客户端', { connectionId });
    }
  } catch (error) {
    logger.error('千牛连接关闭处理失败', { connectionId }, error instanceof Error ? error : new Error(String(error)));
  }
}

// 处理连接激活
async function handleConnectionActivation(ws: ServerWebSocket<Record<string, unknown>>, data: { invitationId: string; clientInfo?: Record<string, unknown> }) {
  try {
    // 现在connectionId就是invitationId，逻辑大大简化
    const connectionId = ws.data['connectionId'] as string;
    const invitationId = data.invitationId || (ws.data['invitationId'] as string);
    const { clientInfo } = data;

    logger.info('开始激活连接', { connectionId, invitationId });

    // 验证连接邀请
    logger.debug('查找连接邀请', { invitationId });
    const invitation = await prisma.connectionInvitation.findUnique({
      where: { id: invitationId as string },
      include: {
        organization: true,
        team: true,
        qianniuClient: true, // 包含关联的客户端信息
      },
    });

    if (!invitation) {
      logger.warn('连接邀请不存在', { invitationId });
      ws.send(
        JSON.stringify({
          type: 'activation_failed',
          reason: '连接邀请不存在',
        }),
      );
      ws.close();
      return;
    }

    if (invitation.status !== 'PENDING') {
      logger.info('邀请状态检查', {
        invitationId,
        status: invitation.status,
      });

      // 如果邀请已被激活，尝试恢复现有连接
      if (invitation.status === 'ACTIVATED') {
        logger.info('尝试恢复现有连接', { invitationId });

        // 查找现有的客户端记录 - 直接使用邀请关联的客户端
        const existingClient = invitation.qianniuClient;

        if (existingClient) {
          logger.info('找到现有客户端', {
            invitationId,
            clientId: existingClient.id,
          });

          // 更新现有客户端的连接信息
          await prisma.qianniuClient.update({
            where: { id: existingClient.id },
            data: {
              connectionId: ws.data['connectionId'] as string,
              isOnline: true,
              lastOnlineAt: new Date(),
              clientInfo: JSON.parse(JSON.stringify(clientInfo || {})),
            },
          });

          // 更新WebSocket连接数据
          ws.data = {
            ...ws.data,
            type: 'activated', // 修复：使用统一的激活状态
            platformType: 'QIANNIU',
            organizationId: invitation.organizationId,
            teamId: invitation.teamId,
            platformData: { clientId: existingClient.id },
          };

          // 添加到连接管理器
          const connection = {
            connectionId: ws.data['connectionId'] as string,
            platformType: 'QIANNIU' as PlatformType,
            organizationId: invitation.organizationId,
            teamId: invitation.teamId,
            platformData: { clientId: existingClient.id },
            ws,
            connectedAt: new Date(),
          };
          platformWsManager.addConnection(connection);

          logger.info('连接恢复成功', {
            invitationId,
            clientId: existingClient.id,
          });
          ws.send(
            JSON.stringify({
              type: 'activation_success',
              clientId: existingClient.id,
              organizationId: invitation.organizationId,
              teamId: invitation.teamId,
            }),
          );
          return;
        }
      }

      // 其他状态（EXPIRED等）仍然拒绝连接
      ws.send(
        JSON.stringify({
          type: 'activation_failed',
          reason: '连接已失效或已使用',
        }),
      );
      ws.close();
      return;
    }

    if (invitation.expiresAt < new Date()) {
      logger.warn('邀请已过期', {
        invitationId,
        expiresAt: invitation.expiresAt.toISOString(),
        currentTime: new Date().toISOString(),
      });
      // 标记为过期
      await prisma.connectionInvitation.update({
        where: { id: invitation.id },
        data: { status: 'EXPIRED' },
      });

      ws.send(
        JSON.stringify({
          type: 'activation_failed',
          reason: '连接邀请已过期',
        }),
      );
      ws.close();
      return;
    }

    // 检查是否存在使用相同connectionId的孤儿客户端记录
    const existingClientWithSameConnectionId = await prisma.qianniuClient.findUnique({
      where: { connectionId: ws.data['connectionId'] as string },
    });

    if (existingClientWithSameConnectionId) {
      logger.info('发现孤儿客户端记录，正在删除', {
        orphanedClientId: existingClientWithSameConnectionId.id,
        connectionId: ws.data['connectionId'],
      });
      await prisma.qianniuClient.delete({
        where: { id: existingClientWithSameConnectionId.id },
      });
    }

    // 创建千牛客户端记录
    logger.info('创建千牛客户端记录', { invitationId: invitation.id });
    const qianniuClient = await prisma.qianniuClient.create({
      data: {
        organizationId: invitation.organizationId,
        teamId: invitation.teamId,
        name: invitation.name,
        description: invitation.description,
        connectionId: ws.data['connectionId'] as string,
        isOnline: true,
        lastOnlineAt: new Date(),
        clientInfo: JSON.parse(JSON.stringify(clientInfo || {})),
      },
    });

    // 更新邀请状态
    await prisma.connectionInvitation.update({
      where: { id: invitation.id },
      data: {
        status: 'ACTIVATED',
        activatedAt: new Date(),
        qianniuClientId: qianniuClient.id,
      },
    });

    // 通知前端列表变化
    notifyQianniuClientListChanged(invitation.organizationId);
    notifyConnectionInvitationListChanged(invitation.organizationId);

    // 升级连接为正式连接
    ws.data = {
      ...ws.data,
      type: 'activated',
      organizationId: invitation.organizationId,
      teamId: invitation.teamId,
      platformType: 'QIANNIU',
      platformData: {
        clientId: qianniuClient.id,
        clientName: qianniuClient.name,
      },
    };

    // 添加到连接管理器
    const connection = {
      connectionId: ws.data['connectionId'] as string,
      platformType: 'QIANNIU' as PlatformType,
      organizationId: invitation.organizationId,
      teamId: invitation.teamId,
      platformData: {
        clientId: qianniuClient.id,
        clientName: qianniuClient.name,
      },
      ws,
      connectedAt: new Date(),
    };

    platformWsManager.addConnection(connection);

    // 通知激活成功
    ws.send(
      JSON.stringify({
        type: 'activation_success',
        clientId: qianniuClient.id,
        organizationName: invitation.organization.name,
        teamName: invitation.team.name,
      }),
    );

    logger.info('千牛连接激活成功', {
      invitationName: invitation.name,
      clientId: qianniuClient.id,
      organizationId: invitation.organizationId,
      teamId: invitation.teamId,
    });

    // 广播连接激活事件给组织内的所有客服
    wsManager.broadcast(
      invitation.organizationId,
      JSON.stringify({
        type: 'connection_activated',
        payload: {
          invitationId: invitation.id,
          clientId: qianniuClient.id,
          clientName: qianniuClient.name,
        },
      }),
    );
  } catch (error) {
    logger.error(
      '千牛连接激活失败',
      {
        connectionId: ws.data['connectionId'],
        invitationId: data.invitationId,
      },
      error instanceof Error ? error : new Error(String(error)),
    );
    ws.send(
      JSON.stringify({
        type: 'activation_failed',
        reason: '服务器内部错误',
      }),
    );
    ws.close();
  }
}

// 处理统一的平台WebSocket消息
async function handlePlatformMessage(ws: ServerWebSocket<Record<string, unknown>>, message: string | Buffer) {
  const startTime = Date.now();
  const messageId = `msg_${Date.now()}_${randomUUID()}`;

  try {
    const messageStr = message.toString();
    const messageSize = messageStr.length;

    logger.info('📨 [WebSocket] 收到原始消息', {
      messageId,
      messageSize,
      connectionId: ws.data?.['connectionId'],
      platformType: ws.data?.['platformType'],
      organizationId: ws.data?.['organizationId'],
      rawMessagePreview: messageStr.substring(0, 200) + (messageStr.length > 200 ? '...' : ''),
    });

    const data = JSON.parse(messageStr);
    const { connectionId, type, platformType, organizationId, teamId, platformData } = ws.data;

    logger.info('🔍 [WebSocket] 消息解析成功', {
      messageId,
      messageType: data.type || data.eventType || 'unknown',
      connectionId,
      type,
      platformType,
      organizationId,
      teamId,
      parsedDataKeys: Object.keys(data),
    });

    // 🔧 优化：将完整消息移到调试级别，避免生产环境日志污染
    logger.debug('📋 [WebSocket] 完整消息内容', {
      messageId,
      fullMessage: JSON.stringify(data, null, 2),
    });

    // 处理连接激活
    if (type === 'invitation_pending' && (data.type === 'activate_invitation' || data.type === 'qianniu_connect')) {
      logger.info('🔗 [WebSocket] 处理连接激活', {
        messageId,
        connectionId,
        activationType: data.type,
        organizationId,
        teamId,
      });
      await handleConnectionActivation(ws, data);
      logger.info('✅ [WebSocket] 连接激活处理完成', {
        messageId,
        connectionId,
        processingTime: Date.now() - startTime,
      });
      return;
    }

    // 处理execute命令响应 - 来自千牛客户端的命令执行结果
    if (data.type === 'execute') {
      logger.info('⚡ [WebSocket] 收到执行响应', {
        messageId,
        connectionId,
        responseLength: data.response?.length || 0,
        responsePreview: typeof data.response === 'string' ? data.response.substring(0, 100) : JSON.stringify(data.response).substring(0, 100),
      });
      // 这里可以处理命令执行的结果，比如发送消息的结果
      // 目前只记录日志，后续可以添加回调处理
      return;
    }

    // 处理千牛API调用响应
    if (data.type === 'qianniu_api_response') {
      logger.info('🔧 [WebSocket] 收到千牛API响应', {
        messageId,
        connectionId,
        requestId: data.requestId,
        responseLength: data.response?.length || 0,
      });

      // 导入千牛API WebSocket服务
      const { qianniuApiWebSocketService } = await import('./services/qianniuApiWebSocketService');
      qianniuApiWebSocketService.handleApiResponse(data);
      return;
    }

    // 更新心跳时间
    if (data.type === 'heartbeat' || data.type === 'hi' || data.type === 'ping') {
      logger.debug('💓 [WebSocket] 心跳消息', {
        messageId,
        connectionId,
        heartbeatType: data.type,
      });
      platformWsManager.updateHeartbeat(connectionId as string);
      ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
      return;
    }

    // 只有已激活的连接才能处理业务消息
    if (type === 'invitation_pending') {
      logger.warn('⚠️ [WebSocket] 未激活连接尝试发送业务消息', {
        messageId,
        connectionId,
        type,
        messageType: data.type,
        organizationId,
        teamId,
      });
      return;
    }

    // 构建平台连接数据
    const connection = {
      connectionId: connectionId as string,
      platformType: platformType as PlatformType,
      organizationId: organizationId as string,
      teamId: teamId as string,
      platformData: platformData as Record<string, unknown>,
    };

    logger.info('🚀 [WebSocket] 开始路由业务消息', {
      messageId,
      connectionId,
      platformType,
      organizationId,
      teamId,
      messageType: data.type,
    });

    // 将消息路由到MessageRouter进行处理
    try {
      await messageRouter.routePlatformMessage(connection, data);
      logger.info('✅ [WebSocket] 消息路由处理成功', {
        messageId,
        connectionId,
        processingTime: Date.now() - startTime,
      });
    } catch (routingError) {
      logger.error(
        '❌ [WebSocket] 消息路由处理失败',
        {
          messageId,
          connectionId,
          processingTime: Date.now() - startTime,
          error: routingError instanceof Error ? routingError.message : String(routingError),
        },
        routingError instanceof Error ? routingError : new Error(String(routingError)),
      );
    }
  } catch (error) {
    logger.error(
      '💥 [WebSocket] 消息处理异常',
      {
        messageId,
        connectionId: ws.data?.['connectionId'],
        platformType: ws.data?.['platformType'],
        organizationId: ws.data?.['organizationId'],
        processingTime: Date.now() - startTime,
        errorName: error instanceof Error ? error.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
      },
      error instanceof Error ? error : new Error(String(error)),
    );
  }
}

logger.info('Hono服务器启动成功', {
  host,
  port,
  url: `http://${host}:${port}`,
});

Bun.serve({
  hostname: host,
  port,
  fetch: async (req: Request, server: Server) => {
    // 尝试将请求升级到WebSocket
    const url = new URL(req.url);

    // 客服WebSocket连接
    if (url.pathname === '/ws') {
      return await handleCustomerServiceWS(req, server, url);
    }

    // 统一的平台WebSocket连接
    if (url.pathname.startsWith('/platform-ws/')) {
      return handlePlatformWS(req, server, url);
    }

    // 对于非WebSocket请求，交由Hono处理
    return app.fetch(req, server);
  },
  websocket: {
    // 当一个新连接建立时
    open(ws: ServerWebSocket<WSData | Record<string, unknown>>) {
      // 判断连接类型
      if ('userId' in ws.data) {
        // 客服WebSocket连接
        const { userId, organizationId } = ws.data as WSData;
        wsManager.addClient(organizationId, userId, ws as ServerWebSocket<WSData>);

        // 更新连接绑定通知服务的在线状态
        connectionBasedNotificationService.userOnline(userId, organizationId);
      } else {
        // 统一的平台WebSocket连接
        const wsData = ws.data as Record<string, unknown>;
        const { connectionId, type, platformType } = wsData;

        logger.info('新的平台WebSocket连接建立', {
          connectionId,
          type,
          platformType,
        });

        // 对于千牛连接，立即检查和更新客户端状态
        if (platformType === 'QIANNIU' && connectionId) {
          handleQianniuConnectionOpen(ws, connectionId as string).catch((error: Error) => {
            logger.error('千牛连接状态检查失败', { connectionId }, error);
          });
        }

        // 只有已激活的连接才添加到连接管理器
        if (type === 'activated') {
          const { organizationId, teamId, platformData } = wsData;

          const connection: UnifiedPlatformConnection = {
            connectionId: connectionId as string,
            platformType: platformType as PlatformType,
            organizationId: organizationId as string,
            teamId: teamId as string,
            platformData: platformData as Record<string, unknown>,
            ws: ws as ServerWebSocket<Record<string, unknown>>,
            connectedAt: new Date(),
          };

          platformWsManager.addConnection(connection);
        } else {
          // 等待激活的连接，只记录日志
          logger.info('平台连接等待激活', { platformType, connectionId });
        }
      }
    },

    // 当连接关闭时
    close(ws: ServerWebSocket<WSData | Record<string, unknown>>) {
      if ('userId' in ws.data) {
        // 客服WebSocket连接
        const { userId, organizationId, socketId } = ws.data as WSData;
        wsManager.removeClient(organizationId, userId, socketId);

        // 更新连接绑定通知服务的离线状态
        connectionBasedNotificationService.userOffline(userId, organizationId);
      } else {
        // 统一的平台WebSocket连接
        const wsData = ws.data as Record<string, unknown>;
        const { connectionId, type, platformType } = wsData;

        logger.info('平台WebSocket连接关闭', { connectionId, type, platformType });

        // 对于千牛连接，立即更新客户端离线状态
        if (platformType === 'QIANNIU' && connectionId) {
          handleQianniuConnectionClose(connectionId as string).catch((error: Error) => {
            logger.error('千牛连接关闭处理失败', { connectionId }, error);
          });
        }

        // 只有已激活的连接才从连接管理器中移除
        if (type === 'activated') {
          // 异步移除连接，不等待完成
          platformWsManager.removeConnection(connectionId as string).catch((error: Error) => {
            logger.error('移除平台连接失败', { connectionId }, error);
          });
        } else {
          // 未激活的连接，只记录日志
          logger.info('平台等待连接已关闭', { platformType, connectionId });
        }
      }
    },

    // 当收到消息时
    message(ws: ServerWebSocket<WSData | Record<string, unknown>>, message: string | Buffer) {
      const messageReceiveTime = Date.now();

      if ('userId' in ws.data) {
        // 客服WebSocket消息 (目前为空，单向推送)
        logger.debug('📨 [WebSocket] 客服端消息', {
          userId: ws.data.userId,
          organizationId: ws.data.organizationId,
          messageSize: message.toString().length,
          receiveTime: messageReceiveTime,
        });
        return;
      } else {
        // 统一的平台WebSocket消息
        logger.debug('📨 [WebSocket] 平台消息接收', {
          connectionId: ws.data?.['connectionId'],
          platformType: ws.data?.['platformType'],
          messageSize: message.toString().length,
          receiveTime: messageReceiveTime,
        });

        handlePlatformMessage(ws as ServerWebSocket<Record<string, unknown>>, message);
      }
    },
  },
});

// 启动千牛TCP服务器（监听9997端口）
qianniuTcpServer
  .start()
  .then(() => {
    logger.info('千牛TCP服务器启动成功', {
      port: 9997,
      note: '请单独运行TCP代理: node tcp-proxy-standalone/index.js',
    });
  })
  .catch((error) => {
    logger.error('千牛TCP服务器启动失败', {}, error instanceof Error ? error : new Error(String(error)));
  });

// 记录Loki服务状态
if (lokiService.isEnabled()) {
  logger.info('Loki日志服务已启用', { service: 'loki' });
} else {
  logger.warn('Loki日志服务未启用，请检查环境变量配置', {
    service: 'loki',
    requiredVars: ['LOKI_URL', 'LOKI_USERNAME', 'LOKI_PASSWORD'],
  });
}

// 验证CDN配置
try {
  const cdnValidation = cdnService.validateConfig();
  if (cdnValidation.valid) {
    logger.info('CDN服务配置验证通过', { service: 'cdn' });
  } else {
    logger.warn('CDN服务配置不完整', {
      service: 'cdn',
      errors: cdnValidation.errors,
    });
  }
} catch (error) {
  logger.error('CDN服务初始化失败', { service: 'cdn' }, error instanceof Error ? error : new Error(String(error)));
}

// 记录CORS配置
const allowedOrigins = [
  'http://localhost:5173',
  'http://localhost:5174',
  'http://localhost:8080',
  'http://localhost:9000',
  ...(Bun.env['YKWY_ASSISTANT_WEB_URL'] ? [Bun.env['YKWY_ASSISTANT_WEB_URL']] : []),
];
logger.info('CORS跨域配置已启用', {
  service: 'cors',
  allowedOrigins,
  credentials: true,
});
