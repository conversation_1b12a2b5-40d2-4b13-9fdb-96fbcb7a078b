import type { Context } from 'hono';

import { ErrorCode } from '../constants/errorCodes.ts';
import { AppError } from '../errors/custom.error.ts';
import { SizeChartService } from '../services/sizeChartService';
import {
  batchCreateEntriesSchema,
  paramIdSchema,
  sizeChartQuerySchema,
  sizeChartTypeQuerySchema,
  upsertSizeChartEntrySchema,
  upsertSizeChartSchema,
  upsertSizeChartTypeSchema,
} from '../types/validators/sizeChartValidator';
import { R } from '../utils/Response.ts';

const sizeChartService = new SizeChartService();

/**
 * 尺码表控制器
 * 处理尺码表类型、尺码表、条目等的基础CRUD操作
 */
export class SizeChartController {
  // ============ 尺码表类型相关操作 ============

  public async upsertSizeChartType(c: Context) {
    const body = await c.req.json();
    const validation = upsertSizeChartTypeSchema.safeParse(body);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.upsertSizeChartType(validation.data);
    return R.success(c, result);
  }

  public async findSizeChartTypes(c: Context) {
    const query = c.req.query();
    const validation = sizeChartTypeQuerySchema.safeParse(query);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.findManySizeChartTypes(validation.data);
    return R.success(c, result);
  }

  public async findSizeChartTypeById(c: Context) {
    const params = c.req.param();
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.findSizeChartTypeById(validation.data.id);
    return R.success(c, result);
  }

  public async deleteSizeChartType(c: Context) {
    const params = c.req.param();
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.deleteSizeChartType(validation.data.id);
    return R.success(c, result);
  }

  // ============ 尺码表相关操作 ============

  public async upsertSizeChart(c: Context) {
    const body = await c.req.json();
    const validation = upsertSizeChartSchema.safeParse(body);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.upsertSizeChart(validation.data);
    return R.success(c, result);
  }

  public async findSizeCharts(c: Context) {
    const query = c.req.query();
    const validation = sizeChartQuerySchema.safeParse(query);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.findManySizeCharts(validation.data);
    return R.success(c, result);
  }

  public async findSizeChartById(c: Context) {
    const params = c.req.param();
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.findSizeChartById(validation.data.id);
    return R.success(c, result);
  }

  public async deleteSizeChart(c: Context) {
    const params = c.req.param();
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.deleteSizeChart(validation.data.id);
    return R.success(c, result);
  }

  // ============ 尺码表条目相关操作 ============

  public async upsertSizeChartEntry(c: Context) {
    const body = await c.req.json();
    const validation = upsertSizeChartEntrySchema.safeParse(body);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.upsertSizeChartEntry(validation.data);
    return R.success(c, result);
  }

  public async findSizeChartEntries(c: Context) {
    const params = c.req.param();
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.findSizeChartEntries(validation.data.id);
    return R.success(c, result);
  }

  public async batchCreateEntries(c: Context) {
    const { id: chartId } = c.req.param();
    const body = await c.req.json();
    const validation = batchCreateEntriesSchema.safeParse(body);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    if (!chartId) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { message: '尺码表ID为必填项' });
    }

    const { entries } = validation.data;
    await sizeChartService.batchCreateEntries(chartId, entries);
    return R.success(c, { message: '条目批量创建成功' });
  }

  public async deleteSizeChartEntry(c: Context) {
    const params = c.req.param();
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    const result = await sizeChartService.deleteSizeChartEntry(validation.data.id);
    return R.success(c, result);
  }

  public async clearSizeChartEntries(c: Context) {
    const params = c.req.param();
    const validation = paramIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    await sizeChartService.clearSizeChartEntries(validation.data.id);
    return R.success(c, { message: '条目清空成功' });
  }
}
