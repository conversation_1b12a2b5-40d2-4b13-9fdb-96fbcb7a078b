### 临时商品 V2 API 测试
### 使用 AccessToken 认证的搜索接口

### 1. 根据查询字符串搜索临时商品 - 匹配商品ID（需要shopId）
GET http://localhost:3009/api/v2/temp-product/search?query=953394282130&shopId=wyts666
Access-Token: PoNyP4f0zYDWuXg8NytR

### 2. 根据查询字符串搜索临时商品 - 匹配完整商品名称（需要shopId）
GET http://localhost:3009/api/v2/temp-product/search?query=AI 智能写作助手 | 高效产出各类文案&shopId=wyts666
Access-Token: PoNyP4f0zYDWuXg8NytR

### 3. 根据查询字符串搜索临时商品 - 匹配款号（需要shopId）
GET http://localhost:3009/api/v2/temp-product/search?query=WYTS001&shopId=wyts666
Access-Token: PoNyP4f0zYDWuXg8NytR

### 4. 根据查询字符串搜索临时商品 - 匹配完整链接（需要shopId）
GET http://localhost:3009/api/v2/temp-product/search?query=http://item.taobao.com/item.htm?id=953394282130&shopId=wyts666
Access-Token: PoNyP4f0zYDWuXg8NytR

### 5. 查询不存在的内容 - 返回空数组
GET http://localhost:3009/api/v2/temp-product/search?query=不存在的商品&shopId=wyts666
Access-Token: PoNyP4f0zYDWuXg8NytR

### 6. 查询指定店铺的所有商品 - query为空
GET http://localhost:3009/api/v2/temp-product/search?shopId=wyts666
Access-Token: PoNyP4f0zYDWuXg8NytR

### 7. 缺少shopId参数 - 返回错误
GET http://localhost:3009/api/v2/temp-product/search?query=932971667258
Access-Token: PoNyP4f0zYDWuXg8NytR

### 8. 安全测试：无AccessToken - 返回403
GET http://localhost:3009/api/v2/temp-product/search?query=轻功

### 9. 安全测试：错误的AccessToken - 返回403
GET http://localhost:3009/api/v2/temp-product/search?query=轻功
Access-Token: invalid-token

### 10. 精确匹配测试 - 完整商品名称
GET http://localhost:3009/api/v2/temp-product/search?query=轻功短袖POLO衫男款女款夏季透气排汗短袖&shopId=shop123
Access-Token: PoNyP4f0zYDWuXg8NytR

### 11. 查询指定店铺下的所有商品 - 只提供shopId
GET http://localhost:3009/api/v2/temp-product/search?shopId=shop456
Access-Token: PoNyP4f0zYDWuXg8NytR

### 12. 不存在的店铺ID测试 - shopId不匹配任何商品
GET http://localhost:3009/api/v2/temp-product/search?query=轻功&shopId=nonexistent-shop
Access-Token: PoNyP4f0zYDWuXg8NytR

### 13. 空shopId测试 - shopId参数为空字符串（应返回错误）
GET http://localhost:3009/api/v2/temp-product/search?query=轻功&shopId=
Access-Token: PoNyP4f0zYDWuXg8NytR

### 14. 缺少shopId参数测试 - 应返回错误
GET http://localhost:3009/api/v2/temp-product/search?query=轻功
Access-Token: PoNyP4f0zYDWuXg8NytR

### ================================
### 临时商品插入接口测试
### ================================

### 15. 插入新临时商品 - 成功场景
POST http://localhost:3009/api/v2/temp-product/insert
Content-Type: application/json
Access-Token: PoNyP4f0zYDWuXg8NytR

{
  "name": "测试商品名称",
  "linkOrId": "https://item.taobao.com/item.htm?id=test12345",
  "productId": "TEST12345",
  "status": "已上架",
  "shopId": "shop123",
  "styleNumber": "T001",
  "imageUrl": "https://example.com/image.jpg",
  "description": {
"basic_info": {
"product_name": "AI智能写作助手",
"product_id": "AW001",
"category": "人工智能软件",
"brand": "智创科技",
"tagline": "让每个人都能成为优秀的内容创作者",
"main_features": [
"多场景写作支持",
"智能内容优化",
"一键生成多版本",
"SEO优化建议",
"原创度检测"
]
},
"product_details": {
"supported_content_types": [
"学术论文",
"商业报告",
"新闻稿件",
"营销文案",
"社交媒体内容",
"小红书种草文",
"抖音脚本",
"朋友圈文案",
"产品描述",
"邮件模板"
],
"core_functions": {
"content_generation": "基于GPT-4架构的内容生成",
"style_adaptation": "10+种写作风格自动适配",
"length_control": "支持50-5000字灵活控制",
"tone_adjustment": "正式/轻松/专业/幽默多种语调",
"seo_optimization": "关键词密度优化和建议",
"originality_check": "99%原创度保证"
},
"technical_specs": {
"ai_model": "GPT-4 Turbo",
"response_time": "3-8秒",
"accuracy_rate": "95%+",
"language_support": ["中文", "英文", "日文"],
"platform_compatibility": ["Web端", "移动端", "API接口"],
"export_formats": ["Word文档(.docx)", "PDF", "纯文本(.txt)", "Markdown"],
"voice_input": "支持语音输入，识别准确率98%+",
"content_audit": "内置敏感词过滤和违规内容检测",
"batch_generation": "专业版支持批量生成，最多50篇",
"api_support": "企业版提供RESTful API和SDK"
}
},
"pricing": {
"free_plan": {
"name": "体验版",
"price": 0,
"features": ["每日3次使用", "基础模板", "500字以内"],
"limitations": "功能受限，不支持批量生成"
},
"pro_plan": {
"name": "专业版",
"price": 99,
"billing_cycle": "月",
"features": [
"无限次使用",
"全场景模板",
"5000字长文",
"批量生成",
"导出多格式",
"优先客服"
]
},
"enterprise_plan": {
"name": "企业版",
"price": 999,
"billing_cycle": "年",
"features": [
"多账户管理",
"定制模板",
"API接口",
"数据报告",
"专属客服",
"培训服务"
]
}
},
"service_guarantee": {
"free_trial": "7天免费试用",
"refund_policy": "30天无理由退款",
"uptime_guarantee": "99.9%可用性保证",
"customer_support": "7*24小时在线客服",
"data_security": "企业级数据加密存储"
},
"user_scenarios": {
"students": "论文写作、课业报告、演讲稿",
"marketers": "广告文案、产品描述、社媒内容",
"bloggers": "爆款标题、种草文案、评测文章",
"entrepreneurs": "商业计划书、产品介绍、投资报告",
"media_workers": "新闻稿件、采访大纲、专题报道"
}
}
}

### 16. 插入临时商品 - 缺少必填字段(名称)
POST http://localhost:3009/api/v2/temp-product/insert
Content-Type: application/json
Access-Token: PoNyP4f0zYDWuXg8NytR

{
  "linkOrId": "https://item.taobao.com/item.htm?id=test12346",
  "productId": "TEST12346",
  "status": "已上架",
  "shopId": "shop123"
}

### 17. 插入临时商品 - 缺少必填字段(shopId)
POST http://localhost:3009/api/v2/temp-product/insert
Content-Type: application/json
Access-Token: PoNyP4f0zYDWuXg8NytR

{
  "name": "测试商品名称2",
  "linkOrId": "https://item.taobao.com/item.htm?id=test12347",
  "productId": "TEST12347",
  "status": "已上架"
}

### 18. 插入临时商品 - 商品ID已存在
POST http://localhost:3009/api/v2/temp-product/insert
Content-Type: application/json
Access-Token: PoNyP4f0zYDWuXg8NytR

{
  "name": "重复商品名称",
  "linkOrId": "https://item.taobao.com/item.htm?id=test12345",
  "productId": "TEST12345",
  "status": "已上架",
  "shopId": "shop123"
}

### 19. 插入临时商品 - 图片链接格式错误
POST http://localhost:3009/api/v2/temp-product/insert
Content-Type: application/json
Access-Token: PoNyP4f0zYDWuXg8NytR

{
  "name": "测试商品名称3",
  "linkOrId": "https://item.taobao.com/item.htm?id=test12348",
  "productId": "TEST12348",
  "status": "已上架",
  "shopId": "shop123",
  "imageUrl": "invalid-url"
}

### 20. 插入临时商品 - 无AccessToken
POST http://localhost:3009/api/v2/temp-product/insert
Content-Type: application/json

{
  "name": "测试商品名称4",
  "linkOrId": "https://item.taobao.com/item.htm?id=test12349",
  "productId": "TEST12349",
  "status": "已上架",
  "shopId": "shop123"
}

### 21. 插入临时商品 - 最小字段集
POST http://localhost:3009/api/v2/temp-product/insert
Content-Type: application/json
Access-Token: PoNyP4f0zYDWuXg8NytR

{
  "name": "最小字段商品",
  "linkOrId": "https://item.taobao.com/item.htm?id=minimal12350",
  "productId": "MINIMAL12350",
  "status": "未上架",
  "shopId": "shop456"
}

### API说明：

### ==================== 搜索接口说明 ====================
### GET /api/v2/temp-product/search
### 这个接口用于根据查询字符串搜索临时商品
### - 使用AccessToken认证，需要在请求头中提供有效的Access-Token
### - shopId参数必填，用于指定要查询的店铺
### - query参数可选，为空时返回shopId下的所有商品，有值时进行精确匹配
### - query同时对productId、name、styleNumber、linkOrId四个字段进行精确匹配
### - 只返回未删除(isDeleted=0)且已上架(status='已上架')的商品
### - 使用精确匹配，不支持模糊查询
### - 按创建时间倒序排列结果
### - 返回的数据包含description字段
### - 如果没有匹配则返回空数组
### 
### 使用场景：
### 1. 查询指定店铺下的所有商品：/search?shopId=shop123
### 2. 查询指定店铺下的特定商品：/search?shopId=shop123&query=M250011

### ==================== 插入接口说明 ====================
### POST /api/v2/temp-product/insert
### 这个接口用于插入新的临时商品（复用现有upsert方法）
### - 使用AccessToken认证，需要在请求头中提供有效的Access-Token
### - 请求体为JSON格式，包含商品信息
### - 必填字段：name, linkOrId, productId, status（shopId可选但推荐提供）
### - 可选字段：styleNumber, imageUrl, description(JSON格式), shopId
### - 不提供id字段表示创建新记录
### - 如果productId已存在则更新现有记录
### - 成功后自动清除相关缓存
### - 返回完整的商品信息包含id和时间戳
### 
### 响应格式：
### - 成功：{"success": true, "data": {...}}
### - 失败：{"success": false, "error": "错误信息"} 