import { Hono } from 'hono';

import { ShippingRestrictedAreaController } from '../controller/shippingRestrictedArea';
import { downloadShippingRestrictedAreaTemplate, importShippingRestrictedAreas } from '../controller/shippingRestrictedAreaFile';

const router = new Hono();
const shippingRestrictedArea = new ShippingRestrictedAreaController();

// 创建/更新发货受限地址
// POST /api/v1/shipping-restricted-area
router.post('/shipping-restricted-area', shippingRestrictedArea.upsert);

// 获取发货受限地址列表（支持分页、过滤）
// GET /api/v1/shipping-restricted-areas
router.get('/shipping-restricted-areas', shippingRestrictedArea.findMany);

// 获取单个发货受限地址详情
// GET /api/v1/shipping-restricted-area/:id
router.get('/shipping-restricted-area/:id', shippingRestrictedArea.findById);

// 删除发货受限地址
// DELETE /api/v1/shipping-restricted-area/:id
router.delete('/shipping-restricted-area/:id', shippingRestrictedArea.delete);

// 批量创建发货受限地址
// POST /api/v1/shipping-restricted-areas/bulk-create
router.post('/shipping-restricted-areas/bulk-create', shippingRestrictedArea.bulkCreate);

// 更新发货受限地址状态
// PUT /api/v1/shipping-restricted-area/:id/status
router.put('/shipping-restricted-area/:id/status', shippingRestrictedArea.updateStatus);

// 批量更新发货受限地址状态
// PUT /api/v1/shipping-restricted-areas/bulk-status
router.put('/shipping-restricted-areas/bulk-status', shippingRestrictedArea.bulkUpdateStatus);

// 检查地址是否受限
// GET /api/v1/shipping-restricted-areas/check
router.get('/shipping-restricted-areas/check', shippingRestrictedArea.checkRestriction);

// 批量删除发货受限地址
// DELETE /api/v1/shipping-restricted-areas/bulk-delete
router.delete('/shipping-restricted-areas/bulk-delete', shippingRestrictedArea.bulkDelete);

// 导入发货受限地址文件
// POST /api/v1/shipping-restricted-areas/import
router.post('/shipping-restricted-areas/import', importShippingRestrictedAreas);

// 下载发货受限地址导入模板
// GET /api/v1/shipping-restricted-areas/template
router.get('/shipping-restricted-areas/template', downloadShippingRestrictedAreaTemplate);

// 同步发货受限地址到Ragflow知识库
// POST /api/v1/shipping-restricted-areas/sync-ragflow
router.post('/shipping-restricted-areas/sync-ragflow', shippingRestrictedArea.syncRagflow);

export default router;
