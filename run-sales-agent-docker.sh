#!/bin/bash

# 销售智能体Docker运行脚本
# 支持日志挂载和环境变量配置

set -e

# 配置变量
CONTAINER_NAME="ykwy-sales-agent"
IMAGE_NAME="ykwy-sales-agent:latest"
HOST_PORT="8000"
CONTAINER_PORT="8000"

# 日志目录配置
HOST_LOG_DIR="$(pwd)/logs/sales-agent"
CONTAINER_LOG_DIR="/app/logs"

# 数据目录配置
HOST_DATA_DIR="$(pwd)/mock-production/data"
CONTAINER_DATA_DIR="/app/data"

# 创建宿主机日志目录
echo "📁 创建日志目录: $HOST_LOG_DIR"
mkdir -p "$HOST_LOG_DIR"

# 创建宿主机数据目录
echo "📁 创建数据目录: $HOST_DATA_DIR"
mkdir -p "$HOST_DATA_DIR"

# 停止并删除现有容器（如果存在）
if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    echo "🛑 停止现有容器: $CONTAINER_NAME"
    docker stop "$CONTAINER_NAME" || true
    echo "🗑️  删除现有容器: $CONTAINER_NAME"
    docker rm "$CONTAINER_NAME" || true
fi

# 构建Docker镜像
echo "🔨 构建Docker镜像: $IMAGE_NAME"
cd mock-production
docker build -t "$IMAGE_NAME" \
    --build-arg OPENAI_API_KEY="${OPENAI_API_KEY}" \
    --build-arg OPENAI_API_BASE="${OPENAI_BASE_URL}" \
    .
cd ..

# 运行Docker容器
echo "🚀 启动销售智能体容器..."
docker run -d \
    --name "$CONTAINER_NAME" \
    --restart unless-stopped \
    -p "${HOST_PORT}:${CONTAINER_PORT}" \
    -v "${HOST_LOG_DIR}:${CONTAINER_LOG_DIR}" \
    -v "${HOST_DATA_DIR}:${CONTAINER_DATA_DIR}" \
    -e OPENAI_API_KEY="${OPENAI_API_KEY}" \
    -e OPENAI_BASE_URL="${OPENAI_BASE_URL}" \
    -e LOG_DIR="${CONTAINER_LOG_DIR}" \
    -e NODE_ENV="${NODE_ENV:-production}" \
    -e LOKI_URL="${LOKI_URL}" \
    -e LOKI_USERNAME="${LOKI_USERNAME}" \
    -e LOKI_PASSWORD="${LOKI_PASSWORD}" \
    -e PYTHONPATH="/app" \
    "$IMAGE_NAME"

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 5

# 检查容器状态
if docker ps --format 'table {{.Names}}\t{{.Status}}' | grep -q "^${CONTAINER_NAME}"; then
    echo "✅ 销售智能体容器启动成功！"
    echo ""
    echo "📊 容器信息:"
    docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep "$CONTAINER_NAME"
    echo ""
    echo "📁 日志文件位置: $HOST_LOG_DIR"
    echo "🌐 API地址: http://localhost:$HOST_PORT"
    echo "📋 健康检查: http://localhost:$HOST_PORT/health"
    echo ""
    echo "📝 查看日志命令:"
    echo "   实时日志: docker logs -f $CONTAINER_NAME"
    echo "   文件日志: tail -f $HOST_LOG_DIR/sales_agent.log"
    echo ""
    echo "🛑 停止容器: docker stop $CONTAINER_NAME"
else
    echo "❌ 容器启动失败！"
    echo "📋 查看错误日志:"
    docker logs "$CONTAINER_NAME"
    exit 1
fi
