import type { Context } from 'hono';
import * as XLSX from 'xlsx';

import { ShippingRestrictedAreaService } from '../services/shippingRestrictedAreaService';

const shippingRestrictedAreaService = new ShippingRestrictedAreaService();

/**
 * 下载发货受限地址导入模板
 */
export const downloadShippingRestrictedAreaTemplate = async (c: Context) => {
  try {
    const format = c.req.query('format') || 'excel';

    // 模板数据 - 根据疫情地区管理的格式
    const templateData = [
      {
        '省份/直辖市': '北京',
        城市: '北京',
        '县/区': '朝阳区',
        发货受限程度: '不受限',
      },
      {
        '省份/直辖市': '北京',
        城市: '北京',
        '县/区': '海淀区',
        发货受限程度: '不受限',
      },
      {
        '省份/直辖市': '上海',
        城市: '上海',
        '县/区': '浦东新区',
        发货受限程度: '受限',
      },
    ];

    let buffer: Buffer;
    let filename: string;
    let contentType: string;

    if (format === 'csv') {
      // 生成CSV
      const ws = XLSX.utils.json_to_sheet(templateData);
      const csv = XLSX.utils.sheet_to_csv(ws);
      buffer = Buffer.from('\uFEFF' + csv, 'utf-8'); // 添加BOM以支持中文
      filename = `发货受限地址模板_${new Date().toISOString().slice(0, 10)}.csv`;
      contentType = 'text/csv; charset=utf-8';
    } else {
      // 生成Excel
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(templateData);
      worksheet['!cols'] = [
        { wch: 15 }, // 省份/直辖市
        { wch: 15 }, // 城市
        { wch: 15 }, // 县/区
        { wch: 12 }, // 发货受限程度
      ];
      XLSX.utils.book_append_sheet(workbook, worksheet, '发货受限地址');
      const xlsxBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      buffer = Buffer.from(xlsxBuffer);
      filename = `发货受限地址模板_${new Date().toISOString().slice(0, 10)}.xlsx`;
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    }

    // 直接返回Response对象，避免使用c.header和c.body
    return new Response(buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
        'Content-Length': buffer.length.toString(),
      },
    });
  } catch (error) {
    console.error('模板下载失败:', error);
    return c.json({ code: 500, msg: '模板下载失败', data: null }, 500);
  }
};

/**
 * 导入发货受限地址文件
 */
export const importShippingRestrictedAreas = async (c: Context) => {
  const startTime = Date.now();
  try {
    // 1. 获取上传文件
    const formData = await c.req.formData();
    const file = formData.get('file') as File | null;
    if (!file) {
      return c.json({ code: 400, msg: '未上传文件', data: null }, 400);
    }

    const ext = file.name.split('.').pop()?.toLowerCase();
    if (!['xlsx', 'xls', 'csv'].includes(ext || '')) {
      return c.json({ code: 400, msg: '仅支持xlsx/xls/csv文件', data: null }, 400);
    }

    // 2. 读取文件内容
    const arrayBuffer = await file.arrayBuffer();
    let rows: Record<string, unknown>[] = [];

    if (ext === 'csv') {
      const text = Buffer.from(arrayBuffer).toString('utf-8');
      const wb = XLSX.read(text, { type: 'string' });
      const sheetName = wb.SheetNames[0];
      if (!sheetName) return c.json({ code: 400, msg: 'CSV文件无内容', data: null }, 400);
      const sheet = wb.Sheets[sheetName];
      if (!sheet) return c.json({ code: 400, msg: 'CSV文件无内容', data: null }, 400);
      rows = XLSX.utils.sheet_to_json(sheet, { defval: '' });
    } else {
      const workbook = XLSX.read(arrayBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) return c.json({ code: 400, msg: 'Excel文件无内容', data: null }, 400);
      const sheet = workbook.Sheets[sheetName];
      if (!sheet) return c.json({ code: 400, msg: 'Excel文件无内容', data: null }, 400);
      rows = XLSX.utils.sheet_to_json(sheet, { defval: '' });
    }

    // 3. 清洗数据
    rows = rows.map((row) => {
      const cleaned: Record<string, unknown> = {};
      Object.keys(row).forEach((key) => {
        const val = row[key];
        if (typeof val === 'string') {
          cleaned[key] = val.replace(/^[\s\r\n]+|[\s\r\n]+$/g, '');
        } else {
          cleaned[key] = val;
        }
      });
      return cleaned;
    });

    // 4. 数据验证和转换
    const errors: string[] = [];
    const importList = rows
      .map((row: Record<string, unknown>, idx: number) => {
        try {
          const province = (row['省份/直辖市'] as string) || '';
          const city = (row['城市'] as string) || '';
          const district = (row['县/区'] as string) || '';

          if (!province || !city || !district) {
            throw new Error(`第${idx + 2}行缺少必填字段：省份/直辖市、城市、县/区`);
          }

          // 处理发货受限程度
          const restrictionText = (row['发货受限程度'] as string) || '不受限';
          let isActive = false;

          if (['受限', '是', 'true', '1'].includes(restrictionText.toLowerCase())) {
            isActive = true;
          } else if (['不受限', '否', 'false', '0'].includes(restrictionText.toLowerCase())) {
            isActive = false;
          } else {
            // 默认为不受限
            isActive = false;
          }

          return {
            province: province.trim(),
            city: city.trim(),
            district: district.trim(),
            isActive,
          };
        } catch (e: unknown) {
          if (e instanceof Error) {
            errors.push(e.message);
          } else {
            errors.push(`第${idx + 2}行数据处理异常`);
          }
          return null;
        }
      })
      .filter(
        (
          item,
        ): item is {
          province: string;
          city: string;
          district: string;
          isActive: boolean;
        } => !!item,
      );

    // 5. 批量导入数据库
    if (importList.length > 0) {
      const result = await shippingRestrictedAreaService.batchImport(importList);

      const duration = Date.now() - startTime;

      return c.json({
        code: 200,
        msg: '导入成功',
        data: {
          successCount: result.successCount,
          failCount: result.failCount,
          newCount: result.newCount,
          updatedCount: result.updatedCount,
          errors: errors.length > 0 ? errors : undefined,
          duration,
        },
      });
    } else {
      return c.json(
        {
          code: 400,
          msg: '没有有效数据',
          data: { errors },
        },
        400,
      );
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ 导入失败！耗时: ${duration}ms`);
    console.error('导入异常:', error);
    console.error('异常堆栈:', error instanceof Error ? error.stack : error);

    return c.json(
      {
        code: 500,
        msg: error instanceof Error ? error.message : '系统发生意外错误',
        data: { duration },
      },
      500,
    );
  }
};
