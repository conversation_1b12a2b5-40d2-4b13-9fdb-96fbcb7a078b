import HttpClient from '../utils/http-client.js';

/**
 * 构建API URL，智能处理端口号
 * @param apiHost API主机地址
 * @param apiPort API端口号
 * @param path API路径
 * @returns 完整的API URL
 */
function buildApiUrl(apiHost: string, apiPort: number, path: string): string {
  let url = apiHost;
  if (!url.includes('://')) {
    url = `http://${url}`;
  }

  // 检查是否需要添加端口号
  const isHttps = url.startsWith('https://');
  const defaultPort = isHttps ? 443 : 80;

  if (apiPort && apiPort !== defaultPort) {
    url = `${url}:${apiPort}`;
  }

  return `${url}${path}`;
}

interface ApiResponse {
  success: boolean;
  data?: {
    cdnUrl?: string;
    invitation?: {
      id: string;
    };
  };
  message?: string;
}

interface Config {
  apiHost: string;
  apiPort: number;
  connectionName: string;
  connectionDescription: string;
  teamId: string;
  expiresInDays: number;
}

interface AuthService {
  getAuthHeaders(): Record<string, string>;
  isLoggedIn(): boolean;
  login(): Promise<boolean>;
  logout(): void;
}

/**
 * API服务
 * 负责与后端API的交互
 */
class ApiService {
  private config: Config;
  private authService: AuthService;

  constructor(config: Config, authService: AuthService) {
    this.config = config;
    this.authService = authService;
  }

  /**
   * 创建连接邀请
   * @returns {Promise<string>} 脚本地址
   */
  async createConnectionInvitation() {
    console.log('📡 正在调用后端API创建连接邀请...');

    // 确保已登录
    if (!this.authService.isLoggedIn()) {
      await this.authService.login();
    }

    const url = buildApiUrl(this.config.apiHost, this.config.apiPort, '/api/v1/connection-invitations');
    const data = {
      name: this.config.connectionName,
      description: this.config.connectionDescription,
      teamId: this.config.teamId,
      expiresInDays: this.config.expiresInDays,
    };

    try {
      const headers = this.authService.getAuthHeaders();
      const response = (await HttpClient.post(url, data, headers)) as ApiResponse;

      if (response.success && response.data && response.data.cdnUrl) {
        console.log('✅ 连接邀请创建成功');
        console.log(`📋 连接ID: ${response.data.invitation.id}`);
        console.log(`🔗 脚本地址: ${response.data.cdnUrl}`);
        return response.data.cdnUrl;
      } else {
        throw new Error('API响应格式错误或缺少脚本地址');
      }
    } catch (error) {
      // 如果是401错误，可能是token过期，尝试重新登录
      if (error.message.includes('401')) {
        console.log('🔄 Token可能已过期，尝试重新登录...');
        this.authService.logout();
        await this.authService.login();

        try {
          const headers = this.authService.getAuthHeaders();
          const retryResponse = (await HttpClient.post(url, data, headers)) as ApiResponse;

          if (retryResponse.success && retryResponse.data && retryResponse.data.cdnUrl) {
            console.log('✅ 连接邀请创建成功');
            console.log(`📋 连接ID: ${retryResponse.data.invitation.id}`);
            console.log(`🔗 脚本地址: ${retryResponse.data.cdnUrl}`);
            return retryResponse.data.cdnUrl;
          }
        } catch (retryError) {
          console.error('❌ 重试后仍然失败:', retryError.message);
        }
      }

      console.error('❌ 创建连接邀请失败:', error.message);
      console.log('💡 请检查：');
      console.log('   - API服务器是否正常运行');
      console.log('   - 网络连接是否正常');
      console.log('   - 团队ID是否正确');
      console.log('   - 用户是否有足够的权限');
      console.log('   - 邮箱和密码是否正确');
      throw error;
    }
  }
}

export default ApiService;
