// 连接邀请相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse, ConnectionInvitation, ConnectionInvitationFilters, ConnectionInvitationResponse, CreateConnectionInvitationRequest, RegenerateConnectionInvitationData } from '../types';

// 连接邀请列表查询选项
export const connectionInvitationsQueryOptions = (filters: ConnectionInvitationFilters = {}) => ({
  queryKey: queryKeys.connectionInvitations(filters),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<ConnectionInvitation[]>>('connection-invitations', filters as Record<string, string | number | boolean | string[] | number[] | null | undefined>);
    return response;
  },
});

// 单个连接邀请详情查询选项
export const connectionInvitationQueryOptions = (id: string) => ({
  queryKey: queryKeys.connectionInvitation(id),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<ConnectionInvitationResponse>>(`connection-invitations/${id}`);
    return response.data;
  },
});

// 创建连接邀请 Mutation
export const createConnectionInvitationMutation = {
  mutationFn: async (data: CreateConnectionInvitationRequest) => {
    const response = await apiClient.post<ApiResponse<ConnectionInvitationResponse>>('connection-invitations', data);
    return response.data;
  },
};

// 撤销连接邀请 Mutation
export const revokeConnectionInvitationMutation = {
  mutationFn: async (id: string) => {
    const response = await apiClient.delete<ApiResponse<{ message: string }>>(`connection-invitations/${id}`);
    return response.data;
  },
};

// 重新生成连接邀请 Mutation
export const regenerateConnectionInvitationMutation = {
  mutationFn: async ({ id, data }: { id: string; data: RegenerateConnectionInvitationData }) => {
    const response = await apiClient.post<ApiResponse<ConnectionInvitationResponse>>(`connection-invitations/${id}/regenerate`, data);
    return response.data;
  },
};
