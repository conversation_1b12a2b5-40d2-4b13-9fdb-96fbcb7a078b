/**
 * @prettier
 */
import { Hono } from 'hono';

import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { platformWsManager } from '../lib/platform-websocket';
import { createResponse, handleError } from '../lib/utils';

const app = new Hono();

/**
 * GET /platform-ws/stats - 获取平台WebSocket连接统计
 */
app.get('/stats', async (c) => {
  try {
    logger.info('获取平台WebSocket连接统计');

    // 获取WebSocket连接统计
    const wsStats = platformWsManager.getStats();

    // 获取数据库中的客户端统计
    const dbStats = await prisma.qianniuClient.groupBy({
      by: ['isOnline', 'organizationId'],
      _count: {
        id: true,
      },
    });

    // 组织统计数据
    const stats = {
      totalConnections: wsStats.totalConnections,
      activeConnections: wsStats.totalConnections,
      onlineClients: await prisma.qianniuClient.count({ where: { isOnline: true } }),
      offlineClients: await prisma.qianniuClient.count({ where: { isOnline: false } }),
      websocket: wsStats,
      database: {
        total: await prisma.qianniuClient.count(),
        online: await prisma.qianniuClient.count({ where: { isOnline: true } }),
        offline: await prisma.qianniuClient.count({ where: { isOnline: false } }),
        byOrganization: dbStats.reduce(
          (acc, stat) => {
            const orgId = stat.organizationId;
            if (!acc[orgId]) {
              acc[orgId] = { total: 0, online: 0, offline: 0 };
            }
            acc[orgId].total += stat._count.id;
            if (stat.isOnline) {
              acc[orgId].online += stat._count.id;
            } else {
              acc[orgId].offline += stat._count.id;
            }
            return acc;
          },
          {} as Record<string, { total: number; online: number; offline: number }>,
        ),
      },
      realTime: {
        activeConnections: wsStats.totalConnections,
        qianniuConnections: wsStats.connectionsByType?.['QIANNIU'] || 0,
      },
    };

    logger.debug('平台WebSocket统计数据', stats);

    return c.json(createResponse(stats, '获取连接统计成功'));
  } catch (error) {
    logger.error('获取平台WebSocket统计失败', {}, error instanceof Error ? error : new Error(String(error)));
    return handleError(c, error);
  }
});

/**
 * GET /platform-ws/connections - 获取活跃的平台连接列表
 */
app.get('/connections', async (c) => {
  try {
    logger.info('获取活跃平台连接列表');

    // 获取所有活跃的WebSocket连接
    const allConnections = platformWsManager.getAllConnections();

    // 转换为前端需要的格式
    const connections = await Promise.all(
      allConnections.map(async (conn) => {
        // 查找对应的客户端信息，包含当前活跃会话和客户信息
        let client = null;
        let currentUser = null;
        
        if (conn.platformData?.['clientId']) {
          client = await prisma.qianniuClient.findUnique({
            where: { id: conn.platformData['clientId'] as string },
            select: {
              id: true,
              name: true,
              team: {
                select: {
                  id: true,
                  name: true,
                },
              },
              accounts: {
                where: { isActive: true },
                select: {
                  id: true,
                  accountName: true,
                  shopName: true,
                  platformType: true,
                  conversations: {
                    where: {
                      status: { not: 'CLOSED' }
                    },
                    orderBy: {
                      lastActivity: 'desc'
                    },
                    take: 1,
                    select: {
                      id: true,
                      title: true, // conversationCode存储在title中
                      lastActivity: true,
                      customer: {
                        select: {
                          id: true,
                          nickname: true,
                          avatar: true,
                          platformCustomerId: true,
                        }
                      }
                    }
                  }
                },
                take: 1,
              },
            },
          });

          // 如果找到活跃账号和对话，提取当前用户信息
          if (client?.accounts?.[0]?.conversations?.[0]) {
            const activeConversation = client.accounts[0].conversations[0];
            currentUser = {
              id: activeConversation.customer.id,
              nickname: activeConversation.customer.nickname,
              avatar: activeConversation.customer.avatar,
              platformCustomerId: activeConversation.customer.platformCustomerId,
              conversationId: activeConversation.title || activeConversation.id,
              lastActivity: activeConversation.lastActivity,
            };
          }
        }

        return {
          id: conn.connectionId,
          clientId: (conn.platformData?.['clientId'] as string) || null,
          connectionId: conn.connectionId,
          isOnline: conn.ws.readyState === 1, // WebSocket.OPEN
          connectedAt: conn.connectedAt.toISOString(),
          lastActivity: conn.lastHeartbeat?.toISOString() || conn.connectedAt.toISOString(),
          platformType: conn.platformType,
          organizationId: conn.organizationId,
          teamId: conn.teamId,
          client: client
            ? {
              id: client.id,
              name: client.name,
              team: client.team,
            }
            : null,
          currentUser: currentUser, // 新增：当前用户信息
        };
      }),
    );

    logger.debug('活跃平台连接数量', { count: connections.length });

    return c.json(createResponse(connections, '获取活跃连接列表成功'));
  } catch (error) {
    logger.error('获取活跃平台连接列表失败', {}, error instanceof Error ? error : new Error(String(error)));
    return handleError(c, error);
  }
});

/**
 * GET /platform-ws/health - 平台WebSocket健康检查
 */
app.get('/health', async (c) => {
  try {
    const stats = platformWsManager.getStats();

    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      connections: {
        total: stats.totalConnections,
        byType: stats.connectionsByType,
        byOrg: stats.connectionsByOrg,
        byTeam: stats.connectionsByTeam,
      },
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };

    return c.json(createResponse(health, 'Platform WebSocket服务健康'));
  } catch (error) {
    return handleError(c, error);
  }
});

export default app;
