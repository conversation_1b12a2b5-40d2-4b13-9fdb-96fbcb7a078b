### Build
```shell
docker build -t kennytian/ykwy-assistant-api:latest -f Dockerfile --no-cache --progress=plain .

docker build --platform linux/amd64 -t kennytian/ykwy-assistant-api:0.0.5 -f Dockerfile --no-cache --progress=plain .
```

### Run
```shell
docker run --rm --name ykwy-assistant-api --env-file .env -p 3000:3000 kennytian/ykwy-assistant-api

docker rm -f ykwy-assistant-api;docker run -p 3000:3000 --name ykwy-assistant-api --env-file .env kennytian/ykwy-assistant-api:latest
```
