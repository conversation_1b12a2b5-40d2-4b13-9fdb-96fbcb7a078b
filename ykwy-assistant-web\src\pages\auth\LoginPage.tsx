import React, { useEffect, useState } from 'react';
import { Link, useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import AuthCard from '../../components/auth/AuthCard';
import ErrorAlert from '../../components/auth/ErrorAlert';
import <PERSON>Field from '../../components/auth/FormField';
import { authClient } from '../../lib/auth-client';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [fieldErrors, setFieldErrors] = useState<{ email?: string; password?: string }>({});

  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  // 从 URL 参数或状态中获取预填邮箱和重定向路径
  useEffect(() => {
    const emailFromParams = searchParams.get('email');
    const emailFromState = location.state?.email;
    if (emailFromParams || emailFromState) {
      setEmail(emailFromParams || emailFromState || '');
    }
  }, [searchParams, location.state]);

  const validateForm = () => {
    const errors: { email?: string; password?: string } = {};

    if (!email) {
      errors.email = '请输入邮箱地址';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    if (!password) {
      errors.password = '请输入密码';
    } else if (password.length < 6) {
      errors.password = '密码至少需要6个字符';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const result = await authClient.signIn.email({
        email,
        password,
      });

      if (result.data?.accessToken) {
        const redirectPath = searchParams.get('redirect') || '/';
        navigate(redirectPath, { replace: true });
      } else {
        setError(result.error?.message || '登录失败，请稍后重试');
      }
    } catch (err: unknown) {
      console.error('Login error:', err);
      setError('登录失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthCard title="欢迎回来" subtitle="登录您的账户继续使用">
      <form onSubmit={handleSubmit} className="space-y-6">
        {error && <ErrorAlert error={error} onDismiss={() => setError('')} />}

        <FormField
          id="email"
          label="邮箱地址"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="输入您的邮箱地址"
          error={fieldErrors.email}
          autoComplete="email"
          autoFocus
        />

        <FormField
          id="password"
          label="密码"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="输入您的密码"
          error={fieldErrors.password}
          autoComplete="current-password"
        />

        <div className="flex items-center justify-between">
          <div className="text-sm">
            <Link to="/auth/forgot-password" className="font-medium text-indigo-600 hover:text-indigo-500">
              忘记密码？
            </Link>
          </div>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          {loading ? (
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              登录中...
            </div>
          ) : (
            '登录'
          )}
        </button>

        <div className="text-center">
          <span className="text-sm text-gray-600">
            还没有账户？{' '}
            <Link to="/auth/register" className="font-medium text-indigo-600 hover:text-indigo-500">
              立即注册
            </Link>
          </span>
        </div>
      </form>
    </AuthCard>
  );
}
