import { SearchOutlined } from '@ant-design/icons';
import { Button, Input, Select, Table } from 'antd';
import React, { useState } from 'react';

const { Option } = Select;

interface OrderRecord {
  key: string;
  priority: string;
  taskName: string;
  productCode: string;
  webDescription: string;
  readStatus: boolean;
  priorityLevel: number;
}

const OrderManagement: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('知识下发');
  const [searchText, setSearchText] = useState('');

  // 顶部按钮功能
  const handleExportProcess = () => {
    console.log('导出流程');
    // 这里可以添加导出流程的逻辑
  };

  const handleExport = () => {
    console.log('导出');
    // 这里可以添加导出的逻辑
  };

  const handleCreate = () => {
    console.log('创建');
    // 这里可以添加创建的逻辑
  };

  // 左侧分类数据
  const categories = [
    { key: '知识下发', label: '知识下发', count: 1 },
    { key: '数据管控', label: '数据管控', count: 1 },
    { key: '上架删除', label: '上架删除', count: 1 },
    { key: '门店配货', label: '门店配货', count: 1 },
    { key: '客服出货', label: '客服出货', count: 1 },
    { key: '已读服务', label: '已读服务', count: 1 },
    { key: '行货数据', label: '行货数据', count: 1 },
    { key: '门店比较', label: '门店比较', count: 1 },
    { key: '交易识别', label: '交易识别', count: 1 },
  ];

  // 表格数据
  const tableData: OrderRecord[] = [
    {
      key: '1',
      priority: '1',
      taskName: '知识下发',
      productCode: '全部商品',
      webDescription: '',
      readStatus: false,
      priorityLevel: 1,
    },
  ];

  // 表格列定义
  const columns = [
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (text: string) => (
        <span className="font-medium" data-oid="nbfg21b">
          {text}
        </span>
      ),
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 120,
      render: (text: string) => (
        <span className="text-blue-600" data-oid="n0:_wxc">
          {text}
        </span>
      ),
    },
    {
      title: '商品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
    },
    {
      title: '网页描述',
      dataIndex: 'webDescription',
      key: 'webDescription',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '已读状态',
      dataIndex: 'readStatus',
      key: 'readStatus',
      width: 100,
      render: (status: boolean) => (
        <span
          className={status ? 'text-green-500' : 'text-gray-500'}
          data-oid="zpjco8h"
        >
          {status ? '已读' : '未读'}
        </span>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priorityLevel',
      key: 'priorityLevel',
      width: 80,
      render: (level: number) => <span data-oid="-imyi52">{level}</span>,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: () => (
        <div className="flex gap-2" data-oid="-w.k16t">
          <Button
            type="link"
            size="small"
            className="p-0 text-blue-600"
            data-oid="f5y2fk."
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            className="p-0 text-blue-600"
            data-oid="ps1al_5"
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            className="p-0 text-red-500"
            data-oid="9y34iz0"
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="flex flex-col h-screen bg-gray-100" data-oid="kskii04">
      {/* 顶部横条 */}
      <div
        className="bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center min-h-16 shadow-sm"
        data-oid="g9_qd04"
      >
        {/* 左侧标题 */}
        <div className="text-lg font-semibold text-gray-800" data-oid="fuyhffy">
          任务列表
        </div>

        {/* 右侧按钮组 */}
        <div className="flex gap-3" data-oid="wzt-jqe">
          <Button
            onClick={handleExportProcess}
            className="border-gray-300 text-gray-700"
            data-oid="-m:9d5s"
          >
            同步配置
          </Button>
          <Button
            onClick={handleExport}
            className="border-gray-300 text-gray-700"
            data-oid="c19n7at"
          >
            跟单规则设置
          </Button>
          <Button type="primary" onClick={handleCreate} data-oid="j.j4d3-">
            创建跟单任务
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex flex-1" data-oid="a.exn-w">
        {/* 左侧分类导航 */}
        <div
          className="w-70 bg-white border-r border-gray-200 py-4"
          data-oid="6bpoe1:"
        >
          <div
            className="px-4 mb-4 text-base font-semibold text-gray-800"
            data-oid="zhr_kd6"
          >
            跟单场景
          </div>

          <div className="px-4" data-oid="gm6abrl">
            <div
              className="mb-4 text-sm text-gray-600 border-b border-gray-100 pb-2"
              data-oid="5r3td4:"
            >
              类型统计
            </div>

            {categories.map((category) => (
              <div
                key={category.key}
                className={`flex justify-between items-center px-3 py-2 my-1 cursor-pointer rounded-md transition-colors ${
                  selectedCategory === category.key
                    ? 'bg-blue-50 text-blue-600 border border-blue-200'
                    : 'text-gray-800 border border-transparent hover:bg-gray-50'
                }`}
                onClick={() => setSelectedCategory(category.key)}
                data-oid="49uz8ij"
              >
                <span className="text-sm" data-oid="231amb:">
                  {category.label}
                </span>
                <span
                  className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded"
                  data-oid="nh1l0ev"
                >
                  {category.count}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧主要内容区 */}
        <div className="flex-1 p-6" data-oid="udvua2p">
          {/* 页面标题 */}
          <div
            className="mb-6 text-xl font-semibold text-gray-800"
            data-oid="j5jemd5"
          >
            日常咨询分析
          </div>

          {/* 搜索和筛选区域 */}
          <div
            className="mb-4 flex justify-between items-center"
            data-oid="o2m_xly"
          >
            <div className="flex gap-4 items-center" data-oid="fz_zt4o">
              <Input
                placeholder="搜索关键词"
                prefix={<SearchOutlined data-oid="_9c:ahk" />}
                className="w-60"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                data-oid="s1cmpmb"
              />

              <Select
                defaultValue="全部状态"
                className="w-30"
                data-oid="ltfh-wz"
              >
                <Option value="all" data-oid="uisuc-w">
                  全部状态
                </Option>
                <Option value="read" data-oid="_m7gtbm">
                  已读
                </Option>
                <Option value="unread" data-oid="h_.t-p-">
                  未读
                </Option>
              </Select>
              <Select
                defaultValue="全部优先级"
                className="w-30"
                data-oid="uxl6pxv"
              >
                <Option value="all" data-oid="6t6-jus">
                  全部优先级
                </Option>
                <Option value="high" data-oid="wo8x74-">
                  高优先级
                </Option>
                <Option value="medium" data-oid="7srjq7v">
                  中优先级
                </Option>
                <Option value="low" data-oid="wvl6vxs">
                  低优先级
                </Option>
              </Select>
            </div>

            <Button type="primary" data-oid="70009qw">
              新建工单
            </Button>
          </div>

          {/* 表格区域 */}
          <div className="bg-white rounded-lg" data-oid="uh00udu">
            <Table
              columns={columns}
              dataSource={tableData}
              pagination={{
                total: 1,
                pageSize: 10,
                showTotal: (total) => `共 ${total} 条记录`,
                showSizeChanger: true,
                showQuickJumper: true,
              }}
              scroll={{ y: 'calc(100vh - 300px)' }}
              className="p-6"
              size="middle"
              data-oid="bf6zx.a"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderManagement;
