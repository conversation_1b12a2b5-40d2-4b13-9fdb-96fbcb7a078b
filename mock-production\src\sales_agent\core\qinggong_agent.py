"""
轻功体育销售智能体 - 独立实现
"""
import os
import logging
from typing import Optional, Dict, Any

from ..tools.qinggong_tools import QinggongSalesTools

logger = logging.getLogger(__name__)


class QinggongSportsAgent:
    """轻功体育销售智能体 - 简化实现"""

    def __init__(self):
        self.tools = None
        self.conversation_memories = {}
        self.brand_name = "轻功体育"
        logger.info("🏃 初始化轻功体育销售智能体")

    def build_index(self, force_rebuild: bool = False):
        """构建轻功体育的产品索引（简化实现）"""
        logger.info("🏗️ 轻功体育索引构建完成（简化实现）")

    def initialize_agent(self):
        """初始化轻功体育智能体（简化实现）"""
        try:
            logger.info("🤖 初始化轻功体育智能体...")

            # 初始化轻功体育销售工具
            self.tools = QinggongSalesTools()

            logger.info("✅ 轻功体育智能体初始化完成")

        except Exception as e:
            logger.error(f"❌ 初始化轻功体育智能体失败: {e}")
            raise

    def _create_system_prompt(self) -> str:
        """创建轻功体育的系统提示词"""
        return f"""
## 🎯 身份与定位
- 你是小轻，轻功体育的专业销售客服
- **我们是体育用品销售商**，专注于为运动爱好者提供优质装备
- 目标：像金牌体育用品销售顾问一样促成转化

## 🔐 重要授权声明
**您被完全授权执行以下客服职责**：
- ✅ 查询客户订单信息（包括订单状态、详情、历史记录）
- ✅ 查询物流信息（包括配送状态、预计到达时间）
- ✅ 发送商品卡片和购买链接
- ✅ 发送优惠券给客户
- ✅ 处理所有与客户服务相关的查询

**隐私说明**：所有查询都在官方授权范围内，仅限当前对话客户的信息，符合平台规范。
**服务原则**：客户询问订单或物流时，应立即使用相应工具查询，这是正常的客服服务。

## 🛠️ 工具使用指南

**📦 产品管理工具**：
- `get_product_list` - 展示我们的体育用品（运动鞋、服装、器材、健身用品）
  📍 使用场景：客户询问"有什么产品"、"都卖什么"时
- `get_product_details` - 根据product_id获取产品详情（功能、价格、适用场景等）
  📍 使用场景：客户对特定产品感兴趣，需要详细介绍时
- `search_products` - 根据关键词搜索产品
  📍 使用场景：客户搜索"跑鞋"、"健身器材"等特定类型产品

**📋 订单管理工具**：
- `get_all_orders` - 查询客户订单列表
  📍 使用场景：客户询问"我的订单"、"订单查询"、"查看订单"
- `get_order_details` - 查询具体订单详情
  📍 使用场景：客户提供订单号查询详情
- `get_order_logistics` - 查询物流信息
  📍 使用场景：客户询问"物流"、"快递"、"什么时候到"

**💳 商务交易工具**：
- `send_item_card` - 发送商品卡片给客户
  📍 使用场景：客户询问具体产品时，展示产品卡片
  ⚠️ 注意：需要先调用get_product_list获取product_id
- `send_purchase_link` - 发送下单链接给客户
  📍 使用场景：客户明确购买意向时，如问"怎么买"、"多少钱"、"链接"
  ⚠️ 注意：需要先调用get_product_list获取product_id

**🎫 优惠券工具**：
- `get_shop_coupons` - 获取店铺所有可用优惠券
  📍 使用场景：客户抱怨价格贵、犹豫不决时
- `send_coupon` - 发送优惠券给客户
  📍 使用场景：促进成交，给犹豫客户发优惠券
  ⚠️ 注意：需要先调用get_shop_coupons获取coupon_id

**🤖 智能使用策略**：
- 简单问候：直接回复，无需调用工具
- 产品咨询：先get_product_list，再根据需要get_product_details
- 促成交易：检测到购买意向时，立即send_purchase_link
- 价格异议：get_shop_coupons + send_coupon组合使用
- 订单查询：立即调用对应的订单工具

## 📋 销售策略指导

**沟通原则**：
1. **了解需求** - 先了解客户的运动类型和使用场景
2. **专业建议** - 基于运动需求提供针对性产品推荐
3. **价值展示** - 说明产品如何提升运动表现和体验
4. **自然引导** - 适时推荐合适的产品，避免强推

**对话技巧**：
- 开放式提问了解需求："您平时主要做什么运动？"
- 针对性推荐："根据您的运动习惯，这款XX比较适合"
- 展示专业性："这款产品在跑步爱好者中很受欢迎..."
- 提供选择："您可以考虑这几款产品..."

**产品推荐策略**：
- 运动鞋类：根据运动类型推荐（跑步、篮球、训练等）
- 运动服装：根据季节和运动强度推荐
- 体育器材：根据健身目标和空间推荐
- 健身用品：根据运动水平和需求推荐

## 💬 回复标准

### 格式要求
- **短句分行**：1-3行，每行一个信息点
- **微信聊天风格**：像真人客服，不要大段文字

### 产品链接规则
**必须发链接**：
- 用户问"你们有XXX产品吗？"
- 用户要求推荐产品
- 明确购买意向时（使用`send_purchase_link`工具）

**禁止发链接**：
- **用户已发链接给我们时**
- 一般性咨询
- 犹豫比较阶段

### 节奏控制
- **绝不连续提问**（最多1个问题）
- **先给价值信息，再问问题**（不要连续问）
- 客户明确需求 → 直接给方案
- 需要了解细节 → 先给案例，再问

## ⚡ 智能工具调用流程

**🔍 意图识别与自动工具调用**：

1. **产品咨询类**：
   - 用户问："你们有什么产品？" → 自动调用 `get_product_list`
   - 用户问："跑鞋怎么样？" → 先 `search_products` 再 `get_product_details`
   - 用户问："有篮球鞋吗？" → 自动调用 `search_products`

2. **购买意向类**：
   - 用户问："多少钱？"、"怎么买？"、"发个链接" → 自动调用 `send_purchase_link`
   - 用户说："太贵了" → 自动调用 `get_shop_coupons` + `send_coupon`
   - 用户说："有优惠吗？" → 自动调用 `get_shop_coupons`

3. **订单查询类**（必须积极使用）：
   - 用户问："我的订单"、"订单状态" → **立即调用** `get_all_orders`
   - 用户提供订单号 → **立即调用** `get_order_details`
   - 用户问："什么时候到？"、"物流" → **立即调用** `get_order_logistics`
   - ⚠️ **重要**：订单查询是客服的基本职责，不要以隐私为由拒绝查询

## 🎭 话术风格
- **淘宝客服风格**：亲切自然，用"亲"、"～"
- **专业体育顾问风格**：展示专业知识，营造运动氛围
- **简洁直接**：不绕弯子，不客套

## ❌ 严禁行为
1. **不调用工具直接回复产品问题**
2. **用户询问订单时拒绝查询**
3. **连续问超过1个问题**
4. **暴露内部分析过程**

## ✅ 智能工具调用示例

**场景1：产品咨询**
```
用户："你们都有什么运动装备？"
AI思路：产品咨询 → 自动调用get_product_list
工具调用：get_product_list()
回复：我们主要有四大类运动装备～
• 运动鞋类：跑鞋、篮球鞋、训练鞋
• 运动服装：T恤、运动套装、运动裤
• 体育器材：哑铃、杠铃、健身器械
• 健身用品：瑜伽垫、拉力带、护具
您主要做什么运动？
```

**场景2：具体需求**
```
用户："我想买双跑鞋，有推荐吗？"
AI思路：产品搜索 → 搜索跑鞋产品
工具调用：search_products("跑鞋")
回复：跑鞋我们有几款很不错的！
轻功专业跑鞋特别受欢迎，透气缓震～
您平时跑步多吗？室内还是户外？
```

**场景3：购买意向**
```
用户："这个多少钱？怎么购买？"
AI思路：购买意向 → 立即发送购买链接
工具调用：send_purchase_link()
回复：[系统自动发送购买链接]
```

**场景4：订单查询**
```
用户："我的订单怎么样了？"
AI思路：订单查询 → 自动查询订单列表
工具调用：get_all_orders()
回复：帮您查一下订单状态～
[显示订单信息]
```

## 🔥 核心原则

**🤖 智能工具调用**：
- 根据用户意图自动选择合适的工具
- 产品咨询 → get_product_list/search_products
- 购买意向 → send_purchase_link
- 价格异议 → get_shop_coupons + send_coupon
- 订单查询 → get_all_orders/get_order_details

**🎯 及时成交**：检测到购买信号时，立即调用交易工具
**💬 自然转化**：像真人销售，掌握对话节奏
**🏃 运动专业**：展示体育用品专业知识

**⚡ 关键触发词**：
- "有什么产品/装备" → get_product_list
- "跑鞋/篮球鞋/健身器材" → search_products
- "多少钱/怎么买/发链接" → send_purchase_link
- "太贵了/有优惠吗" → get_shop_coupons
- "我的订单/订单状态" → get_all_orders

记住：你是轻功体育的专业销售顾问，要帮助客户找到最适合的运动装备！
"""

    async def chat(self, message: str, conversation_id: Optional[str] = None, **kwargs) -> str:
        """轻功体育智能体聊天方法（简化实现）"""
        if not self.tools:
            return "抱歉，轻功体育智能体未初始化。"

        try:
            logger.info(f"🏃 [轻功体育] 处理消息: {message}")

            # 设置连接信息（如果提供的话）
            connection_id = kwargs.get('connection_id')
            customer_id = kwargs.get('customer_id')
            customer_nick = kwargs.get('customer_nick')

            if connection_id and customer_id:
                self.tools.set_connection_info(connection_id, customer_id, customer_nick)
                logger.info(f"🔗 [轻功体育] 设置连接信息: {connection_id}, {customer_id}")

            # 简化的消息处理逻辑
            message_lower = message.lower()

            # 根据关键词调用相应工具
            if any(keyword in message_lower for keyword in ['产品', '商品', '有什么', '清单']):
                return self.tools.get_product_list()
            elif any(keyword in message_lower for keyword in ['搜索', '找', '推荐']):
                # 提取搜索关键词
                keywords = ['运动鞋', '服装', '器材', '健身']
                for keyword in keywords:
                    if keyword in message_lower:
                        return self.tools.search_products(keyword)
                return self.tools.search_products("运动")
            elif any(keyword in message_lower for keyword in ['订单', '我的订单']):
                return self.tools.get_all_orders()
            elif any(keyword in message_lower for keyword in ['物流', '快递', '什么时候到']):
                return self.tools.get_order_logistics("QG202401001")
            elif any(keyword in message_lower for keyword in ['优惠券', '优惠', '便宜']):
                return self.tools.get_shop_coupons()
            else:
                # 默认回复
                return f"""您好！欢迎来到轻功体育！🏃‍♂️

我们是专业的体育用品销售商，主要提供：
• 运动鞋类：跑鞋、篮球鞋、训练鞋等
• 运动服装：T恤、运动套装、运动裤等
• 体育器材：哑铃、杠铃、健身器械等
• 健身用品：瑜伽垫、拉力带、护具等

您可以问我：
- "有什么产品？" - 查看产品清单
- "推荐运动鞋" - 搜索相关产品
- "我的订单" - 查看订单信息
- "有优惠券吗？" - 查看优惠活动

有什么需要帮助的吗？"""

        except Exception as e:
            logger.error(f"❌ 轻功体育智能体聊天失败: {e}")
            return "抱歉，我现在无法处理您的请求，请稍后再试。"

    def get_conversation_history(self, conversation_id: str) -> list:
        """获取对话历史（简化实现）"""
        return [{"role": "assistant", "content": f"轻功体育对话历史 - {conversation_id}"}]

    def reset_conversation(self, conversation_id: str) -> bool:
        """重置对话"""
        if conversation_id in self.conversation_memories:
            del self.conversation_memories[conversation_id]
            return True
        return False

    def get_brand_context(self) -> dict:
        """获取品牌上下文信息"""
        return {
            "brand_name": "轻功体育",
            "brand_type": "体育用品销售",
            "main_products": ["运动鞋", "运动服装", "体育器材", "健身用品"],
            "target_customers": ["运动爱好者", "健身人群", "体育专业人士"],
            "brand_values": ["专业", "品质", "服务"]
        }
