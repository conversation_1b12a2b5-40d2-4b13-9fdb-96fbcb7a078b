import { <PERSON>, Settings, Trash2, Wifi, WifiOff } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { type QianniuClient, useDeleteQianniuClient, useQianniuClients } from '../../services';
import { QianniuAccountList } from './QianniuAccountList';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface QianniuClientListProps {
  teamId?: string;
}

export function QianniuClientList({ teamId }: QianniuClientListProps) {
  const [selectedClient, setSelectedClient] = useState<QianniuClient | null>(null);

  // 获取千牛客户端列表
  const { data: clientsResponse, isLoading } = useQianniuClients({ teamId });
  const clients = clientsResponse?.data || [];

  // 删除千牛客户端
  const deleteClientMutation = useDeleteQianniuClient();

  const handleDeleteClient = (client: QianniuClient) => {
    const accountCount = client._count?.accounts ?? client.accounts?.length ?? 0;
    if (accountCount > 0) {
      toast.error('该客户端下还有千牛账号，无法删除');
      return;
    }

    if (confirm(`确定要删除千牛客户端 "${client.name}" 吗？`)) {
      deleteClientMutation.mutate(client.id, {
        onSuccess: () => {
          toast.success('千牛客户端删除成功');
        },
        onError: (error) => {
          toast.error(`删除失败: ${error.message}`);
        },
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-muted-foreground">加载中...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">千牛客户端管理</h2>
          <p className="text-muted-foreground">管理团队的千牛客户端连接和账号</p>
        </div>
      </div>

      {/* 客户端列表 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {clients?.map((client) => (
          <Card key={client.id} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Monitor className="w-5 h-5 text-muted-foreground" />
                  <CardTitle className="text-lg">{client.name}</CardTitle>
                </div>
                <div className="flex items-center space-x-2">
                  {/* 优先使用实时状态，回退到数据库状态 */}
                  {(client.realTimeStatus?.isOnline ?? client.isOnline) ? (
                    <Badge variant="default" className="bg-green-500">
                      <Wifi className="w-3 h-3 mr-1" />
                      {client.realTimeStatus ? '实时在线' : '在线'}
                    </Badge>
                  ) : (
                    <Badge variant="secondary">
                      <WifiOff className="w-3 h-3 mr-1" />
                      离线
                    </Badge>
                  )}
                  {/* 显示连接数量（如果有实时状态） */}
                  {client.realTimeStatus && client.realTimeStatus.connectionCount > 0 && (
                    <Badge variant="outline" className="text-xs">
                      {client.realTimeStatus.connectionCount} 连接
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* 基本信息 */}
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">所属团队: {client.team.name}</div>
                {client.description && <div className="text-sm text-muted-foreground">{client.description}</div>}
                <div className="text-sm text-muted-foreground">账号数量: {client._count?.accounts ?? client.accounts?.length ?? 0}</div>
                {/* 优先显示实时状态的最后在线时间 */}
                {(client.realTimeStatus?.lastSeen || client.lastOnlineAt) && (
                  <div className="text-sm text-muted-foreground">
                    最后在线: {new Date(client.realTimeStatus?.lastSeen || client.lastOnlineAt!).toLocaleString()}
                    {client.realTimeStatus && <span className="ml-1 text-green-600">(实时)</span>}
                  </div>
                )}
              </div>

              {/* 账号列表预览 */}
              {client.accounts.length > 0 && (
                <div className="space-y-2">
                  <div className="text-sm font-medium">登录账号:</div>
                  <div className="space-y-1">
                    {client.accounts.slice(0, 3).map((account) => (
                      <div key={account.id} className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {account.platformType}
                          </Badge>
                          <span className="truncate">{account.shopName || account.accountName}</span>
                        </div>
                        <Badge variant={account.isLoggedIn ? 'default' : 'secondary'} className="text-xs">
                          {account.isLoggedIn ? '已登录' : '未登录'}
                        </Badge>
                      </div>
                    ))}
                    {client.accounts.length > 3 && <div className="text-xs text-muted-foreground">还有 {client.accounts.length - 3} 个账号...</div>}
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex items-center justify-between pt-2 border-t">
                <Button variant="outline" size="sm" onClick={() => setSelectedClient(client)}>
                  <Settings className="w-4 h-4 mr-2" />
                  管理账号
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleDeleteClient(client)} disabled={(client._count?.accounts ?? client.accounts?.length ?? 0) > 0}>
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {clients?.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Monitor className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">暂无千牛客户端</h3>
            <p className="text-muted-foreground mb-4">添加第一个千牛客户端开始管理</p>
          </CardContent>
        </Card>
      )}

      {/* 账号管理对话框 */}
      {selectedClient && <QianniuAccountList client={selectedClient} onClose={() => setSelectedClient(null)} />}
    </div>
  );
}
