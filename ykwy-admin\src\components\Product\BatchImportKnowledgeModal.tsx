import { InboxOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, message, Modal, Radio, Upload } from 'antd';
import React, { useState } from 'react';

interface BatchImportKnowledgeModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (file: File | null, mode: string) => void;
}

const templateUrl = '/templates/knowledge-import-template.xlsx';

const BatchImportKnowledgeModal: React.FC<BatchImportKnowledgeModalProps> = ({
  open,
  onCancel,
  onOk,
}) => {
  const [mode, setMode] = useState('append-skip');
  const [file, setFile] = useState<File | null>(null);

  const handleUpload = (info: any) => {
    if (info.file.status === 'done' || info.file.status === 'success') {
      setFile(info.file.originFileObj || info.file);
      message.success('文件上传成功');
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
    }
  };

  const beforeUpload = (file: File) => {
    const isXlsx =
      file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.name.endsWith('.xlsx');
    if (!isXlsx) {
      message.error('仅支持 .xlsx 格式文件');
    }
    return isXlsx || Upload.LIST_IGNORE;
  };

  const handleOk = () => {
    onOk(file, mode);
  };

  return (
    <Modal
      open={open}
      title={
        <span className="font-medium" data-oid="ft6qi9a">
          批量导入商品知识
        </span>
      }
      onCancel={onCancel}
      onOk={handleOk}
      width={650}
      footer={[
        <Button
          key="cancel"
          onClick={onCancel}
          className="min-w-[72px]"
          data-oid="h0n-qqu"
        >
          取消
        </Button>,
        <Button
          key="ok"
          type="primary"
          disabled={!file}
          onClick={handleOk}
          className="min-w-[72px]"
          data-oid="wwpl:8t"
        >
          确定
        </Button>,
      ]}
      bodyStyle={{ padding: 0 }}
      style={{ top: 40 }}
      data-oid="s1oo6l_"
    >
      <div className="px-6 pt-6 pb-0" data-oid="6x.4qml">
        <Radio.Group
          value={mode}
          onChange={(e) => setMode(e.target.value)}
          className="mb-4 flex gap-8"
          data-oid="p3ujbn6"
        >
          <Radio value="append-skip" data-oid="nwmxkqg">
            增量导入（跳过同名）
          </Radio>
          <Radio value="append-cover" data-oid="_1ng135">
            增量导入（覆盖同名）
          </Radio>
          <Radio value="remove-cover" data-oid="69ez8dt">
            清除覆盖导入
          </Radio>
        </Radio.Group>
        {mode === 'append-skip' && (
          <Alert
            type="info"
            showIcon
            className="mb-4 bg-[#fffbe6] border border-[#ffe58f] text-[#d89614]"
            message={
              <span className="text-[#d89614]" data-oid="-l_ija1">
                增量导入（跳过同名）
              </span>
            }
            description={
              <span className="text-xs text-[#d89614]" data-oid=".r_7ukn">
                在已有的商品知识基础上，将表格内导入的商品知识做为增量添加
              </span>
            }
            data-oid="tubwjhd"
          />
        )}
        <Upload.Dragger
          name="file"
          accept=".xlsx"
          maxCount={1}
          beforeUpload={beforeUpload}
          showUploadList={file ? { showRemoveIcon: true } : false}
          onChange={handleUpload}
          onRemove={() => setFile(null)}
          className="bg-[#f8fafd] border border-dashed border-[#dbeafe] rounded-lg py-6 mb-4"
          data-oid="q8mwkbg"
        >
          <div
            className="flex flex-col items-center justify-center"
            data-oid="d:o.j48"
          >
            <InboxOutlined
              className="text-2xl text-[#1677ff] mb-2"
              data-oid="n.goj58"
            />

            <span className="text-[#1677ff] cursor-pointer" data-oid="j5q43vc">
              添加文件
            </span>
            <div className="text-xs text-[#888] mt-2" data-oid="gc4hv-a">
              点击选择文件，或将文件拖拽到这里上传。限一个文件，文件格式为.xlsx
            </div>
          </div>
        </Upload.Dragger>
        <div
          className="flex justify-between items-center mb-2"
          data-oid="j55mmxi"
        >
          <a
            href={templateUrl}
            download
            className="inline-block border border-[#1677ff] text-[#1677ff] px-3 py-1 rounded hover:bg-[#e6f0ff] text-sm"
            data-oid="i7p2iqd"
          >
            下载模板
          </a>
          <span className="text-xs text-[#888]" data-oid="rxc9aqi">
            提示：请在模板&quot;Sheet2&quot;中查看
          </span>
        </div>
        <div
          className="bg-[#f8fafd] rounded-lg p-4 text-xs text-[#666] mb-4"
          data-oid="a5xcgg1"
        >
          <ol className="list-decimal pl-4 space-y-1" data-oid="xf0xhzp">
            <li data-oid="871g5pg">
              若想使用导入图片，可使用&quot;&&&&quot;加图片链接，示例&&&http链接
            </li>
            <li data-oid="hza:0ko">单次导入不超过5000条</li>
            <li data-oid="l5gt:f4">支持.xlsx文件，文件名后缀需小写</li>
            <li data-oid="gpqk:.a">导入结果请在任务中心查看</li>
          </ol>
        </div>
      </div>
    </Modal>
  );
};

export default BatchImportKnowledgeModal;
