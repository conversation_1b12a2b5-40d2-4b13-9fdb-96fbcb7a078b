// 销售智能体API路由

import { Hono } from 'hono';

import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { salesAgentIntegrationService } from '../services/salesAgentIntegrationService';

// 创建路由
const app = new Hono();

// 生成推荐回复
app.get('/recommendations/conversation/:conversationId', async (c) => {
  try {
    const conversationId = c.req.param('conversationId');

    // 获取会话信息
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      select: {
        id: true,
        organizationId: true,
        messages: {
          orderBy: {
            createdAt: 'asc',
          },
          take: 10,
        },
      },
    });

    if (!conversation) {
      return c.json({ error: 'Conversation not found' }, 404);
    }

    logger.debug('生成推荐回复', {
      conversationId,
    });

    // 生成推荐回复
    const recommendations = await salesAgentIntegrationService.generateRecommendations(conversationId, conversation.messages);

    return c.json({ recommendations });
  } catch (error) {
    logger.error('生成推荐回复失败', { conversationId: c.req.param('conversationId') }, error instanceof Error ? error : new Error(String(error)));
    return c.json({ error: 'Failed to generate recommendations' }, 500);
  }
});

export default app;
