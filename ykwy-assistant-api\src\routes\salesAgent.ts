// 销售智能体API路由

import { Hono } from 'hono';

import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { salesAgentIntegrationService } from '../services/salesAgentIntegrationService';

// 创建路由
const app = new Hono();

// 生成推荐回复
app.get('/recommendations/conversation/:conversationId', async (c) => {
  try {
    const conversationId = c.req.param('conversationId');

    // 获取会话信息 - 检查UUID格式避免Prisma验证错误
    let conversations: any[] = [];
    let allMessages: any[] = [];
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(conversationId);

    if (isUUID) {
      // 如果是UUID格式，通过ID查找单个对话
      const conversation = await prisma.conversation.findUnique({
        where: { id: conversationId },
        select: {
          id: true,
          organizationId: true,
          qianniuAccountId: true,
          customerId: true,
          conversationCode: true,
          messages: {
            orderBy: {
              createdAt: 'asc',
            },
            take: 20, // 增加消息数量以获得更多上下文
          },
        },
      });
      if (conversation) {
        conversations = [conversation];
        allMessages = conversation.messages || [];
      }
    } else if (conversationId) {
      // 如果不是UUID格式，查找所有相同conversationCode的对话
      logger.info('查找所有相同conversationCode的对话', { conversationId });

      // 查找所有匹配的对话（而不是只找第一个）
      conversations = await prisma.conversation.findMany({
        where: {
          OR: [
            {
              conversationCode: conversationId, // 精确匹配
              status: { not: 'CLOSED' },
            },
            {
              conversationCode: { startsWith: `${conversationId}#` }, // 前缀匹配，支持千牛完整格式
              status: { not: 'CLOSED' },
            },
          ],
        },
        orderBy: [
          { lastActivity: 'desc' }, // 按最近活跃排序
          { createdAt: 'desc' }, // 最新创建的
        ],
        select: {
          id: true,
          organizationId: true,
          qianniuAccountId: true,
          customerId: true,
          conversationCode: true,
          createdAt: true,
          lastActivity: true,
          messages: {
            orderBy: {
              createdAt: 'asc',
            },
            take: 20, // 每个对话最多20条消息
          },
        },
      });

      // 合并所有对话的消息，按时间排序
      allMessages = conversations
        .flatMap((conv) =>
          (conv.messages || []).map((msg: any) => ({
            ...msg,
            conversationId: conv.id, // 标记消息来源对话
          })),
        )
        .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
        .slice(-30); // 只保留最近30条消息

      logger.info('找到相同conversationCode的对话', {
        conversationId,
        conversationCount: conversations.length,
        conversationIds: conversations.map((c) => c.id),
        totalMessages: allMessages.length,
      });

      // 兼容性处理：如果新字段中没找到，查找旧的 title 字段
      if (conversations.length === 0) {
        conversations = await prisma.conversation.findMany({
          where: {
            OR: [
              { title: conversationId }, // 精确匹配旧格式
              { title: { startsWith: `${conversationId}#` } }, // 前缀匹配旧格式
            ],
            status: { not: 'CLOSED' },
            conversationCode: null, // 确保是旧数据
          },
          orderBy: [
            { lastActivity: 'desc' }, // 按最近活跃排序
            { createdAt: 'desc' }, // 最新创建的
          ],
          select: {
            id: true,
            organizationId: true,
            qianniuAccountId: true,
            customerId: true,
            title: true,
            messages: {
              orderBy: {
                createdAt: 'asc',
              },
              take: 20,
            },
          },
        });

        // 如果找到旧格式对话，迁移到新字段并合并消息
        if (conversations.length > 0) {
          // 批量迁移所有找到的对话
          await Promise.all(
            conversations.map((conv) =>
              prisma.conversation.update({
                where: { id: conv.id },
                data: {
                  conversationCode: conversationId,
                  title: null, // 清理旧的 title 字段
                  lastActivity: new Date(),
                },
              }),
            ),
          );

          // 合并所有消息
          allMessages = conversations
            .flatMap((conv) =>
              (conv.messages || []).map((msg: any) => ({
                ...msg,
                conversationId: conv.id,
              })),
            )
            .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
            .slice(-30);

          logger.info('迁移并合并旧格式对话', {
            conversationId,
            migratedCount: conversations.length,
            totalMessages: allMessages.length,
          });
        }
      }
    }

    // 如果没有找到任何对话，直接返回默认推荐
    if (conversations.length === 0) {
      logger.info('没有找到任何对话，返回默认推荐', { conversationId });

      // 返回默认推荐，避免创建空对话
      const defaultRecommendations = await salesAgentIntegrationService.generateDefaultRecommendations();
      return c.json({
        recommendations: defaultRecommendations,
        note: '没有找到相关对话，返回默认推荐',
        conversationCount: 0,
      });
    }

    logger.debug('生成推荐回复（基于多个对话）', {
      conversationId,
      conversationCount: conversations.length,
      totalMessageCount: allMessages.length,
    });

    // 生成推荐回复，使用合并后的所有消息
    const recommendations = await salesAgentIntegrationService.generateRecommendations(conversationId, allMessages);

    return c.json({
      recommendations,
      conversationCount: conversations.length,
      messageCount: allMessages.length,
      note: conversations.length > 1 ? `基于${conversations.length}个相关对话生成推荐` : undefined,
    });
  } catch (error) {
    logger.error('生成推荐回复失败', { conversationId: c.req.param('conversationId') }, error instanceof Error ? error : new Error(String(error)));

    // 返回默认推荐而不是错误
    try {
      const defaultRecommendations = await salesAgentIntegrationService.generateDefaultRecommendations();
      return c.json({
        recommendations: defaultRecommendations,
        note: '生成失败，返回默认推荐',
      });
    } catch (defaultError) {
      return c.json({ error: 'Failed to generate recommendations' }, 500);
    }
  }
});

export default app;
