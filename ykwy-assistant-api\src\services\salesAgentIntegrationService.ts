// 销售智能体集成服务 - 后端

import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { notifyConversationListChanged, notifyMessageListChanged } from './eventService';
import { qianniuMessageService } from './qianniuMessageService';

interface SalesAgentChatRequest {
  message: string;
  conversation_id?: string;
  connection_id?: string;
  customer_id?: string;
  buyer_nick?: string;
}

interface SalesAgentChatResponse {
  response: string;
  session_id?: string;
}

interface Message {
  id: string;
  conversationId: string;
  content: string;
  senderType: 'CUSTOMER' | 'CUSTOMER_SERVICE' | 'SYSTEM' | 'AI';
  messageType: string;
  createdAt: Date;
  metadata?: unknown;
}

// 防抖状态接口
interface DebounceState {
  controller?: AbortController; // 当前正在进行的请求控制器
  pendingMessages: string[]; // 待处理的消息队列
  isProcessing: boolean; // 是否正在处理中
  lastMessageTime: number; // 最后一条消息的时间戳
}

class SalesAgentIntegrationService {
  // 防抖状态管理 - 每个会话一个状态
  private debounceStates = new Map<string, DebounceState>();

  /**
   * 处理新消息 - 自动回复（带智能防抖）
   */
  async handleNewMessage(message: Message): Promise<void> {
    try {
      const { conversationId, senderType, content } = message;

      logger.info('🤖 [SalesAgent] 收到新消息处理请求', {
        conversationId,
        senderType,
        content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
        messageId: message.id,
      });

      // 只处理客户消息
      if (senderType !== 'CUSTOMER') {
        logger.debug('🤖 [SalesAgent] 跳过非客户消息', { conversationId, senderType });
        return;
      }

      // 获取会话信息，包括千牛连接信息
      const conversation = await prisma.conversation.findUnique({
        where: { id: conversationId },
        select: {
          id: true,
          organizationId: true,
          customerId: true,
          autoReplyEnabled: true,
          qianniuAccount: {
            select: {
              client: {
                select: {
                  connectionId: true,
                },
              },
            },
          },
          customer: {
            select: {
              nickname: true,
            },
          },
        },
      });

      logger.debug('🤖 [SalesAgent] 会话信息获取成功', { conversation });

      if (!conversation) {
        logger.warn('🤖 [SalesAgent] 找不到会话', { conversationId });
        return;
      }

      // 检查是否启用自动回复
      const envAutoReply = process.env['SALES_AGENT_AUTO_REPLY'];
      const autoReplyEnabled = envAutoReply === 'true';

      if (!autoReplyEnabled) {
        logger.warn('🤖 [SalesAgent] 销售智能体自动回复未启用', {
          conversationId,
          organizationId: conversation.organizationId,
          envValue: envAutoReply,
        });
        return;
      }

      // 智能防抖处理
      await this.handleMessageWithDebounce(message, conversation);
    } catch (error) {
      logger.error('🤖 [SalesAgent] ❌ 销售智能体自动回复处理失败', { conversationId: message.conversationId }, error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 带防抖的消息处理
   */
  private async handleMessageWithDebounce(message: Message, conversation: any): Promise<void> {
    const { conversationId, content } = message;
    const currentTime = Date.now();

    // 获取或创建防抖状态
    let debounceState = this.debounceStates.get(conversationId);
    if (!debounceState) {
      debounceState = {
        pendingMessages: [],
        isProcessing: false,
        lastMessageTime: currentTime,
      };
      this.debounceStates.set(conversationId, debounceState);
    }

    // 如果有正在进行的请求，取消它
    if (debounceState.controller && !debounceState.controller.signal.aborted) {
      logger.info('🤖 [SalesAgent] 检测到新消息，取消正在进行的请求', { conversationId });
      debounceState.controller.abort();
    }

    // 将新消息添加到待处理队列
    debounceState.pendingMessages.push(content);
    debounceState.lastMessageTime = currentTime;

    logger.info('🤖 [SalesAgent] 消息加入防抖队列', {
      conversationId,
      queueLength: debounceState.pendingMessages.length,
      isProcessing: debounceState.isProcessing,
    });

    // 如果当前没有在处理，启动处理
    if (!debounceState.isProcessing) {
      debounceState.isProcessing = true;
      await this.processDebounceQueue(conversationId, conversation);
    }
  }

  /**
   * 处理防抖队列
   */
  private async processDebounceQueue(
    conversationId: string,
    conversation: {
      organizationId: string;
      customerId?: string;
      customer?: { nickname?: string };
      qianniuAccount?: { client?: { connectionId?: string } };
    },
  ): Promise<void> {
    const debounceState = this.debounceStates.get(conversationId);
    if (!debounceState) return;

    try {
      // 等待一小段时间，确保没有更多消息到来
      await new Promise((resolve) => setTimeout(resolve, 500));

      // 检查是否有待处理的消息
      if (debounceState.pendingMessages.length === 0) {
        debounceState.isProcessing = false;
        return;
      }

      // 合并所有待处理的消息
      const combinedMessage = debounceState.pendingMessages.join('\n');
      debounceState.pendingMessages = []; // 清空队列

      logger.info('🤖 [SalesAgent] 开始处理合并消息', {
        conversationId,
        messageCount: debounceState.pendingMessages.length,
        combinedLength: combinedMessage.length,
        preview: combinedMessage.substring(0, 200) + (combinedMessage.length > 200 ? '...' : ''),
      });

      // 创建新的AbortController
      debounceState.controller = new AbortController();

      // 调用销售智能体API生成回复
      const connectionId = conversation.qianniuAccount?.client?.connectionId;
      const reply = await this.generateReply(combinedMessage, conversationId, connectionId, conversation.customerId, conversation.customer?.nickname, debounceState.controller);

      // 检查请求是否被取消
      if (debounceState.controller.signal.aborted) {
        logger.info('🤖 [SalesAgent] 请求被取消，跳过后续处理', { conversationId });
        debounceState.isProcessing = false;
        return;
      }

      if (reply) {
        logger.info('🤖 [SalesAgent] 回复生成成功', {
          conversationId,
          replyLength: reply.length,
          replyPreview: reply.substring(0, 100) + (reply.length > 100 ? '...' : ''),
        });

        // 保存AI回复消息
        await this.saveAIMessage(conversationId, reply);

        // 发送消息到千牛客户端
        await this.sendToQianniu(conversationId, reply);

        // 通知前端更新
        notifyMessageListChanged(conversation.organizationId, conversationId);
        notifyConversationListChanged(conversation.organizationId);

        logger.info('🤖 [SalesAgent] ✅ 销售智能体自动回复处理完成', { conversationId });
      } else {
        logger.warn('🤖 [SalesAgent] ❌ 回复生成失败，没有返回内容', { conversationId });
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        logger.info('🤖 [SalesAgent] 请求被主动取消', { conversationId });
      } else {
        logger.error('🤖 [SalesAgent] 处理防抖队列失败', { conversationId }, error instanceof Error ? error : new Error(String(error)));
      }
    } finally {
      debounceState.isProcessing = false;

      // 如果在处理过程中又有新消息到来，继续处理
      if (debounceState.pendingMessages.length > 0) {
        logger.info('🤖 [SalesAgent] 检测到新的待处理消息，继续处理', {
          conversationId,
          queueLength: debounceState.pendingMessages.length,
        });
        debounceState.isProcessing = true;
        setImmediate(() => this.processDebounceQueue(conversationId, conversation));
      }
    }
  }

  /**
   * 生成回复
   */
  private async generateReply(message: string, conversationId: string, connectionId?: string, customerId?: string, buyerNick?: string, controller?: AbortController): Promise<string | null> {
    try {
      logger.info('🤖 [SalesAgent] 开始生成回复', {
        conversationId,
        connectionId,
        customerId,
        messageLength: message.length,
        messagePreview: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
      });

      // 准备请求
      const request: SalesAgentChatRequest = {
        message,
        conversation_id: conversationId,
        connection_id: connectionId,
        customer_id: customerId,
        buyer_nick: buyerNick,
      };

      logger.debug('🤖 [SalesAgent] 准备调用API', {
        conversationId,
        requestPayload: request,
      });

      // 调用销售智能体API
      const response = await this.callSalesAgentAPI(request, controller);

      if (response) {
        logger.info('🤖 [SalesAgent] API调用成功', {
          conversationId,
          responseLength: response.response?.length || 0,
          sessionId: response.session_id,
        });
        return response.response;
      }

      logger.warn('🤖 [SalesAgent] API返回空响应，使用回退消息', { conversationId });
      return '抱歉，我暂时无法回答您的问题，请稍后再试或联系人工客服。';
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        logger.info('🤖 [SalesAgent] 生成回复被取消', { conversationId });
        throw error; // 重新抛出取消错误
      }
      logger.error('🤖 [SalesAgent] 生成回复失败', { conversationId }, error instanceof Error ? error : new Error(String(error)));
      return '抱歉，我暂时无法回答您的问题，请稍后再试或联系人工客服。';
    }
  }

  /**
   * 调用销售智能体API
   */
  private async callSalesAgentAPI(request: SalesAgentChatRequest, controller?: AbortController): Promise<SalesAgentChatResponse | null> {
    try {
      const salesAgentUrl = process.env['SALES_AGENT_URL'] || 'http://localhost:8000';
      const endpoint = '/chat';
      const fullUrl = `${salesAgentUrl}${endpoint}`;

      logger.info('🤖 [SalesAgent] 调用销售智能体API', {
        url: fullUrl,
        conversationId: request.conversation_id,
        messageLength: request.message?.length || 0,
      });

      logger.debug('🤖 [SalesAgent] API请求详情', {
        url: fullUrl,
        requestPayload: request,
      });

      const startTime = Date.now();

      // 使用传入的controller或创建新的用于超时控制
      const abortController = controller || new AbortController();
      const timeoutId = setTimeout(() => abortController.abort(), 1000 * 60 * 3);

      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: abortController.signal,
      });

      // 清除超时定时器
      clearTimeout(timeoutId);

      const duration = Date.now() - startTime;
      logger.info('🤖 [SalesAgent] API响应接收', {
        conversationId: request.conversation_id,
        status: response.status,
        statusText: response.statusText,
        duration: `${duration}ms`,
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('🤖 [SalesAgent] ❌ API响应错误', {
          conversationId: request.conversation_id,
          status: response.status,
          statusText: response.statusText,
          errorText: errorText.substring(0, 500),
          url: fullUrl,
        });
        return null;
      }

      const data = await response.json();
      logger.info('🤖 [SalesAgent] ✅ API调用成功', {
        conversationId: request.conversation_id,
        responseLength: data.response?.length || 0,
        sessionId: data.session_id,
        duration: `${duration}ms`,
      });

      return data as SalesAgentChatResponse;
    } catch (error) {
      // 特殊处理超时错误
      if (error instanceof Error && error.name === 'AbortError') {
        logger.error(
          '🤖 [SalesAgent] ❌ API调用超时',
          {
            conversationId: request.conversation_id,
            url: process.env['SALES_AGENT_URL'] || 'http://localhost:8000',
            timeout: '30秒',
          },
          error,
        );
      } else {
        logger.error(
          '🤖 [SalesAgent] ❌ API调用失败',
          {
            conversationId: request.conversation_id,
            url: process.env['SALES_AGENT_URL'] || 'http://localhost:8000',
          },
          error instanceof Error ? error : new Error(String(error)),
        );
      }
      return null;
    }
  }

  /**
   * 保存AI消息
   */
  private async saveAIMessage(conversationId: string, content: string): Promise<void> {
    try {
      // 获取下一个序列号
      const lastMessage = await prisma.message.findFirst({
        where: { conversationId },
        orderBy: { sequenceNumber: 'desc' },
        select: { sequenceNumber: true },
      });

      const sequenceNumber = (lastMessage?.sequenceNumber || 0) + 1;

      await prisma.message.create({
        data: {
          conversationId,
          content,
          senderType: 'AI',
          messageType: 'TEXT',
          sequenceNumber,
        },
      });
    } catch (error) {
      logger.error('保存AI消息失败', { conversationId }, error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 发送消息到千牛
   */
  private async sendToQianniu(conversationId: string, content: string): Promise<void> {
    try {
      // 调用千牛消息服务发送消息
      const message = {
        content,
        messageType: 'TEXT',
        senderType: 'AI',
      };
      await qianniuMessageService.sendMessageToQianniu(conversationId, message);
    } catch (error) {
      logger.error('发送消息到千牛失败', { conversationId }, error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 生成推荐回复
   */
  async generateRecommendations(conversationId: string, messages: Message[]): Promise<string[]> {
    try {
      // 获取最近的客户消息
      const customerMessages = messages.filter((msg) => msg.senderType === 'CUSTOMER').sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      if (customerMessages.length === 0) {
        return this.getDefaultRecommendations();
      }

      const latestMessage = customerMessages[0]?.content;
      if (!latestMessage) {
        return this.getDefaultRecommendations();
      }

      logger.debug('调用API生成推荐', { conversationId });

      // 并发调用三次chat端点，每次生成一个独立的推荐
      const recommendationPromises = Array.from({ length: 3 }, async (_, index) => {
        try {
          const request: SalesAgentChatRequest = {
            message: latestMessage,
            conversation_id: `${conversationId}_rec_${index}`, // 为每个推荐使用不同的会话ID
          };

          const apiResponse = await this.callSalesAgentAPI(request);
          const response = apiResponse?.response?.trim() || '';

          logger.debug(`推荐${index + 1}API响应`, { conversationId, response });

          return response;
        } catch (error) {
          logger.warn(`生成推荐${index + 1}失败`, { conversationId }, error instanceof Error ? error : new Error(String(error)));
          return null;
        }
      });

      // 等待所有推荐生成完成
      const results = await Promise.all(recommendationPromises);

      // 过滤掉失败的推荐，保留有效的推荐
      const validRecommendations = results.filter((rec: string | null): rec is string => rec !== null && rec.length > 0);

      // 如果没有足够的推荐，添加默认推荐
      const recommendations = [...validRecommendations];
      while (recommendations.length < 3) {
        const defaultRecs = this.getDefaultRecommendations();
        const neededCount = 3 - recommendations.length;
        recommendations.push(...defaultRecs.slice(0, neededCount));
      }

      // 只取前3个推荐
      const finalRecommendations = recommendations.slice(0, 3);

      logger.info('生成推荐完成', {
        conversationId,
        validCount: validRecommendations.length,
        totalCount: finalRecommendations.length,
        recommendations: finalRecommendations,
      });

      return finalRecommendations;
    } catch (error) {
      logger.error('生成推荐失败', { conversationId }, error instanceof Error ? error : new Error(String(error)));
      return this.getDefaultRecommendations();
    }
  }

  /**
   * 获取默认推荐
   */
  private getDefaultRecommendations(): string[] {
    return [
      '感谢您的咨询，我们会尽快为您处理。如有其他问题，请随时联系我们。',
      '您好，很高兴为您服务。请问还有什么我可以帮助您的吗？',
      '我们的专业团队会为您提供最优质的服务，请问您对我们的产品有什么具体的问题吗？',
    ];
  }
}

export const salesAgentIntegrationService = new SalesAgentIntegrationService();
