// 对话相关 React Query Hooks

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { useOrganization } from '../../hooks/useOrganization';
import { authClient } from '../../lib/auth-client';
import { queryKeys } from '../../lib/query-keys';
import { conversationQueryOptions, conversationsQueryOptions, createConversationMutation, updateConversationMutation } from '../api/conversations';
import type { ConversationFilters } from '../types';

// 获取对话列表
export const useConversations = (filters: ConversationFilters = {}, page = 1, limit = 20) => {
  const { data: session } = authClient.useSession();
  const { organizationId } = useOrganization();
  const user = session?.user as { id: string } | undefined;

  console.log('🔍 [useConversations] 用户信息:', { user, organizationId });

  return useQuery({
    ...conversationsQueryOptions({ ...filters, organizationId }, page, limit),
    enabled: !!organizationId, // 只有 organizationId 存在时才启用查询
    retry: (failureCount, error) => {
      // 如果是 organizationId 缺失错误，不要重试
      if (error?.message === 'organizationId is required') {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// 获取单个对话详情
export const useConversation = (id?: string) => {
  return useQuery({
    ...conversationQueryOptions(id!),
    enabled: !!id, // 只有 ID 存在时才启用查询
  });
};

// 创建对话
export const useCreateConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...createConversationMutation,
    onSuccess: () => {
      // 刷新对话列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversations(),
      });
    },
  });
};

// 更新对话
export const useUpdateConversation = (conversationId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    ...updateConversationMutation(conversationId),
    onSuccess: (data) => {
      // 更新缓存中的对话详情
      queryClient.setQueryData(queryKeys.conversation(conversationId), data);
      // 刷新对话列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversations(),
      });
    },
  });
};
