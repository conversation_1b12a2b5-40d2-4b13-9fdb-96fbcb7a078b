import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 知识库分类基础数据
const knowledgeCategoriesData = [
  {
    name: '开始语',
    code: '1',
    level: 1,
    sortOrder: 1,
    description: '客服接待的开场话术',
    isActive: true,
  },
  {
    name: '图片识别',
    code: '2',
    level: 1,
    sortOrder: 2,
    description: '通过图片识别商品相关问题',
    isActive: true,
  },
  {
    name: '无法识别',
    code: '3',
    level: 1,
    sortOrder: 3,
    description: '无法识别的问题类型',
    isActive: true,
  },
  {
    name: '聊天互动',
    code: '4',
    level: 1,
    sortOrder: 4,
    description: '日常聊天互动话术',
    isActive: true,
  },
  {
    name: '商品问题',
    code: '5',
    level: 1,
    sortOrder: 5,
    description: '商品相关的咨询问题',
    isActive: true,
  },
  {
    name: '订单相关',
    code: '6',
    level: 1,
    sortOrder: 6,
    description: '订单查询、修改等相关问题',
    isActive: true,
  },
  {
    name: '活动优惠',
    code: '7',
    level: 1,
    sortOrder: 7,
    description: '促销活动和优惠信息',
    isActive: true,
  },
  {
    name: '下单付款',
    code: '8',
    level: 1,
    sortOrder: 8,
    description: '下单流程和付款相关问题',
    isActive: true,
  },
  {
    name: '物流问题',
    code: '9',
    level: 1,
    sortOrder: 9,
    description: '配送、物流相关问题',
    isActive: true,
  },
  {
    name: '售后问题',
    code: '10',
    level: 1,
    sortOrder: 10,
    description: '售后服务相关问题',
    isActive: true,
  },
  {
    name: '发票问题',
    code: '11',
    level: 1,
    sortOrder: 11,
    description: '发票相关问题',
    isActive: true,
  },
  {
    name: '安装问题',
    code: '12',
    level: 1,
    sortOrder: 12,
    description: '商品安装相关问题',
    isActive: true,
  },
  {
    name: '活动问题',
    code: '13',
    level: 1,
    sortOrder: 13,
    description: '活动相关咨询问题',
    isActive: true,
  },
  {
    name: '其他',
    code: '14',
    level: 1,
    sortOrder: 14,
    description: '其他未分类问题',
    isActive: true,
  },
];

export async function seedKnowledgeCategories(prismaClient?: PrismaClient) {
  const client = prismaClient || prisma;
  console.log('🌱 开始播种知识库分类数据...');

  try {
    // 清除现有的知识库分类数据
    await client.knowledgeCategory.deleteMany({});
    console.log('🧹 清理现有知识库分类数据完成');

    // 插入基础分类数据
    for (const categoryData of knowledgeCategoriesData) {
      await client.knowledgeCategory.create({
        data: categoryData,
      });
    }

    console.log(`✅ 成功插入 ${knowledgeCategoriesData.length} 条知识库分类记录`);
    console.log('🎉 知识库分类种子数据播种完成！');

    // 显示插入的数据
    const categories = await client.knowledgeCategory.findMany({
      orderBy: { sortOrder: 'asc' },
    });

    console.log('\n📋 已创建的知识库分类：');
    categories.forEach((category: any) => {
      console.log(`  ${category.code}: ${category.name} (${category.description})`);
    });

  } catch (error) {
    console.error('❌ 播种知识库分类数据时出错:', error);
    throw error;
  }
}

// 如果直接运行此文件，则执行种子数据播种
if (require.main === module) {
  seedKnowledgeCategories()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
} 