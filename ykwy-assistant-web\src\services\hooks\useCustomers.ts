// 客户相关 React Query Hooks

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { queryKeys } from '../../lib/query-keys';
import { createCustomerMutation, customerQueryOptions, customersQueryOptions, updateCustomerMutation } from '../api/customers';
import type { CustomerFilters } from '../types';

// 获取客户详情
export const useCustomer = (customerId?: string) => {
  return useQuery({
    ...customerQueryOptions(customerId || ''),
    enabled: !!customerId,
  });
};

// 获取客户列表
export const useCustomers = (filters: CustomerFilters = {}, page = 1, limit = 20) => {
  return useQuery({
    ...customersQueryOptions(filters, page, limit),
  });
};

// 创建客户
export const useCreateCustomer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...createCustomerMutation,
    onSuccess: () => {
      // 刷新客户列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers(),
      });
    },
  });
};

// 更新客户
export const useUpdateCustomer = (customerId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    ...updateCustomerMutation(customerId),
    onSuccess: (data) => {
      // 更新缓存中的客户详情
      queryClient.setQueryData(queryKeys.customer(customerId), data);
      // 刷新客户列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.customers(),
      });
    },
  });
};
