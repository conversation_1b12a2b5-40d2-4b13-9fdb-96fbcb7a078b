# Seed 文件结构说明

## 概述

为了更好地管理和维护种子数据，我们将原本的 `seed.ts` 文件拆分成了三个独立的文件，每个文件负责不同类型的数据。

## 文件结构

### 1. `prisma/seed.ts` - 主要种子数据
负责创建核心业务数据：
- 尺码表类型 (SizeChartType)
- 尺码表 (SizeChart)
- 商品数据 (Product)
- 商品标识符 (ProductIdentifier)
- 商品-尺码表绑定关系 (ProductSizeChartBinding)

### 2. `prisma/seed-questions.ts` - 问答知识库数据
负责创建客服问答相关数据：
- 问答记录 (QuestionAndAnswer)
- 包含20条常见客服问答
- 涵盖快递、退换货、商品咨询等场景

### 3. `prisma/seed-temp-products.ts` - 临时商品数据
负责创建临时商品数据：
- 临时商品记录 (TempProduct)
- 包含10条QINKUNG轻功品牌商品数据
- 来源于CSV文件的前10条记录

## 可用脚本

### 单独运行
```bash
# 运行主要种子数据（尺码表、商品等）
bun run db:seed

# 运行问答知识库数据
bun run db:seed:questions

# 运行临时商品数据
bun run db:seed:temp-products
```

### 完整运行
```bash
# 运行所有种子数据
bun run db:seed:all
```

## 数据依赖关系

- `seed.ts` - 独立运行，创建核心数据结构
- `seed-questions.ts` - 独立运行，只依赖问答表结构
- `seed-temp-products.ts` - 独立运行，只依赖临时商品表结构

每个文件都会在运行前清理相关的旧数据，确保数据的一致性。

## 优势

1. **模块化管理**: 每个文件职责单一，便于维护
2. **灵活部署**: 可以根据需要单独导入特定类型的数据
3. **开发效率**: 开发时可以只导入需要的数据，减少等待时间
4. **代码可读性**: 代码结构更清晰，更容易理解和修改

## 注意事项

- 运行 `db:seed:all` 会按顺序执行所有seed文件
- 每个seed文件都有独立的数据清理逻辑
- 如果只需要部分数据，建议使用对应的单独脚本 