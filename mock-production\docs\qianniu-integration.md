# 千牛API集成文档

## 概述

销售智能体已完整集成千牛API，支持真实的订单查询、物流跟踪、客户管理等功能。当千牛API不可用时，系统会自动降级到模拟数据，确保服务的高可用性。

## 架构设计

```
Chat端点 → SalesAgent → SalesTools → QianniuClient → 千牛API
                                          ↓
                                     模拟数据(降级)
```

## 核心组件

### 1. QianniuClient (千牛API客户端)

位置：`src/sales_agent/services/qianniu_client.py`

**主要功能：**
- 封装所有千牛API接口
- 统一错误处理和重试机制
- 智能降级到模拟数据
- 详细的日志记录

**核心接口：**

#### 订单管理
```python
# 获取订单列表
await qianniu_client.get_orders(connection_id, customer_id)

# 获取订单详情
await qianniu_client.get_order_detail(connection_id, customer_id, order_id)

# 查询近期订单
await qianniu_client.query_recent_orders(connection_id, customer_id)

# 查询历史订单
await qianniu_client.query_history_orders(connection_id, customer_id)
```

#### 物流管理
```python
# 获取物流信息
await qianniu_client.get_logistics_info(connection_id, order_id)
```

#### 客户管理
```python
# 获取客户信息
await qianniu_client.get_customer_info(connection_id, customer_id)

# 获取客户列表
await qianniu_client.get_customers(connection_id)
```

#### 商品管理
```python
# 搜索店铺商品
await qianniu_client.get_shop_items(connection_id, customer_id)

# 发送商品卡片
await qianniu_client.send_item_card(connection_id, customer_id, item_ids)
```

#### 优惠券管理
```python
# 获取店铺优惠券
await qianniu_client.get_shop_coupons(connection_id)

# 发送优惠券
await qianniu_client.send_coupon(connection_id, customer_id, name, activity_id)
```

### 2. QianniuConfig (配置管理)

位置：`src/sales_agent/config/qianniu_config.py`

**配置项：**
- 基础配置：API地址、超时时间、重试次数
- 认证配置：API密钥和密钥
- 功能开关：真实API开关、降级开关、缓存开关
- 日志配置：日志级别、请求/响应记录
- 端点配置：所有API端点映射

### 3. SalesTools (销售工具集成)

位置：`src/sales_agent/tools/sales_tools.py`

**订单管理工具：**
- `get_all_orders()` - 获取订单列表
- `get_order_details()` - 获取订单详情
- `get_order_logistics()` - 获取物流状态

## 配置说明

### 环境变量配置

复制 `.env.example` 为 `.env` 并配置以下关键参数：

```bash
# 千牛API基础配置
QIANNIU_API_BASE_URL=http://localhost:3002
QIANNIU_API_TIMEOUT=30
QIANNIU_API_RETRY_TIMES=3

# 千牛API功能开关
QIANNIU_ENABLE_REAL_API=true
QIANNIU_ENABLE_FALLBACK=true

# 千牛API日志配置
QIANNIU_LOG_REQUESTS=true
QIANNIU_LOG_RESPONSES=false
```

### 重要配置说明

- `QIANNIU_ENABLE_REAL_API`: 是否启用真实API调用
- `QIANNIU_ENABLE_FALLBACK`: API失败时是否降级到模拟数据
- `QIANNIU_API_BASE_URL`: 千牛API的基础地址
- `QIANNIU_API_TIMEOUT`: API请求超时时间（秒）
- `QIANNIU_API_RETRY_TIMES`: 失败重试次数

## 使用方式

### 1. 基本聊天（带千牛参数）

```bash
POST /chat
Content-Type: application/json

{
  "message": "我想查看我的订单",
  "session_id": "session_123",
  "connection_id": "qianniu_conn_456",
  "customer_id": "customer_789"
}
```

### 2. 订单查询示例

```bash
POST /chat
Content-Type: application/json

{
  "message": "订单WY202500000001的物流状态怎么样？",
  "connection_id": "qianniu_conn_456",
  "customer_id": "customer_789"
}
```

## 降级机制

系统具备完善的降级机制：

1. **有连接信息时**：
   - 首先尝试调用千牛API
   - API失败时自动降级到模拟数据
   - 对用户透明，保持一致体验

2. **无连接信息时**：
   - 直接使用模拟数据
   - 保证基本功能可用

3. **错误处理**：
   - 网络异常自动重试
   - 超时自动降级
   - 详细错误日志记录

## 测试验证

使用提供的测试文件验证集成：

```bash
# 测试文件位置
ykwy-assistant-api/test/sales-agent-with-qianniu.rest
```

**测试场景包括：**
- 基本聊天功能
- 订单查询功能
- 物流查询功能
- 降级机制测试
- 错误处理测试

## 监控和日志

### 日志级别
- `INFO`: 基本请求信息
- `DEBUG`: 详细请求/响应数据
- `ERROR`: 错误和异常信息

### 关键日志标识
- `🌐 [千牛API]`: API请求日志
- `✅`: 成功操作
- `❌`: 失败操作
- `⚠️`: 警告信息
- `🔄`: 重试操作

## 故障排查

### 常见问题

1. **API连接失败**
   - 检查 `QIANNIU_API_BASE_URL` 配置
   - 确认千牛API服务是否正常运行
   - 查看网络连接状态

2. **认证失败**
   - 检查 `QIANNIU_API_KEY` 和 `QIANNIU_API_SECRET`
   - 确认API密钥是否有效

3. **降级不生效**
   - 检查 `QIANNIU_ENABLE_FALLBACK` 是否为 true
   - 确认模拟数据是否正常加载

### 调试建议

1. 启用详细日志：
   ```bash
   QIANNIU_LOG_REQUESTS=true
   QIANNIU_LOG_RESPONSES=true
   ```

2. 测试API连接：
   ```bash
   curl -X GET "http://localhost:3002/health"
   ```

3. 检查配置：
   ```python
   from src.sales_agent.config.qianniu_config import qianniu_config
   print(qianniu_config.to_dict())
   ```

## 扩展开发

### 添加新的API接口

1. 在 `QianniuClient` 中添加新方法
2. 在 `QianniuConfig` 中添加端点配置
3. 在 `SalesTools` 中集成新功能
4. 添加相应的测试用例

### 自定义配置

可以通过继承 `QianniuConfig` 类来实现自定义配置：

```python
class CustomQianniuConfig(QianniuConfig):
    def __init__(self):
        super().__init__()
        # 自定义配置
        self.custom_setting = "value"
```

## 安全注意事项

1. **API密钥安全**：
   - 不要在代码中硬编码API密钥
   - 使用环境变量管理敏感信息
   - 定期轮换API密钥

2. **网络安全**：
   - 使用HTTPS连接
   - 配置适当的超时时间
   - 实施请求频率限制

3. **数据安全**：
   - 不要在日志中记录敏感客户信息
   - 实施数据脱敏处理
   - 遵循数据保护法规
