import type { TempProduct } from '@prisma/client';

/**
 * 临时商品 DTO - 基本信息
 */
export type TempProductDto = TempProduct;

/**
 * 临时商品列表 DTO
 */
export interface TempProductListDto {
  /** 临时商品列表 */
  items: TempProductDto[];
  /** 总数 */
  total: number;
}

/**
 * 批量删除结果 DTO
 */
export interface BulkDeleteResultDto {
  /** 实际删除的数量 */
  count: number;
}

/**
 * 批量导入结果 DTO
 */
export interface BatchImportResultDto {
  /** 成功处理的数量 */
  successCount: number;
  /** 失败的数量 */
  failCount: number;
  /** 新增的数量 */
  newCount: number;
  /** 更新的数量 */
  updatedCount: number;
  /** 错误信息列表 */
  errors?: string[];
}

/**
 * API 通用响应格式
 */
export interface ApiResponse<T> {
  /** 是否成功 */
  success: boolean;
  /** 返回数据 */
  data?: T;
  /** 错误信息 */
  error?: string;
  /** 分页信息 */
  pagination?: {
    total: number;
    skip: number;
    take?: number;
  };
}
