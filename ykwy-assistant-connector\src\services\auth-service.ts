import HttpClient from '../utils/http-client.js';

/**
 * 构建API URL，智能处理端口号
 * @param apiHost API主机地址
 * @param apiPort API端口号
 * @param path API路径
 * @returns 完整的API URL
 */
function buildApiUrl(apiHost: string, apiPort: number, path: string): string {
  let url = apiHost;
  if (!url.includes('://')) {
    url = `http://${url}`;
  }

  // 检查是否需要添加端口号
  const isHttps = url.startsWith('https://');
  const defaultPort = isHttps ? 443 : 80;

  if (apiPort && apiPort !== defaultPort) {
    url = `${url}:${apiPort}`;
  }

  return `${url}${path}`;
}

interface AuthResponse {
  success: boolean;
  data?: {
    accessToken?: string;
    user?: {
      name: string;
      email: string;
      organizationId?: string;
      teamId?: string;
    };
  };
  message?: string;
}

interface Config {
  apiHost: string;
  apiPort: number;
  email: string;
  password: string;
}

/**
 * 认证服务
 * 负责用户登录和token管理
 */
class AuthService {
  private config: Config;
  private authToken: string | null;

  constructor(config: Config) {
    this.config = config;
    this.authToken = null;
  }

  /**
   * 用户登录
   * @returns {Promise<boolean>} 登录是否成功
   */
  async login() {
    console.log('🔐 正在登录...');

    const url = buildApiUrl(this.config.apiHost, this.config.apiPort, '/api/auth/login');
    console.log('🔗 请求URL:', url);
    const data = {
      email: this.config.email,
      password: this.config.password,
    };

    try {
      const response = (await HttpClient.post(url, data)) as AuthResponse;

      if (response.success && response.data && response.data.accessToken) {
        this.authToken = response.data.accessToken;
        console.log('✅ 登录成功');
        console.log(`👤 用户: ${response.data.user.name} (${response.data.user.email})`);
        console.log(`🏢 组织: ${response.data.user.organizationId || '未分配'}`);
        console.log(`👥 团队: ${response.data.user.teamId || '未分配'}`);
        return true;
      } else {
        throw new Error('登录响应格式错误或缺少accessToken');
      }
    } catch (error) {
      console.error('❌ 登录失败:', (error as Error).message);
      console.log('💡 请检查：');
      console.log('   - 邮箱和密码是否正确');
      console.log('   - API服务器是否正常运行');
      console.log('   - 网络连接是否正常');
      throw error;
    }
  }

  /**
   * 获取认证token
   * @returns {string|null} 认证token
   */
  getAuthToken() {
    return this.authToken;
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return !!this.authToken;
  }

  /**
   * 登出
   */
  logout() {
    this.authToken = null;
  }

  /**
   * 获取认证头
   * @returns {Object} 认证头对象
   */
  getAuthHeaders() {
    if (!this.authToken) {
      throw new Error('未登录，无法获取认证头');
    }

    return {
      Authorization: `Bearer ${this.authToken}`,
    };
  }
}

export default AuthService;
