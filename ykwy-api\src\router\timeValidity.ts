import { Hono } from 'hono';

import { TimeValidityController } from '../controller/timeValidity';

const router = new Hono();
const timeValidity = new TimeValidityController();

// ================================
// 时效配置相关 API 路由
// ================================

// 创建时效配置
// POST /api/v1/time-validity
router.post('/time-validity', timeValidity.create);

// 获取时效配置列表（支持分页、过滤、排序）
// GET /api/v1/time-validity
router.get('/time-validity', timeValidity.findMany);

// 根据ID获取时效配置详情
// GET /api/v1/time-validity/:id
router.get('/time-validity/:id', timeValidity.findById);

// 更新时效配置
// PUT /api/v1/time-validity/:id
router.put('/time-validity/:id', timeValidity.update);

// 删除时效配置
// DELETE /api/v1/time-validity/:id
router.delete('/time-validity/:id', timeValidity.delete);

// 检查时效配置是否当前有效
// GET /api/v1/time-validity/:id/check
router.get('/time-validity/:id/check', timeValidity.checkValidity);

// 批量删除时效配置
// POST /api/v1/time-validity/bulk-delete
router.post('/time-validity/bulk-delete', timeValidity.bulkDelete);

export default router;
