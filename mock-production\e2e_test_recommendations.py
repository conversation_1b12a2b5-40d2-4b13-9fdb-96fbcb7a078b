#!/usr/bin/env python3
"""
推荐接口端到端测试
完整测试 /recommendations 端点的各种场景
"""
import asyncio
import aiohttp
import json
import time
import statistics
from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class TestResult:
    """测试结果"""
    name: str
    success: bool
    response_time: float
    status_code: int
    recommendations_count: int
    error_message: str = ""
    recommendations: List[str] = None


class RecommendationsE2ETest:
    """推荐接口端到端测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results: List[TestResult] = []
    
    async def test_basic_functionality(self, session: aiohttp.ClientSession) -> List[TestResult]:
        """测试基本功能"""
        print("🔧 测试基本功能")
        print("-" * 30)
        
        test_cases = [
            {
                "name": "标准对话 - role/content格式",
                "payload": {
                    "conversation_history": [
                        {"role": "customer", "content": "你们有什么产品？"},
                        {"role": "agent", "content": "我们有AI PPT制作、AI视频创作和AI写作助手。"},
                        {"role": "customer", "content": "AI PPT制作怎么样？"}
                    ],
                    "brand_id": "ykwy",
                    "conversation_id": "test_basic_1"
                }
            },
            {
                "name": "兼容格式 - speaker/message格式",
                "payload": {
                    "conversation_history": [
                        {"speaker": "customer", "message": "你们有什么运动装备？"},
                        {"speaker": "service", "message": "我们有运动鞋、运动服装、体育器材。"},
                        {"speaker": "customer", "message": "有跑鞋推荐吗？"}
                    ],
                    "brand_id": "qinggong",
                    "conversation_id": "test_basic_2"
                }
            },
            {
                "name": "空对话历史",
                "payload": {
                    "conversation_history": [],
                    "brand_id": "ykwy",
                    "conversation_id": "test_empty"
                }
            },
            {
                "name": "单条消息",
                "payload": {
                    "conversation_history": [
                        {"role": "customer", "content": "你好"}
                    ],
                    "brand_id": "ykwy",
                    "conversation_id": "test_single"
                }
            },
            {
                "name": "价格咨询场景",
                "payload": {
                    "conversation_history": [
                        {"role": "customer", "content": "AI写作助手多少钱？"},
                        {"role": "agent", "content": "AI写作助手的价格是99元/月。"},
                        {"role": "customer", "content": "有点贵，有优惠吗？"}
                    ],
                    "brand_id": "ykwy",
                    "conversation_id": "test_price"
                }
            }
        ]
        
        results = []
        for test_case in test_cases:
            result = await self._execute_test(session, test_case["name"], test_case["payload"])
            results.append(result)
            print(f"  {'✅' if result.success else '❌'} {result.name}: {result.response_time:.3f}s")
            if not result.success:
                print(f"    错误: {result.error_message}")
        
        return results
    
    async def test_brand_support(self, session: aiohttp.ClientSession) -> List[TestResult]:
        """测试品牌支持"""
        print("\n🏢 测试品牌支持")
        print("-" * 30)
        
        brands = ["ykwy", "qinggong"]
        results = []
        
        for brand in brands:
            payload = {
                "conversation_history": [
                    {"role": "customer", "content": "你们有什么产品？"},
                    {"role": "customer", "content": "价格怎么样？"}
                ],
                "brand_id": brand,
                "conversation_id": f"test_brand_{brand}"
            }
            
            result = await self._execute_test(session, f"品牌测试 - {brand}", payload)
            results.append(result)
            print(f"  {'✅' if result.success else '❌'} {brand}: {result.response_time:.3f}s")
            if result.success and result.recommendations:
                print(f"    推荐示例: {result.recommendations[0][:50]}...")
        
        return results
    
    async def test_error_handling(self, session: aiohttp.ClientSession) -> List[TestResult]:
        """测试错误处理"""
        print("\n❌ 测试错误处理")
        print("-" * 30)
        
        test_cases = [
            {
                "name": "无效品牌ID",
                "payload": {
                    "conversation_history": [
                        {"role": "customer", "content": "测试消息"}
                    ],
                    "brand_id": "invalid_brand",
                    "conversation_id": "test_invalid_brand"
                },
                "expect_success": True  # 应该fallback到默认品牌
            },
            {
                "name": "缺少必需字段",
                "payload": {
                    "conversation_history": [
                        {"role": "customer", "content": "测试消息"}
                    ]
                    # 缺少 brand_id
                },
                "expect_success": True  # 应该使用默认值
            },
            {
                "name": "格式混合",
                "payload": {
                    "conversation_history": [
                        {"role": "customer", "content": "第一条消息"},
                        {"speaker": "service", "message": "第二条消息"},
                        {"role": "customer", "content": "第三条消息"}
                    ],
                    "brand_id": "ykwy",
                    "conversation_id": "test_mixed_format"
                },
                "expect_success": True
            }
        ]
        
        results = []
        for test_case in test_cases:
            result = await self._execute_test(session, test_case["name"], test_case["payload"])
            results.append(result)
            
            expected = test_case.get("expect_success", False)
            actual_success = result.success
            
            if expected == actual_success:
                print(f"  ✅ {result.name}: 符合预期")
            else:
                print(f"  ❌ {result.name}: 不符合预期 (期望: {'成功' if expected else '失败'}, 实际: {'成功' if actual_success else '失败'})")
        
        return results
    
    async def test_performance(self, session: aiohttp.ClientSession) -> List[TestResult]:
        """测试性能"""
        print("\n⚡ 测试性能")
        print("-" * 30)
        
        # 标准测试负载
        payload = {
            "conversation_history": [
                {"role": "customer", "content": "你们有什么产品？"},
                {"role": "agent", "content": "我们有多款优质产品。"},
                {"role": "customer", "content": "价格怎么样？"}
            ],
            "brand_id": "ykwy",
            "conversation_id": "perf_test"
        }
        
        # 连续测试10次
        results = []
        response_times = []
        
        for i in range(10):
            test_payload = payload.copy()
            test_payload["conversation_id"] = f"perf_test_{i}"
            
            result = await self._execute_test(session, f"性能测试 {i+1}", test_payload)
            results.append(result)
            
            if result.success:
                response_times.append(result.response_time)
                print(f"  ✅ 测试 {i+1}: {result.response_time:.3f}s")
            else:
                print(f"  ❌ 测试 {i+1}: 失败")
        
        # 性能统计
        if response_times:
            avg_time = statistics.mean(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            median_time = statistics.median(response_times)
            
            print(f"\n📊 性能统计:")
            print(f"  平均响应时间: {avg_time:.3f}s")
            print(f"  最快响应时间: {min_time:.3f}s")
            print(f"  最慢响应时间: {max_time:.3f}s")
            print(f"  中位数响应时间: {median_time:.3f}s")
            
            # 性能评估
            if avg_time < 1.0:
                print("  🎉 性能优秀！")
            elif avg_time < 3.0:
                print("  ✅ 性能良好")
            else:
                print("  ⚠️ 性能需要优化")
        
        return results
    
    async def test_concurrent_requests(self, session: aiohttp.ClientSession) -> List[TestResult]:
        """测试并发请求"""
        print("\n🚀 测试并发请求")
        print("-" * 30)
        
        # 创建5个并发请求
        tasks = []
        for i in range(5):
            payload = {
                "conversation_history": [
                    {"role": "customer", "content": f"并发测试消息 {i+1}"},
                    {"role": "customer", "content": "这是第二条消息"}
                ],
                "brand_id": "ykwy",
                "conversation_id": f"concurrent_test_{i}"
            }
            
            task = self._execute_test(session, f"并发测试 {i+1}", payload)
            tasks.append(task)
        
        # 并发执行
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        successful_count = sum(1 for r in results if r.success)
        
        print(f"  并发数: 5")
        print(f"  总耗时: {total_time:.3f}s")
        print(f"  成功数: {successful_count}/5")
        print(f"  成功率: {successful_count/5*100:.1f}%")
        
        return results
    
    async def _execute_test(self, session: aiohttp.ClientSession, name: str, payload: Dict[str, Any]) -> TestResult:
        """执行单个测试"""
        start_time = time.time()
        
        try:
            async with session.post(
                f"{self.base_url}/recommendations",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=15)
            ) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    recommendations = result.get("recommendations", [])
                    
                    return TestResult(
                        name=name,
                        success=True,
                        response_time=response_time,
                        status_code=response.status,
                        recommendations_count=len(recommendations),
                        recommendations=recommendations
                    )
                else:
                    error_text = await response.text()
                    return TestResult(
                        name=name,
                        success=False,
                        response_time=response_time,
                        status_code=response.status,
                        recommendations_count=0,
                        error_message=f"HTTP {response.status}: {error_text}"
                    )
                    
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                name=name,
                success=False,
                response_time=response_time,
                status_code=0,
                recommendations_count=0,
                error_message=str(e)
            )
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🔧 推荐接口端到端测试")
        print("=" * 50)
        
        async with aiohttp.ClientSession() as session:
            # 健康检查
            print("🏥 健康检查")
            print("-" * 30)
            try:
                async with session.get(f"{self.base_url}/health") as response:
                    if response.status == 200:
                        health_data = await response.json()
                        print("✅ 服务健康")
                        if 'available_brands' in health_data:
                            print(f"  支持品牌: {health_data['available_brands']}")
                    else:
                        print(f"❌ 服务异常: HTTP {response.status}")
                        return
            except Exception as e:
                print(f"❌ 无法连接服务: {e}")
                return
            
            # 执行所有测试
            all_results = []
            
            all_results.extend(await self.test_basic_functionality(session))
            all_results.extend(await self.test_brand_support(session))
            all_results.extend(await self.test_error_handling(session))
            all_results.extend(await self.test_performance(session))
            all_results.extend(await self.test_concurrent_requests(session))
            
            # 生成测试报告
            self._generate_report(all_results)
    
    def _generate_report(self, results: List[TestResult]):
        """生成测试报告"""
        print("\n📊 测试报告")
        print("=" * 50)
        
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.success)
        failed_tests = total_tests - successful_tests
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%")
        
        if results:
            response_times = [r.response_time for r in results if r.success]
            if response_times:
                avg_time = statistics.mean(response_times)
                print(f"平均响应时间: {avg_time:.3f}s")
        
        # 失败的测试
        failed_results = [r for r in results if not r.success]
        if failed_results:
            print(f"\n❌ 失败的测试:")
            for result in failed_results:
                print(f"  - {result.name}: {result.error_message}")
        
        # 推荐质量检查
        successful_results = [r for r in results if r.success and r.recommendations]
        if successful_results:
            print(f"\n📝 推荐质量检查:")
            for result in successful_results[:3]:  # 显示前3个
                print(f"  {result.name}:")
                for i, rec in enumerate(result.recommendations, 1):
                    print(f"    {i}. {rec[:80]}...")
        
        print(f"\n{'✅ 所有测试通过！' if failed_tests == 0 else '⚠️ 部分测试失败'}")


async def main():
    """主函数"""
    tester = RecommendationsE2ETest()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
