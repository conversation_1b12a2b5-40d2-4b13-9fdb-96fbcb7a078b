import {
  ApiResponse,
  KnowledgeCategorySimple,
} from '@/models/questionAndAnswer';
import { request } from '@umijs/max';

const API_BASE_URL =
  (process.env.UMI_APP_API_URL || 'http://localhost:3009') + '/api/v1';

/**
 * 获取所有启用的分类（用于下拉选择）
 */
export async function getAvailableCategories(options?: { [key: string]: any }) {
  return request<ApiResponse<KnowledgeCategorySimple[]>>(
    `${API_BASE_URL}/knowledgeCategories/active`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 获取分类编码到名称的映射
 */
export async function getCategoryCodeNameMap(options?: { [key: string]: any }) {
  return request<ApiResponse<Record<string, string>>>(
    `${API_BASE_URL}/knowledgeCategories/map`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 根据编码列表批量获取分类信息
 */
export async function getCategoriesByCodes(
  codes: string[],
  options?: { [key: string]: any },
) {
  return request<ApiResponse<KnowledgeCategorySimple[]>>(
    `${API_BASE_URL}/knowledgeCategories/codes`,
    {
      method: 'GET',
      params: {
        codes: codes.join(','),
      },
      ...(options || {}),
    },
  );
}

/**
 * 获取知识库分类详情（根据ID）
 */
export async function getKnowledgeCategoryById(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<KnowledgeCategorySimple>>(
    `${API_BASE_URL}/knowledgeCategory/${id}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 获取知识库分类详情（根据编码）
 */
export async function getKnowledgeCategoryByCode(
  code: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<KnowledgeCategorySimple>>(
    `${API_BASE_URL}/knowledgeCategory/code/${encodeURIComponent(code)}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 获取分类树形结构
 */
export async function getKnowledgeCategoryTree(options?: {
  [key: string]: any;
}) {
  return request<ApiResponse<any[]>>(
    `${API_BASE_URL}/knowledgeCategories/tree`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 获取知识库分类列表（带分页和筛选）
 */
export async function getKnowledgeCategoryList(
  params?: {
    name?: string;
    code?: string;
    level?: number;
    parentId?: string;
    activeOnly?: boolean;
    skip?: number;
    take?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    includeDeleted?: boolean;
  },
  options?: { [key: string]: any },
) {
  return request<
    ApiResponse<{
      items: KnowledgeCategorySimple[];
      total: number;
    }>
  >(`${API_BASE_URL}/knowledgeCategories`, {
    method: 'GET',
    params,
    ...(options || {}),
  });
}
