import { Button, Checkbox, Input, List, Modal, Tag } from 'antd';
import React, { useState } from 'react';

interface TagType {
  id: string;
  name: string;
}

interface ProductTagSelectorModalProps {
  open: boolean;
  onOk: (selected: TagType[]) => void;
  onCancel: () => void;
  allTags?: TagType[];
  selectedTags?: TagType[];
}

const ProductTagSelectorModal: React.FC<ProductTagSelectorModalProps> = ({
  open,
  onOk,
  onCancel,
  allTags = [],
  selectedTags = [],
}) => {
  const [search, setSearch] = useState('');
  const [addMode, setAddMode] = useState(false);
  const [addValue, setAddValue] = useState('');
  const [selected, setSelected] = useState<TagType[]>(selectedTags);

  // 过滤未关联标签
  const filteredTags = allTags.filter(
    (tag) =>
      tag.name.includes(search) && !selected.some((sel) => sel.id === tag.id),
  );

  const handleAdd = () => {
    if (addValue.trim()) {
      const newTag = { id: Date.now().toString(), name: addValue.trim() };
      setSelected([...selected, newTag]);
      setAddValue('');
      setAddMode(false);
    }
  };

  const handleSelect = (tag: TagType) => {
    setSelected([...selected, tag]);
  };

  const handleRemove = (tag: TagType) => {
    setSelected(selected.filter((t) => t.id !== tag.id));
  };

  const handleClear = () => setSelected([]);

  const handleOk = () => onOk(selected);

  return (
    <Modal
      open={open}
      title={
        <span className="font-medium" data-oid="s0x0_2n">
          选择商品标签
        </span>
      }
      onCancel={onCancel}
      onOk={handleOk}
      width={650}
      footer={[
        <Button
          key="cancel"
          onClick={onCancel}
          className="min-w-[72px]"
          data-oid="3k7x0x4"
        >
          取消
        </Button>,
        <Button
          key="ok"
          type="primary"
          disabled={selected.length === 0}
          onClick={handleOk}
          className="min-w-[72px]"
          data-oid="2h5htue"
        >
          确定
        </Button>,
      ]}
      style={{ top: 40 }}
      data-oid="oxbfw4y"
    >
      <div className="flex h-[500px]" data-oid="raguibp">
        {/* 左侧 */}
        <div
          className="w-[220px] border-r border-[#f0f0f0] p-4 bg-[#fafbfc] flex flex-col"
          data-oid="cjlzmv-"
        >
          <Input.Search
            placeholder="搜索标签名称"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="mb-2"
            allowClear
            data-oid="g4ymym6"
          />

          {addMode ? (
            <div className="flex mb-2" data-oid="30d3t0z">
              <Input
                value={addValue}
                onChange={(e) => setAddValue(e.target.value)}
                size="small"
                className="mr-1"
                onPressEnter={handleAdd}
                autoFocus
                data-oid="0an4d73"
              />

              <Button
                size="small"
                type="primary"
                onClick={handleAdd}
                data-oid="t10mp5h"
              >
                添加
              </Button>
            </div>
          ) : (
            <Button
              block
              size="small"
              type="dashed"
              className="mb-2"
              onClick={() => setAddMode(true)}
              data-oid="pcpdn3_"
            >
              + 添加商品标签
            </Button>
          )}
          <div className="mb-2 flex items-center" data-oid="_zazjbf">
            <span className="flex-1 text-[#666]" data-oid="-pv07jr">
              全选
            </span>
            <Checkbox
              checked={
                filteredTags.length > 0 &&
                filteredTags.every((tag) =>
                  selected.some((sel) => sel.id === tag.id),
                )
              }
              indeterminate={
                filteredTags.some((tag) =>
                  selected.some((sel) => sel.id === tag.id),
                ) &&
                !filteredTags.every((tag) =>
                  selected.some((sel) => sel.id === tag.id),
                )
              }
              onChange={(e) => {
                if (e.target.checked) {
                  setSelected([
                    ...selected,
                    ...filteredTags.filter(
                      (tag) => !selected.some((sel) => sel.id === tag.id),
                    ),
                  ]);
                } else {
                  setSelected(
                    selected.filter(
                      (sel) => !filteredTags.some((tag) => tag.id === sel.id),
                    ),
                  );
                }
              }}
              data-oid="sw_yl3n"
            />
          </div>
          <div className="text-[#999] text-[13px] mb-1" data-oid="4dd0orm">
            未关联商品标签
          </div>
          <div className="max-h-[320px] overflow-y-auto" data-oid="ca5obya">
            <List
              size="small"
              dataSource={filteredTags}
              renderItem={(tag) => (
                <List.Item className="py-1" data-oid="ilg50:j">
                  <Checkbox
                    checked={selected.some((sel) => sel.id === tag.id)}
                    onChange={(e) =>
                      e.target.checked ? handleSelect(tag) : handleRemove(tag)
                    }
                    data-oid="rd.qekw"
                  >
                    {tag.name}
                  </Checkbox>
                </List.Item>
              )}
              locale={{
                emptyText: (
                  <span className="text-[#bbb]" data-oid="40q6flc">
                    暂无标签
                  </span>
                ),
              }}
              data-oid="g2ifu0r"
            />
          </div>
        </div>
        {/* 右侧 */}
        <div className="flex-1 p-4 relative" data-oid="a9vs.t.">
          <div className="flex items-center mb-2" data-oid="26.p6bv">
            <span className="text-[#666] font-medium" data-oid="ccvicpw">
              已选({selected.length})
            </span>
            <Button
              type="link"
              size="small"
              className="ml-auto"
              onClick={handleClear}
              disabled={selected.length === 0}
              data-oid="hd7m3ni"
            >
              清空
            </Button>
          </div>
          <div className="min-h-[32px]" data-oid="aqfv.pn">
            {selected.length === 0 ? (
              <span className="text-[#bbb]" data-oid="svncaf9">
                --
              </span>
            ) : (
              selected.map((tag) => (
                <Tag
                  key={tag.id}
                  closable
                  onClose={() => handleRemove(tag)}
                  className="mb-2"
                  data-oid="o98nrj-"
                >
                  {tag.name}
                </Tag>
              ))
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ProductTagSelectorModal;
