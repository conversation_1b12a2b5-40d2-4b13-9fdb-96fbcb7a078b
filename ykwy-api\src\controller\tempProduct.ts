import Bun from 'bun';
import type { Context } from 'hono';

import redis from '../client/redis.js';
import { BusinessLogicError, ValidationError } from '../errors/custom.error.ts';
import { ProductRagflowService } from '../services/productRagflowService.ts';
import { TempProductService } from '../services/tempProductService';
import { tempProductBulkDeleteSchema, tempProductParamIdSchema, tempProductQuerySchema, tempProductSearchSchema, upsertTempProductSchema } from '../types/validators/tempProductValidator';
import { removeScanKeys } from '../utils/cache.js';
import { R } from '../utils/Response.ts';

const tempProductService = new TempProductService();

/**
 * 临时商品控制器，处理临时商品相关的请求
 */
export class TempProductController {
  /**
   * 创建或更新临时商品
   * @param c Hono Context
   */
  public async upsert(c: Context) {
    const body = await c.req.json();
    // 参数校验
    const validation = upsertTempProductSchema.safeParse(body);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v1/temp-product',
      });
    }
    // 调用服务层
    const result = await tempProductService.upsert(validation.data);

    // 业务逻辑校验示例：假设服务层返回null表示商品已存在
    if (!result) {
      throw new BusinessLogicError('商品已存在，不能重复创建', 'BIZ_PRODUCT_EXISTS', 400, { requestBody: body, endpoint: 'POST /api/v1/temp-product' });
    }
    // 清除相关缓存
    try {
      // 如果是更新操作（有id），清除详情缓存
      if (validation.data.id) {
        await redis.del(`temp-product:detail:${validation.data.id}`);
      }
      // 清除productId缓存
      await redis.del(`temp-product:productId:${result.productId}`);
      // 清除所有列表缓存
      await removeScanKeys(redis, 'temp-product:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 获取单个临时商品详情
   * @param c Hono Context
   */
  public async findById(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = tempProductParamIdSchema.safeParse(params);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'GET /api/v1/temp-product/:id',
      });
    }
    const cacheKey = `temp-product:detail:${validation.data.id}`;
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return R.success(c, JSON.parse(cached));
      }
    } catch (cacheError) {
      console.error('缓存读取失败:', cacheError);
    }

    // 查询临时商品
    const result = await tempProductService.findById(validation.data.id);
    if (!result) {
      throw new BusinessLogicError('临时商品未找到', 'BIZ_PRODUCT_NOT_FOUND', 404, {
        productId: validation.data.id,
        endpoint: 'GET /api/v1/temp-product/:id',
      });
    }
    // 缓存结果
    try {
      const expire = parseInt(Bun.env['REDIS_CACHE_EXPIRE'] || '3600');
      await redis.setex(cacheKey, expire, JSON.stringify(result));
    } catch (cacheError) {
      console.error('缓存写入失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 根据查询字符串搜索临时商品
   * @param c Hono Context
   */
  public async searchByQuery(c: Context) {
    try {
      const searchParams = {
        query: c.req.query('query'),
        shopId: c.req.query('shopId'),
      };

      // 参数校验
      const validation = tempProductSearchSchema.safeParse(searchParams);
      if (!validation.success) {
        return c.json(
          {
            code: 400,
            message: validation.error.issues[0]?.message || '参数验证失败',
            data: [],
          },
          400,
        );
      }

      const { query, shopId } = validation.data;
      const results = await tempProductService.searchByQuery(query, shopId);

      return c.json({
        code: 0,
        message: 'success',
        data: results,
      });
    } catch (error) {
      console.error('搜索临时商品失败:', error);
      return c.json(
        {
          code: 500,
          message: 'fail',
          data: [],
        },
        500,
      );
    }
  }

  /**
   * 获取临时商品列表（支持分页、过滤）
   * @param c Hono Context
   */
  public async findMany(c: Context) {
    const query = c.req.query();
    // 参数校验
    const validation = tempProductQuerySchema.safeParse(query);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestQuery: query,
        endpoint: 'GET /api/v1/temp-products',
      });
    }
    // 业务逻辑校验示例：分页参数非法
    if (validation.data.take <= 0 || validation.data.skip < 0) {
      throw new BusinessLogicError('分页参数非法', 'BIZ_INVALID_PAGINATION', 400, { requestQuery: query, endpoint: 'GET /api/v1/temp-products' });
    }
    // 构建缓存键（包含 onlyWithoutShopId 参数）
    const { skip, take, name, productId, status, includeDeleted } = validation.data;
    const cacheKey = `temp-product:list:${skip}-${take}-${name || ''}-${productId || ''}-${status || ''}-${includeDeleted || ''}-onlyWithoutShopId:true`;

    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return R.success(c, JSON.parse(cached));
      }
    } catch (cacheError) {
      console.error('缓存读取失败:', cacheError);
    }
    // 查询临时商品列表（只查询没有shopId的数据）
    const queryParams = {
      ...validation.data,
      // 添加过滤条件：只查询shopId为null的数据
      onlyWithoutShopId: true,
    };
    const result = await tempProductService.findMany(queryParams);
    // 业务逻辑校验示例：无数据
    if (!result || (Array.isArray(result.items) && result.items.length === 0)) {
      throw new BusinessLogicError('没有符合条件的临时商品', 'BIZ_PRODUCT_LIST_EMPTY', 404, { requestQuery: query, endpoint: 'GET /api/v1/temp-products' });
    }
    // 缓存结果
    try {
      const expire = parseInt(Bun.env['REDIS_CACHE_EXPIRE'] || '3600');
      await redis.setex(cacheKey, expire, JSON.stringify(result));
    } catch (cacheError) {
      console.error('缓存写入失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 软删除单个临时商品
   * @param c Hono Context
   */
  public async delete(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = tempProductParamIdSchema.safeParse(params);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'DELETE /api/v1/temp-product/:id',
      });
    }
    // 软删除
    const result = await tempProductService.softDelete(validation.data.id);
    // 业务逻辑校验示例：假设服务层返回null表示商品状态不允许删除
    if (!result) {
      throw new BusinessLogicError('商品状态不允许删除', 'BIZ_PRODUCT_DELETE_DENIED', 400, {
        productId: validation.data.id,
        endpoint: 'DELETE /api/v1/temp-product/:id',
      });
    }
    // 清除相关缓存
    try {
      // 清除详情缓存
      await redis.del(`temp-product:detail:${validation.data.id}`);
      // 清除productId缓存
      await redis.del(`temp-product:productId:${result.productId}`);
      // 清除所有列表缓存
      await removeScanKeys(redis, 'temp-product:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 恢复已删除临时商品
   * @param c Hono Context
   */
  public async restore(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = tempProductParamIdSchema.safeParse(params);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'POST /api/v1/temp-product/:id/restore',
      });
    }
    // 恢复
    const result = await tempProductService.restore(validation.data.id);
    // 业务逻辑校验示例：假设服务层返回null表示商品已被永久删除，无法恢复
    if (!result) {
      throw new BusinessLogicError('商品已被永久删除，无法恢复', 'BIZ_PRODUCT_CANNOT_RESTORE', 400, {
        productId: validation.data.id,
        endpoint: 'POST /api/v1/temp-product/:id/restore',
      });
    }
    // 清除相关缓存
    try {
      // 清除详情缓存
      await redis.del(`temp-product:detail:${validation.data.id}`);
      // 清除productId缓存
      await redis.del(`temp-product:productId:${result.productId}`);
      // 清除所有列表缓存
      await removeScanKeys(redis, 'temp-product:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 批量软删除临时商品
   * @param c Hono Context
   */
  public async bulkDelete(c: Context) {
    const body = await c.req.json();
    // 参数校验
    const validation = tempProductBulkDeleteSchema.safeParse(body);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v1/temp-products/bulk-delete',
      });
    }
    // 业务逻辑校验：ids 不能为空
    if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
      throw new BusinessLogicError('删除ID列表不能为空', 'BIZ_BULK_DELETE_EMPTY', 400, {
        requestBody: body,
        endpoint: 'POST /api/v1/temp-products/bulk-delete',
      });
    }
    // 批量软删除
    const result = await tempProductService.bulkDelete(validation.data.ids);
    // 业务逻辑校验：假设服务层返回null或空数组表示未删除任何商品
    if (!result || (Array.isArray(result) && result.length === 0)) {
      throw new BusinessLogicError('未删除任何商品', 'BIZ_BULK_DELETE_NONE', 400, {
        requestBody: body,
        endpoint: 'POST /api/v1/temp-products/bulk-delete',
      });
    }
    // 清除相关缓存
    try {
      // 清除所有相关详情缓存
      for (const id of validation.data.ids) {
        await redis.del(`temp-product:detail:${id}`);
      }
      // 清除所有列表缓存
      await removeScanKeys(redis, 'temp-product:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 批量创建临时商品
   * @param c Hono Context
   */
  public async bulkCreate(c: Context) {
    const body = await c.req.json();
    // 参数校验 - 这里简单验证是数组
    if (!Array.isArray(body)) {
      throw new ValidationError([], {
        message: '请求体必须是数组',
        requestBody: body,
        endpoint: 'POST /api/v1/temp-products/bulk-create',
      });
    }
    // 业务逻辑校验：数组不能为空
    if (body.length === 0) {
      throw new BusinessLogicError('批量创建列表不能为空', 'BIZ_BULK_CREATE_EMPTY', 400, {
        requestBody: body,
        endpoint: 'POST /api/v1/temp-products/bulk-create',
      });
    }
    // 批量创建
    const result = await tempProductService.bulkCreate(body);
    // 业务逻辑校验：假设服务层返回null或空数组表示未创建任何商品
    if (!result || (Array.isArray(result) && result.length === 0)) {
      throw new BusinessLogicError('未创建任何商品', 'BIZ_BULK_CREATE_NONE', 400, {
        requestBody: body,
        endpoint: 'POST /api/v1/temp-products/bulk-create',
      });
    }
    // 清除相关缓存
    try {
      await removeScanKeys(redis, 'temp-product:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 清空所有临时商品
   * @param c Hono Context
   */
  public async truncate(c: Context) {
    const result = await tempProductService.truncate();
    // 业务逻辑校验：假设服务层返回false表示清空失败
    if (!result) {
      throw new BusinessLogicError('清空临时商品失败', 'BIZ_TRUNCATE_FAILED', 500, { endpoint: 'DELETE /api/v1/temp-products/truncate' });
    }
    // 清除相关缓存
    try {
      await removeScanKeys(redis, 'temp-product:list');
      await removeScanKeys(redis, 'temp-product:detail');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }
  private productRagflowService: ProductRagflowService;
  constructor() {
    this.productRagflowService = new ProductRagflowService();
  }

  /**
   * 同步商品数据到Ragflow知识库
   * @param c Hono Context
   */
  public syncToRagflow = async (c: Context) => {
    try {
      const docId = await this.productRagflowService.syncToRagflow();
      // 业务逻辑校验：假设docId为null表示同步失败
      if (!docId) {
        throw new BusinessLogicError('同步到Ragflow失败', 'BIZ_SYNC_RAGFLOW_FAILED', 500, { endpoint: 'POST /api/v1/temp-products/sync-ragflow' });
      }
      return c.json({ code: 0, data: { docId } });
    } catch (err: unknown) {
      const message = err instanceof Error ? `同步失败: ${err.message}` : '同步失败: 未知错误';
      throw new BusinessLogicError(message, 'BIZ_SYNC_RAGFLOW_FAILED', 500, {
        endpoint: 'POST /api/v1/temp-products/sync-ragflow',
      });
    }
  };
}
