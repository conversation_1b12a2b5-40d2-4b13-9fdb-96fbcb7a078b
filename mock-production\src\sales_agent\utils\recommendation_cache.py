"""
推荐缓存模块 - 提高推荐生成性能
"""
import hashlib
import time
import logging
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CachedRecommendation:
    """缓存的推荐"""
    recommendations: List[str]
    timestamp: float
    conversation_id: str


class RecommendationCache:
    """推荐缓存管理器"""
    
    def __init__(self, cache_ttl: int = 300):  # 5分钟缓存
        self.cache: Dict[str, CachedRecommendation] = {}
        self.cache_ttl = cache_ttl
        self.max_cache_size = 1000  # 最大缓存条目数
    
    def _generate_cache_key(self, conversation_history: List[Dict[str, Any]], conversation_id: str = None) -> str:
        """生成缓存键"""
        # 只使用最近的几条消息生成缓存键
        recent_messages = conversation_history[-4:] if len(conversation_history) > 4 else conversation_history
        
        # 创建消息内容的哈希
        content_parts = []
        for msg in recent_messages:
            content_parts.append(f"{msg.get('role', '')}:{msg.get('content', '')}")
        
        content_str = "|".join(content_parts)
        cache_key = hashlib.md5(content_str.encode()).hexdigest()
        
        return cache_key
    
    def get_cached_recommendations(self, conversation_history: List[Dict[str, Any]], conversation_id: str = None) -> Optional[List[str]]:
        """获取缓存的推荐"""
        try:
            cache_key = self._generate_cache_key(conversation_history, conversation_id)
            
            if cache_key in self.cache:
                cached = self.cache[cache_key]
                
                # 检查是否过期
                if time.time() - cached.timestamp < self.cache_ttl:
                    logger.info(f"🎯 [缓存] 命中推荐缓存: {cache_key[:8]}")
                    return cached.recommendations
                else:
                    # 过期，删除缓存
                    del self.cache[cache_key]
                    logger.info(f"⏰ [缓存] 推荐缓存过期: {cache_key[:8]}")
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ [缓存] 获取推荐缓存失败: {e}")
            return None
    
    def cache_recommendations(self, conversation_history: List[Dict[str, Any]], recommendations: List[str], conversation_id: str = None):
        """缓存推荐"""
        try:
            cache_key = self._generate_cache_key(conversation_history, conversation_id)
            
            # 检查缓存大小，如果超过限制则清理旧缓存
            if len(self.cache) >= self.max_cache_size:
                self._cleanup_old_cache()
            
            self.cache[cache_key] = CachedRecommendation(
                recommendations=recommendations,
                timestamp=time.time(),
                conversation_id=conversation_id or "unknown"
            )
            
            logger.info(f"💾 [缓存] 推荐已缓存: {cache_key[:8]}")
            
        except Exception as e:
            logger.warning(f"⚠️ [缓存] 缓存推荐失败: {e}")
    
    def _cleanup_old_cache(self):
        """清理旧缓存"""
        try:
            current_time = time.time()
            expired_keys = []
            
            for key, cached in self.cache.items():
                if current_time - cached.timestamp > self.cache_ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
            
            # 如果清理后仍然太多，删除最旧的一半
            if len(self.cache) >= self.max_cache_size:
                sorted_items = sorted(self.cache.items(), key=lambda x: x[1].timestamp)
                keys_to_remove = [item[0] for item in sorted_items[:len(sorted_items)//2]]
                
                for key in keys_to_remove:
                    del self.cache[key]
            
            logger.info(f"🧹 [缓存] 清理完成，当前缓存条目: {len(self.cache)}")
            
        except Exception as e:
            logger.error(f"❌ [缓存] 清理缓存失败: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        valid_count = 0
        expired_count = 0
        
        for cached in self.cache.values():
            if current_time - cached.timestamp < self.cache_ttl:
                valid_count += 1
            else:
                expired_count += 1
        
        return {
            "total_entries": len(self.cache),
            "valid_entries": valid_count,
            "expired_entries": expired_count,
            "cache_ttl": self.cache_ttl,
            "max_cache_size": self.max_cache_size
        }


# 全局缓存实例
recommendation_cache = RecommendationCache()


def get_smart_recommendations(conversation_history: List[Dict[str, Any]], conversation_id: str = None) -> List[str]:
    """智能推荐生成（基于规则的快速版本）"""
    try:
        if not conversation_history:
            return [
                "感谢您的咨询，我来为您详细解答这个问题。",
                "我理解您的关切，让我为您提供更多相关信息。",
                "根据您的情况，我建议您可以考虑以下解决方案。"
            ]
        
        # 获取最后一条客户消息
        last_customer_msg = None
        for msg in reversed(conversation_history):
            if msg.get('role') == 'customer':
                last_customer_msg = msg.get('content', '').lower()
                break
        
        if not last_customer_msg:
            return [
                "感谢您的咨询，我来为您详细解答这个问题。",
                "我理解您的关切，让我为您提供更多相关信息。",
                "根据您的情况，我建议您可以考虑以下解决方案。"
            ]
        
        # 基于关键词的智能推荐
        recommendations = []
        
        # 价格相关
        if any(word in last_customer_msg for word in ['贵', '便宜', '价格', '多少钱', '优惠']):
            recommendations.extend([
                "我理解您对价格的关注，让我为您查看一下当前的优惠活动。",
                "这个价格确实很有竞争力，而且我们还有一些专享优惠可以为您申请。",
                "除了价格，我们的服务质量和售后保障也是很多客户选择我们的原因。"
            ])
        
        # 产品咨询
        elif any(word in last_customer_msg for word in ['产品', '服务', '功能', '怎么样', '介绍']):
            recommendations.extend([
                "我来为您详细介绍一下这个产品的特点和优势。",
                "根据您的需求，我推荐您了解一下我们的核心功能。",
                "很多客户都对这个产品的效果很满意，我可以分享一些案例给您。"
            ])
        
        # 订单相关
        elif any(word in last_customer_msg for word in ['订单', '物流', '发货', '快递']):
            recommendations.extend([
                "我马上为您查询一下订单状态和物流信息。",
                "关于您的订单，我来帮您跟进一下具体情况。",
                "如果有任何物流问题，我们会及时为您处理。"
            ])
        
        # 购买意向
        elif any(word in last_customer_msg for word in ['买', '购买', '下单', '付款']):
            recommendations.extend([
                "太好了！我来为您介绍一下购买流程和注意事项。",
                "现在下单的话，我可以为您申请一些额外的优惠。",
                "我来为您发送购买链接，有任何问题随时联系我。"
            ])
        
        # 默认推荐
        else:
            recommendations.extend([
                "感谢您的咨询，我来为您详细解答这个问题。",
                "我理解您的关切，让我为您提供更多相关信息。",
                "根据您的情况，我建议您可以考虑以下解决方案。"
            ])
        
        # 确保有3个推荐
        while len(recommendations) < 3:
            recommendations.append("我会尽力为您提供最好的服务和解决方案。")
        
        return recommendations[:3]
        
    except Exception as e:
        logger.error(f"❌ 智能推荐生成失败: {e}")
        return [
            "感谢您的咨询，我来为您详细解答这个问题。",
            "我理解您的关切，让我为您提供更多相关信息。",
            "根据您的情况，我建议您可以考虑以下解决方案。"
        ]
