// 千牛相关类型定义

export interface QianniuClient {
  id: string;
  name: string;
  description?: string;
  isOnline: boolean;
  lastOnlineAt?: string;
  organizationId: string;
  teamId: string;
  invitationId: string;
  connectionId?: string;
  clientInfo?: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
  team: {
    id: string;
    name: string;
  };
  organization: {
    id: string;
    name: string;
  };
  accounts: Array<{
    id: string;
    accountName: string;
    shopName?: string;
    platformType: string;
    isActive: boolean;
    isLoggedIn: boolean;
    brand?: {
      id: string;
      name: string;
      logo?: string;
    };
  }>;
  invitation?: {
    id: string;
    name: string;
    status: string;
  };
  _count?: {
    accounts: number;
  };
  // 实时状态字段
  realTimeStatus?: {
    isOnline: boolean;
    lastSeen: string;
    connectionCount: number;
  };
}

export interface QianniuClientFilters {
  teamId?: string;
  organizationId?: string;
  isOnline?: boolean;
}

export interface RegisterQianniuClientRequest {
  invitationId: string;
  name: string;
  description?: string;
  clientInfo?: Record<string, unknown>;
}

export interface UpdateQianniuClientStatusRequest {
  isOnline: boolean;
  connectionId?: string;
  clientInfo?: Record<string, unknown>;
}

// TCP 相关类型
export interface TcpConnectionInfo {
  tcpConnections: string[]; // TCP客户端ID数组
  connectionMappings: Record<string, string>; // connectionId -> clientId映射
  totalConnections: number;
  onlineClients: Array<{
    id: string;
    name: string;
    connectionId: string;
    accounts: Array<{
      accountName: string;
      shopName?: string;
      platformType: string;
    }>;
  }>;
  unmappedTcpConnections: string[]; // 未映射的TCP连接
}

export interface TcpConnectionMapping {
  connectionId: string;
  clientId: string;
  mappedAt: string;
  mappedBy: 'auto' | 'manual';
  confidence?: number;
}

// 千牛账号相关类型
export interface QianniuAccount {
  id: string;
  clientId: string;
  brandId: string;
  accountName: string;
  accountId: string;
  shopName?: string;
  shopId?: string;
  platformType: PlatformType;
  isActive: boolean;
  isLoggedIn: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  brand?: {
    id: string;
    name: string;
    logo?: string;
  };
  client?: {
    id: string;
    name: string;
  };
}

export type PlatformType = 'TAOBAO' | 'TMALL' | 'PINDUODUO' | 'JD' | 'WECHAT' | 'DOUYIN' | 'XIAOHONGSHU' | 'OTHER';

export interface CreateQianniuAccountRequest {
  clientId: string;
  brandId: string;
  accountName: string;
  accountId: string;
  shopName?: string;
  shopId?: string;
  platformType: PlatformType;
}

export interface UpdateQianniuAccountRequest {
  accountName?: string;
  shopName?: string;
  shopId?: string;
  platformType?: PlatformType;
  isActive?: boolean;
  isLoggedIn?: boolean;
}

export interface RegisterTcpConnectionRequest {
  connectionId: string;
  tcpClientId?: string; // 匹配后端API期望的字段名
}

export interface TestSendRequest {
  connectionId: string;
  targetId: string;
  message: string;
}

export interface TestSendResponse {
  data: {
    connectionId: string;
    targetId: string;
    message: string;
    success: boolean;
  };
  message: string;
}

// 监控相关类型
export interface ConnectionStats {
  totalConnections: number;
  activeConnections: number;
  onlineClients: number;
  offlineClients: number;
  websocket: {
    totalConnections: number;
    connectionsByType: Record<string, number>;
    connectionsByOrg: Record<string, number>;
    connectionsByTeam: Record<string, number>;
  };
  database: {
    total: number;
    online: number;
    offline: number;
    byOrganization: Record<
      string,
      {
        total: number;
        online: number;
        offline: number;
      }
    >;
  };
  realTime: {
    activeConnections: number;
    qianniuConnections: number;
  };
}

export interface QianniuConnection {
  id: string;
  clientId: string | null;
  connectionId: string;
  isOnline: boolean;
  connectedAt: string;
  lastActivity: string;
  platformType: string;
  organizationId: string;
  teamId: string;
  client: {
    id: string;
    name: string;
    team: {
      id: string;
      name: string;
    };
  } | null;
}

export interface MessageStats {
  overview: {
    total: number;
    today: number;
    week: number;
    month: number;
  };
  bySenderType: Record<string, number>;
  byMessageType: Record<string, number>;
  hourlyDistribution: Array<{ hour: number; count: number }>;
}
