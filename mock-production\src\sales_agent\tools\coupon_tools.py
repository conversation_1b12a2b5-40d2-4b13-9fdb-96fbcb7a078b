"""
优惠券工具类 - 提供优惠券相关的工具函数
"""
import logging
import asyncio
import json
from typing import List, Dict, Any
from .base_tools import BaseTools
from ..services.qianniu_client import qianniu_client

logger = logging.getLogger(__name__)

class CouponTools(BaseTools):
    """优惠券工具类 - 提供优惠券查询和发送功能"""

    def get_shop_coupons(self) -> str:
        """获取店铺所有优惠券

        获取当前店铺的所有可用优惠券信息，以便发送给犹豫不决或抱怨价格的客户。

        Returns:
            优惠券列表信息
        """
        logger.info(f"🔧 [工具调用] get_shop_coupons")

        # 检查必要参数
        connection_id = self.current_connection_id

        if not connection_id:
            logger.error("   ❌ 未设置千牛连接ID")
            return "无法获取优惠券信息，未配置千牛连接"

        try:
            # 使用qianniu_client获取店铺优惠券
            logger.info(f"   🌐 调用千牛API获取店铺优惠券")

            # 调用千牛API获取店铺优惠券
            try:
                # 检查是否已经在事件循环中
                asyncio.get_running_loop()
                # 如果已经在事件循环中，在新线程中运行
                import concurrent.futures
                import threading

                def run_in_thread():
                    return asyncio.run(qianniu_client.get_shop_coupons(connection_id=connection_id))

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    response = future.result()
            except RuntimeError:
                # 没有运行的事件循环，可以直接使用 asyncio.run
                response = asyncio.run(qianniu_client.get_shop_coupons(
                    connection_id=connection_id
                ))

            logger.info(f"   📦 千牛API返回结果: {response}")

            if not response.get('success'):
                logger.error(f"   ❌ 获取店铺优惠券失败: {response}")
                return "抱歉，获取优惠券信息失败，请稍后再试。"

            # 解析优惠券数据 - 根据实际API返回结构调整
            coupon_data = response.get('data', {}).get('data', {})
            public_coupons = coupon_data.get('publicCouponList', [])
            private_coupons = coupon_data.get('privateCouponList', [])

            # 合并公共券和私人券
            all_coupons = public_coupons + private_coupons

            if not all_coupons:
                return "店铺当前没有可用的优惠券。"

            # 格式化优惠券信息
            coupon_info = "店铺现有以下优惠券：\n\n"
            for idx, coupon in enumerate(all_coupons, 1):
                coupon_name = coupon.get('name', '未命名优惠券')
                activity_id = coupon.get('activityId', '')
                description = coupon.get('description', '无描述')

                # 获取优惠金额
                amount = coupon.get('amount', '')

                # 获取使用门槛
                threshold = coupon.get('threshold', '')

                # 获取有效期
                start_time = coupon.get('startTime', '')
                end_time = coupon.get('endTime', '')

                # 获取优惠券适用商品
                items = coupon.get('items', [])

                coupon_info += f"{idx}. {coupon_name}\n"
                coupon_info += f"   活动ID: {activity_id}\n"
                coupon_info += f"   描述: {description}\n"

                if amount:
                    coupon_info += f"   优惠金额: ¥{amount}\n"

                if threshold:
                    coupon_info += f"   使用门槛: 满¥{threshold}\n"

                if start_time and end_time:
                    coupon_info += f"   有效期: {start_time} 至 {end_time}\n"

                if items and len(items) > 0:
                    item_ids_str = ", ".join([str(item) for item in items])
                    coupon_info += f"   适用商品ID: {item_ids_str}\n"

                is_public = coupon.get('isPublic', False)
                coupon_info += f"   券类型: {'公开券' if is_public else '私人券'}\n"

                coupon_info += "\n"

            logger.info(f"   📦 找到 {len(all_coupons)} 张优惠券 (公开券: {len(public_coupons)}, 私人券: {len(private_coupons)})")
            return coupon_info

        except Exception as e:
            logger.error(f"   ❌ 获取店铺优惠券出错: {str(e)}")
            import traceback
            logger.error(f"   ❌ 异常详情: {traceback.format_exc()}")
            return "抱歉，获取优惠券信息失败，请稍后再试。"

    def send_coupon(self, activity_id: str, coupon_name: str = "", description: str = "") -> str:
        """发送优惠券给客户

        Args:
            activity_id: 优惠券活动ID，必填参数
            coupon_name: 优惠券名称，可选，默认为"优惠券"
            description: 优惠券描述，可选，默认为"限时专享优惠"

        Returns:
            发送结果信息
        """
        logger.info(f"🔧 [工具调用] send_coupon")
        logger.info(f"   📥 输入参数: activity_id='{activity_id}', coupon_name='{coupon_name}', description='{description}'")

        # 检查必填参数
        if not activity_id:
            logger.error("   ❌ 未提供优惠券活动ID")
            return "发送优惠券失败，请提供优惠券活动ID参数"

        # 检查必要参数
        connection_id = self.current_connection_id
        customer_id = self.current_customer_id

        if not connection_id:
            logger.error("   ❌ 未设置千牛连接ID")
            return "无法发送优惠券，未配置千牛连接"

        if not customer_id:
            logger.error("   ❌ 未设置客户ID")
            return "无法发送优惠券，未获取到客户信息"

        # 使用默认值（如果未提供）
        if not coupon_name:
            coupon_name = "店铺优惠券"
        if not description:
            description = "限时专享优惠"

        try:
            # 使用qianniu_client发送优惠券
            logger.info(f"   🌐 调用千牛API发送优惠券")

            # 调用千牛API发送优惠券
            try:
                # 检查是否已经在事件循环中
                asyncio.get_running_loop()
                # 如果已经在事件循环中，在新线程中运行
                import concurrent.futures

                def run_in_thread():
                    return asyncio.run(qianniu_client.send_coupon(
                        connection_id=connection_id,
                        customer_id=customer_id,
                        name=coupon_name,
                        activity_id=activity_id,
                        description=description
                    ))

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    response = future.result()
            except RuntimeError:
                # 没有运行的事件循环，可以直接使用 asyncio.run
                response = asyncio.run(qianniu_client.send_coupon(
                    connection_id=connection_id,
                    customer_id=customer_id,
                    name=coupon_name,
                    activity_id=activity_id,
                    description=description
                ))

            logger.info(f"   📦 千牛API返回结果: {response}")

            if response.get('success'):
                logger.info(f"   ✅ 发送优惠券成功")

                # 构建优惠券信息
                response_msg = f"已为您发送「{coupon_name}」优惠券，请在聊天窗口中查看并领取。\n\n{description}"
                response_msg += "\n\n优惠券使用说明：\n1. 点击聊天窗口中的优惠券链接领取\n2. 下单时自动抵扣\n3. 如有任何问题请随时咨询"

                return response_msg
            else:
                logger.error(f"   ❌ 发送优惠券失败: {response}")
                return "抱歉，发送优惠券失败，请稍后再试。"

        except Exception as e:
            logger.error(f"   ❌ 发送优惠券出错: {str(e)}")
            import traceback
            logger.error(f"   ❌ 异常详情: {traceback.format_exc()}")
            return "抱歉，发送优惠券失败，请稍后再试。"