export type LayoutMode = 'fixed' | 'scrollable';

// 页面布局配置
export const PAGE_LAYOUT_CONFIG: Record<string, LayoutMode> = {
  // 固定布局页面 - 对话管理
  '/': 'fixed',
  '/conversations': 'fixed',

  // 可滚动布局页面 - 连接管理、设置、配置等
  '/connections': 'scrollable',
  '/ai-config': 'scrollable',
  '/settings': 'scrollable',
};

export function getPageLayoutMode(pathname: string): LayoutMode {
  return PAGE_LAYOUT_CONFIG[pathname] || 'scrollable';
}
