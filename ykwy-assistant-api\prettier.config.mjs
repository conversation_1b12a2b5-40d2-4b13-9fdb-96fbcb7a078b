export default {
  printWidth: 200,
  singleQuote: true,
  trailingComma: 'all',
  endOfLine: 'auto',
  tabWidth: 2,
  semi: true,
  bracketSpacing: true,
  // 以下配置帮助解决与ESLint的冲突
  overrides: [
    {
      files: ['src/lib/connection-tracker.ts', 'src/routes/platform-ws.ts', 'src/services/qianniuTcpServer.ts'],
      options: {
        // 对有问题的文件禁用格式化
        requirePragma: true,
      },
    },
  ],
};
