"""
轻功体育销售工具集
"""
import logging
from typing import List, Dict, Any, Optional
from llama_index.core.tools import FunctionTool

from .base_tools import BaseTools
from .order_tools import OrderTools
from .commerce_tools import CommerceTools
from .coupon_tools import CouponTools
from ..data_loader.qinggong_loader import QinggongDataLoader

logger = logging.getLogger(__name__)


class QinggongSalesTools(BaseTools):
    """轻功体育销售工具集"""

    def __init__(self):
        super().__init__()
        self.brand_name = "轻功体育"
        self.data_loader = QinggongDataLoader()
        self.products = self.data_loader.products

        # 初始化千牛API工具（复用原有实现）
        self.order_tools = OrderTools()
        self.commerce_tools = CommerceTools()
        self.coupon_tools = CouponTools()

    def set_connection_info(self, connection_id: str, customer_id: str, customer_nick: str = None):
        """设置连接信息，传递给千牛API工具"""
        # 设置千牛API工具的连接信息
        self.order_tools.set_connection_info(connection_id, customer_id, customer_nick)
        self.commerce_tools.set_connection_info(connection_id, customer_id, customer_nick)
        self.coupon_tools.set_connection_info(connection_id, customer_id, customer_nick)

    def get_all_tools(self) -> List[FunctionTool]:
        """获取所有轻功体育销售工具"""
        tools = []

        # 产品管理工具
        tools.extend(self._get_product_tools())

        # 订单管理工具（简化实现）
        tools.extend(self._get_order_tools())

        # 商务交易工具（简化实现）
        tools.extend(self._get_commerce_tools())

        return tools

    def _get_product_tools(self) -> List[FunctionTool]:
        """获取产品管理工具"""
        return [\
            FunctionTool.from_defaults(
                fn=self.get_product_list,
                name="get_product_list",
                description="获取轻功体育所有产品的列表，包含产品信息和ID。当客户需要了解产品清单时使用。"
            ),
            FunctionTool.from_defaults(
                fn=self.get_product_details,
                name="get_product_details",
                description="根据产品ID获取轻功体育产品的详细信息，包括价格、特点、库存等。参数：product_id（产品ID）"
            ),
            FunctionTool.from_defaults(
                fn=self.search_products,
                name="search_products",
                description="根据关键词搜索轻功体育产品。参数：keyword（搜索关键词），category（可选，产品分类）"
            )
        ]

    def _get_order_tools(self) -> List[FunctionTool]:
        """获取订单管理工具（简化实现）"""
        return [
            FunctionTool.from_defaults(
                fn=self.get_all_orders,
                name="get_all_orders",
                description="查询客户在轻功体育的订单列表。当客户询问'我的订单'、'订单查询'时使用。"
            ),
            FunctionTool.from_defaults(
                fn=self.get_order_details,
                name="get_order_details",
                description="查询轻功体育订单的详细信息。参数：order_id（订单号）"
            ),
            FunctionTool.from_defaults(
                fn=self.get_order_logistics,
                name="get_order_logistics",
                description="查询轻功体育订单的物流信息。参数：order_id（订单号）"
            )
        ]

    def _get_commerce_tools(self) -> List[FunctionTool]:
        """获取商务交易工具（简化实现）"""
        return [
            FunctionTool.from_defaults(
                fn=self.send_item_card,
                name="send_item_card",
                description="发送轻功体育商品卡片给客户。参数：product_ids（产品ID列表）"
            ),
            FunctionTool.from_defaults(
                fn=self.send_purchase_link,
                name="send_purchase_link",
                description="发送轻功体育商品的购买链接。参数：product_id（产品ID）"
            )
        ]

    def _get_coupon_tools(self) -> List[FunctionTool]:
        """获取优惠券工具（简化实现）"""
        return [
            FunctionTool.from_defaults(
                fn=self.get_shop_coupons,
                name="get_shop_coupons",
                description="获取轻功体育店铺的所有可用优惠券"
            ),
            FunctionTool.from_defaults(
                fn=self.send_coupon,
                name="send_coupon",
                description="发送轻功体育优惠券给客户。参数：coupon_id（优惠券ID）"
            )
        ]

    # 产品管理工具实现
    def get_product_list(self) -> str:
        """获取轻功体育产品列表"""
        try:
            logger.info("🏃 [轻功体育] 获取产品列表")

            result = "轻功体育产品清单：\n\n"

            categories = {}
            for product in self.products:
                category = product['category']
                if category not in categories:
                    categories[category] = []
                categories[category].append(product)

            for category, products in categories.items():
                result += f"【{category}】\n"
                for product in products:
                    result += f"• {product['name']} (ID: {product['id']}) - ¥{product['price']}\n"
                    result += f"  {product['description'][:50]}...\n"
                result += "\n"

            result += "如需了解具体产品详情，请告诉我产品ID或名称！"

            logger.info(f"✅ 返回 {len(self.products)} 个轻功体育产品")
            return result

        except Exception as e:
            logger.error(f"❌ 获取轻功体育产品列表失败: {e}")
            return "抱歉，暂时无法获取产品列表，请稍后再试。"

    def get_product_details(self, product_id: str) -> str:
        """获取产品详情"""
        try:
            logger.info(f"🏃 [轻功体育] 获取产品详情: {product_id}")

            product = None
            for p in self.products:
                if p['id'] == product_id:
                    product = p
                    break

            if not product:
                return f"抱歉，没有找到ID为 {product_id} 的产品。"

            result = f"【{product['name']}】详细信息：\n\n"
            result += f"🏷️ 产品ID: {product['id']}\n"
            result += f"📂 分类: {product['category']}\n"
            result += f"💰 价格: ¥{product['price']}\n"
            result += f"📝 描述: {product['description']}\n"
            result += f"✨ 特点: {', '.join(product['features'])}\n"
            result += f"📦 库存: {product['stock']} 件\n"

            if 'sizes' in product:
                result += f"📏 可选尺寸: {', '.join(product['sizes'])}\n"
            if 'weights' in product:
                result += f"⚖️ 可选重量: {', '.join(product['weights'])}\n"
            if 'colors' in product:
                result += f"🎨 可选颜色: {', '.join(product['colors'])}\n"

            result += f"\n🏪 品牌: {product['brand']}"
            result += f"\n\n需要购买或了解更多信息吗？"

            return result

        except Exception as e:
            logger.error(f"❌ 获取产品详情失败: {e}")
            return "抱歉，获取产品详情时出现错误，请稍后再试。"

    def search_products(self, keyword: str, category: Optional[str] = None) -> str:
        """搜索产品"""
        try:
            logger.info(f"🏃 [轻功体育] 搜索产品: {keyword}, 分类: {category}")

            results = []
            keyword_lower = keyword.lower()

            for product in self.products:
                # 检查分类过滤
                if category and product['category'] != category:
                    continue

                # 检查关键词匹配
                if (keyword_lower in product['name'].lower() or
                    keyword_lower in product['description'].lower() or
                    keyword_lower in product['category'].lower() or
                    any(keyword_lower in feature.lower() for feature in product['features'])):
                    results.append(product)

            if not results:
                return f"抱歉，没有找到与 '{keyword}' 相关的产品。"

            result = f"找到 {len(results)} 个与 '{keyword}' 相关的产品：\n\n"

            for product in results[:5]:  # 最多显示5个结果
                result += f"• {product['name']} (ID: {product['id']})\n"
                result += f"  分类: {product['category']} | 价格: ¥{product['price']}\n"
                result += f"  {product['description'][:60]}...\n\n"

            if len(results) > 5:
                result += f"还有 {len(results) - 5} 个相关产品，需要查看更多吗？"

            return result

        except Exception as e:
            logger.error(f"❌ 搜索产品失败: {e}")
            return "抱歉，搜索产品时出现错误，请稍后再试。"

    # 订单管理工具实现（调用千牛API）
    def get_all_orders(self) -> str:
        """获取客户订单列表（调用千牛API）"""
        logger.info("🏃 [轻功体育] 调用千牛API获取订单列表")
        return self.order_tools.get_all_orders()

    def get_order_details(self, order_id: str) -> str:
        """获取订单详情（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API获取订单详情: {order_id}")
        return self.order_tools.get_order_details(order_id)

    def get_order_logistics(self, order_id: str) -> str:
        """获取物流信息（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API获取物流信息: {order_id}")
        return self.order_tools.get_order_logistics(order_id)

    # 商务交易工具实现（调用千牛API）
    def send_item_card(self, product_ids: List[str]) -> str:
        """发送商品卡片（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API发送商品卡片: {product_ids}")
        return self.commerce_tools.send_item_card(product_ids)

    def send_purchase_link(self, product_id: str) -> str:
        """发送购买链接（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API发送购买链接: {product_id}")
        return self.commerce_tools.send_purchase_link(product_id)

    # 优惠券工具实现（调用千牛API）
    def get_shop_coupons(self) -> str:
        """获取店铺优惠券（调用千牛API）"""
        logger.info("🏃 [轻功体育] 调用千牛API获取店铺优惠券")
        return self.coupon_tools.get_shop_coupons()

    def send_coupon(self, coupon_id: str) -> str:
        """发送优惠券（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API发送优惠券: {coupon_id}")
        return self.coupon_tools.send_coupon(coupon_id)
