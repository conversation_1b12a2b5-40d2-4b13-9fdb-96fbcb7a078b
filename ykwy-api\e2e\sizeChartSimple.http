### 创建简单尺码表
POST http://localhost:3000/v1/size-chart-simple
Content-Type: application/json

{
  "name": "测试尺码表",
  "sizeRange": "成人体重(70.0kg～90.0kg),成人身高(160.0cm～180.0cm)",
  "sizeValue": "S, M, L, XL"
}

### 获取简单尺码表列表 (首次查询会缓存结果)
GET http://localhost:3000/v1/size-chart-simple

### 获取简单尺码表列表 (第二次查询会从缓存返回，速度更快)
GET http://localhost:3000/v1/size-chart-simple

### 根据ID获取简单尺码表 (需要替换为实际ID)
GET http://localhost:3000/v1/size-chart-simple/{{sizeChartSimpleId}}

### 更新简单尺码表 (需要替换为实际ID)
PUT http://localhost:3000/v1/size-chart-simple/{{sizeChartSimpleId}}
Content-Type: application/json

{
  "name": "更新后的尺码表",
  "sizeRange": "成人体重(80.0kg～100.0kg),成人身高(170.0cm～190.0cm)",
  "sizeValue": "M, L, XL, XXL"
}

### 删除简单尺码表 (需要替换为实际ID)
DELETE http://localhost:3000/v1/size-chart-simple/{{sizeChartSimpleId}}

### 检查名称是否已存在 - 新名称
GET {{base_url}}/api/sizechart-simple/check-name?name=新尺码表名称
Content-Type: application/json

### 检查名称是否已存在 - 排除指定ID
GET {{base_url}}/api/sizechart-simple/check-name?name=现有名称&excludeId=uuid-here
Content-Type: application/json

### 创建简单尺码表 - 首次创建
POST {{base_url}}/api/sizechart-simple
Content-Type: application/json

{
  "name": "Upsert测试尺码表",
  "sizeRange": "XS-XL",
  "sizeValue": "XS,S,M,L,XL"
}

### 创建简单尺码表 - 相同name，会自动更新现有记录
POST {{base_url}}/api/sizechart-simple
Content-Type: application/json

{
  "name": "Upsert测试尺码表",
  "sizeRange": "XXS-XXL",
  "sizeValue": "XXS,XS,S,M,L,XL,XXL"
}

### 创建简单尺码表 - 不同name，会创建新记录
POST {{base_url}}/api/sizechart-simple
Content-Type: application/json

{
  "name": "另一个尺码表",
  "sizeRange": "S-L",
  "sizeValue": "S,M,L"
}

### 验证upsert结果 - 查看列表
GET {{base_url}}/api/sizechart-simple
Content-Type: application/json 