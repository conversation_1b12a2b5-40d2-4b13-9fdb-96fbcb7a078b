import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { getCurrentUser, getUserOrganizationId } from '../lib/auth-utils';
import { prisma } from '../lib/db';
import { createErrorResponse, createResponse, handleError } from '../lib/utils';

const app = new Hono();

// 验证Schema
const OrganizationIdSchema = z.object({
  organizationId: z.string().uuid('无效的组织ID'),
});

/**
 * GET /brands/organization/:organizationId - 获取组织的品牌列表
 */
app.get('/organization/:organizationId', zValidator('param', OrganizationIdSchema), async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    const userOrganizationId = await getUserOrganizationId(user.id);
    if (!userOrganizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    const { organizationId } = c.req.valid('param');

    // 验证用户是否有权限访问该组织
    if (userOrganizationId !== organizationId) {
      return c.json(createErrorResponse('无权限访问该组织'), 403);
    }

    const brands = await prisma.brand.findMany({
      where: {
        organizationId,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        logo: true,
        website: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return c.json(createResponse(brands, '获取品牌列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /brands - 获取当前用户组织的品牌列表
 */
app.get('/', async (c) => {
  try {
    const user = await getCurrentUser(c);
    if (!user) {
      return c.json(createErrorResponse('未登录'), 401);
    }

    const organizationId = await getUserOrganizationId(user.id);
    if (!organizationId) {
      return c.json(createErrorResponse('缺少组织信息'), 401);
    }

    const brands = await prisma.brand.findMany({
      where: {
        organizationId,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        logo: true,
        website: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return c.json(createResponse(brands, '获取品牌列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

export default app;
