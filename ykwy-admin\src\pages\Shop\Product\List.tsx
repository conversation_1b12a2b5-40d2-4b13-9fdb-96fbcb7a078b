import BatchImportKnowledgeModal from '@/components/Product/BatchImportKnowledgeModal';
import BatchImportProductModal from '@/components/Product/BatchImportProductModal';
import BindSizeTableModal from '@/components/Product/BindSizeTableModal';
import ExportProductAttrModal from '@/components/Product/ExportProductAttrModal';
import KnowledgeDownloadDrawer from '@/components/Product/KnowledgeDownloadDrawer';
import ProductKnowledgeModal from '@/components/Product/ProductKnowledgeModal';
import ProductTagSelectorModal from '@/components/Product/ProductTagSelectorModal';
import {
  DownloadOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  Button,
  Dropdown,
  Input,
  Menu,
  Pagination,
  Select,
  Space,
  Table,
  Tag,
} from 'antd';
import React, { useState } from 'react';
import TagManagement from './TagManagement';

interface TagType {
  id: string;
  name: string;
}

interface SizeTableType {
  id: string;
  name: string;
  bindCount: number;
  type?: 'normal' | 'complex';
}

const mockData = [
  {
    key: '1',
    img: 'https://img12.360buyimg.com/n3/jfs/t1/312068/23/5450/83959/683975e3F2af05fff/fd738d7840aa0a63.jpg',
    title:
      'QINKUNG轻功圆领训练跑步背心马拉松运动户外夏季透气速干无袖T女款背心 芊巧绿 S',
    status: '已上架',
    id: '10159008211415',
    updateTime: '2025-05-30 19:58:43',
    consult: 4,
    knowledge: '-',
    scene: 0,
    intent: 0,
    custom: 0,
    sizeTable: true,
  },
  {
    key: '2',
    img: 'https://img12.360buyimg.com/n3/jfs/t1/312068/23/5450/83959/683975e3F2af05fff/fd738d7840aa0a63.jpg',
    title:
      'QINKUNG轻功圆领训练跑步背心马拉松运动户外夏季透气速干无袖T女款背心 芊巧绿 L',
    status: '已上架',
    id: '10159008211417',
    updateTime: '2025-05-30 19:58:43',
    consult: 0,
    knowledge: '-',
    scene: 0,
    intent: 0,
    custom: 0,
    sizeTable: true,
  },
  {
    key: '3',
    img: 'https://img12.360buyimg.com/n3/jfs/t1/312068/23/5450/83959/683975e3F2af05fff/fd738d7840aa0a63.jpg',
    title:
      'QINKUNG轻功圆领训练跑步背心马拉松运动户外夏季透气速干无袖T女款背心 芊巧绿 M',
    status: '已上架',
    id: '10159008211418',
    updateTime: '2025-05-30 19:58:43',
    consult: 0,
    knowledge: '-',
    scene: 0,
    intent: 0,
    custom: 0,
    sizeTable: true,
  },
];

const List: React.FC = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [currentProduct, setCurrentProduct] = useState<any>(null);
  const [tagModalOpen, setTagModalOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<TagType[]>([]);
  const [sizeTableModalOpen, setSizeTableModalOpen] = useState(false);
  const [productModalOpen, setProductModalOpen] = useState(false);
  const [knowledgeModalOpen, setKnowledgeModalOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedSizeTable, setSelectedSizeTable] =
    useState<SizeTableType | null>(null);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [showTagManagement, setShowTagManagement] = useState(false);

  const allTags: TagType[] = [
    { id: '1', name: '新品' },
    { id: '2', name: '热卖' },
    { id: '3', name: '限时折扣' },
  ];

  const sizeTables: SizeTableType[] = [
    { id: '1', name: '女士 宽松', bindCount: 10 },
    { id: '2', name: '男士 宽松', bindCount: 513 },
    { id: '3', name: '男士 修身/合身版', bindCount: 1030 },
    { id: '4', name: '女士 修身/合身版', bindCount: 446 },
    { id: '5', name: '男女情侣装合身版', bindCount: 0, type: 'complex' },
    { id: '6', name: '情侣/宽松版', bindCount: 0, type: 'complex' },
  ];

  const menu = (
    <Menu
      style={{ minWidth: 140 }}
      onClick={({ key }) => {
        if (key === 'product') setProductModalOpen(true);
        if (key === 'knowledge') setKnowledgeModalOpen(true);
        setDropdownOpen(false);
      }}
      data-oid="nw2-z7x"
    >
      <Menu.Item
        key="product"
        style={{
          fontSize: 14,
          color: '#222',
          lineHeight: '22px',
          padding: '8px 20px',
        }}
        data-oid="i33u3.y"
      >
        商品列表
      </Menu.Item>
      <Menu.Item
        key="knowledge"
        style={{
          fontSize: 14,
          color: '#222',
          lineHeight: '22px',
          padding: '8px 20px',
        }}
        data-oid="yxv1-9m"
      >
        商品知识
      </Menu.Item>
    </Menu>
  );

  const exportMenu = (
    <Menu
      style={{ minWidth: 120 }}
      items={[
        {
          key: 'product',
          label: (
            <span style={{ fontSize: 14, color: '#222' }} data-oid="_.h2dqg">
              商品属性
            </span>
          ),

          onClick: () => setExportModalVisible(true),
        },
        {
          key: 'knowledge',
          label: (
            <span style={{ fontSize: 14, color: '#222' }} data-oid="d.u.1bt">
              商品知识
            </span>
          ),

          onClick: () => setDrawerVisible(true),
        },
      ]}
      data-oid="r75p45:"
    />
  );

  const handleAddKnowledge = (record: any) => {
    setCurrentProduct({
      imgUrl: record.img,
      name: record.title,
      desc: `商品ID: ${record.id}`,
    });
    setModalOpen(true);
  };

  const handleModalOk = () => {
    // 这里可以处理表单提交逻辑
    setModalOpen(false);
  };

  const handleModalCancel = () => {
    setModalOpen(false);
  };

  const columns = [
    {
      title: '商品',
      dataIndex: 'title',
      width: 400,
      render: (_: any, record: any) => (
        <Space align="start" data-oid="4m6u2lw">
          <img
            src={record.img}
            alt="商品"
            style={{ width: 48, height: 48, borderRadius: 4 }}
            data-oid="bc3q2lo"
          />

          <div data-oid="6gzd9.9">
            <div style={{ fontWeight: 500 }} data-oid="o_1dy7_">
              {record.title}
            </div>
            <div style={{ margin: '4px 0' }} data-oid="7nz47qc">
              <span style={{ color: '#999' }} data-oid="77ytzgo">
                商品标签：
              </span>
              <Tag color="orange" data-oid="j1054bz">
                {record.status}
              </Tag>
              <Tag data-oid="j7hka.6">非预售</Tag>
              <Button
                size="small"
                icon={<PlusOutlined data-oid="_006_.v" />}
                style={{ marginLeft: 8 }}
                onClick={() => setTagModalOpen(true)}
                data-oid="3mbitdc"
              />
            </div>
            <div style={{ color: '#999', fontSize: 12 }} data-oid="3dybxw_">
              商品ID: {record.id}
              <br data-oid="z.:lirk" />
              更新时间: {record.updateTime}
              <br data-oid=".d3y-zq" />
              近30日咨询量: {record.consult}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '商品知识',
      dataIndex: 'knowledge',
      width: 120,
      render: (_: any, record: any) => (
        <Button
          size="small"
          icon={<PlusOutlined data-oid="dlrwt4w" />}
          onClick={() => handleAddKnowledge(record)}
          data-oid="l:9tg1n"
        />
      ),
    },
    {
      title: '关联问答场景',
      dataIndex: 'scene',
      width: 180,
      render: () => (
        <div data-oid="b3i08e_">
          <a style={{ display: 'block' }} data-oid="mmudvjm">
            行业场景(0)
          </a>
          <a style={{ display: 'block' }} data-oid="wu2r7:0">
            精准意图(0)
          </a>
          <a style={{ display: 'block' }} data-oid="7vstd.l">
            自定义问题(0)
          </a>
        </div>
      ),
    },
    {
      title: '尺码表',
      dataIndex: 'sizeTable',
      width: 100,
      render: () => (
        <Button
          size="small"
          onClick={() => setSizeTableModalOpen(true)}
          data-oid="4be9i5a"
        >
          绑定尺码表
        </Button>
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      render: () => <a data-oid="j9s::cw">详情</a>,
    },
  ];

  if (showTagManagement) {
    return (
      <TagManagement
        onBack={() => setShowTagManagement(false)}
        data-oid="9.hzt_1"
      />
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen p-6" data-oid="vo7fdfh">
      <div className="bg-white rounded-lg p-6" data-oid="gturgyf">
        <div className="font-semibold text-lg mb-4" data-oid="6lea9:u">
          商品列表
        </div>
        {/* 顶部操作区 */}
        <div className="flex justify-end gap-2 mb-3" data-oid="gs99wfd">
          <Button data-oid="wczy2ry">批量绑定尺码表</Button>
          <Button data-oid="ncp0u48">同步商品</Button>
          <Dropdown
            overlay={menu}
            trigger={['hover']}
            placement="bottomLeft"
            arrow
            open={dropdownOpen}
            onOpenChange={setDropdownOpen}
            data-oid="i9d_sln"
          >
            <Button
              type="default"
              style={{
                fontSize: 14,
                color: '#222',
                borderColor: '#d9d9d9',
                borderRadius: 8,
                display: 'inline-flex',
                alignItems: 'center',
              }}
              data-oid="s6f6ea5"
            >
              <ImportOutlined style={{ marginRight: 4 }} data-oid="rm2-xbr" />
              批量导入
            </Button>
          </Dropdown>

          <BatchImportProductModal
            open={productModalOpen}
            onCancel={() => setProductModalOpen(false)}
            onOk={() => setProductModalOpen(false)}
            data-oid="f96afo3"
          />

          <BatchImportKnowledgeModal
            open={knowledgeModalOpen}
            onCancel={() => setKnowledgeModalOpen(false)}
            onOk={() => setKnowledgeModalOpen(false)}
            data-oid="dan:uie"
          />

          <Dropdown
            overlay={exportMenu}
            trigger={['hover']}
            placement="bottomLeft"
            arrow
            data-oid="ckqn-5-"
          >
            <Button
              type="default"
              style={{
                fontSize: 14,
                color: '#222',
                borderColor: '#d9d9d9',
                borderRadius: 8,
                display: 'inline-flex',
                alignItems: 'center',
              }}
              data-oid="9kxv-2e"
            >
              <DownloadOutlined style={{ marginRight: 4 }} data-oid="km598bh" />
              批量导出
            </Button>
          </Dropdown>
          <Button onClick={() => setShowTagManagement(true)} data-oid="ktn7cdq">
            商品标签管理
          </Button>
        </div>
        {/* 筛选区 */}
        <div className="flex gap-2 mb-3" data-oid="9m.:dke">
          <Button data-oid="xlgdx-o">筛选</Button>
          <Select
            className="w-36"
            placeholder="商品名称"
            allowClear
            data-oid=":.9meso"
          />

          <Input
            className="w-52"
            placeholder="请输入进行回车"
            suffix={<SearchOutlined data-oid="x12nk71" />}
            data-oid=":39at97"
          />

          <Select
            className="w-36"
            placeholder="全部商品"
            allowClear
            data-oid="thnv8ke"
          />
        </div>
        {/* 条件标签区 */}
        <div
          className="bg-yellow-50 border border-yellow-300 rounded-md p-2 mb-3"
          data-oid="3w9s7t1"
        >
          <span className="text-yellow-500 mr-2" data-oid="i022owm">
            筛选条件：
          </span>
          <Tag closable data-oid="jr0yzsn">
            商品状态: 已上架
          </Tag>
          <a className="ml-4" data-oid="yjh7f4n">
            清空
          </a>
        </div>
        {/* 表格区 */}
        <Table
          columns={columns}
          dataSource={mockData}
          pagination={false}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          bordered={false}
          className="bg-white"
          scroll={{ y: 'calc(100vh - 300px)' }}
          data-oid="xn9n6gs"
        />

        {/* 分页区 */}
        <div
          className="flex justify-between items-center mt-4"
          data-oid="9drnpqq"
        >
          <span data-oid="8r6k5ul">共 3559 条相关记录</span>
          <Pagination
            total={3559}
            current={1}
            pageSize={10}
            showSizeChanger
            showQuickJumper
            className="m-0"
            data-oid="6bol2.0"
          />
        </div>
        <ProductKnowledgeModal
          open={modalOpen}
          onCancel={handleModalCancel}
          onOk={handleModalOk}
          product={currentProduct}
          data-oid="yjcev8d"
        />

        <ProductTagSelectorModal
          open={tagModalOpen}
          onOk={(tags) => {
            setSelectedTags(tags);
            setTagModalOpen(false);
          }}
          onCancel={() => setTagModalOpen(false)}
          allTags={allTags}
          selectedTags={selectedTags}
          data-oid="s2_p7av"
        />

        <BindSizeTableModal
          open={sizeTableModalOpen}
          onOk={(table) => {
            setSelectedSizeTable(table);
            setSizeTableModalOpen(false);
          }}
          onCancel={() => setSizeTableModalOpen(false)}
          sizeTables={sizeTables}
          selectedId={selectedSizeTable?.id}
          data-oid="nza0h.o"
        />

        <ExportProductAttrModal
          visible={exportModalVisible}
          onCancel={() => setExportModalVisible(false)}
          onExport={() => {
            setExportModalVisible(false);
            setDrawerVisible(true);
          }}
          data-oid="7x3:x.b"
        />

        <KnowledgeDownloadDrawer
          visible={drawerVisible}
          onClose={() => setDrawerVisible(false)}
          data-oid="sj69j:p"
        />
      </div>
    </div>
  );
};

export default List;
