import { logger } from '../lib/logger';
import type { PlatformAdapter, StandardMessage } from '../types/platform';

/**
 * 千牛平台适配器
 * 基于现有千牛桥接代码实现消息格式转换
 */
export class QianniuAdapter implements PlatformAdapter {
  /**
   * 验证连接数据
   */
  validateConnection(connectionData: Record<string, unknown>): boolean {
    // 验证千牛连接必需的字段
    return !!(connectionData['clientId'] && connectionData['teamId'] && connectionData['organizationId']);
  }

  /**
   * 将千牛原始消息转换为标准格式 (统一使用 type + response 格式)
   */
  transformInbound(platformData: Record<string, unknown>, rawMessage: Record<string, unknown>): StandardMessage | null {
    try {
      logger.debug('千牛原始消息接收', {
        rawMessage: JSON.stringify(rawMessage, null, 2),
      });

      const messageType = rawMessage['type'];
      logger.debug('千牛消息类型', { messageType });

      switch (messageType) {
        case 'receiveNewMsg':
          return this.transformNewMessage(platformData, rawMessage);
        case 'onConversationChange':
          // 跳过会话切换消息，不再生成系统消息
          logger.debug('跳过会话切换消息处理');
          return null;
        case 'onShopRobotReceriveNewMsgs':
          // 🔧 修复：通知消息只用于触发，不保存到数据库
          logger.debug('跳过通知消息处理 - 等待receiveNewMsg获取完整内容');
          return null;
        default:
          logger.warn('不支持的千牛消息类型', { messageType });
          return null;
      }
    } catch (error) {
      logger.error('千牛入站消息转换失败', {}, error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }

  /**
   * 将标准格式消息转换为千牛格式
   */
  transformOutbound(_platformData: Record<string, unknown>, message: Record<string, unknown>): Record<string, unknown> | null {
    try {
      // 基于TCP Socket的发送格式
      return this.buildTcpMessage(message);
    } catch (error) {
      logger.error('千牛出站消息转换失败', {}, error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }

  /**
   * 处理新消息 - 添加消息方向过滤
   */
  private transformNewMessage(platform: Record<string, unknown>, rawMessage: Record<string, unknown>): StandardMessage | null {
    try {
      logger.debug('处理千牛新消息');

      const responseData = JSON.parse(rawMessage['response'] as string);
      const messages = responseData.result;

      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        logger.warn('千牛消息结果数组中未找到消息数据');
        return null;
      }

      logger.debug('千牛消息数组信息', {
        totalMessages: messages.length,
      });

      // 🔧 修复：遍历所有消息，找到客户发送的消息
      // 因为千牛可能会在一个响应中包含多条消息（客服+客户），我们只处理客户的消息
      for (let i = 0; i < messages.length; i++) {
        const messageData = messages[i];

        logger.debug('检查消息', {
          messageIndex: i,
          messageId: messageData.mcode?.messageId,
          fromNick: messageData.fromid?.nick,
          toNick: messageData.toid?.nick,
          loginNick: messageData.loginid?.nick,
          summary: messageData.summary,
        });

        // 🔧 修复：添加消息方向过滤（参考开源项目逻辑）
        const fromNick = messageData.fromid?.nick; // 发送方
        const toNick = messageData.toid?.nick; // 接收方
        const loginNick = messageData.loginid?.nick; // 当前登录的客服

        logger.debug('千牛消息方向分析', {
          fromNick,
          toNick,
          loginNick,
          messageDirection: 'analyzing',
        });

        // 参考开源项目：只处理客户发给客服的消息
        // if (m.fromid.nick != _seller.Nick && m.toid.nick == _seller.Nick)
        if (fromNick === loginNick) {
          logger.debug('跳过客服发送的消息，避免重复保存', { fromNick, loginNick });
          continue; // 继续检查下一条消息
        }

        if (toNick !== loginNick) {
          logger.debug('跳过非当前客服接收的消息', { toNick, loginNick });
          continue; // 继续检查下一条消息
        }

        // 提取消息内容
        const content = messageData.summary || messageData.originalData?.text || '新消息';

        // 过滤掉自动生成的系统消息，如"当前用户来自 商品详情页"
        if (content.includes('当前用户来自') || content.includes('来自商品详情页') || content.includes('来自店铺首页') || content.includes('来自搜索页')) {
          logger.debug('过滤自动生成的来源页面系统消息', { content });
          continue; // 跳过这条消息，继续检查下一条
        }

        logger.debug('确认为客户发给客服的消息，继续处理', { fromNick, toNick, loginNick });

        // 从实际日志结构提取数据
        // 重要：使用cCode作为senderId，这是千牛的标准客户ID格式（如：20705090.1-3492726745.1#11001@cntaobao）
        const conversationCode = messageData.cid?.ccode;
        const senderId = conversationCode || messageData.fromid?.nick || messageData.fromid?.targetId || 'unknown';
        const senderName = messageData.fromid?.display || messageData.fromid?.nick || '客户';

        logger.debug('提取千牛客户ID信息', {
          conversationCode,
          fromNick: messageData.fromid?.nick,
          senderId,
        });

        return {
          platformMessageId: messageData.mcode?.messageId || `msg_${Date.now()}`,
          senderId,
          senderName,
          senderType: 'customer',
          content,
          messageType: 'text',
          platformId: platform['id'] as string,
          platformType: platform['type'] as string,
          organizationId: platform['organizationId'] as string,
          timestamp: new Date(parseInt(messageData.sendTime) || Date.now()),
          metadata: {
            eventType: 'receiveNewMsg',
            messageData,
            conversationCode: messageData.cid?.ccode,
            messageDirection: 'customer_to_service', // 标记消息方向
          },
        };
      }

      // 如果遍历完所有消息都没有找到客户消息，返回null
      logger.debug('消息数组中未找到客户发送的消息');
      return null;
    } catch (error) {
      logger.error('千牛新消息解析失败', {}, error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }

  /**
   * 构建TCP消息格式
   * 适配参考项目的TCP Socket通信格式
   */
  private buildTcpMessage(message: Record<string, unknown>): Record<string, unknown> {
    const content = message['content'] as string;
    const metadata = message['metadata'] as Record<string, unknown> | undefined;
    const targetBuyerNick = metadata?.['targetBuyerNick'] as string | undefined;

    if (!targetBuyerNick) {
      throw new Error('Missing targetBuyerNick in message metadata');
    }

    // 构建TCP消息格式，参考项目的格式
    return {
      method: 'sendtext',
      id: targetBuyerNick, // 使用买家昵称作为ID
      text: content,
      keep: 'on',
    };
  }
}

// 导出单例
export const qianniuAdapter = new QianniuAdapter();
