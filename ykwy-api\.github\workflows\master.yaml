# https://github.com/effortless-innovations/deshan-api/actions
# https://tooltt.com/crontab/c/102.html

name: 易康无忧客服管理系统

on:
  push:
    branches:
      - master
      - 'releases/**'
    # schedule:
    # 每6分钟执行
    # - cron: '*/6 * * * *'
    # 每天的5:30执行命令
    # - cron: '30 5 * * *'
    # 每月10号的5:32分执行
    # - cron: '32 5 10 * *'
    # 每年的6月3日20点47分执行
    # - cron: '47 20 3 6 *'
  workflow_dispatch:

jobs:
  Deployment:
    runs-on: ubuntu-latest
    steps:
      - name: Record start time
        run: echo "START_TIME=$(date +%s)" >> $GITHUB_ENV

      #- name: Set up QEMU
      #  uses: docker/setup-qemu-action@v3

      #- name: Set up Docker Buildx
      #  uses: docker/setup-buildx-action@v3

      - name: Checkout the latest code
        uses: actions/checkout@v4

      - name: Install Bun runtime
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Run ESLint
        run: |
          bun install
          bun run lint

      - name: Run TypeScript Compiler
        run: |
          bun install
          bun run tsc

      - name: Cache Bun install cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.bun/install/cache
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lock') }}

      - name: Generate environment file
        shell: bash
        run: |
          echo ${{secrets.ENV_CONTENT}} | tee .env

      - name: Get npm version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@v1.3.1
        with:
          path: .

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and push docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          #platforms: linux/amd64,linux/arm64
          platforms: linux/amd64
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            "${{ secrets.DOCKERHUB_USERNAME }}/${{ github.event.repository.name }}:latest"
            "${{ secrets.DOCKERHUB_USERNAME }}/${{ github.event.repository.name }}:${{ steps.package-version.outputs.current-version }}"

      - name: Send WeChat Work failure notification
        if: failure()
        uses: chf007/action-wechat-work@master
        env:
          WECHAT_WORK_BOT_WEBHOOK: ${{ secrets.WECHAT_WORK_BOT_WEBHOOK }}
        with:
          msgtype: markdown
          content: |
            **Repository：** ${{ github.repository }}
            **Workflow：** ${{ github.workflow }}
            **Event：** ${{ github.event_name }}
            [View Details](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: Execute remote ssh commands
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ vars.REMOTE_HOST }}
          username: ${{ vars.REMOTE_USER }}
          key: ${{ secrets.REMOTE_SSH_KEY }}
          script: |
            cd ~/deploy/${{ github.event.repository.name }}
            bash up.sh

      - name: Calculate execution duration
        run: |
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - $START_TIME))
          echo "DURATION=$DURATION" >> $GITHUB_ENV

      - name: Send WeChat Work success notification
        uses: chf007/action-wechat-work@master
        env:
          WECHAT_WORK_BOT_WEBHOOK: ${{secrets.WECHAT_WORK_BOT_WEBHOOK}}
        with:
          msgtype: markdown
          content: "${{github.actor}} 刚刚把 ${{github.event.repository.name}} 项目部署到 ${{vars.REMOTE_HOST}} 服务器，最新版本：v${{ steps.package-version.outputs.current-version }}，操作状态：${{job.status}}，耗时：${{env.DURATION}} 秒，请各位留意！\n > <@田坤> 请抽空 [code review](${{github.event.compare}})，演示地址：[${{github.event.repository.name}}.wuyoutansuo.com](${{github.event.repository.name}}.wuyoutansuo.com)"
