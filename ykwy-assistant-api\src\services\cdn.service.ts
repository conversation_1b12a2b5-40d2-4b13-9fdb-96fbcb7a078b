/**
 * CDN服务 - MinIO集成
 * 自动推送生成的脚本到CDN
 */

import { S3Client } from 'bun';

import { logger } from '../lib/logger';

interface CDNUploadResult {
  cdnUrl: string;
  uploadTime: Date;
  size: number;
}

class CDNService {
  private s3: S3Client;
  private bucketName: string;
  private cdnBaseUrl: string;
  private accessKey: string;
  private secretKey: string;

  constructor() {
    // 从环境变量读取配置
    this.bucketName = Bun.env['MINIO_BUCKET_NAME'] || '';
    this.cdnBaseUrl = Bun.env['CDN_BASE_URL'] || '';
    this.accessKey = Bun.env['MINIO_ACCESS_KEY'] || '';
    this.secretKey = Bun.env['MINIO_SECRET_KEY'] || '';

    logger.info('初始化 CDN 服务', {
      service: 'MinIO/S3',
      endpoint: this.cdnBaseUrl,
      bucket: this.bucketName,
      hasAccessKey: !!this.accessKey,
      hasSecretKey: !!this.secretKey,
      accessKeyLength: this.accessKey ? this.accessKey.length : 0,
      secretKeyLength: this.secretKey ? this.secretKey.length : 0,
      initTime: new Date().toISOString(),
    });

    // 验证必要的环境变量
    if (!this.accessKey || !this.secretKey || !this.bucketName || !this.cdnBaseUrl) {
      const missingVars = [];
      if (!this.accessKey) missingVars.push('MINIO_ACCESS_KEY');
      if (!this.secretKey) missingVars.push('MINIO_SECRET_KEY');
      if (!this.bucketName) missingVars.push('MINIO_BUCKET_NAME');
      if (!this.cdnBaseUrl) missingVars.push('CDN_BASE_URL');

      logger.error('CDN 配置不完整，无法初始化服务', {
        missingVariables: missingVars,
        providedConfig: {
          hasAccessKey: !!this.accessKey,
          hasSecretKey: !!this.secretKey,
          hasBucketName: !!this.bucketName,
          hasCdnBaseUrl: !!this.cdnBaseUrl,
          bucketName: this.bucketName || 'NOT_SET',
          cdnBaseUrl: this.cdnBaseUrl || 'NOT_SET',
        },
        requiredVars: ['MINIO_ACCESS_KEY', 'MINIO_SECRET_KEY', 'MINIO_BUCKET_NAME', 'CDN_BASE_URL'],
        troubleshooting: {
          checkEnvFile: '检查 .env 文件是否包含所有必需的 CDN 配置',
          checkDeployment: '检查部署环境是否正确设置了环境变量',
          exampleConfig: 'CDN_BASE_URL=https://your-cdn.com, MINIO_BUCKET_NAME=your-bucket',
        },
      });
      throw new Error(`CDN配置不完整，缺少环境变量: ${missingVars.join(', ')}`);
    }

    try {
      this.s3 = new S3Client({
        accessKeyId: this.accessKey,
        secretAccessKey: this.secretKey,
        bucket: this.bucketName,
        endpoint: this.cdnBaseUrl,
      });

      logger.info('S3 客户端初始化成功', {
        endpoint: this.cdnBaseUrl,
        bucket: this.bucketName,
        clientCreated: true,
      });
    } catch (error) {
      logger.error(
        'S3 客户端初始化失败',
        {
          endpoint: this.cdnBaseUrl,
          bucket: this.bucketName,
          error: error instanceof Error ? error.message : String(error),
        },
        error instanceof Error ? error : new Error(String(error)),
      );
      throw new Error(`S3 客户端初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 上传脚本到CDN
   */
  async uploadScript(invitationId: string, scriptContent: string): Promise<CDNUploadResult> {
    const uploadStartTime = Date.now();
    const key = `${invitationId}.js`;
    const scriptSize = Buffer.byteLength(scriptContent, 'utf8');

    logger.info('开始 CDN 脚本上传流程', {
      invitationId,
      fileName: key,
      scriptSize,
      scriptSizeKB: Math.round((scriptSize / 1024) * 100) / 100,
      targetBucket: this.bucketName,
      targetEndpoint: this.cdnBaseUrl,
      uploadStartTime: new Date(uploadStartTime).toISOString(),
    });

    // 验证脚本内容
    if (!scriptContent || scriptContent.trim().length === 0) {
      logger.error('脚本内容为空，无法上传', {
        invitationId,
        scriptLength: scriptContent?.length || 0,
      });
      throw new Error('脚本内容为空，无法上传到 CDN');
    }

    // 验证脚本内容是否包含必要的标识
    const hasInvitationId = scriptContent.includes(invitationId);
    const hasWebSocketUrl = scriptContent.includes('ws://') || scriptContent.includes('wss://');
    const hasInjectionCode = scriptContent.includes('千牛客户端注入脚本');

    logger.debug('脚本内容验证', {
      invitationId,
      hasInvitationId,
      hasWebSocketUrl,
      hasInjectionCode,
      scriptPreview: scriptContent.substring(0, 200) + '...',
    });

    if (!hasInvitationId || !hasInjectionCode) {
      logger.warn('脚本内容可能不完整', {
        invitationId,
        hasInvitationId,
        hasWebSocketUrl,
        hasInjectionCode,
        recommendation: '检查脚本生成逻辑是否正确',
      });
    }

    try {
      // 预检查 S3 客户端状态
      logger.debug('预检查 S3 客户端配置', {
        invitationId,
        hasS3Client: !!this.s3,
        bucket: this.bucketName,
        endpoint: this.cdnBaseUrl,
      });

      const fileData = Buffer.from(scriptContent, 'utf8');
      const actualSize = fileData.length;

      logger.info('准备上传文件到 S3', {
        invitationId,
        bucket: this.bucketName,
        key,
        contentType: 'application/javascript',
        size: actualSize,
        sizeKB: Math.round((actualSize / 1024) * 100) / 100,
        endpoint: this.cdnBaseUrl,
        uploadMethod: 's3.write',
      });

      // 记录上传前的时间戳
      const s3UploadStartTime = Date.now();
      logger.debug('开始 S3 写入操作', {
        invitationId,
        s3UploadStartTime: new Date(s3UploadStartTime).toISOString(),
      });

      // 执行 S3 上传
      const result = await this.s3.write(key, fileData, {});

      const s3UploadDuration = Date.now() - s3UploadStartTime;
      logger.info('S3 写入操作完成', {
        invitationId,
        s3Result: result,
        s3UploadDuration: `${s3UploadDuration}ms`,
        resultType: typeof result,
        resultValue: String(result),
      });

      // 生成公开访问URL
      const cdnUrl = this.getPublicUrl(invitationId);
      const totalUploadDuration = Date.now() - uploadStartTime;

      logger.info('CDN 脚本上传成功', {
        invitationId,
        cdnUrl,
        size: actualSize,
        sizeKB: Math.round((actualSize / 1024) * 100) / 100,
        s3Location: result,
        s3UploadDuration: `${s3UploadDuration}ms`,
        totalUploadDuration: `${totalUploadDuration}ms`,
        accessType: 'public',
        expires: 'never',
        uploadCompletedAt: new Date().toISOString(),
        nextSteps: ['脚本已可通过 CDN URL 访问', '建议验证 CDN URL 的可访问性', '检查脚本内容是否正确加载'],
      });

      // 可选：验证上传结果
      logger.debug('验证上传结果', {
        invitationId,
        expectedUrl: cdnUrl,
        recommendation: '可通过 HTTP 请求验证文件是否可访问',
      });

      return {
        cdnUrl,
        uploadTime: new Date(),
        size: actualSize,
      };
    } catch (error) {
      const uploadDuration = Date.now() - uploadStartTime;
      const errorDetails = {
        invitationId,
        bucket: this.bucketName,
        key,
        endpoint: this.cdnBaseUrl,
        uploadDuration: `${uploadDuration}ms`,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        scriptSize,
        troubleshooting: {
          checkCredentials: '验证 MINIO_ACCESS_KEY 和 MINIO_SECRET_KEY 是否正确',
          checkEndpoint: '验证 CDN_BASE_URL 是否可访问',
          checkBucket: '验证 MINIO_BUCKET_NAME 是否存在且有写入权限',
          checkNetwork: '检查网络连接和防火墙设置',
          checkS3Service: '验证 S3 兼容服务是否正常运行',
        },
      };

      logger.error('CDN 脚本上传失败', errorDetails, error instanceof Error ? error : new Error(String(error)));

      // 根据错误类型提供更具体的错误信息
      let userFriendlyMessage = 'CDN上传失败';
      if (error instanceof Error) {
        if (error.message.includes('Access Denied') || error.message.includes('403')) {
          userFriendlyMessage = 'CDN上传失败：访问权限不足，请检查访问密钥配置';
        } else if (error.message.includes('Network') || error.message.includes('timeout')) {
          userFriendlyMessage = 'CDN上传失败：网络连接问题，请检查网络设置';
        } else if (error.message.includes('Bucket') || error.message.includes('404')) {
          userFriendlyMessage = 'CDN上传失败：存储桶不存在或配置错误';
        } else {
          userFriendlyMessage = `CDN上传失败：${error.message}`;
        }
      }

      throw new Error(userFriendlyMessage);
    }
  }

  /**
   * 删除CDN上的脚本
   */
  async deleteScript(invitationId: string): Promise<void> {
    logger.info('正在删除CDN脚本', { invitationId, fileName: `${invitationId}.js` });

    try {
      const key = `${invitationId}.js`;
      // Bun S3Client 没有直接的删除方法，我们可以尝试覆盖为空文件或跳过删除
      logger.warn('MinIO 删除功能暂未实现，跳过删除', { key });
      logger.info('CDN脚本删除请求已处理', { invitationId, fileName: `${invitationId}.js` });
    } catch (error) {
      logger.error('CDN删除失败', { invitationId }, error instanceof Error ? error : new Error(String(error)));
      // 删除失败不抛出错误，因为文件可能已经不存在
    }
  }

  /**
   * 生成公开访问URL（无过期时间）
   */
  getPublicUrl(invitationId: string): string {
    const key = `${invitationId}.js`;
    return `${this.cdnBaseUrl}/${this.bucketName}/${key}`;
  }

  /**
   * 检查脚本是否存在于CDN
   */
  async scriptExists(invitationId: string): Promise<boolean> {
    try {
      const url = this.getPublicUrl(invitationId);
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      // 如果请求失败，说明文件不存在
      return false;
    }
  }

  /**
   * 获取脚本信息
   */
  async getScriptInfo(invitationId: string): Promise<{
    exists: boolean;
    size?: number;
    lastModified?: Date;
    cdnUrl: string;
  }> {
    const cdnUrl = `${this.cdnBaseUrl}/${this.bucketName}/${invitationId}.js`;

    try {
      const exists = await this.scriptExists(invitationId);

      if (exists) {
        return {
          exists: true,
          size: undefined, // MinIO 客户端暂不支持获取文件大小
          lastModified: undefined, // MinIO 客户端暂不支持获取修改时间
          cdnUrl,
        };
      } else {
        return { exists: false, cdnUrl };
      }
    } catch (error: unknown) {
      logger.error('获取CDN脚本信息失败', { invitationId }, error instanceof Error ? error : new Error(String(error)));
      return { exists: false, cdnUrl };
    }
  }

  /**
   * 批量清理过期脚本
   */
  async cleanupExpiredScripts(expiredInvitationIds: string[]): Promise<void> {
    logger.info('开始清理过期脚本', { count: expiredInvitationIds.length, invitationIds: expiredInvitationIds });

    const deletePromises = expiredInvitationIds.map((id) =>
      this.deleteScript(id).catch((error) => logger.error('删除脚本失败', { invitationId: id }, error instanceof Error ? error : new Error(String(error)))),
    );

    await Promise.all(deletePromises);
    logger.info('过期脚本清理完成', { count: expiredInvitationIds.length });
  }

  /**
   * 验证CDN配置
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.accessKey) {
      errors.push('缺少 MINIO_ACCESS_KEY 环境变量');
    }

    if (!this.secretKey) {
      errors.push('缺少 MINIO_SECRET_KEY 环境变量');
    }

    if (!this.bucketName) {
      errors.push('缺少 MINIO_BUCKET_NAME 环境变量');
    }

    if (!this.cdnBaseUrl) {
      errors.push('缺少 CDN_BASE_URL 环境变量');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 测试CDN连接
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const validation = this.validateConfig();
      if (!validation.valid) {
        return {
          success: false,
          message: `配置错误: ${validation.errors.join(', ')}`,
        };
      }

      logger.info('测试 MinIO 连接配置', {
        bucket: this.bucketName,
        endpoint: this.cdnBaseUrl,
      });

      // 测试上传一个小文件
      const testContent = `// CDN连接测试 - ${new Date().toISOString()}`;
      const testId = `test-${Date.now()}`;

      logger.info('测试文件上传', { testId });
      await this.uploadScript(testId, testContent);

      logger.info('测试文件上传成功', { testId });
      logger.info('清理测试文件', { testId });
      await this.deleteScript(testId);

      return {
        success: true,
        message: 'CDN连接测试成功',
      };
    } catch (error) {
      logger.error('CDN测试详细错误', {}, error instanceof Error ? error : new Error(String(error)));
      return {
        success: false,
        message: `CDN连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  }
}

export const cdnService = new CDNService();
