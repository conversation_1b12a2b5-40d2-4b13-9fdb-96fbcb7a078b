import { execSync } from 'node:child_process';
import os from 'node:os';

/**
 * 进程管理工具
 * 负责检测和管理千牛客户端进程
 */
class ProcessManager {
  /**
   * 检查千牛进程状态
   * @returns {Promise<Array>} 进程列表
   */
  static async checkQianniuProcess() {
    try {
      const command = os.platform() === 'win32' ? 'tasklist /FI "IMAGENAME eq AliWorkbench.exe" /FO CSV' : 'ps aux | grep -i aliworkbench';

      const result = execSync(command, { encoding: 'utf8' });

      if (os.platform() === 'win32') {
        const lines = result.split('\n').filter((line) => line.includes('AliWorkbench.exe'));
        return lines
          .map((line) => {
            const parts = line.split(',');
            if (parts.length >= 2) {
              return {
                name: parts[0].replace(/"/g, ''),
                pid: parts[1].replace(/"/g, ''),
              };
            }
            return null;
          })
          .filter(Boolean);
      } else {
        // Linux/Mac 处理逻辑
        const lines = result.split('\n').filter((line) => line.includes('aliworkbench') && !line.includes('grep'));
        return lines.map((line) => {
          const parts = line.trim().split(/\s+/);
          return {
            name: 'AliWorkbench',
            pid: parts[1],
          };
        });
      }
    } catch {
      return [];
    }
  }

  /**
   * 等待千牛进程状态改变
   * @param {boolean} shouldBeRunning - 期望的运行状态
   * @param {number} timeout - 超时时间（毫秒）
   * @returns {Promise<Array>} 进程列表
   */
  static async waitForQianniuState(shouldBeRunning, timeout = 30000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const processes = await this.checkQianniuProcess();
      const isRunning = processes.length > 0;

      if (isRunning === shouldBeRunning) {
        return processes;
      }

      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    throw new Error(`超时：千牛进程状态未达到预期（期望${shouldBeRunning ? '运行' : '关闭'}）`);
  }

  /**
   * 显示进程信息
   * @param {Array} processes - 进程列表
   */
  static displayProcesses(processes) {
    if (processes.length === 0) {
      console.log('未检测到千牛进程');
      return;
    }

    console.log('检测到的千牛进程:');
    processes.forEach((proc) => {
      console.log(`   - PID: ${proc.pid}, 名称: ${proc.name}`);
    });
  }

  /**
   * 持续监听千牛进程变化
   * @param {Function} onNewProcess - 新进程回调函数 (process) => void
   * @param {Function} onProcessExit - 进程退出回调函数 (process) => void
   * @param {number} interval - 检测间隔（毫秒）
   * @returns {Object} 监听器对象，包含 stop() 方法
   */
  static startProcessMonitor(onNewProcess, onProcessExit = null, interval = 2000) {
    let isRunning = true;
    const knownProcesses = new Map(); // PID -> process info

    const monitor = async () => {
      while (isRunning) {
        try {
          const currentProcesses = await this.checkQianniuProcess();
          const currentPids = new Set(currentProcesses.map((p) => p.pid));
          const knownPids = new Set(knownProcesses.keys());

          // 检测新进程
          for (const process of currentProcesses) {
            if (!knownPids.has(process.pid)) {
              console.log(`🆕 检测到新的千牛进程: PID ${process.pid}`);
              knownProcesses.set(process.pid, process);
              if (onNewProcess) {
                try {
                  await onNewProcess(process);
                } catch (error) {
                  console.error(`❌ 处理新进程时出错: ${error.message}`);
                }
              }
            }
          }

          // 检测退出的进程
          if (onProcessExit) {
            for (const [pid, process] of knownProcesses) {
              if (!currentPids.has(pid)) {
                console.log(`🚪 检测到千牛进程退出: PID ${pid}`);
                knownProcesses.delete(pid);
                try {
                  await onProcessExit(process);
                } catch (error) {
                  console.error(`❌ 处理进程退出时出错: ${error.message}`);
                }
              }
            }
          }

          // 等待下次检测
          await new Promise((resolve) => setTimeout(resolve, interval));
        } catch (error) {
          console.error(`❌ 进程监听出错: ${error.message}`);
          await new Promise((resolve) => setTimeout(resolve, interval));
        }
      }
    };

    // 启动监听
    monitor();

    // 返回控制对象
    return {
      stop: () => {
        isRunning = false;
        console.log('🛑 停止千牛进程监听');
      },
      getKnownProcesses: () => Array.from(knownProcesses.values()),
    };
  }

  /**
   * 等待至少有一个千牛进程启动
   * @param {number} timeout - 超时时间（毫秒）
   * @returns {Promise<Array>} 进程列表
   */
  static async waitForAnyQianniuProcess(timeout = 120000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const processes = await this.checkQianniuProcess();
      if (processes.length > 0) {
        return processes;
      }
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    throw new Error('超时：未检测到千牛进程启动');
  }
}

export default ProcessManager;
