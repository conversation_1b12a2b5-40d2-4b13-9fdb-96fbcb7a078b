import { downloadImportTemplate } from '@/services/tempProduct';
import { DownloadOutlined, InboxOutlined } from '@ant-design/icons';
import { Button, message, Modal, Radio, Upload } from 'antd';
import React, { useState } from 'react';

interface BatchImportProductModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (file: File | null, mode: string) => void;
}

const BatchImportProductModal: React.FC<BatchImportProductModalProps> = ({
  open,
  onCancel,
  onOk,
}) => {
  const [mode, setMode] = useState('current');
  const [file, setFile] = useState<File | null>(null);
  // const [uploading, setUploading] = useState(false);

  const handleUpload = (info: any) => {
    if (info.file.status === 'done' || info.file.status === 'success') {
      setFile(info.file.originFileObj || info.file);
      message.success('文件上传成功');
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
    }
  };

  const beforeUpload = (file: File) => {
    const isXlsx =
      file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.name.endsWith('.xlsx');
    if (!isXlsx) {
      message.error('仅支持 .xlsx 格式文件');
    }
    return isXlsx || Upload.LIST_IGNORE;
  };

  const handleOk = () => {
    onOk(file, mode);
  };

  return (
    <Modal
      open={open}
      title={
        <span className="font-medium" data-oid="qrwb.yh">
          导入商品
        </span>
      }
      onCancel={onCancel}
      onOk={handleOk}
      width={650}
      footer={[
        <Button
          key="cancel"
          onClick={onCancel}
          className="min-w-[72px]"
          data-oid="g_gkbnh"
        >
          取消
        </Button>,
        <Button
          key="ok"
          type="primary"
          disabled={!file}
          onClick={handleOk}
          className="min-w-[72px]"
          data-oid="g8:9zvj"
        >
          确定
        </Button>,
      ]}
      bodyStyle={{ padding: 0 }}
      style={{ top: 40 }}
      data-oid="y1reech"
    >
      <div className="px-6 pt-6 pb-0" data-oid="xdq-qh3">
        <Radio.Group
          value={mode}
          onChange={(e) => setMode(e.target.value)}
          className="mb-4 flex gap-8"
          data-oid="e:a5zv3"
        >
          <Radio value="current" data-oid="eh4w0u9">
            重复信息以本次导入为准
          </Radio>
          <Radio value="exist" data-oid="aqdissw">
            重复信息以已有信息为准
          </Radio>
        </Radio.Group>
        <Upload.Dragger
          name="file"
          accept=".xlsx"
          maxCount={1}
          beforeUpload={beforeUpload}
          showUploadList={file ? { showRemoveIcon: true } : false}
          onChange={handleUpload}
          onRemove={() => setFile(null)}
          className="bg-[#f8fafd] border border-dashed border-[#dbeafe] rounded-lg py-6 mb-4"
          data-oid="ij02e6w"
        >
          <div
            className="flex flex-col items-center justify-center"
            data-oid="q7t-tng"
          >
            <InboxOutlined
              className="text-2xl text-[#1677ff] mb-2"
              data-oid="32qxq0q"
            />

            <span className="text-[#1677ff] cursor-pointer" data-oid="xq2upu7">
              添加文件
            </span>
            <div className="text-xs text-[#888] mt-2" data-oid="lkwmy3k">
              点击选择文件，或将文件拖拽到这里上传。限一个文件，文件格式为.xlsx
            </div>
          </div>
        </Upload.Dragger>
        <div
          className="bg-[#f8fafd] rounded-lg p-4 text-xs text-[#666] mb-4"
          data-oid=":k84qb7"
        >
          <ol className="list-decimal pl-4 space-y-1" data-oid="xz_u03a">
            <li data-oid="sohvxe.">
              关联分类，请填写&quot;商品分类&quot;列。我们将按照分类名称查找关联、替换，该单元格为空则不绑定或解绑分类
            </li>
            <li data-oid="s6gapnk">
              关联标签，请填写&quot;商品标签&quot;列，不同的商品标签请使用中文逗号&quot;，&quot;进行分割，我们将给对应的商品关联上所有标签。注意：若后台不存在该标签，系统会自动新增。该单元格为空则会清空该商品关联的全部商品标签
            </li>
            <li data-oid="nb14st8">
              关联尺码表，请填写&quot;尺码表&quot;列。请按照尺码表名称-尺码类型填写，该单元格为空则不绑定或解绑尺码表
            </li>
            <li data-oid="ulghqlq">
              设置属性，请填写&quot;属性&quot;列。根据单元格名称查找或对应属性，注意平台属性名称为平台自定义/属性名称/自定义
            </li>
            <li data-oid="59mkl4n">支持.xlsx文件，文件名后缀需小写</li>
            <li data-oid=":0eequk">
              一次最多导入1万条数据，更多数据请分次导入
            </li>
            <li data-oid="oh89.ca">
              导入时根据商品基础信息去重，请勿删除该列信息
            </li>
            <li data-oid="j57m0cv">导入结果请在任务中心查看</li>
          </ol>
        </div>
        <div className="flex justify-end" data-oid="2aczeyz">
          <Button
            icon={<DownloadOutlined />}
            onClick={async () => {
              try {
                const { blob, filename } = await downloadImportTemplate();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                message.success('模板下载成功');
              } catch (error) {
                message.error('模板下载失败，请稍后重试');
              }
            }}
            className="inline-block border border-[#1677ff] text-[#1677ff] px-3 py-1 rounded hover:bg-[#e6f0ff] text-sm"
            data-oid="3x:iqvw"
          >
            下载模板
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default BatchImportProductModal;
