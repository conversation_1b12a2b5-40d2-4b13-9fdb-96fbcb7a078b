import React, { useCallback, useEffect, useRef, useState } from 'react';

interface UseAutoScrollOptions {
  /**
   * 是否启用自动滚动
   */
  enabled?: boolean;
  /**
   * 滚动行为
   */
  behavior?: 'auto' | 'smooth';
  /**
   * 距离底部多少像素时认为用户在底部
   */
  threshold?: number;
  /**
   * 依赖项数组，当依赖项变化时触发滚动
   */
  dependencies?: unknown[];
  /**
   * 滚动延迟（毫秒）
   */
  delay?: number;
}

interface UseAutoScrollReturn {
  /**
   * 滚动容器的ref
   */
  scrollRef: React.RefObject<HTMLDivElement | null>;
  /**
   * 手动滚动到底部
   */
  scrollToBottom: () => void;
  /**
   * 是否在底部
   */
  isAtBottom: boolean;
  /**
   * 是否应该自动滚动
   */
  shouldAutoScroll: boolean;
  /**
   * 设置是否应该自动滚动
   */
  setShouldAutoScroll: (should: boolean) => void;
}

export function useAutoScroll({ enabled = true, behavior = 'smooth', threshold = 100, dependencies = [], delay = 100 }: UseAutoScrollOptions = {}): UseAutoScrollReturn {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const scrollTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const isScrollingRef = useRef(false);

  // 检查是否在底部
  const checkIfAtBottom = useCallback(() => {
    const element = scrollRef.current;
    if (!element) return false;

    const { scrollTop, scrollHeight, clientHeight } = element;
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
    return distanceFromBottom <= threshold;
  }, [threshold]);

  // 滚动到底部
  const scrollToBottom = useCallback(
    (immediate = false) => {
      const element = scrollRef.current;
      if (!element) return;

      // 清除之前的延迟滚动
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      const doScroll = () => {
        isScrollingRef.current = true;
        element.scrollTo({
          top: element.scrollHeight,
          behavior: immediate ? 'auto' : behavior,
        });

        // 滚动完成后重置标志
        setTimeout(
          () => {
            isScrollingRef.current = false;
            setIsAtBottom(true);
          },
          immediate ? 0 : 300,
        );
      };

      if (delay > 0 && !immediate) {
        scrollTimeoutRef.current = setTimeout(doScroll, delay);
      } else {
        doScroll();
      }
    },
    [behavior, delay],
  );

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    // 如果是程序触发的滚动，忽略
    if (isScrollingRef.current) return;

    const atBottom = checkIfAtBottom();
    setIsAtBottom(atBottom);

    // 如果用户滚动到底部，重新启用自动滚动
    if (atBottom && !shouldAutoScroll) {
      setShouldAutoScroll(true);
    }
    // 如果用户向上滚动，禁用自动滚动
    else if (!atBottom && shouldAutoScroll) {
      setShouldAutoScroll(false);
    }
  }, [checkIfAtBottom, shouldAutoScroll]);

  // 监听滚动事件
  useEffect(() => {
    const element = scrollRef.current;
    if (!element) return;

    element.addEventListener('scroll', handleScroll, { passive: true });
    return () => element.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // 当依赖项变化时自动滚动
  useEffect(() => {
    if (!enabled || !shouldAutoScroll) return;

    // 延迟滚动，确保DOM已更新
    const timeoutId = setTimeout(() => {
      scrollToBottom();
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [enabled, shouldAutoScroll, scrollToBottom, ...dependencies]);

  // 初始化时滚动到底部
  useEffect(() => {
    if (enabled) {
      // 立即滚动到底部，不使用动画
      setTimeout(() => scrollToBottom(true), 0);
    }
  }, [enabled, scrollToBottom]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return {
    scrollRef,
    scrollToBottom,
    isAtBottom,
    shouldAutoScroll,
    setShouldAutoScroll,
  };
}

// 简化版本的自动滚动Hook，用于简单场景
export function useSimpleAutoScroll(dependencies: unknown[] = []) {
  const { scrollRef, scrollToBottom } = useAutoScroll({
    dependencies,
    behavior: 'smooth',
    delay: 100,
  });

  return { scrollRef, scrollToBottom };
}
