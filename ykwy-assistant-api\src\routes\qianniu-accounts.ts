import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { prisma } from '../lib/db';
import { createErrorResponse, createResponse, handleError } from '../lib/utils';

const app = new Hono();

// 验证Schema
const AccountIdSchema = z.object({
  accountId: z.string().uuid('无效的账号ID'),
});

const ClientIdSchema = z.object({
  clientId: z.string().uuid('无效的客户端ID'),
});

const CreateAccountSchema = z.object({
  clientId: z.string().uuid('无效的客户端ID'),
  brandId: z.string().uuid('无效的品牌ID'),
  accountName: z.string().min(1, '账号名称不能为空'),
  accountId: z.string().min(1, '账号ID不能为空'),
  shopName: z.string().optional(),
  shopId: z.string().optional(),
  platformType: z.enum(['TAOBAO', 'TMALL', 'PINDUODUO', 'JD', 'WECHAT', 'DOUYIN', 'XIAOHONGSHU', 'OTHER']),
});

const UpdateAccountSchema = z.object({
  accountName: z.string().min(1, '账号名称不能为空').optional(),
  shopName: z.string().optional(),
  shopId: z.string().optional(),
  platformType: z.enum(['TAOBAO', 'TMALL', 'PINDUODUO', 'JD', 'WECHAT', 'DOUYIN', 'XIAOHONGSHU', 'OTHER']).optional(),
  isActive: z.boolean().optional(),
  isLoggedIn: z.boolean().optional(),
});

/**
 * GET /qianniu-accounts/client/:clientId - 获取客户端的所有账号
 */
app.get('/client/:clientId', zValidator('param', ClientIdSchema), async (c) => {
  try {
    const { clientId } = c.req.valid('param');

    const accounts = await prisma.qianniuAccount.findMany({
      where: { clientId },
      include: {
        brand: {
          select: { id: true, name: true, logo: true },
        },
        client: {
          select: { id: true, name: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return c.json(createResponse(accounts, '获取客户端账号列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /qianniu-accounts/:accountId - 获取特定账号详情
 */
app.get('/:accountId', zValidator('param', AccountIdSchema), async (c) => {
  try {
    const { accountId } = c.req.valid('param');

    const account = await prisma.qianniuAccount.findUnique({
      where: { id: accountId },
      include: {
        brand: {
          select: { id: true, name: true, logo: true },
        },
        client: {
          select: { id: true, name: true },
        },
      },
    });

    if (!account) {
      return c.json(createErrorResponse('千牛账号不存在'), 404);
    }

    return c.json(createResponse(account, '获取千牛账号详情成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /qianniu-accounts - 创建千牛账号
 */
app.post('/', zValidator('json', CreateAccountSchema), async (c) => {
  try {
    const data = c.req.valid('json');

    // 验证客户端是否存在
    const client = await prisma.qianniuClient.findUnique({
      where: { id: data.clientId },
    });

    if (!client) {
      return c.json(createErrorResponse('千牛客户端不存在'), 404);
    }

    // 验证品牌是否存在
    const brand = await prisma.brand.findUnique({
      where: { id: data.brandId },
    });

    if (!brand) {
      return c.json(createErrorResponse('品牌不存在'), 404);
    }

    // 检查同一客户端下账号ID是否已存在
    const existingAccount = await prisma.qianniuAccount.findFirst({
      where: {
        clientId: data.clientId,
        accountId: data.accountId,
      },
    });

    if (existingAccount) {
      return c.json(createErrorResponse('该客户端下已存在相同的账号ID'), 400);
    }

    // 检查同一品牌在同一平台是否已有账号
    const existingBrandAccount = await prisma.qianniuAccount.findFirst({
      where: {
        brandId: data.brandId,
        platformType: data.platformType,
      },
    });

    if (existingBrandAccount) {
      return c.json(createErrorResponse('该品牌在此平台已有账号'), 400);
    }

    const account = await prisma.qianniuAccount.create({
      data: {
        ...data,
        isActive: true,
        isLoggedIn: false,
      },
      include: {
        brand: {
          select: { id: true, name: true, logo: true },
        },
        client: {
          select: { id: true, name: true },
        },
      },
    });

    return c.json(createResponse(account, '千牛账号创建成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * PUT /qianniu-accounts/:accountId - 更新千牛账号
 */
app.put('/:accountId', zValidator('param', AccountIdSchema), zValidator('json', UpdateAccountSchema), async (c) => {
  try {
    const { accountId } = c.req.valid('param');
    const data = c.req.valid('json');

    const account = await prisma.qianniuAccount.update({
      where: { id: accountId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      include: {
        brand: {
          select: { id: true, name: true, logo: true },
        },
        client: {
          select: { id: true, name: true },
        },
      },
    });

    return c.json(createResponse(account, '千牛账号更新成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * DELETE /qianniu-accounts/:accountId - 删除千牛账号
 */
app.delete('/:accountId', zValidator('param', AccountIdSchema), async (c) => {
  try {
    const { accountId } = c.req.valid('param');

    // 检查账号是否存在并获取完整信息
    const account = await prisma.qianniuAccount.findUnique({
      where: { id: accountId },
      select: {
        id: true,
        clientId: true,
        accountName: true,
      },
    });

    if (!account) {
      return c.json(createErrorResponse('千牛账号不存在'), 404);
    }

    // 删除账号
    await prisma.qianniuAccount.delete({
      where: { id: accountId },
    });

    return c.json(createResponse(account, '千牛账号删除成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

export default app;
