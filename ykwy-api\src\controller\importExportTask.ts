import type { Context } from 'hono';

import { ErrorCode } from '../constants/errorCodes.ts';
import { AppError } from '../errors/custom.error.ts';
import { ImportExportTaskService } from '../services/importExportTaskService';
import { importExportTaskParamIdSchema, importExportTaskQuerySchema } from '../types/validators/importExportTaskValidator';
import { R } from '../utils/Response.ts';

const importExportTaskService = new ImportExportTaskService();

/**
 * 导入导出任务控制器（只读），提供任务查询功能
 */
export class ImportExportTaskController {
  /**
   * 获取单个任务详情
   * @param c Hono Context
   */
  public async findById(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = importExportTaskParamIdSchema.safeParse(params);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    // 查询任务
    const result = await importExportTaskService.findById(validation.data.id);
    if (!result) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, { message: '任务未找到' });
    }

    return R.success(c, result);
  }

  /**
   * 获取任务列表（支持分页、过滤）
   * @param c Hono Context
   */
  public async findMany(c: Context) {
    const query = c.req.query();
    // 参数校验
    const validation = importExportTaskQuerySchema.safeParse(query);
    if (!validation.success) {
      throw new AppError(ErrorCode.G002_VALIDATION_ERROR, { issues: validation.error.issues });
    }

    // 查询任务列表
    const result = await importExportTaskService.findMany(validation.data);

    return R.success(c, result);
  }

  /**
   * 获取任务统计信息
   * @param c Hono Context
   */
  public async getStatistics(c: Context) {
    // 获取统计信息
    const result = await importExportTaskService.getStatistics();

    return R.success(c, result);
  }

  /**
   * 获取最近的任务列表
   * @param c Hono Context
   */
  public async getRecentTasks(c: Context) {
    const query = c.req.query();
    const limit = parseInt(query['limit'] || '10');
    const taskType = query['taskType'] as 'import' | 'export' | undefined;

    // 获取最近任务
    const result = await importExportTaskService.getRecentTasks(limit, taskType);

    return R.success(c, result);
  }
}
