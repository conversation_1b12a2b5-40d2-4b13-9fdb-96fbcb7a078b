import type {
  CompositeSizeChartSimple,
  CreateCompositeSizeChartSimpleInput,
  CreateSizeChartSimpleInput,
  SizeChartSimple,
  UpdateCompositeSizeChartSimpleInput,
  UpdateSizeChartSimpleInput,
} from '@/models/sizeChart';
import {
  createCompositeSizeChartSimple,
  createSizeChartSimple,
  deleteCompositeSizeChartSimple,
  deleteSizeChartSimple,
  getCompositeSizeChartSimpleList,
  getSizeChartSimpleList,
  updateCompositeSizeChartSimple,
  updateSizeChartSimple,
} from '@/services/sizeChart';
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Space,
  Table,
  Tabs,
  Tag,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';

const { Search } = Input;
const { TabPane } = Tabs;

const SizeTableSimple: React.FC = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState<string>('sizeChart');
  const [loading, setLoading] = useState<boolean>(false);

  // 简单尺码表相关状态
  const [sizeChartList, setSizeChartList] = useState<SizeChartSimple[]>([]);
  const [sizeChartTotal, setSizeChartTotal] = useState<number>(0);
  const [sizeChartPage, setSizeChartPage] = useState<number>(1);
  const [sizeChartPageSize, setSizeChartPageSize] = useState<number>(10);
  const [sizeChartSearchName, setSizeChartSearchName] = useState<string>('');

  // 复合尺码表相关状态
  const [compositeSizeChartList, setCompositeSizeChartList] = useState<
    CompositeSizeChartSimple[]
  >([]);
  const [compositeSizeChartTotal, setCompositeSizeChartTotal] =
    useState<number>(0);
  const [compositeSizeChartPage, setCompositeSizeChartPage] =
    useState<number>(1);
  const [compositeSizeChartPageSize, setCompositeSizeChartPageSize] =
    useState<number>(10);
  const [compositeSizeChartSearchName, setCompositeSizeChartSearchName] =
    useState<string>('');

  // 弹窗相关状态
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [currentRecord, setCurrentRecord] = useState<
    SizeChartSimple | CompositeSizeChartSimple | null
  >(null);
  const [form] = Form.useForm();

  // 获取简单尺码表列表
  const fetchSizeChartList = async () => {
    try {
      setLoading(true);
      const params = {
        page: sizeChartPage,
        limit: sizeChartPageSize,
        name: sizeChartSearchName || undefined,
      };

      const response = await getSizeChartSimpleList(params);

      if (response.code === 200) {
        setSizeChartList(response.data.data);
        setSizeChartTotal(response.data.total);
      } else {
        message.error(response.msg || '获取尺码表列表失败');
      }
    } catch (error) {
      message.error('获取尺码表列表失败');
      console.error('获取尺码表列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取复合尺码表列表
  const fetchCompositeSizeChartList = async () => {
    try {
      setLoading(true);
      const params = {
        page: compositeSizeChartPage,
        limit: compositeSizeChartPageSize,
        name: compositeSizeChartSearchName || undefined,
      };

      const response = await getCompositeSizeChartSimpleList(params);

      if (response.code === 200) {
        setCompositeSizeChartList(response.data.data);
        setCompositeSizeChartTotal(response.data.total);
      } else {
        message.error(response.msg || '获取复合尺码表列表失败');
      }
    } catch (error) {
      message.error('获取复合尺码表列表失败');
      console.error('获取复合尺码表列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理创建/编辑提交
  const handleSubmit = async (values: any) => {
    try {
      if (activeTab === 'sizeChart') {
        if (modalType === 'create') {
          const response = await createSizeChartSimple(
            values as CreateSizeChartSimpleInput,
          );
          if (response.code === 200) {
            message.success('创建尺码表成功');
            fetchSizeChartList();
          } else {
            message.error(response.msg || '创建尺码表失败');
          }
        } else {
          if (currentRecord) {
            const response = await updateSizeChartSimple(
              currentRecord.id,
              values as UpdateSizeChartSimpleInput,
            );
            if (response.code === 200) {
              message.success('更新尺码表成功');
              fetchSizeChartList();
            } else {
              message.error(response.msg || '更新尺码表失败');
            }
          }
        }
      } else {
        if (modalType === 'create') {
          const response = await createCompositeSizeChartSimple(
            values as CreateCompositeSizeChartSimpleInput,
          );
          if (response.code === 200) {
            message.success('创建复合尺码表成功');
            fetchCompositeSizeChartList();
          } else {
            message.error(response.msg || '创建复合尺码表失败');
          }
        } else {
          if (currentRecord) {
            const response = await updateCompositeSizeChartSimple(
              currentRecord.id,
              values as UpdateCompositeSizeChartSimpleInput,
            );
            if (response.code === 200) {
              message.success('更新复合尺码表成功');
              fetchCompositeSizeChartList();
            } else {
              message.error(response.msg || '更新复合尺码表失败');
            }
          }
        }
      }
      setModalVisible(false);
      form.resetFields();
      setCurrentRecord(null);
    } catch (error) {
      message.error('操作失败');
      console.error('操作失败:', error);
    }
  };

  // 处理删除
  const handleDelete = async (
    record: SizeChartSimple | CompositeSizeChartSimple,
  ) => {
    try {
      let response;
      if (activeTab === 'sizeChart') {
        response = await deleteSizeChartSimple(record.id);
      } else {
        response = await deleteCompositeSizeChartSimple(record.id);
      }

      if (response.code === 200) {
        message.success('删除成功');
        if (activeTab === 'sizeChart') {
          fetchSizeChartList();
        } else {
          fetchCompositeSizeChartList();
        }
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
      console.error('删除失败:', error);
    }
  };

  // 简单尺码表表格列定义
  const sizeChartColumns: ColumnsType<SizeChartSimple> = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_, __, index) =>
        (sizeChartPage - 1) * sizeChartPageSize + index + 1,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
    },
    {
      title: '尺码范围',
      dataIndex: 'sizeRange',
      key: 'sizeRange',
      width: 400,
      render: (text: string) => (
        <div
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: '尺码值',
      dataIndex: 'sizeValue',
      key: 'sizeValue',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'isDeleted',
      key: 'isDeleted',
      width: 80,
      render: (isDeleted: number) => (
        <Tag color={isDeleted === 0 ? 'green' : 'red'}>
          {isDeleted === 0 ? '正常' : '已删除'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setModalType('edit');
              setCurrentRecord(record);
              form.setFieldsValue(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 复合尺码表表格列定义
  const compositeSizeChartColumns: ColumnsType<CompositeSizeChartSimple> = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_, __, index) =>
        (compositeSizeChartPage - 1) * compositeSizeChartPageSize + index + 1,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 180,
      ellipsis: true,
      render: (text: string) => (
        <div style={{ whiteSpace: 'nowrap' }} title={text}>
          {text}
        </div>
      ),
    },
    {
      title: '尺码范围',
      dataIndex: 'sizeRange',
      key: 'sizeRange',
      width: 450,
      render: (text: string) => {
        // 处理男女款的换行显示
        const formattedText = text
          .replace(/男款:/g, '男款:\n')
          .replace(/女款:/g, '\n女款:\n');

        return (
          <div style={{ whiteSpace: 'pre-line', lineHeight: '1.4' }}>
            {formattedText}
          </div>
        );
      },
    },
    {
      title: '尺码值',
      dataIndex: 'sizeValue',
      key: 'sizeValue',
      width: 200,
      render: (text: string) => {
        // 处理男女款的换行显示
        const formattedText = text
          .replace(/男款:/g, '男款:\n')
          .replace(/女款:/g, '\n女款:\n');

        return (
          <div style={{ whiteSpace: 'pre-line', lineHeight: '1.4' }}>
            {formattedText}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'isDeleted',
      key: 'isDeleted',
      width: 80,
      render: (isDeleted: number) => (
        <Tag color={isDeleted === 0 ? 'green' : 'red'}>
          {isDeleted === 0 ? '正常' : '已删除'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setModalType('edit');
              setCurrentRecord(record);
              form.setFieldsValue(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 初始化数据
  useEffect(() => {
    if (activeTab === 'sizeChart') {
      fetchSizeChartList();
    } else {
      fetchCompositeSizeChartList();
    }
  }, [
    activeTab,
    sizeChartPage,
    sizeChartPageSize,
    compositeSizeChartPage,
    compositeSizeChartPageSize,
  ]);

  // 标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  return (
    <PageContainer
      title="简单尺码表管理"
      content="管理商品的尺码表信息，包括简单尺码表和复合尺码表"
    >
      <Card>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="简单尺码表" key="sizeChart">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Search
                  placeholder="搜索尺码表名称"
                  allowClear
                  style={{ width: 300 }}
                  onSearch={(value) => {
                    setSizeChartSearchName(value);
                    setSizeChartPage(1);
                  }}
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setModalType('create');
                    setCurrentRecord(null);
                    form.resetFields();
                    setModalVisible(true);
                  }}
                >
                  新增尺码表
                </Button>
                <Button icon={<ReloadOutlined />} onClick={fetchSizeChartList}>
                  刷新
                </Button>
              </Space>
            </div>

            <Table
              columns={sizeChartColumns}
              dataSource={sizeChartList}
              rowKey="id"
              loading={loading}
              scroll={{ x: 'max-content', y: 'calc(100vh - 300px)' }}
              pagination={{
                current: sizeChartPage,
                pageSize: sizeChartPageSize,
                total: sizeChartTotal,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
                onChange: (page, pageSize) => {
                  setSizeChartPage(page);
                  setSizeChartPageSize(pageSize || 10);
                },
              }}
            />
          </TabPane>

          <TabPane tab="复合尺码表" key="compositeSizeChart">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Search
                  placeholder="搜索复合尺码表名称"
                  allowClear
                  style={{ width: 300 }}
                  onSearch={(value) => {
                    setCompositeSizeChartSearchName(value);
                    setCompositeSizeChartPage(1);
                  }}
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setModalType('create');
                    setCurrentRecord(null);
                    form.resetFields();
                    setModalVisible(true);
                  }}
                >
                  新增复合尺码表
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchCompositeSizeChartList}
                >
                  刷新
                </Button>
              </Space>
            </div>

            <Table
              columns={compositeSizeChartColumns}
              dataSource={compositeSizeChartList}
              rowKey="id"
              loading={loading}
              scroll={{ x: 'max-content', y: 'calc(100vh - 300px)' }}
              pagination={{
                current: compositeSizeChartPage,
                pageSize: compositeSizeChartPageSize,
                total: compositeSizeChartTotal,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
                onChange: (page, pageSize) => {
                  setCompositeSizeChartPage(page);
                  setCompositeSizeChartPageSize(pageSize || 10);
                },
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建/编辑弹窗 */}
      <Modal
        title={
          modalType === 'create'
            ? `新增${activeTab === 'sizeChart' ? '尺码表' : '复合尺码表'}`
            : `编辑${activeTab === 'sizeChart' ? '尺码表' : '复合尺码表'}`
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setCurrentRecord(null);
        }}
        onOk={() => form.submit()}
        destroyOnHidden
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input placeholder="请输入名称" />
          </Form.Item>

          {activeTab === 'compositeSizeChart' && (
            <Form.Item
              name="type"
              label="类型"
              rules={[{ required: true, message: '请输入类型' }]}
            >
              <Input placeholder="请输入类型" />
            </Form.Item>
          )}

          <Form.Item
            name="sizeRange"
            label="尺码范围"
            rules={[{ required: true, message: '请输入尺码范围' }]}
          >
            <Input placeholder="请输入尺码范围" />
          </Form.Item>

          <Form.Item
            name="sizeValue"
            label="尺码值"
            rules={[{ required: true, message: '请输入尺码值' }]}
          >
            <Input.TextArea
              placeholder="请输入尺码值"
              rows={4}
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default SizeTableSimple;
