// 对话自动回复控制相关 React Query hooks

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { queryKeys } from '../../lib/query-keys';
import { batchSetConversationAutoReplyMutation, cancelConversationAutoReplyMutation, conversationAutoReplyStatusQueryOptions, setConversationAutoReplyMutation } from '../api/conversationAutoReply';

// 获取对话自动回复状态
export const useConversationAutoReplyStatus = (conversationId: string) => {
  return useQuery(conversationAutoReplyStatusQueryOptions(conversationId));
};

// 设置对话自动回复状态
export const useSetConversationAutoReply = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...setConversationAutoReplyMutation,
    onSuccess: (data, variables) => {
      console.log(`[Auto Reply Frontend] Mutation success:`, data);

      // 更新缓存
      queryClient.setQueryData(queryKeys.conversationAutoReplyStatus(variables.conversationId), {
        conversationId: data.conversationId,
        autoReplyEnabled: data.autoReplyEnabled,
      });

      // 强制刷新查询以确保状态同步
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversationAutoReplyStatus(variables.conversationId),
      });

      // 刷新对话详情数据
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversation(variables.conversationId),
      });

      // 如果状态发生了变化，可以触发其他相关查询的更新
      if (data.changed) {
        console.log(`[Auto Reply] Conversation ${variables.conversationId} auto reply ${data.autoReplyEnabled ? 'enabled' : 'disabled'}`);
      }
    },
    onError: (error) => {
      console.error('[Auto Reply] Failed to set auto reply status:', error);
    },
  });
};

// 直接取消自动回复（客服接入专用）
export const useCancelConversationAutoReply = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...cancelConversationAutoReplyMutation,
    onSuccess: (data, conversationId) => {
      console.log(`[Auto Reply Frontend] Cancel auto reply success:`, data);

      // 更新缓存
      queryClient.setQueryData(queryKeys.conversationAutoReplyStatus(conversationId), {
        conversationId: data.conversationId,
        autoReplyEnabled: false,
      });

      // 强制刷新查询以确保状态同步
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversationAutoReplyStatus(conversationId),
      });

      // 刷新对话详情数据
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversation(conversationId),
      });

      // 如果状态发生了变化，可以触发其他相关查询的更新
      if (data.changed) {
        console.log(`[Auto Reply] Conversation ${conversationId} auto reply cancelled`);
      }
    },
    onError: (error) => {
      console.error('[Auto Reply] Failed to cancel auto reply:', error);
    },
  });
};

// 批量设置对话自动回复状态
export const useBatchSetConversationAutoReply = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...batchSetConversationAutoReplyMutation,
    onSuccess: (data, variables) => {
      // 批量更新缓存
      variables.conversationIds.forEach((conversationId) => {
        queryClient.setQueryData(queryKeys.conversationAutoReplyStatus(conversationId), {
          conversationId,
          autoReplyEnabled: data.autoReplyEnabled,
        });

        // 刷新每个对话的详情数据
        queryClient.invalidateQueries({
          queryKey: queryKeys.conversation(conversationId),
        });
      });

      console.log(`[Auto Reply] Batch ${data.autoReplyEnabled ? 'enabled' : 'disabled'} auto reply for ${data.updatedCount} conversations`);
    },
    onError: (error) => {
      console.error('[Auto Reply] Failed to batch set auto reply status:', error);
    },
  });
};
