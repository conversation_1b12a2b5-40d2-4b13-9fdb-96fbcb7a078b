import { request } from '@umijs/max';

const API_BASE_URL =
  (process.env.UMI_APP_API_URL || 'http://localhost:3009') + '/api/v1';

// 导入导出任务类型定义
export interface ImportExportTask {
  id: string;
  taskName: string;
  taskType: 'import' | 'export';
  taskTime: string;
  fileName?: string;
  status: 'pending' | 'success' | 'failed';
  result?: string;
  filePath?: string;
  createdAt: string;
  updatedAt: string;
}

// 查询参数
export interface ImportExportTaskQueryParams {
  current?: number;
  pageSize?: number;
  taskType?: 'import' | 'export';
  status?: 'pending' | 'success' | 'failed';
  taskName?: string;
  startTime?: string;
  endTime?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 列表响应
export interface ImportExportTaskListResponse {
  items: ImportExportTask[];
  total: number;
  current: number;
  pageSize: number;
}

// 统计信息
export interface ImportExportTaskStatistics {
  totalTasks: number;
  pendingTasks: number;
  successTasks: number;
  failedTasks: number;
  importTasks: number;
  exportTasks: number;
  todayTasks: number;
  weekTasks: number;
}

// API响应格式
export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 获取导入导出任务列表
 */
export async function getImportExportTaskList(
  params: ImportExportTaskQueryParams = {},
): Promise<ApiResponse<ImportExportTaskListResponse>> {
  return request(`${API_BASE_URL}/import-export-tasks`, {
    method: 'GET',
    params,
  });
}

/**
 * 获取单个任务详情
 */
export async function getImportExportTaskDetail(
  id: string,
): Promise<ApiResponse<ImportExportTask>> {
  return request(`${API_BASE_URL}/import-export-task/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取任务统计信息
 */
export async function getImportExportTaskStatistics(): Promise<
  ApiResponse<ImportExportTaskStatistics>
> {
  return request(`${API_BASE_URL}/import-export-tasks/statistics`, {
    method: 'GET',
  });
}

/**
 * 获取最近任务列表
 */
export async function getRecentImportExportTasks(
  limit: number = 10,
): Promise<ApiResponse<ImportExportTask[]>> {
  return request(`${API_BASE_URL}/import-export-tasks/recent`, {
    method: 'GET',
    params: { limit },
  });
}

/**
 * 取消导入任务 (这个接口可能需要后端支持)
 */
export async function cancelImportTask(id: string): Promise<ApiResponse<void>> {
  return request(`${API_BASE_URL}/import-export-task/${id}/cancel`, {
    method: 'POST',
  });
}
