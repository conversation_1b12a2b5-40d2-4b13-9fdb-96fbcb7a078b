### 创建商品
POST http://localhost:3009/api/v1/product
Content-Type: application/json

{
  "title": "测试商品",
  "sourceUrl": "https://detail.1688.com/offer/123456789.html",
  "status": true,
  "category": "服装",
  "model": "T001",
  "tags": ["夏季", "短袖", "棉质"],
  "platform100146": "特殊属性值1",
  "platformFunction": "舒适透气",
  "platformMaterial": "100%棉",
  "platformStyle": "休闲款",
  "platformSeason": "夏季",
  "platformGender": "男女通用"
}

### 获取商品列表（基础查询）
GET http://localhost:3009/api/v1/products?skip=0&take=10

### 获取商品列表（带分类过滤）
GET http://localhost:3009/api/v1/products?skip=0&take=10&category=服装

### 获取单个商品详情
GET http://localhost:3009/api/v1/product/{{productId}}

### 更新商品
POST http://localhost:3009/api/v1/product
Content-Type: application/json

{
  "id": "{{productId}}",
  "title": "测试商品-更新",
  "sourceUrl": "https://detail.1688.com/offer/987654321.html",
  "status": false,
  "category": "服装-更新",
  "model": "T002",
  "tags": ["秋季", "长袖", "混纺"],
  "platform162887": "特殊属性值2",
  "platformFunction": "保暖舒适",
  "platformMaterial": "80%棉20%聚酯纤维",
  "platformStyle": "正装款",
  "platformSeason": "秋冬",
  "platformGender": "男士"
}

### 软删除商品
DELETE http://localhost:3009/api/v1/product/{{productId}}

### 恢复已删除商品
POST http://localhost:3009/api/v1/product/{{productId}}/restore

### 批量软删除
POST http://localhost:3009/api/v1/products/bulk-delete
Content-Type: application/json

{
  "ids": ["{{productId}}", "{{productId2}}"]
}

### 按状态查询商品（上架）
GET http://localhost:3009/api/v1/products?status=true

### 按状态查询商品（下架）
GET http://localhost:3009/api/v1/products?status=false

### 按分类查询商品
GET http://localhost:3009/api/v1/products?category=服装

### 按型号查询商品
GET http://localhost:3009/api/v1/products?model=T001

### 综合查询测试
GET http://localhost:3009/api/v1/products?status=true&category=服装&skip=0&take=5 