import { z } from 'zod';

/**
 * 临时商品ID参数校验
 */
export const tempProductParamIdSchema = z.object({
  /** 临时商品ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

export const tempProductParamProductIdSchema = z.object({
  /** 商品ID */
  productId: z.string().min(1, { message: 'productId 不能为空' }),
});

/**
 * 临时商品输入参数校验 Schema
 */
export const tempProductInputSchema = z.object({
  /** 临时商品ID，更新时必传，创建时可选 */
  id: z.string().uuid().optional(),
  /** 商品名称 (必须字段) */
  name: z.string().min(1, { message: '商品名称为必填项' }),
  /** 商品链接或id (必须字段) */
  linkOrId: z.string().min(1, { message: '商品链接或ID为必填项' }),
  /** 商品ID (必须字段) */
  productId: z.string().min(1, { message: '商品ID为必填项' }),
  /** 商品状态 (必须字段) */
  status: z.string().min(1, { message: '商品状态为必填项' }),
  /** 货号/款号 (可选字段) */
  styleNumber: z.string().optional(),
  /** 商品图片链接 (可选字段) */
  imageUrl: z.string().url({ message: '图片链接格式不正确' }).optional(),
  /** 描述 (可选字段，JSON格式) */
  description: z.any().optional(),
  /** 店铺ID (可选字段) */
  shopId: z.string().optional(),
});

/**
 * 创建/更新临时商品参数校验
 * @deprecated 使用 tempProductInputSchema 替代
 */
export const upsertTempProductSchema = tempProductInputSchema;

/**
 * 临时商品查询参数校验
 */
export const tempProductQuerySchema = z.object({
  /** 按商品名称模糊搜索 */
  name: z.string().optional(),
  /** 按状态过滤 */
  status: z.string().optional(),
  /** 按商品ID过滤 */
  productId: z.string().optional(),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10)),
  /** 是否包含已删除 */
  includeDeleted: z
    .string()
    .optional()
    .default('false')
    .transform((val) => val === 'true'),
  /** 只查询没有shopId的数据 */
  onlyWithoutShopId: z.boolean().optional(),
});

/**
 * 批量删除参数校验
 */
export const tempProductBulkDeleteSchema = z.object({
  /** 临时商品ID数组 */
  ids: z.array(z.string().uuid()),
});

/**
 * 临时商品搜索参数校验 Schema
 * 用于v2版本的搜索接口
 */
export const tempProductSearchSchema = z.object({
  /** 查询字符串，用于匹配productId、name、styleNumber、linkOrId字段，可为空 */
  query: z.string().max(200, { message: '查询字符串不能超过200个字符' }).optional(),
  /** 店铺ID，用于精确匹配shopId字段，必填 */
  shopId: z.string().min(1, { message: '店铺ID不能为空' }),
});

/**
 * 导出临时商品参数校验
 */
export const tempProductExportSchema = z.object({
  /** 导出格式 */
  format: z.enum(['excel', 'csv'], { message: '导出格式必须是excel或csv' }),
  /** 要导出的字段列表 */
  fields: z.array(z.enum(['name', 'linkOrId', 'productId', 'status', 'styleNumber', 'imageUrl', 'createdAt', 'updatedAt']), { message: '无效的字段名' }).min(1, { message: '至少选择一个字段' }),
  /** 查询过滤条件（可选） */
  filters: z
    .object({
      /** 按商品名称模糊搜索 */
      name: z.string().optional(),
      /** 按状态过滤 */
      status: z.string().optional(),
      /** 按商品ID过滤 */
      productId: z.string().optional(),
      /** 是否包含已删除 */
      includeDeleted: z.boolean().optional().default(false),
    })
    .optional(),
});

// ========== 类型定义 ==========

/**
 * 临时商品输入类型
 */
export type TempProductInput = z.infer<typeof tempProductInputSchema>;

/**
 * 临时商品查询参数类型
 */
export type TempProductQuery = z.infer<typeof tempProductQuerySchema>;

/**
 * 临时商品ID参数类型
 */
export type TempProductIdParam = z.infer<typeof tempProductParamIdSchema>;

/**
 * 商品ID参数类型
 */
export type TempProductProductIdParam = z.infer<typeof tempProductParamProductIdSchema>;

/**
 * 批量删除参数类型
 */
export type TempProductBulkDelete = z.infer<typeof tempProductBulkDeleteSchema>;

/**
 * 搜索参数类型
 */
export type TempProductSearch = z.infer<typeof tempProductSearchSchema>;

/**
 * 导出参数类型
 */
export type TempProductExport = z.infer<typeof tempProductExportSchema>;
