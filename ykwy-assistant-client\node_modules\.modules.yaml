hoistPattern:
  - '*'
hoistedLocations:
  7zip-bin@5.2.0:
    - node_modules\7zip-bin
  '@alloc/quick-lru@5.2.0':
    - node_modules\@alloc\quick-lru
  '@ampproject/remapping@2.3.0':
    - node_modules\@ampproject\remapping
  '@babel/code-frame@7.27.1':
    - node_modules\@babel\code-frame
  '@babel/compat-data@7.28.0':
    - node_modules\@babel\compat-data
  '@babel/core@7.28.0':
    - node_modules\@babel\core
  '@babel/generator@7.28.0':
    - node_modules\@babel\generator
  '@babel/helper-compilation-targets@7.27.2':
    - node_modules\@babel\helper-compilation-targets
  '@babel/helper-globals@7.28.0':
    - node_modules\@babel\helper-globals
  '@babel/helper-module-imports@7.27.1':
    - node_modules\@babel\helper-module-imports
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    - node_modules\@babel\helper-module-transforms
  '@babel/helper-plugin-utils@7.27.1':
    - node_modules\@babel\helper-plugin-utils
  '@babel/helper-string-parser@7.27.1':
    - node_modules\@babel\helper-string-parser
  '@babel/helper-validator-identifier@7.27.1':
    - node_modules\@babel\helper-validator-identifier
  '@babel/helper-validator-option@7.27.1':
    - node_modules\@babel\helper-validator-option
  '@babel/helpers@7.28.2':
    - node_modules\@babel\helpers
  '@babel/parser@7.28.0':
    - node_modules\@babel\parser
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    - node_modules\@babel\plugin-transform-react-jsx-self
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    - node_modules\@babel\plugin-transform-react-jsx-source
  '@babel/template@7.27.2':
    - node_modules\@babel\template
  '@babel/traverse@7.28.0':
    - node_modules\@babel\traverse
  '@babel/types@7.28.2':
    - node_modules\@babel\types
  '@develar/schema-utils@2.6.5':
    - node_modules\@develar\schema-utils
  '@electron/asar@3.2.18':
    - node_modules\@electron\asar
  '@electron/asar@3.4.1':
    - node_modules\electron-winstaller\node_modules\@electron\asar
  '@electron/fuses@1.8.0':
    - node_modules\@electron\fuses
  '@electron/get@2.0.3':
    - node_modules\@electron\get
  '@electron/node-gyp@https://codeload.github.com/electron/node-gyp/tar.gz/06b29aafb7708acef8b3669835c8a7857ebc92d2':
    - node_modules\@electron\node-gyp
  '@electron/notarize@2.5.0':
    - node_modules\@electron\notarize
  '@electron/osx-sign@1.3.1':
    - node_modules\@electron\osx-sign
  '@electron/rebuild@3.7.0':
    - node_modules\@electron\rebuild
  '@electron/universal@2.0.1':
    - node_modules\@electron\universal
  '@electron/windows-sign@1.2.2':
    - node_modules\@electron\windows-sign
  '@esbuild/win32-x64@0.25.8':
    - node_modules\@esbuild\win32-x64
  '@eslint-community/eslint-utils@4.7.0(eslint@9.32.0(jiti@1.21.7))':
    - node_modules\@eslint-community\eslint-utils
  '@eslint-community/regexpp@4.12.1':
    - node_modules\@eslint-community\regexpp
  '@eslint/config-array@0.21.0':
    - node_modules\@eslint\config-array
  '@eslint/config-helpers@0.3.0':
    - node_modules\@eslint\config-helpers
  '@eslint/core@0.15.1':
    - node_modules\@eslint\core
  '@eslint/eslintrc@3.3.1':
    - node_modules\@eslint\eslintrc
  '@eslint/js@9.32.0':
    - node_modules\@eslint\js
  '@eslint/object-schema@2.1.6':
    - node_modules\@eslint\object-schema
  '@eslint/plugin-kit@0.3.4':
    - node_modules\@eslint\plugin-kit
  '@floating-ui/core@1.7.2':
    - node_modules\@floating-ui\core
  '@floating-ui/dom@1.7.2':
    - node_modules\@floating-ui\dom
  '@floating-ui/react-dom@2.1.4(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@floating-ui\react-dom
  '@floating-ui/utils@0.2.10':
    - node_modules\@floating-ui\utils
  '@gar/promisify@1.1.3':
    - node_modules\@gar\promisify
  '@hookform/resolvers@5.2.0(react-hook-form@7.61.1(react@19.1.1))':
    - node_modules\@hookform\resolvers
  '@humanfs/core@0.19.1':
    - node_modules\@humanfs\core
  '@humanfs/node@0.16.6':
    - node_modules\@humanfs\node
  '@humanwhocodes/module-importer@1.0.1':
    - node_modules\@humanwhocodes\module-importer
  '@humanwhocodes/retry@0.3.1':
    - node_modules\@humanwhocodes\retry
  '@humanwhocodes/retry@0.4.3':
    - node_modules\eslint\node_modules\@humanwhocodes\retry
  '@isaacs/balanced-match@4.0.1':
    - node_modules\@isaacs\balanced-match
  '@isaacs/brace-expansion@5.0.0':
    - node_modules\@isaacs\brace-expansion
  '@isaacs/cliui@8.0.2':
    - node_modules\@isaacs\cliui
  '@jridgewell/gen-mapping@0.3.12':
    - node_modules\@jridgewell\gen-mapping
  '@jridgewell/resolve-uri@3.1.2':
    - node_modules\@jridgewell\resolve-uri
  '@jridgewell/sourcemap-codec@1.5.4':
    - node_modules\@jridgewell\sourcemap-codec
  '@jridgewell/trace-mapping@0.3.29':
    - node_modules\@jridgewell\trace-mapping
  '@malept/cross-spawn-promise@2.0.0':
    - node_modules\@malept\cross-spawn-promise
  '@malept/flatpak-bundler@0.4.0':
    - node_modules\@malept\flatpak-bundler
  '@nodelib/fs.scandir@2.1.5':
    - node_modules\@nodelib\fs.scandir
  '@nodelib/fs.stat@2.0.5':
    - node_modules\@nodelib\fs.stat
  '@nodelib/fs.walk@1.2.8':
    - node_modules\@nodelib\fs.walk
  '@npmcli/fs@2.1.2':
    - node_modules\@npmcli\fs
  '@npmcli/move-file@2.0.1':
    - node_modules\@npmcli\move-file
  '@pkgjs/parseargs@0.11.0':
    - node_modules\@pkgjs\parseargs
  '@radix-ui/number@1.1.1':
    - node_modules\@radix-ui\number
  '@radix-ui/primitive@1.1.2':
    - node_modules\@radix-ui\primitive
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-arrow
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-collection
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-compose-refs
  '@radix-ui/react-context@1.1.2(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-context
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-direction
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-dismissable-layer
  '@radix-ui/react-dropdown-menu@2.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-dropdown-menu
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-focus-guards
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-focus-scope
  '@radix-ui/react-id@1.1.1(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-id
  '@radix-ui/react-label@2.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-label
  '@radix-ui/react-menu@2.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-menu
  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-popper
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-portal
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-presence
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-primitive
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-roving-focus
  '@radix-ui/react-select@2.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-select
  '@radix-ui/react-slot@1.2.3(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-slot
  '@radix-ui/react-switch@1.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-switch
  '@radix-ui/react-tooltip@1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-tooltip
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-use-callback-ref
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-use-controllable-state
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-use-effect-event
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-use-escape-keydown
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-use-layout-effect
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-use-previous
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-use-rect
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.8)(react@19.1.1)':
    - node_modules\@radix-ui\react-use-size
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    - node_modules\@radix-ui\react-visually-hidden
  '@radix-ui/rect@1.1.1':
    - node_modules\@radix-ui\rect
  '@rolldown/pluginutils@1.0.0-beta.27':
    - node_modules\@rolldown\pluginutils
  '@rollup/rollup-win32-x64-msvc@4.46.1':
    - node_modules\@rollup\rollup-win32-x64-msvc
  '@sindresorhus/is@4.6.0':
    - node_modules\@sindresorhus\is
  '@standard-schema/utils@0.3.0':
    - node_modules\@standard-schema\utils
  '@szmarczak/http-timer@4.0.6':
    - node_modules\@szmarczak\http-timer
  '@tanstack/query-core@5.83.0':
    - node_modules\@tanstack\query-core
  '@tanstack/react-query@5.83.0(react@19.1.1)':
    - node_modules\@tanstack\react-query
  '@tootallnate/once@2.0.0':
    - node_modules\@tootallnate\once
  '@types/babel__core@7.20.5':
    - node_modules\@types\babel__core
  '@types/babel__generator@7.27.0':
    - node_modules\@types\babel__generator
  '@types/babel__template@7.4.4':
    - node_modules\@types\babel__template
  '@types/babel__traverse@7.20.7':
    - node_modules\@types\babel__traverse
  '@types/cacheable-request@6.0.3':
    - node_modules\@types\cacheable-request
  '@types/debug@4.1.12':
    - node_modules\@types\debug
  '@types/estree@1.0.8':
    - node_modules\@types\estree
  '@types/fs-extra@9.0.13':
    - node_modules\@types\fs-extra
  '@types/http-cache-semantics@4.0.4':
    - node_modules\@types\http-cache-semantics
  '@types/json-schema@7.0.15':
    - node_modules\@types\json-schema
  '@types/keyv@3.1.4':
    - node_modules\@types\keyv
  '@types/ms@2.1.0':
    - node_modules\@types\ms
  '@types/node@22.16.5':
    - node_modules\@types\node
  '@types/react-dom@19.1.6(@types/react@19.1.8)':
    - node_modules\@types\react-dom
  '@types/react@19.1.8':
    - node_modules\@types\react
  '@types/responselike@1.0.3':
    - node_modules\@types\responselike
  '@types/yauzl@2.10.3':
    - node_modules\@types\yauzl
  '@typescript-eslint/eslint-plugin@8.38.0(@typescript-eslint/parser@8.38.0(eslint@9.32.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.32.0(jiti@1.21.7))(typescript@5.8.3)':
    - node_modules\@typescript-eslint\eslint-plugin
  '@typescript-eslint/parser@8.38.0(eslint@9.32.0(jiti@1.21.7))(typescript@5.8.3)':
    - node_modules\@typescript-eslint\parser
  '@typescript-eslint/project-service@8.38.0(typescript@5.8.3)':
    - node_modules\@typescript-eslint\project-service
  '@typescript-eslint/scope-manager@8.38.0':
    - node_modules\@typescript-eslint\scope-manager
  '@typescript-eslint/tsconfig-utils@8.38.0(typescript@5.8.3)':
    - node_modules\@typescript-eslint\tsconfig-utils
  '@typescript-eslint/type-utils@8.38.0(eslint@9.32.0(jiti@1.21.7))(typescript@5.8.3)':
    - node_modules\@typescript-eslint\type-utils
  '@typescript-eslint/types@8.38.0':
    - node_modules\@typescript-eslint\types
  '@typescript-eslint/typescript-estree@8.38.0(typescript@5.8.3)':
    - node_modules\@typescript-eslint\typescript-estree
  '@typescript-eslint/utils@8.38.0(eslint@9.32.0(jiti@1.21.7))(typescript@5.8.3)':
    - node_modules\@typescript-eslint\utils
  '@typescript-eslint/visitor-keys@8.38.0':
    - node_modules\@typescript-eslint\visitor-keys
  '@vitejs/plugin-react@4.7.0(vite@6.3.5(@types/node@22.16.5)(jiti@1.21.7)(yaml@2.8.0))':
    - node_modules\@vitejs\plugin-react
  '@xmldom/xmldom@0.8.10':
    - node_modules\@xmldom\xmldom
  abbrev@1.1.1:
    - node_modules\abbrev
  acorn-jsx@5.3.2(acorn@8.15.0):
    - node_modules\acorn-jsx
  acorn@8.15.0:
    - node_modules\acorn
  agent-base@6.0.2:
    - node_modules\agent-base
  agent-base@7.1.4:
    - node_modules\builder-util\node_modules\agent-base
  agentkeepalive@4.6.0:
    - node_modules\agentkeepalive
  aggregate-error@3.1.0:
    - node_modules\aggregate-error
  ajv-keywords@3.5.2(ajv@6.12.6):
    - node_modules\ajv-keywords
  ajv@6.12.6:
    - node_modules\ajv
  ansi-regex@5.0.1:
    - node_modules\ansi-regex
  ansi-regex@6.1.0:
    - node_modules\@isaacs\cliui\node_modules\ansi-regex
  ansi-styles@3.2.1:
    - node_modules\npm-run-all\node_modules\ansi-styles
  ansi-styles@4.3.0:
    - node_modules\ansi-styles
  ansi-styles@6.2.1:
    - node_modules\@isaacs\cliui\node_modules\ansi-styles
  any-promise@1.3.0:
    - node_modules\any-promise
  anymatch@3.1.3:
    - node_modules\anymatch
  app-builder-bin@5.0.0-alpha.12:
    - node_modules\app-builder-bin
  app-builder-lib@26.0.12(dmg-builder@26.0.12)(electron-builder-squirrel-windows@26.0.12):
    - node_modules\app-builder-lib
  arg@5.0.2:
    - node_modules\arg
  argparse@2.0.1:
    - node_modules\argparse
  aria-hidden@1.2.6:
    - node_modules\aria-hidden
  array-buffer-byte-length@1.0.2:
    - node_modules\array-buffer-byte-length
  array-includes@3.1.9:
    - node_modules\array-includes
  array.prototype.findlast@1.2.5:
    - node_modules\array.prototype.findlast
  array.prototype.flat@1.3.3:
    - node_modules\array.prototype.flat
  array.prototype.flatmap@1.3.3:
    - node_modules\array.prototype.flatmap
  array.prototype.tosorted@1.1.4:
    - node_modules\array.prototype.tosorted
  arraybuffer.prototype.slice@1.0.4:
    - node_modules\arraybuffer.prototype.slice
  async-exit-hook@2.0.1:
    - node_modules\async-exit-hook
  async-function@1.0.0:
    - node_modules\async-function
  async@3.2.6:
    - node_modules\async
  asynckit@0.4.0:
    - node_modules\asynckit
  at-least-node@1.0.0:
    - node_modules\at-least-node
  autoprefixer@10.4.21(postcss@8.5.6):
    - node_modules\autoprefixer
  available-typed-arrays@1.0.7:
    - node_modules\available-typed-arrays
  balanced-match@1.0.2:
    - node_modules\balanced-match
  base64-js@1.5.1:
    - node_modules\base64-js
  binary-extensions@2.3.0:
    - node_modules\binary-extensions
  bl@4.1.0:
    - node_modules\bl
  boolean@3.2.0:
    - node_modules\boolean
  brace-expansion@1.1.12:
    - node_modules\minimatch\node_modules\brace-expansion
  brace-expansion@2.0.2:
    - node_modules\brace-expansion
  braces@3.0.3:
    - node_modules\braces
  browserslist@4.25.1:
    - node_modules\browserslist
  buffer-crc32@0.2.13:
    - node_modules\buffer-crc32
  buffer-from@1.1.2:
    - node_modules\buffer-from
  buffer@5.7.1:
    - node_modules\buffer
  builder-util-runtime@9.3.1:
    - node_modules\builder-util-runtime
  builder-util@26.0.11:
    - node_modules\builder-util
  cacache@16.1.3:
    - node_modules\cacache
  cacheable-lookup@5.0.4:
    - node_modules\cacheable-lookup
  cacheable-request@7.0.4:
    - node_modules\cacheable-request
  call-bind-apply-helpers@1.0.2:
    - node_modules\call-bind-apply-helpers
  call-bind@1.0.8:
    - node_modules\call-bind
  call-bound@1.0.4:
    - node_modules\call-bound
  callsites@3.1.0:
    - node_modules\callsites
  camelcase-css@2.0.1:
    - node_modules\camelcase-css
  caniuse-lite@1.0.30001731:
    - node_modules\caniuse-lite
  chalk@2.4.2:
    - node_modules\npm-run-all\node_modules\chalk
  chalk@4.1.2:
    - node_modules\chalk
  chokidar@3.6.0:
    - node_modules\chokidar
  chownr@2.0.0:
    - node_modules\chownr
  chromium-pickle-js@0.2.0:
    - node_modules\chromium-pickle-js
  ci-info@3.9.0:
    - node_modules\ci-info
  class-variance-authority@0.7.1:
    - node_modules\class-variance-authority
  clean-stack@2.2.0:
    - node_modules\clean-stack
  cli-cursor@3.1.0:
    - node_modules\cli-cursor
  cli-spinners@2.9.2:
    - node_modules\cli-spinners
  cliui@8.0.1:
    - node_modules\cliui
  clone-response@1.0.3:
    - node_modules\clone-response
  clone@1.0.4:
    - node_modules\clone
  clsx@2.1.1:
    - node_modules\clsx
  color-convert@1.9.3:
    - node_modules\npm-run-all\node_modules\color-convert
  color-convert@2.0.1:
    - node_modules\color-convert
  color-name@1.1.3:
    - node_modules\npm-run-all\node_modules\color-name
  color-name@1.1.4:
    - node_modules\color-name
  combined-stream@1.0.8:
    - node_modules\combined-stream
  commander@4.1.1:
    - node_modules\sucrase\node_modules\commander
  commander@5.1.0:
    - node_modules\commander
  commander@9.5.0:
    - node_modules\postject\node_modules\commander
  compare-version@0.1.2:
    - node_modules\compare-version
  concat-map@0.0.1:
    - node_modules\concat-map
  concurrently@9.2.0:
    - node_modules\concurrently
  config-file-ts@0.2.8-rc1:
    - node_modules\config-file-ts
  convert-source-map@2.0.0:
    - node_modules\convert-source-map
  cross-dirname@0.1.0:
    - node_modules\cross-dirname
  cross-spawn@6.0.6:
    - node_modules\npm-run-all\node_modules\cross-spawn
  cross-spawn@7.0.6:
    - node_modules\cross-spawn
  cssesc@3.0.0:
    - node_modules\cssesc
  csstype@3.1.3:
    - node_modules\csstype
  data-view-buffer@1.0.2:
    - node_modules\data-view-buffer
  data-view-byte-length@1.0.2:
    - node_modules\data-view-byte-length
  data-view-byte-offset@1.0.1:
    - node_modules\data-view-byte-offset
  date-fns@4.1.0:
    - node_modules\date-fns
  debug@4.4.1:
    - node_modules\debug
  decompress-response@6.0.0:
    - node_modules\decompress-response
  deep-is@0.1.4:
    - node_modules\deep-is
  defaults@1.0.4:
    - node_modules\defaults
  defer-to-connect@2.0.1:
    - node_modules\defer-to-connect
  define-data-property@1.1.4:
    - node_modules\define-data-property
  define-properties@1.2.1:
    - node_modules\define-properties
  delayed-stream@1.0.0:
    - node_modules\delayed-stream
  detect-libc@2.0.4:
    - node_modules\detect-libc
  detect-node-es@1.1.0:
    - node_modules\detect-node-es
  detect-node@2.1.0:
    - node_modules\detect-node
  didyoumean@1.2.2:
    - node_modules\didyoumean
  dir-compare@4.2.0:
    - node_modules\dir-compare
  dlv@1.1.3:
    - node_modules\dlv
  dmg-builder@26.0.12(electron-builder-squirrel-windows@26.0.12):
    - node_modules\dmg-builder
  doctrine@2.1.0:
    - node_modules\doctrine
  dotenv-expand@11.0.7:
    - node_modules\dotenv-expand
  dotenv@16.6.1:
    - node_modules\dotenv
  dunder-proto@1.0.1:
    - node_modules\dunder-proto
  eastasianwidth@0.2.0:
    - node_modules\eastasianwidth
  ejs@3.1.10:
    - node_modules\ejs
  electron-builder-squirrel-windows@26.0.12(dmg-builder@26.0.12):
    - node_modules\electron-builder-squirrel-windows
  electron-builder@26.0.12(electron-builder-squirrel-windows@26.0.12):
    - node_modules\electron-builder
  electron-publish@26.0.11:
    - node_modules\electron-publish
  electron-to-chromium@1.5.192:
    - node_modules\electron-to-chromium
  electron-winstaller@5.4.0:
    - node_modules\electron-winstaller
  electron@37.2.2:
    - node_modules\electron
  emoji-regex@8.0.0:
    - node_modules\emoji-regex
  emoji-regex@9.2.2:
    - node_modules\@isaacs\cliui\node_modules\emoji-regex
  encoding@0.1.13:
    - node_modules\encoding
  end-of-stream@1.4.5:
    - node_modules\end-of-stream
  env-paths@2.2.1:
    - node_modules\env-paths
  err-code@2.0.3:
    - node_modules\err-code
  error-ex@1.3.2:
    - node_modules\error-ex
  es-abstract@1.24.0:
    - node_modules\es-abstract
  es-define-property@1.0.1:
    - node_modules\es-define-property
  es-errors@1.3.0:
    - node_modules\es-errors
  es-iterator-helpers@1.2.1:
    - node_modules\es-iterator-helpers
  es-object-atoms@1.1.1:
    - node_modules\es-object-atoms
  es-set-tostringtag@2.1.0:
    - node_modules\es-set-tostringtag
  es-shim-unscopables@1.1.0:
    - node_modules\es-shim-unscopables
  es-to-primitive@1.3.0:
    - node_modules\es-to-primitive
  es6-error@4.1.1:
    - node_modules\es6-error
  esbuild@0.25.8:
    - node_modules\esbuild
  escalade@3.2.0:
    - node_modules\escalade
  escape-string-regexp@1.0.5:
    - node_modules\npm-run-all\node_modules\escape-string-regexp
  escape-string-regexp@4.0.0:
    - node_modules\escape-string-regexp
  eslint-plugin-react-hooks@5.2.0(eslint@9.32.0(jiti@1.21.7)):
    - node_modules\eslint-plugin-react-hooks
  eslint-plugin-react@7.37.5(eslint@9.32.0(jiti@1.21.7)):
    - node_modules\eslint-plugin-react
  eslint-scope@8.4.0:
    - node_modules\eslint-scope
  eslint-visitor-keys@3.4.3:
    - node_modules\@eslint-community\eslint-utils\node_modules\eslint-visitor-keys
  eslint-visitor-keys@4.2.1:
    - node_modules\eslint-visitor-keys
  eslint@9.32.0(jiti@1.21.7):
    - node_modules\eslint
  espree@10.4.0:
    - node_modules\espree
  esquery@1.6.0:
    - node_modules\esquery
  esrecurse@4.3.0:
    - node_modules\esrecurse
  estraverse@5.3.0:
    - node_modules\estraverse
  esutils@2.0.3:
    - node_modules\esutils
  exponential-backoff@3.1.2:
    - node_modules\exponential-backoff
  extract-zip@2.0.1:
    - node_modules\extract-zip
  fast-deep-equal@3.1.3:
    - node_modules\fast-deep-equal
  fast-glob@3.3.3:
    - node_modules\fast-glob
  fast-json-stable-stringify@2.1.0:
    - node_modules\fast-json-stable-stringify
  fast-levenshtein@2.0.6:
    - node_modules\fast-levenshtein
  fastq@1.19.1:
    - node_modules\fastq
  fd-slicer@1.1.0:
    - node_modules\fd-slicer
  fdir@6.4.6(picomatch@4.0.3):
    - node_modules\fdir
  file-entry-cache@8.0.0:
    - node_modules\file-entry-cache
  filelist@1.0.4:
    - node_modules\filelist
  fill-range@7.1.1:
    - node_modules\fill-range
  find-up@5.0.0:
    - node_modules\find-up
  flat-cache@4.0.1:
    - node_modules\flat-cache
  flatted@3.3.3:
    - node_modules\flatted
  for-each@0.3.5:
    - node_modules\for-each
  foreground-child@3.3.1:
    - node_modules\foreground-child
  form-data@4.0.4:
    - node_modules\form-data
  fraction.js@4.3.7:
    - node_modules\fraction.js
  fs-extra@10.1.0:
    - node_modules\fs-extra
  fs-extra@11.3.0:
    - node_modules\@electron\universal\node_modules\fs-extra
    - node_modules\@electron\windows-sign\node_modules\fs-extra
  fs-extra@7.0.1:
    - node_modules\electron-winstaller\node_modules\fs-extra
  fs-extra@8.1.0:
    - node_modules\@electron\get\node_modules\fs-extra
  fs-extra@9.1.0:
    - node_modules\@electron\fuses\node_modules\fs-extra
    - node_modules\@electron\notarize\node_modules\fs-extra
    - node_modules\@malept\flatpak-bundler\node_modules\fs-extra
  fs-minipass@2.1.0:
    - node_modules\fs-minipass
  fs.realpath@1.0.0:
    - node_modules\fs.realpath
  function-bind@1.1.2:
    - node_modules\function-bind
  function.prototype.name@1.1.8:
    - node_modules\function.prototype.name
  functions-have-names@1.2.3:
    - node_modules\functions-have-names
  gensync@1.0.0-beta.2:
    - node_modules\gensync
  get-caller-file@2.0.5:
    - node_modules\get-caller-file
  get-intrinsic@1.3.0:
    - node_modules\get-intrinsic
  get-nonce@1.0.1:
    - node_modules\get-nonce
  get-proto@1.0.1:
    - node_modules\get-proto
  get-stream@5.2.0:
    - node_modules\get-stream
  get-symbol-description@1.1.0:
    - node_modules\get-symbol-description
  glob-parent@5.1.2:
    - node_modules\glob-parent
  glob-parent@6.0.2:
    - node_modules\eslint\node_modules\glob-parent
    - node_modules\tailwindcss\node_modules\glob-parent
  glob@10.4.5:
    - node_modules\sucrase\node_modules\glob
    - node_modules\config-file-ts\node_modules\glob
  glob@11.0.3:
    - node_modules\rimraf\node_modules\glob
  glob@7.2.3:
    - node_modules\glob
    - node_modules\cacache\node_modules\rimraf\node_modules\glob
  glob@8.1.0:
    - node_modules\@electron\node-gyp\node_modules\glob
    - node_modules\cacache\node_modules\glob
  global-agent@3.0.0:
    - node_modules\global-agent
  globals@14.0.0:
    - node_modules\@eslint\eslintrc\node_modules\globals
  globals@16.3.0:
    - node_modules\globals
  globalthis@1.0.4:
    - node_modules\globalthis
  gopd@1.2.0:
    - node_modules\gopd
  got@11.8.6:
    - node_modules\got
  graceful-fs@4.2.11:
    - node_modules\graceful-fs
  graphemer@1.4.0:
    - node_modules\graphemer
  has-bigints@1.1.0:
    - node_modules\has-bigints
  has-flag@3.0.0:
    - node_modules\npm-run-all\node_modules\has-flag
  has-flag@4.0.0:
    - node_modules\has-flag
  has-property-descriptors@1.0.2:
    - node_modules\has-property-descriptors
  has-proto@1.2.0:
    - node_modules\has-proto
  has-symbols@1.1.0:
    - node_modules\has-symbols
  has-tostringtag@1.0.2:
    - node_modules\has-tostringtag
  hasown@2.0.2:
    - node_modules\hasown
  hosted-git-info@2.8.9:
    - node_modules\normalize-package-data\node_modules\hosted-git-info
  hosted-git-info@4.1.0:
    - node_modules\hosted-git-info
  http-cache-semantics@4.2.0:
    - node_modules\http-cache-semantics
  http-proxy-agent@5.0.0:
    - node_modules\http-proxy-agent
  http-proxy-agent@7.0.2:
    - node_modules\builder-util\node_modules\http-proxy-agent
  http2-wrapper@1.0.3:
    - node_modules\http2-wrapper
  https-proxy-agent@5.0.1:
    - node_modules\https-proxy-agent
  https-proxy-agent@7.0.6:
    - node_modules\builder-util\node_modules\https-proxy-agent
  humanize-ms@1.2.1:
    - node_modules\humanize-ms
  iconv-lite@0.6.3:
    - node_modules\iconv-lite
  ieee754@1.2.1:
    - node_modules\ieee754
  ignore@5.3.2:
    - node_modules\ignore
  ignore@7.0.5:
    - node_modules\@typescript-eslint\eslint-plugin\node_modules\ignore
  import-fresh@3.3.1:
    - node_modules\import-fresh
  imurmurhash@0.1.4:
    - node_modules\imurmurhash
  indent-string@4.0.0:
    - node_modules\indent-string
  infer-owner@1.0.4:
    - node_modules\infer-owner
  inflight@1.0.6:
    - node_modules\inflight
  inherits@2.0.4:
    - node_modules\inherits
  internal-slot@1.1.0:
    - node_modules\internal-slot
  ip-address@9.0.5:
    - node_modules\ip-address
  is-array-buffer@3.0.5:
    - node_modules\is-array-buffer
  is-arrayish@0.2.1:
    - node_modules\is-arrayish
  is-async-function@2.1.1:
    - node_modules\is-async-function
  is-bigint@1.1.0:
    - node_modules\is-bigint
  is-binary-path@2.1.0:
    - node_modules\is-binary-path
  is-boolean-object@1.2.2:
    - node_modules\is-boolean-object
  is-callable@1.2.7:
    - node_modules\is-callable
  is-ci@3.0.1:
    - node_modules\is-ci
  is-core-module@2.16.1:
    - node_modules\is-core-module
  is-data-view@1.0.2:
    - node_modules\is-data-view
  is-date-object@1.1.0:
    - node_modules\is-date-object
  is-extglob@2.1.1:
    - node_modules\is-extglob
  is-finalizationregistry@1.1.1:
    - node_modules\is-finalizationregistry
  is-fullwidth-code-point@3.0.0:
    - node_modules\is-fullwidth-code-point
  is-generator-function@1.1.0:
    - node_modules\is-generator-function
  is-glob@4.0.3:
    - node_modules\is-glob
  is-interactive@1.0.0:
    - node_modules\is-interactive
  is-lambda@1.0.1:
    - node_modules\is-lambda
  is-map@2.0.3:
    - node_modules\is-map
  is-negative-zero@2.0.3:
    - node_modules\is-negative-zero
  is-number-object@1.1.1:
    - node_modules\is-number-object
  is-number@7.0.0:
    - node_modules\is-number
  is-regex@1.2.1:
    - node_modules\is-regex
  is-set@2.0.3:
    - node_modules\is-set
  is-shared-array-buffer@1.0.4:
    - node_modules\is-shared-array-buffer
  is-string@1.1.1:
    - node_modules\is-string
  is-symbol@1.1.1:
    - node_modules\is-symbol
  is-typed-array@1.1.15:
    - node_modules\is-typed-array
  is-unicode-supported@0.1.0:
    - node_modules\is-unicode-supported
  is-weakmap@2.0.2:
    - node_modules\is-weakmap
  is-weakref@1.1.1:
    - node_modules\is-weakref
  is-weakset@2.0.4:
    - node_modules\is-weakset
  isarray@2.0.5:
    - node_modules\isarray
  isbinaryfile@4.0.10:
    - node_modules\isbinaryfile
  isbinaryfile@5.0.4:
    - node_modules\app-builder-lib\node_modules\isbinaryfile
  isexe@2.0.0:
    - node_modules\isexe
  iterator.prototype@1.1.5:
    - node_modules\iterator.prototype
  jackspeak@3.4.3:
    - node_modules\jackspeak
  jackspeak@4.1.1:
    - node_modules\rimraf\node_modules\jackspeak
  jake@10.9.2:
    - node_modules\jake
  jiti@1.21.7:
    - node_modules\jiti
  js-tokens@4.0.0:
    - node_modules\js-tokens
  js-yaml@4.1.0:
    - node_modules\js-yaml
  jsbn@1.1.0:
    - node_modules\jsbn
  jsesc@3.1.0:
    - node_modules\jsesc
  json-buffer@3.0.1:
    - node_modules\json-buffer
  json-parse-better-errors@1.0.2:
    - node_modules\json-parse-better-errors
  json-schema-traverse@0.4.1:
    - node_modules\json-schema-traverse
  json-stable-stringify-without-jsonify@1.0.1:
    - node_modules\json-stable-stringify-without-jsonify
  json-stringify-safe@5.0.1:
    - node_modules\json-stringify-safe
  json5@2.2.3:
    - node_modules\json5
  jsonfile@4.0.0:
    - node_modules\@electron\get\node_modules\jsonfile
    - node_modules\electron-winstaller\node_modules\jsonfile
  jsonfile@6.1.0:
    - node_modules\jsonfile
  jsx-ast-utils@3.3.5:
    - node_modules\jsx-ast-utils
  keyv@4.5.4:
    - node_modules\keyv
  lazy-val@1.0.5:
    - node_modules\lazy-val
  levn@0.4.1:
    - node_modules\levn
  lilconfig@3.1.3:
    - node_modules\lilconfig
  lines-and-columns@1.2.4:
    - node_modules\lines-and-columns
  load-json-file@4.0.0:
    - node_modules\load-json-file
  locate-path@6.0.0:
    - node_modules\locate-path
  lodash.merge@4.6.2:
    - node_modules\lodash.merge
  lodash@4.17.21:
    - node_modules\lodash
  log-symbols@4.1.0:
    - node_modules\log-symbols
  loose-envify@1.4.0:
    - node_modules\loose-envify
  lowercase-keys@2.0.0:
    - node_modules\lowercase-keys
  lru-cache@10.4.3:
    - node_modules\path-scurry\node_modules\lru-cache
  lru-cache@11.1.0:
    - node_modules\rimraf\node_modules\lru-cache
  lru-cache@5.1.1:
    - node_modules\@babel\helper-compilation-targets\node_modules\lru-cache
  lru-cache@6.0.0:
    - node_modules\hosted-git-info\node_modules\lru-cache
  lru-cache@7.18.3:
    - node_modules\lru-cache
  lucide-react@0.475.0(react@19.1.1):
    - node_modules\lucide-react
  make-fetch-happen@10.2.1:
    - node_modules\make-fetch-happen
  matcher@3.0.0:
    - node_modules\matcher
  math-intrinsics@1.1.0:
    - node_modules\math-intrinsics
  memorystream@0.3.1:
    - node_modules\memorystream
  merge2@1.4.1:
    - node_modules\merge2
  micromatch@4.0.8:
    - node_modules\micromatch
  mime-db@1.52.0:
    - node_modules\mime-db
  mime-types@2.1.35:
    - node_modules\mime-types
  mime@2.6.0:
    - node_modules\mime
  mimic-fn@2.1.0:
    - node_modules\mimic-fn
  mimic-response@1.0.1:
    - node_modules\mimic-response
  mimic-response@3.1.0:
    - node_modules\decompress-response\node_modules\mimic-response
  minimatch@10.0.3:
    - node_modules\rimraf\node_modules\minimatch
    - node_modules\app-builder-lib\node_modules\minimatch
  minimatch@3.1.2:
    - node_modules\minimatch
  minimatch@5.1.6:
    - node_modules\@electron\node-gyp\node_modules\minimatch
    - node_modules\filelist\node_modules\minimatch
    - node_modules\cacache\node_modules\glob\node_modules\minimatch
  minimatch@9.0.5:
    - node_modules\@typescript-eslint\typescript-estree\node_modules\minimatch
    - node_modules\sucrase\node_modules\minimatch
    - node_modules\@electron\universal\node_modules\minimatch
    - node_modules\config-file-ts\node_modules\minimatch
  minimist@1.2.8:
    - node_modules\minimist
  minipass-collect@1.0.2:
    - node_modules\minipass-collect
  minipass-fetch@2.1.2:
    - node_modules\minipass-fetch
  minipass-flush@1.0.5:
    - node_modules\minipass-flush
  minipass-pipeline@1.2.4:
    - node_modules\minipass-pipeline
  minipass-sized@1.0.3:
    - node_modules\minipass-sized
  minipass@3.3.6:
    - node_modules\minipass
  minipass@5.0.0:
    - node_modules\tar\node_modules\minipass
  minipass@7.1.2:
    - node_modules\rimraf\node_modules\minipass
    - node_modules\sucrase\node_modules\minipass
    - node_modules\config-file-ts\node_modules\minipass
    - node_modules\path-scurry\node_modules\minipass
  minizlib@2.1.2:
    - node_modules\minizlib
  mkdirp@0.5.6:
    - node_modules\temp\node_modules\mkdirp
  mkdirp@1.0.4:
    - node_modules\mkdirp
  ms@2.1.3:
    - node_modules\ms
  mz@2.7.0:
    - node_modules\mz
  nanoid@3.3.11:
    - node_modules\nanoid
  natural-compare@1.4.0:
    - node_modules\natural-compare
  negotiator@0.6.4:
    - node_modules\negotiator
  nice-try@1.0.5:
    - node_modules\nice-try
  node-abi@3.75.0:
    - node_modules\node-abi
  node-api-version@0.2.1:
    - node_modules\node-api-version
  node-releases@2.0.19:
    - node_modules\node-releases
  nopt@6.0.0:
    - node_modules\nopt
  normalize-package-data@2.5.0:
    - node_modules\normalize-package-data
  normalize-path@3.0.0:
    - node_modules\normalize-path
  normalize-range@0.1.2:
    - node_modules\normalize-range
  normalize-url@6.1.0:
    - node_modules\normalize-url
  npm-run-all@4.1.5:
    - node_modules\npm-run-all
  object-assign@4.1.1:
    - node_modules\object-assign
  object-hash@3.0.0:
    - node_modules\object-hash
  object-inspect@1.13.4:
    - node_modules\object-inspect
  object-keys@1.1.1:
    - node_modules\object-keys
  object.assign@4.1.7:
    - node_modules\object.assign
  object.entries@1.1.9:
    - node_modules\object.entries
  object.fromentries@2.0.8:
    - node_modules\object.fromentries
  object.values@1.2.1:
    - node_modules\object.values
  once@1.4.0:
    - node_modules\once
  onetime@5.1.2:
    - node_modules\onetime
  optionator@0.9.4:
    - node_modules\optionator
  ora@5.4.1:
    - node_modules\ora
  own-keys@1.0.1:
    - node_modules\own-keys
  p-cancelable@2.1.1:
    - node_modules\p-cancelable
  p-limit@3.1.0:
    - node_modules\p-limit
  p-locate@5.0.0:
    - node_modules\p-locate
  p-map@4.0.0:
    - node_modules\p-map
  package-json-from-dist@1.0.1:
    - node_modules\package-json-from-dist
  parent-module@1.0.1:
    - node_modules\parent-module
  parse-json@4.0.0:
    - node_modules\parse-json
  path-exists@4.0.0:
    - node_modules\path-exists
  path-is-absolute@1.0.1:
    - node_modules\path-is-absolute
  path-key@2.0.1:
    - node_modules\npm-run-all\node_modules\path-key
  path-key@3.1.1:
    - node_modules\path-key
  path-parse@1.0.7:
    - node_modules\path-parse
  path-scurry@1.11.1:
    - node_modules\path-scurry
  path-scurry@2.0.0:
    - node_modules\rimraf\node_modules\path-scurry
  path-type@3.0.0:
    - node_modules\path-type
  pe-library@0.4.1:
    - node_modules\pe-library
  pend@1.2.0:
    - node_modules\pend
  picocolors@1.1.1:
    - node_modules\picocolors
  picomatch@2.3.1:
    - node_modules\picomatch
  picomatch@4.0.3:
    - node_modules\vite\node_modules\picomatch
    - node_modules\fdir\node_modules\picomatch
    - node_modules\tinyglobby\node_modules\picomatch
  pidtree@0.3.1:
    - node_modules\pidtree
  pify@2.3.0:
    - node_modules\read-cache\node_modules\pify
  pify@3.0.0:
    - node_modules\pify
  pirates@4.0.7:
    - node_modules\pirates
  plist@3.1.0:
    - node_modules\plist
  possible-typed-array-names@1.1.0:
    - node_modules\possible-typed-array-names
  postcss-import@15.1.0(postcss@8.5.6):
    - node_modules\postcss-import
  postcss-js@4.0.1(postcss@8.5.6):
    - node_modules\postcss-js
  postcss-load-config@4.0.2(postcss@8.5.6):
    - node_modules\postcss-load-config
  postcss-nested@6.2.0(postcss@8.5.6):
    - node_modules\postcss-nested
  postcss-selector-parser@6.1.2:
    - node_modules\postcss-selector-parser
  postcss-value-parser@4.2.0:
    - node_modules\postcss-value-parser
  postcss@8.5.6:
    - node_modules\postcss
  postject@1.0.0-alpha.6:
    - node_modules\postject
  prelude-ls@1.2.1:
    - node_modules\prelude-ls
  proc-log@2.0.1:
    - node_modules\proc-log
  progress@2.0.3:
    - node_modules\progress
  promise-inflight@1.0.1:
    - node_modules\promise-inflight
  promise-retry@2.0.1:
    - node_modules\promise-retry
  prop-types@15.8.1:
    - node_modules\prop-types
  pump@3.0.3:
    - node_modules\pump
  punycode@2.3.1:
    - node_modules\punycode
  queue-microtask@1.2.3:
    - node_modules\queue-microtask
  quick-lru@5.1.1:
    - node_modules\quick-lru
  react-dom@19.1.1(react@19.1.1):
    - node_modules\react-dom
  react-hook-form@7.61.1(react@19.1.1):
    - node_modules\react-hook-form
  react-is@16.13.1:
    - node_modules\react-is
  react-refresh@0.17.0:
    - node_modules\react-refresh
  react-remove-scroll-bar@2.3.8(@types/react@19.1.8)(react@19.1.1):
    - node_modules\react-remove-scroll-bar
  react-remove-scroll@2.7.1(@types/react@19.1.8)(react@19.1.1):
    - node_modules\react-remove-scroll
  react-style-singleton@2.2.3(@types/react@19.1.8)(react@19.1.1):
    - node_modules\react-style-singleton
  react@19.1.1:
    - node_modules\react
  read-binary-file-arch@1.0.6:
    - node_modules\read-binary-file-arch
  read-cache@1.0.0:
    - node_modules\read-cache
  read-pkg@3.0.0:
    - node_modules\read-pkg
  readable-stream@3.6.2:
    - node_modules\readable-stream
  readdirp@3.6.0:
    - node_modules\readdirp
  reflect.getprototypeof@1.0.10:
    - node_modules\reflect.getprototypeof
  regexp.prototype.flags@1.5.4:
    - node_modules\regexp.prototype.flags
  require-directory@2.1.1:
    - node_modules\require-directory
  resedit@1.7.2:
    - node_modules\resedit
  resolve-alpn@1.2.1:
    - node_modules\resolve-alpn
  resolve-from@4.0.0:
    - node_modules\resolve-from
  resolve@1.22.10:
    - node_modules\resolve
  resolve@2.0.0-next.5:
    - node_modules\eslint-plugin-react\node_modules\resolve
  responselike@2.0.1:
    - node_modules\responselike
  restore-cursor@3.1.0:
    - node_modules\restore-cursor
  retry@0.12.0:
    - node_modules\retry
  reusify@1.1.0:
    - node_modules\reusify
  rimraf@2.6.3:
    - node_modules\temp\node_modules\rimraf
  rimraf@3.0.2:
    - node_modules\@npmcli\move-file\node_modules\rimraf
    - node_modules\cacache\node_modules\rimraf
  rimraf@6.0.1:
    - node_modules\rimraf
  roarr@2.15.4:
    - node_modules\roarr
  rollup@4.46.1:
    - node_modules\rollup
  run-parallel@1.2.0:
    - node_modules\run-parallel
  rxjs@7.8.2:
    - node_modules\rxjs
  safe-array-concat@1.1.3:
    - node_modules\safe-array-concat
  safe-buffer@5.2.1:
    - node_modules\safe-buffer
  safe-push-apply@1.0.0:
    - node_modules\safe-push-apply
  safe-regex-test@1.1.0:
    - node_modules\safe-regex-test
  safer-buffer@2.1.2:
    - node_modules\safer-buffer
  sanitize-filename@1.6.3:
    - node_modules\sanitize-filename
  sax@1.4.1:
    - node_modules\sax
  scheduler@0.26.0:
    - node_modules\scheduler
  semver-compare@1.0.0:
    - node_modules\semver-compare
  semver@5.7.2:
    - node_modules\npm-run-all\node_modules\semver
    - node_modules\tiny-async-pool\node_modules\semver
    - node_modules\normalize-package-data\node_modules\semver
  semver@6.3.1:
    - node_modules\eslint-plugin-react\node_modules\semver
    - node_modules\@babel\core\node_modules\semver
    - node_modules\@electron\get\node_modules\semver
    - node_modules\@babel\helper-compilation-targets\node_modules\semver
  semver@7.7.2:
    - node_modules\semver
  serialize-error@7.0.1:
    - node_modules\serialize-error
  set-function-length@1.2.2:
    - node_modules\set-function-length
  set-function-name@2.0.2:
    - node_modules\set-function-name
  set-proto@1.0.0:
    - node_modules\set-proto
  shebang-command@1.2.0:
    - node_modules\npm-run-all\node_modules\shebang-command
  shebang-command@2.0.0:
    - node_modules\shebang-command
  shebang-regex@1.0.0:
    - node_modules\npm-run-all\node_modules\shebang-regex
  shebang-regex@3.0.0:
    - node_modules\shebang-regex
  shell-quote@1.8.3:
    - node_modules\shell-quote
  side-channel-list@1.0.0:
    - node_modules\side-channel-list
  side-channel-map@1.0.1:
    - node_modules\side-channel-map
  side-channel-weakmap@1.0.2:
    - node_modules\side-channel-weakmap
  side-channel@1.1.0:
    - node_modules\side-channel
  signal-exit@3.0.7:
    - node_modules\signal-exit
  signal-exit@4.1.0:
    - node_modules\foreground-child\node_modules\signal-exit
  simple-update-notifier@2.0.0:
    - node_modules\simple-update-notifier
  smart-buffer@4.2.0:
    - node_modules\smart-buffer
  socks-proxy-agent@7.0.0:
    - node_modules\socks-proxy-agent
  socks@2.8.6:
    - node_modules\socks
  source-map-js@1.2.1:
    - node_modules\source-map-js
  source-map-support@0.5.21:
    - node_modules\source-map-support
  source-map@0.6.1:
    - node_modules\source-map
  spdx-correct@3.2.0:
    - node_modules\spdx-correct
  spdx-exceptions@2.5.0:
    - node_modules\spdx-exceptions
  spdx-expression-parse@3.0.1:
    - node_modules\spdx-expression-parse
  spdx-license-ids@3.0.21:
    - node_modules\spdx-license-ids
  sprintf-js@1.1.3:
    - node_modules\sprintf-js
  ssri@9.0.1:
    - node_modules\ssri
  stat-mode@1.0.0:
    - node_modules\stat-mode
  stop-iteration-iterator@1.1.0:
    - node_modules\stop-iteration-iterator
  string-width@4.2.3:
    - node_modules\string-width
    - node_modules\string-width-cjs
  string-width@5.1.2:
    - node_modules\@isaacs\cliui\node_modules\string-width
  string.prototype.matchall@4.0.12:
    - node_modules\string.prototype.matchall
  string.prototype.padend@3.1.6:
    - node_modules\string.prototype.padend
  string.prototype.repeat@1.0.0:
    - node_modules\string.prototype.repeat
  string.prototype.trim@1.2.10:
    - node_modules\string.prototype.trim
  string.prototype.trimend@1.0.9:
    - node_modules\string.prototype.trimend
  string.prototype.trimstart@1.0.8:
    - node_modules\string.prototype.trimstart
  string_decoder@1.3.0:
    - node_modules\string_decoder
  strip-ansi@6.0.1:
    - node_modules\strip-ansi-cjs
    - node_modules\strip-ansi
  strip-ansi@7.1.0:
    - node_modules\@isaacs\cliui\node_modules\strip-ansi
  strip-bom@3.0.0:
    - node_modules\strip-bom
  strip-json-comments@3.1.1:
    - node_modules\strip-json-comments
  sucrase@3.35.0:
    - node_modules\sucrase
  sumchecker@3.0.1:
    - node_modules\sumchecker
  supports-color@5.5.0:
    - node_modules\npm-run-all\node_modules\supports-color
  supports-color@7.2.0:
    - node_modules\supports-color
  supports-color@8.1.1:
    - node_modules\concurrently\node_modules\supports-color
  supports-preserve-symlinks-flag@1.0.0:
    - node_modules\supports-preserve-symlinks-flag
  tailwind-merge@3.3.1:
    - node_modules\tailwind-merge
  tailwindcss@3.4.17:
    - node_modules\tailwindcss
  tar@6.2.1:
    - node_modules\tar
  temp-file@3.4.0:
    - node_modules\temp-file
  temp@0.9.4:
    - node_modules\temp
  thenify-all@1.6.0:
    - node_modules\thenify-all
  thenify@3.3.1:
    - node_modules\thenify
  tiny-async-pool@1.3.0:
    - node_modules\tiny-async-pool
  tinyglobby@0.2.14:
    - node_modules\tinyglobby
  tmp-promise@3.0.3:
    - node_modules\tmp-promise
  tmp@0.2.3:
    - node_modules\tmp
  to-regex-range@5.0.1:
    - node_modules\to-regex-range
  tree-kill@1.2.2:
    - node_modules\tree-kill
  truncate-utf8-bytes@1.0.2:
    - node_modules\truncate-utf8-bytes
  ts-api-utils@2.1.0(typescript@5.8.3):
    - node_modules\ts-api-utils
  ts-interface-checker@0.1.13:
    - node_modules\ts-interface-checker
  tslib@2.8.1:
    - node_modules\tslib
  type-check@0.4.0:
    - node_modules\type-check
  type-fest@0.13.1:
    - node_modules\type-fest
  typed-array-buffer@1.0.3:
    - node_modules\typed-array-buffer
  typed-array-byte-length@1.0.3:
    - node_modules\typed-array-byte-length
  typed-array-byte-offset@1.0.4:
    - node_modules\typed-array-byte-offset
  typed-array-length@1.0.7:
    - node_modules\typed-array-length
  typescript-eslint@8.38.0(eslint@9.32.0(jiti@1.21.7))(typescript@5.8.3):
    - node_modules\typescript-eslint
  typescript@5.8.3:
    - node_modules\typescript
  unbox-primitive@1.1.0:
    - node_modules\unbox-primitive
  undici-types@6.21.0:
    - node_modules\undici-types
  unique-filename@2.0.1:
    - node_modules\unique-filename
  unique-slug@3.0.0:
    - node_modules\unique-slug
  universalify@0.1.2:
    - node_modules\@electron\get\node_modules\universalify
    - node_modules\electron-winstaller\node_modules\universalify
  universalify@2.0.1:
    - node_modules\universalify
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    - node_modules\update-browserslist-db
  uri-js@4.4.1:
    - node_modules\uri-js
  use-callback-ref@1.3.3(@types/react@19.1.8)(react@19.1.1):
    - node_modules\use-callback-ref
  use-sidecar@1.1.3(@types/react@19.1.8)(react@19.1.1):
    - node_modules\use-sidecar
  utf8-byte-length@1.0.5:
    - node_modules\utf8-byte-length
  util-deprecate@1.0.2:
    - node_modules\util-deprecate
  validate-npm-package-license@3.0.4:
    - node_modules\validate-npm-package-license
  vite-plugin-electron@0.28.8:
    - node_modules\vite-plugin-electron
  vite@6.3.5(@types/node@22.16.5)(jiti@1.21.7)(yaml@2.8.0):
    - node_modules\vite
  wcwidth@1.0.1:
    - node_modules\wcwidth
  which-boxed-primitive@1.1.1:
    - node_modules\which-boxed-primitive
  which-builtin-type@1.2.1:
    - node_modules\which-builtin-type
  which-collection@1.0.2:
    - node_modules\which-collection
  which-typed-array@1.1.19:
    - node_modules\which-typed-array
  which@1.3.1:
    - node_modules\npm-run-all\node_modules\which
  which@2.0.2:
    - node_modules\which
  word-wrap@1.2.5:
    - node_modules\word-wrap
  wrap-ansi@7.0.0:
    - node_modules\wrap-ansi-cjs
    - node_modules\wrap-ansi
  wrap-ansi@8.1.0:
    - node_modules\@isaacs\cliui\node_modules\wrap-ansi
  wrappy@1.0.2:
    - node_modules\wrappy
  xmlbuilder@15.1.1:
    - node_modules\xmlbuilder
  y18n@5.0.8:
    - node_modules\y18n
  yallist@3.1.1:
    - node_modules\@babel\helper-compilation-targets\node_modules\yallist
  yallist@4.0.0:
    - node_modules\yallist
  yaml@2.8.0:
    - node_modules\yaml
  yargs-parser@21.1.1:
    - node_modules\yargs-parser
  yargs@17.7.2:
    - node_modules\yargs
  yauzl@2.10.0:
    - node_modules\yauzl
  yocto-queue@0.1.0:
    - node_modules\yocto-queue
  zod@3.25.76:
    - node_modules\zod
  zustand@5.0.6(@types/react@19.1.8)(react@19.1.1):
    - node_modules\zustand
ignoredBuilds:
  - esbuild
  - electron
  - electron-winstaller
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: hoisted
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Fri, 01 Aug 2025 07:55:25 GMT
publicHoistPattern:
  - '*'
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@rollup/rollup-android-arm-eabi@4.46.1'
  - '@rollup/rollup-android-arm64@4.46.1'
  - '@rollup/rollup-darwin-arm64@4.46.1'
  - '@rollup/rollup-darwin-x64@4.46.1'
  - '@rollup/rollup-freebsd-arm64@4.46.1'
  - '@rollup/rollup-freebsd-x64@4.46.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.1'
  - '@rollup/rollup-linux-arm64-gnu@4.46.1'
  - '@rollup/rollup-linux-arm64-musl@4.46.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.1'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.1'
  - '@rollup/rollup-linux-riscv64-musl@4.46.1'
  - '@rollup/rollup-linux-s390x-gnu@4.46.1'
  - '@rollup/rollup-linux-x64-gnu@4.46.1'
  - '@rollup/rollup-linux-x64-musl@4.46.1'
  - '@rollup/rollup-win32-arm64-msvc@4.46.1'
  - '@rollup/rollup-win32-ia32-msvc@4.46.1'
  - '@types/plist@3.0.5'
  - '@types/verror@1.10.11'
  - assert-plus@1.0.0
  - astral-regex@2.0.0
  - cli-truncate@2.1.0
  - core-util-is@1.0.2
  - crc@3.8.0
  - dmg-license@1.0.11
  - extsprintf@1.4.1
  - fsevents@2.3.3
  - iconv-corefoundation@1.1.7
  - node-addon-api@1.7.2
  - slice-ansi@3.0.0
  - verror@1.10.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\workspace\ykwy-assistant-app\ykwy-assistant-client\node_modules\.pnpm
virtualStoreDirMaxLength: 60
