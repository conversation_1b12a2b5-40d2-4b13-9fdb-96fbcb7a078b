import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { createErrorResponse, createResponse, handleError } from '../lib/utils';
import { qianniuTcpServer } from '../services/qianniuTcpServer';

const app = new Hono();

// 验证schemas
const RegisterConnectionSchema = z.object({
  connectionId: z.string().min(1, '连接ID不能为空'),
  tcpClientId: z.string().optional(),
});

/**
 * GET /qianniu-tcp/connections - 获取TCP连接状态
 */
app.get('/connections', async (c) => {
  try {
    logger.info('收到获取TCP连接状态请求');

    const connectionsInfo = qianniuTcpServer.getConnectionsInfo();
    logger.debug('TCP连接信息', { connectionsInfo });

    // 获取数据库中的在线客户端信息
    const onlineClients = await prisma.qianniuClient.findMany({
      where: {
        isOnline: true,
        connectionId: { not: '' },
      },
      select: {
        id: true,
        name: true,
        connectionId: true,
        accounts: {
          select: {
            accountName: true,
            shopName: true,
            platformType: true,
          },
        },
      },
    });

    logger.info('数据库中的在线客户端数量', { count: onlineClients.length });

    const responseData = {
      ...connectionsInfo,
      onlineClients,
      unmappedTcpConnections: qianniuTcpServer.getUnmappedTcpConnections(),
    };

    logger.debug('返回TCP连接状态数据', { responseData });
    return c.json(createResponse(responseData, '获取TCP连接状态成功'));
  } catch (error) {
    logger.error('获取TCP连接状态失败', {}, error instanceof Error ? error : new Error(String(error)));
    return handleError(c, error);
  }
});

/**
 * POST /qianniu-tcp/register - 手动注册连接映射
 */
app.post('/register', zValidator('json', RegisterConnectionSchema), async (c) => {
  try {
    const { connectionId, tcpClientId } = c.req.valid('json');
    logger.info('收到注册连接映射请求', { connectionId, tcpClientId: tcpClientId || '未指定' });

    // 检查connectionId是否存在于数据库中
    const client = await prisma.qianniuClient.findFirst({
      where: { connectionId },
    });

    if (!client) {
      logger.warn('连接ID不存在于数据库中', { connectionId });
      return c.json(createErrorResponse('连接ID不存在'), 404);
    }

    logger.info('找到对应的客户端', {
      clientId: client.id,
      clientName: client.name,
      connectionId: client.connectionId,
    });

    // 尝试注册连接映射
    logger.info('开始注册连接映射', { connectionId, tcpClientId });
    const success = qianniuTcpServer.manualRegisterConnection(connectionId, tcpClientId);

    if (!success) {
      logger.error('注册连接映射失败', { connectionId, tcpClientId });
      return c.json(createErrorResponse('注册连接映射失败'), 400);
    }

    const responseData = {
      connectionId,
      tcpClientId: tcpClientId || '自动选择',
      success: true,
    };

    logger.info('连接映射注册成功', { responseData });
    return c.json(createResponse(responseData, '连接映射注册成功'));
  } catch (error) {
    logger.error('注册连接映射异常', {}, error instanceof Error ? error : new Error(String(error)));
    return handleError(c, error);
  }
});

/**
 * DELETE /qianniu-tcp/connections/:connectionId - 删除连接映射
 */
app.delete('/connections/:connectionId', async (c) => {
  try {
    const connectionId = c.req.param('connectionId');

    if (!connectionId) {
      return c.json(createErrorResponse('连接ID不能为空'), 400);
    }

    // 删除映射关系
    const tcpClientId = qianniuTcpServer['connectionIdToClientId'].get(connectionId);
    if (tcpClientId) {
      qianniuTcpServer['connectionIdToClientId'].delete(connectionId);
      qianniuTcpServer['clientIdToConnectionId'].delete(tcpClientId);

      return c.json(
        createResponse(
          {
            connectionId,
            tcpClientId,
            deleted: true,
          },
          '连接映射删除成功',
        ),
      );
    } else {
      return c.json(createErrorResponse('连接映射不存在'), 404);
    }
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /qianniu-tcp/test-send - 测试发送消息
 */
app.post(
  '/test-send',
  zValidator(
    'json',
    z.object({
      connectionId: z.string().min(1, '连接ID不能为空'),
      targetId: z.string().min(1, '目标ID不能为空'),
      message: z.string().min(1, '消息内容不能为空'),
    }),
  ),
  async (c) => {
    try {
      const { connectionId, targetId, message } = c.req.valid('json');
      logger.info('收到测试发送消息请求', {
        connectionId,
        targetId,
        message,
      });

      // 检查连接映射是否存在
      logger.debug('检查连接映射是否存在', { connectionId });
      if (!qianniuTcpServer.hasConnectionMapping(connectionId)) {
        logger.warn('连接映射不存在', { connectionId });
        return c.json(createErrorResponse('连接映射不存在，请先注册连接'), 400);
      }

      logger.info('连接映射存在，开始发送消息', { connectionId });

      // 尝试发送消息
      const success = await qianniuTcpServer.sendTextMessage(connectionId, targetId, message);

      const responseData = {
        connectionId,
        targetId,
        message,
        success,
      };

      logger.info(`消息发送${success ? '成功' : '失败'}`, { responseData });
      return c.json(createResponse(responseData, success ? '消息发送成功' : '消息发送失败'));
    } catch (error) {
      logger.error('测试发送消息异常', {}, error instanceof Error ? error : new Error(String(error)));
      return handleError(c, error);
    }
  },
);

export default app;
