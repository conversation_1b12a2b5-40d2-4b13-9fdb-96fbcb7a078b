/**
 * @prettier
 */
import { logger } from './logger';

/**
 * 千牛连接链路追踪器
 * 用于追踪从注入脚本生成到TCP连接建立的完整流程
 */

export interface ConnectionTrackingContext {
  invitationId: string;
  organizationId: string;
  teamId: string;
  connectionId?: string;
  tcpClientId?: string;
  phase: 'script_generated' | 'websocket_connected' | 'websocket_activated' | 'tcp_connected' | 'fully_linked';
  timestamp: string;
  metadata?: Record<string, unknown>;
}

class ConnectionTracker {
  private connections = new Map<string, ConnectionTrackingContext>();

  /**
   * 记录注入脚本生成
   */
  trackScriptGeneration(invitationId: string, organizationId: string, teamId: string, metadata?: Record<string, unknown>) {
    const context: ConnectionTrackingContext = {
      invitationId,
      organizationId,
      teamId,
      phase: 'script_generated',
      timestamp: new Date().toISOString(),
      metadata,
    };

    this.connections.set(invitationId, context);

    logger.info('🎯 [连接追踪] 注入脚本已生成', {
      invitationId,
      organizationId,
      teamId,
      userAgent: metadata?.['userAgent'],
      ip: metadata?.['ip'],
      phase: 'script_generated',
    });
  }

  /**
   * 记录WebSocket连接建立
   */
  trackWebSocketConnection(invitationId: string, connectionId: string, metadata?: Record<string, unknown>) {
    const context = this.connections.get(invitationId);
    if (context) {
      context.connectionId = connectionId;
      context.phase = 'websocket_connected';
      context.timestamp = new Date().toISOString();
      context.metadata = { ...context.metadata, ...metadata };

      logger.info('🔗 [连接追踪] WebSocket连接已建立', {
        invitationId,
        connectionId,
        organizationId: context.organizationId,
        teamId: context.teamId,
        clientInfo: metadata?.['clientInfo'],
        phase: 'websocket_connected',
      });
    } else {
      logger.warn('⚠️ [连接追踪] WebSocket连接建立但未找到对应的邀请记录', {
        invitationId,
        connectionId,
      });
    }
  }

  /**
   * 记录WebSocket连接激活（数据库状态更新）
   */
  trackWebSocketActivation(invitationId: string, activationResult: { clientsUpdated: number; newClientCreated?: boolean }) {
    const context = this.connections.get(invitationId);
    if (context) {
      context.phase = 'websocket_activated';
      context.timestamp = new Date().toISOString();

      logger.info('✅ [连接追踪] WebSocket连接已激活', {
        invitationId,
        connectionId: context.connectionId,
        organizationId: context.organizationId,
        teamId: context.teamId,
        clientsUpdated: activationResult.clientsUpdated,
        newClientCreated: activationResult.newClientCreated,
        phase: 'websocket_activated',
      });
    } else {
      logger.warn('⚠️ [连接追踪] WebSocket激活但未找到对应的连接记录', {
        invitationId,
        activationResult,
      });
    }
  }

  /**
   * 记录TCP客户端连接
   */
  trackTcpConnection(tcpClientId: string, username: string, loginID: string, connectionId?: string) {
    logger.info('🔌 [连接追踪] TCP客户端已连接', {
      tcpClientId,
      username,
      loginID,
      connectionId,
      phase: 'tcp_connected',
    });

    // 如果找到了对应的connectionId，更新追踪记录
    if (connectionId) {
      for (const [invitationId, context] of this.connections.entries()) {
        if (context.connectionId === connectionId) {
          context.tcpClientId = tcpClientId;
          context.phase = 'fully_linked';
          context.timestamp = new Date().toISOString();

          logger.info('🎉 [连接追踪] 连接链路完全建立', {
            invitationId,
            connectionId,
            tcpClientId,
            organizationId: context.organizationId,
            teamId: context.teamId,
            username,
            loginID,
            phase: 'fully_linked',
            totalDuration: this.calculateDuration(context),
          });
          break;
        }
      }
    }
  }

  /**
   * 记录TCP连接匹配失败
   */
  trackTcpMatchingFailure(
    tcpClientId: string,
    username: string,
    loginID: string,
    diagnostics: {
      onlineClientsCount: number;
      accountsChecked: number;
      availableConnectionIds: string[];
    },
  ) {
    logger.error('❌ [连接追踪] TCP连接匹配失败', {
      tcpClientId,
      username,
      loginID,
      diagnostics,
      phase: 'tcp_matching_failed',
    });
  }

  /**
   * 记录连接断开
   */
  trackDisconnection(identifier: string, type: 'websocket' | 'tcp', reason?: string) {
    logger.info('🔌 [连接追踪] 连接已断开', {
      identifier,
      type,
      reason,
      timestamp: new Date().toISOString(),
    });

    // 更新相关的追踪记录
    for (const [invitationId, context] of this.connections.entries()) {
      if ((type === 'websocket' && context.connectionId === identifier) || (type === 'tcp' && context.tcpClientId === identifier)) {
        logger.info('📊 [连接追踪] 连接会话结束', {
          invitationId,
          connectionId: context.connectionId,
          tcpClientId: context.tcpClientId,
          organizationId: context.organizationId,
          teamId: context.teamId,
          finalPhase: context.phase,
          sessionDuration: this.calculateDuration(context),
        });
      }
    }
  }

  /**
   * 获取连接状态诊断信息
   */
  getDiagnostics(invitationId?: string) {
    if (invitationId) {
      const context = this.connections.get(invitationId);
      return context
        ? {
          invitationId,
          currentPhase: context.phase,
          connectionId: context.connectionId,
          tcpClientId: context.tcpClientId,
          duration: this.calculateDuration(context),
          lastUpdate: context.timestamp,
        }
        : null;
    }

    // 返回所有连接的概览
    const overview = {
      totalConnections: this.connections.size,
      byPhase: {} as Record<string, number>,
      activeConnections: [] as any[],
    };

    for (const [invitationId, context] of this.connections.entries()) {
      overview.byPhase[context.phase] = (overview.byPhase[context.phase] || 0) + 1;
      if (context.phase === 'fully_linked') {
        overview.activeConnections.push({
          invitationId,
          connectionId: context.connectionId,
          tcpClientId: context.tcpClientId,
          duration: this.calculateDuration(context),
        });
      }
    }

    return overview;
  }

  /**
   * 计算连接持续时间
   */
  private calculateDuration(context: ConnectionTrackingContext): string {
    const start = this.connections.get(context.invitationId)?.timestamp;
    if (!start) return 'unknown';

    const startTime = new Date(start).getTime();
    const currentTime = new Date().getTime();
    const duration = currentTime - startTime;

    return `${(duration / 1000).toFixed(2)}s`;
  }

  /**
   * 清理过期的追踪记录（24小时后）
   */
  cleanup() {
    const now = new Date().getTime();
    const expireTime = 24 * 60 * 60 * 1000; // 24小时

    for (const [invitationId, context] of this.connections.entries()) {
      const contextTime = new Date(context.timestamp).getTime();
      if (now - contextTime > expireTime) {
        this.connections.delete(invitationId);
        logger.debug('🧹 [连接追踪] 清理过期追踪记录', { invitationId });
      }
    }
  }
}

// 创建全局追踪器实例
export const connectionTracker = new ConnectionTracker();

// 定期清理过期记录
setInterval(
  () => {
    connectionTracker.cleanup();
  },
  60 * 60 * 1000,
); // 每小时清理一次
