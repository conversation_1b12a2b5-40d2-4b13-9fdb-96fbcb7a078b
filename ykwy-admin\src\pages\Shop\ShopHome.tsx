import { Area, Column, Line, Pie } from '@ant-design/charts';
import {
  Area<PERSON>hartOutlined,
  Bar<PERSON><PERSON>Outlined,
  BulbOutlined,
  CheckCircleOutlined,
  LineChartOutlined,
  PieChartOutlined,
  RobotOutlined,
  SwapOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Card, Progress } from 'antd';
import React, { useMemo, useState } from 'react';

// 生成最近7天的日期数组
const generateLast7Days = () => {
  const dates = [];
  const today = new Date();

  for (let i = 6; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    dates.push(`${month}/${day}`);
  }

  return dates;
};

// 生成日期范围描述
const getDateRangeDescription = () => {
  const today = new Date();
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - 6);

  const startMonth = startDate.getMonth() + 1;
  const startDay = startDate.getDate();
  const endMonth = today.getMonth() + 1;
  const endDay = today.getDate();

  return `${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`;
};

// 图表错误边界组件
class ChartErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: {
    children: React.ReactNode;
    fallback?: React.ReactNode;
  }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('Chart render error:', error);
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Chart error details:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="text-center text-gray-400 text-sm py-4">
            图表加载失败
          </div>
        )
      );
    }

    return this.props.children;
  }
}

const ShopHome: React.FC = () => {
  // 动态生成最近7天日期
  const last7Days = useMemo(() => generateLast7Days(), []);
  const dateRangeDesc = useMemo(() => getDateRangeDescription(), []);

  // 接待量数据（使用动态日期）
  const receptionData = useMemo(
    () => [
      {
        title: '总接待量',
        value: '2,865',
        unit: '人次',
        icon: <TeamOutlined className="text-xl" />,
        chartData: last7Days.map((day, index) => ({
          day,
          count: [245, 312, 189, 456, 398, 278, 987][index],
        })),
      },
      {
        title: '全自动接待人数',
        value: '1,542',
        unit: '人',
        icon: <RobotOutlined className="text-xl" />,
        chartData: last7Days.map((day, index) => ({
          day,
          count: [142, 189, 98, 234, 201, 145, 533][index],
        })),
      },
      {
        title: '辅助接待人数',
        value: '1,323',
        unit: '人',
        icon: <UserOutlined className="text-xl" />,
        chartData: last7Days.map((day, index) => ({
          day,
          count: [103, 123, 91, 222, 197, 133, 454][index],
        })),
      },
    ],
    [last7Days],
  );

  // 解决率数据
  const resolutionData = [
    {
      title: '转人工率',
      value: '12.8',
      unit: '%',
      icon: <SwapOutlined className="text-xl" />,
      color: '#ff7875',
      chartData: [
        { type: '已转人工', value: 12.8 },
        { type: '未转人工', value: 87.2 },
      ],
    },
    {
      title: '智能辅助回复率',
      value: '87.2',
      unit: '%',
      icon: <BulbOutlined className="text-xl" />,
      color: '#40a9ff',
      chartData: [
        { type: '智能回复', value: 87.2 },
        { type: '人工回复', value: 12.8 },
      ],
    },
    {
      title: '问题解决率',
      value: '94.5',
      unit: '%',
      icon: <CheckCircleOutlined className="text-xl" />,
      color: '#73d13d',
      chartData: [
        { type: '已解决', value: 94.5 },
        { type: '未解决', value: 5.5 },
      ],
    },
  ];

  // 图表类型状态管理
  const [chartTypes, setChartTypes] = useState<{ [key: number]: string }>({
    0: 'column', // 总接待量默认柱状图
    1: 'line', // 全自动接待默认折线图
    2: 'area', // 辅助接待默认面积图
  });

  const [resolutionChartTypes, setResolutionChartTypes] = useState<{
    [key: number]: string;
  }>({
    0: 'pie', // 转人工率默认饼图
    1: 'progress', // 智能辅助回复率默认进度条
    2: 'pie', // 问题解决率默认饼图
  });

  // 切换接待量图表类型
  const toggleReceptionChart = (index: number) => {
    const types = ['column', 'line', 'area'];
    const currentIndex = types.indexOf(chartTypes[index]);
    const nextIndex = (currentIndex + 1) % types.length;
    setChartTypes((prev) => ({ ...prev, [index]: types[nextIndex] }));
  };

  // 切换解决率图表类型
  const toggleResolutionChart = (index: number) => {
    const types = ['pie', 'progress'];
    const currentIndex = types.indexOf(resolutionChartTypes[index]);
    const nextIndex = (currentIndex + 1) % types.length;
    setResolutionChartTypes((prev) => ({ ...prev, [index]: types[nextIndex] }));
  };

  // 柱状图配置
  const getColumnConfig = (data: any[]) => ({
    data: data || [],
    xField: 'day',
    yField: 'count',
    height: 120,
    autoFit: true,
    columnStyle: {
      radius: [4, 4, 0, 0],
    },
    color: '#40a9ff',
    xAxis: {
      label: {
        style: {
          fontSize: 10,
          fill: '#666',
        },
      },
      line: null,
      tickLine: null,
    },
    yAxis: {
      label: {
        style: {
          fontSize: 10,
          fill: '#666',
        },
      },
      grid: {
        line: {
          style: {
            stroke: '#f0f0f0',
            lineWidth: 1,
          },
        },
      },
    },
  });

  // 折线图配置
  const getLineConfig = (data: any[]) => ({
    data: data || [],
    xField: 'day',
    yField: 'count',
    height: 120,
    autoFit: true,
    color: '#52c41a',
    smooth: true,
    point: {
      size: 4,
      shape: 'circle',
    },
    xAxis: {
      label: {
        style: {
          fontSize: 10,
          fill: '#666',
        },
      },
      line: null,
      tickLine: null,
    },
    yAxis: {
      label: {
        style: {
          fontSize: 10,
          fill: '#666',
        },
      },
      grid: {
        line: {
          style: {
            stroke: '#f0f0f0',
            lineWidth: 1,
          },
        },
      },
    },
  });

  // 面积图配置
  const getAreaConfig = (data: any[]) => ({
    data: data || [],
    xField: 'day',
    yField: 'count',
    height: 120,
    autoFit: true,
    color: '#722ed1',
    smooth: true,
    areaStyle: {
      fillOpacity: 0.6,
    },
    xAxis: {
      label: {
        style: {
          fontSize: 10,
          fill: '#666',
        },
      },
      line: null,
      tickLine: null,
    },
    yAxis: {
      label: {
        style: {
          fontSize: 10,
          fill: '#666',
        },
      },
      grid: {
        line: {
          style: {
            stroke: '#f0f0f0',
            lineWidth: 1,
          },
        },
      },
    },
  });

  // 饼图配置
  const getPieConfig = (data: any[], color: string) => ({
    data: data || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    height: 96,
    width: 96,
    autoFit: false,
    color: [color, '#f0f0f0'],
    legend: false,
    label: false,
    interactions: [{ type: 'element-active' }],
  });

  // 渲染接待量图表
  const renderReceptionChart = (data: any[], type: string) => {
    switch (type) {
      case 'line':
        return <Line {...getLineConfig(data)} />;
      case 'area':
        return <Area {...getAreaConfig(data)} />;
      case 'column':
      default:
        return <Column {...getColumnConfig(data)} />;
    }
  };

  // 获取图表切换按钮图标
  const getChartIcon = (type: string) => {
    switch (type) {
      case 'line':
        return <LineChartOutlined />;
      case 'area':
        return <AreaChartOutlined />;
      case 'pie':
        return <PieChartOutlined />;
      case 'column':
      default:
        return <BarChartOutlined />;
    }
  };

  return (
    <div className="bg-background p-8">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            店铺数据总览
          </h1>
          <p className="text-muted-foreground">
            实时监控店铺接待量和问题解决情况
          </p>
        </div>

        {/* 接待量数据 */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4 flex items-center">
            <TeamOutlined className="mr-2" />
            接待量统计
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {receptionData.map((item, index) => (
              <Card
                key={item.title}
                className="border-border bg-card hover:shadow-md transition-shadow duration-200"
                style={{ minHeight: '240px' }}
              >
                <div className="p-6 h-full flex flex-col">
                  {/* 顶部区域：图标和数据 */}
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-primary/10 rounded-lg mt-1">
                        {item.icon}
                      </div>
                      <div>
                        <div className="text-muted-foreground text-sm mb-1">
                          {item.title}
                        </div>
                        <div className="flex items-baseline">
                          <span className="text-2xl font-bold text-foreground">
                            {item.value}
                          </span>
                          <span className="text-muted-foreground text-sm ml-1">
                            {item.unit}
                          </span>
                        </div>
                      </div>
                    </div>
                    {/* 图表切换按钮 */}
                    <Button
                      type="text"
                      size="small"
                      icon={getChartIcon(chartTypes[index])}
                      onClick={() => toggleReceptionChart(index)}
                      className="text-muted-foreground hover:text-foreground"
                    />
                  </div>

                  {/* 底部：图表 */}
                  <div className="flex-1 flex flex-col">
                    <div className="text-xs text-muted-foreground mb-3">
                      {dateRangeDesc}
                    </div>
                    <div className="flex-1">
                      <ChartErrorBoundary>
                        {renderReceptionChart(
                          item.chartData,
                          chartTypes[index],
                        )}
                      </ChartErrorBoundary>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* 解决率数据 */}
        <div>
          <h2 className="text-xl font-semibold text-foreground mb-4 flex items-center">
            <BulbOutlined className="mr-2" />
            解决率统计
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {resolutionData.map((item, index) => (
              <Card
                key={item.title}
                className="border-border bg-card hover:shadow-md transition-shadow duration-200"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      {item.icon}
                    </div>
                    {/* 图表切换按钮 */}
                    <Button
                      type="text"
                      size="small"
                      icon={getChartIcon(resolutionChartTypes[index])}
                      onClick={() => toggleResolutionChart(index)}
                      className="text-muted-foreground hover:text-foreground"
                    />
                  </div>

                  {/* 主要内容：左侧数据，右侧图表 */}
                  <div className="flex items-center justify-between">
                    {/* 左侧数据 */}
                    <div className="flex-1">
                      <div className="text-muted-foreground text-sm mb-2">
                        {item.title}
                      </div>
                      <div className="flex items-baseline mb-3">
                        <span className="text-3xl font-bold text-foreground">
                          {item.value}
                        </span>
                        <span className="text-muted-foreground text-sm ml-1">
                          {item.unit}
                        </span>
                      </div>
                      {resolutionChartTypes[index] === 'progress' && (
                        <Progress
                          percent={parseFloat(item.value)}
                          strokeColor={item.color}
                          showInfo={false}
                          size="small"
                        />
                      )}
                      {resolutionChartTypes[index] === 'pie' && (
                        <div className="text-xs text-muted-foreground mt-2">
                          比例分布
                        </div>
                      )}
                    </div>

                    {/* 右侧图表 */}
                    {resolutionChartTypes[index] === 'pie' && (
                      <div className="w-24 h-24 ml-4 flex items-center justify-center">
                        <ChartErrorBoundary
                          fallback={
                            <div className="w-full h-full bg-gray-100 rounded-full flex items-center justify-center text-xs text-gray-400">
                              图表
                            </div>
                          }
                        >
                          <Pie
                            {...getPieConfig(item.chartData, item.color)}
                            onReady={() =>
                              console.log(
                                `饼图 ${index} 渲染成功`,
                                item.chartData,
                              )
                            }
                            onError={(error: Error) =>
                              console.error(`饼图 ${index} 渲染错误:`, error)
                            }
                          />
                        </ChartErrorBoundary>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShopHome;
