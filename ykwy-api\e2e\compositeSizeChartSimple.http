### 创建复合简单尺码表
POST http://localhost:3000/v1/composite-size-chart-simple
Content-Type: application/json

{
  "name": "测试情侣装尺码表",
  "type": "情侣装尺码表",
  "sizeRange": "男款: 成人体重(70.0kg～90.0kg),成人身高(170.0cm～185.0cm) 女款: 成人体重(50.0kg～70.0kg),成人身高(155.0cm～170.0cm)",
  "sizeValue": "男款: M,L,XL 女款: S,M,L"
}

### 获取复合简单尺码表列表 (首次查询会缓存结果)
GET http://localhost:3000/v1/composite-size-chart-simple

### 获取复合简单尺码表列表 (第二次查询会从缓存返回，速度更快)
GET http://localhost:3000/v1/composite-size-chart-simple

### 根据ID获取复合简单尺码表 (需要替换为实际ID)
GET http://localhost:3000/v1/composite-size-chart-simple/{{compositeSizeChartSimpleId}}

### 更新复合简单尺码表 (需要替换为实际ID)
PUT http://localhost:3000/v1/composite-size-chart-simple/{{compositeSizeChartSimpleId}}
Content-Type: application/json

{
  "name": "更新后的情侣装尺码表",
  "type": "情侣装尺码表",
  "sizeRange": "男款: 成人体重(75.0kg～95.0kg),成人身高(175.0cm～190.0cm) 女款: 成人体重(55.0kg～75.0kg),成人身高(160.0cm～175.0cm)",
  "sizeValue": "男款: L,XL,XXL 女款: M,L,XL"
}

### 删除复合简单尺码表 (需要替换为实际ID)
DELETE http://localhost:3000/v1/composite-size-chart-simple/{{compositeSizeChartSimpleId}}

### 检查名称是否已存在 - 新名称
GET http://localhost:3000/v1/composite-size-chart-simple/check-name?name=新复合尺码表名称
Content-Type: application/json

### 检查名称是否已存在 - 排除指定ID
GET http://localhost:3000/v1/composite-size-chart-simple/check-name?name=现有名称&excludeId=uuid-here
Content-Type: application/json

### 创建复合简单尺码表 - 首次创建
POST {{base_url}}/api/composite-sizechart-simple
Content-Type: application/json

{
  "name": "Upsert测试复合尺码表",
  "type": "服装",
  "sizeRange": "XS-XL",
  "sizeValue": "XS,S,M,L,XL"
}

### 创建复合简单尺码表 - 相同name，会自动更新现有记录
POST {{base_url}}/api/composite-sizechart-simple
Content-Type: application/json

{
  "name": "Upsert测试复合尺码表",
  "type": "运动服装",
  "sizeRange": "XXS-XXL",
  "sizeValue": "XXS,XS,S,M,L,XL,XXL"
}

### 创建复合简单尺码表 - 不同name，会创建新记录
POST {{base_url}}/api/composite-sizechart-simple
Content-Type: application/json

{
  "name": "另一个复合尺码表",
  "type": "鞋子",
  "sizeRange": "35-42",
  "sizeValue": "35,36,37,38,39,40,41,42"
}

### 验证upsert结果 - 查看列表
GET {{base_url}}/api/composite-sizechart-simple
Content-Type: application/json 