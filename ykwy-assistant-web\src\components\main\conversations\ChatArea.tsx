import { useCallback, useEffect, useRef, useState } from 'react';

import { useAppStore } from '../../../lib/store';
import { useConversation, useConversationMessages, useSendMessage } from '../../../services';
import { useSetConversationAutoReply } from '../../../services/hooks/useConversationAutoReply';
import { LoadingState } from '../../common/LoadingStates';
import QueryErrorBoundary from '../../common/QueryErrorBoundary';
import MessageInput, { MessageInputRef } from './MessageInput';
import MessageList from './MessageList';

interface ChatAreaProps {
  conversationId?: string;
}

export default function ChatArea({ conversationId }: ChatAreaProps) {
  const messageInputRef = useRef<MessageInputRef>(null);
  const messageListRef = useRef<HTMLDivElement>(null);

  // 检测是否为小屏幕PC
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      // 768px 以上 1024px 以下为小屏幕PC
      setIsSmallScreen(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 为每个对话保存输入框内容的状态
  const conversationInputsRef = useRef<Record<string, string>>({});

  // 存储上一次获取的消息ID，用于检测新消息
  const prevMessagesRef = useRef<Record<string, string[]>>({});

  // 记录上一次的会话ID，用于检测对话切换
  const prevConversationIdRef = useRef<string | undefined>(undefined);

  // 记录是否需要滚动到底部
  const shouldScrollToBottomRef = useRef(false);

  // 从store中获取客户消息通知相关方法
  const addCustomerMessage = useAppStore((state) => state.addCustomerMessage);
  const setMessageAutoReplied = useAppStore((state) => state.setMessageAutoReplied);
  const markConversationAsRead = useAppStore((state) => state.markConversationAsRead);

  // 获取对话详情
  const { data: conversation, isLoading: conversationLoading, error: conversationError, refetch: refetchConversation } = useConversation(conversationId);

  // 获取消息列表
  const { data: messagesData, isLoading: messagesLoading, error: messagesError, refetch: refetchMessages } = useConversationMessages(conversationId);

  // 滚动到底部的回调函数
  const scrollToBottom = useCallback(() => {
    if (!messageListRef.current) return;

    // 寻找消息列表底部锚点并滚动
    const scrollContainer = messageListRef.current.querySelector('.overflow-y-auto');
    if (scrollContainer) {
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
    }
  }, []);

  // 发送消息的mutation
  const sendMessageMutation = useSendMessage();

  // 获取设置自动回复的mutation
  const setAutoReplyMutation = useSetConversationAutoReply();

  // 处理收到的新消息
  useEffect(() => {
    if (!conversationId || !messagesData?.data || messagesData.data.length === 0) return;

    // 获取当前会话的历史消息ID
    const previousMessageIds = prevMessagesRef.current[conversationId] || [];

    // 获取当前所有消息ID
    const currentMessageIds = messagesData.data.map((msg) => msg.id);

    // 找出新消息
    const newMessages = messagesData.data.filter((msg) => !previousMessageIds.includes(msg.id) && msg.senderType === 'CUSTOMER');

    // 检查是否有新消息
    const hasNewMessages = currentMessageIds.length > previousMessageIds.length;

    // 如果有新消息，标记需要滚动到底部
    if (hasNewMessages) {
      shouldScrollToBottomRef.current = true;

      // 短暂延迟后滚动到底部
      setTimeout(() => {
        scrollToBottom();
      }, 150);
    }

    // 更新历史消息ID记录
    prevMessagesRef.current[conversationId] = currentMessageIds;

    // 如果是当前正在查看的会话，标记为已读
    if (conversationId && document.visibilityState === 'visible') {
      markConversationAsRead(conversationId);
    } else {
      // 添加新的客户消息通知
      newMessages.forEach((msg) => {
        addCustomerMessage({
          conversationId,
          messageId: msg.id,
          customerName: conversation?.customer?.nickname || '未知客户',
          timestamp: msg.sentAt,
          isAutoReplied: conversation?.autoReplyEnabled || false,
        });
      });
    }

    // 设置自动回复状态
    if (conversation) {
      setMessageAutoReplied(conversationId, conversation.autoReplyEnabled);
    }
  }, [conversationId, messagesData, addCustomerMessage, setMessageAutoReplied, markConversationAsRead, conversation, scrollToBottom]);

  // 当切换对话时，将当前对话标记为已读
  useEffect(() => {
    if (!conversationId) return;

    // 检查是否是对话切换或首次加载
    if (prevConversationIdRef.current !== conversationId) {
      // 立即标记为已读
      markConversationAsRead(conversationId);

      // 标记需要滚动到底部
      shouldScrollToBottomRef.current = true;

      // 短暂延迟后滚动到底部，确保内容已加载
      setTimeout(() => {
        scrollToBottom();
      }, 200);

      // 更新上一次对话ID
      prevConversationIdRef.current = conversationId;
    }
  }, [conversationId, markConversationAsRead, scrollToBottom]);

  // 确保聊天界面可见时标记为已读
  useEffect(() => {
    if (!conversationId) return;

    // 处理页面可见性变化
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        markConversationAsRead(conversationId);
      }
    };

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 首次加载也标记为已读
    if (document.visibilityState === 'visible') {
      markConversationAsRead(conversationId);
    }

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [conversationId, markConversationAsRead]);
  // 当切换对话时，恢复目标对话的输入内容
  useEffect(() => {
    if (!conversationId || !messageInputRef.current) return;

    // 恢复当前对话的输入内容
    const savedContent = conversationInputsRef.current[conversationId] || '';
    messageInputRef.current.setMessage(savedContent);
  }, [conversationId]);

  // 处理输入内容变化，实时保存到对应对话
  const handleInputChange = useCallback(
    (content: string) => {
      if (conversationId) {
        conversationInputsRef.current[conversationId] = content;
      }
    },
    [conversationId],
  );

  // 处理发送消息
  const handleSendMessage = useCallback(
    (content: string) => {
      if (!conversationId || !content.trim()) return;

      // 获取当前自动回复状态
      const autoReplyEnabled = conversation?.autoReplyEnabled || false;

      sendMessageMutation.mutate(
        {
          conversationId,
          content,
        },
        {
          onSuccess: () => {
            // 发送成功后，清空当前对话的保存状态
            conversationInputsRef.current[conversationId] = '';

            // 标记需要滚动到底部
            shouldScrollToBottomRef.current = true;

            // 延迟滚动，确保新消息已加载
            setTimeout(() => {
              scrollToBottom();
            }, 200);

            // 消息列表会自动更新并滚动到底部
            if (messageInputRef.current) {
              messageInputRef.current.focus();
            }

            // 如果当前自动回复是关闭状态，发送完消息后重新开启
            if (!autoReplyEnabled) {
              console.log('[ChatArea] Re-enabling auto-reply after sending message');
              setAutoReplyMutation.mutate(
                {
                  conversationId,
                  enabled: true,
                },
                {
                  onSuccess: (result) => {
                    console.log('[ChatArea] Auto-reply re-enabled:', result);
                  },
                  onError: (error) => {
                    console.error('[ChatArea] Failed to re-enable auto-reply:', error);
                  },
                },
              );
            }
          },
        },
      );
    },
    [conversationId, sendMessageMutation, scrollToBottom, conversation?.autoReplyEnabled, setAutoReplyMutation],
  );

  // 处理AI推荐使用
  useEffect(() => {
    const handleUseAIRecommendation = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { content } = customEvent.detail;
      if (content && messageInputRef.current) {
        // 使用setMessage方法正确更新组件状态
        messageInputRef.current.setMessage(content);
        messageInputRef.current.focus();
      }
    };

    window.addEventListener('useAIRecommendation', handleUseAIRecommendation as EventListener);
    return () => {
      window.removeEventListener('useAIRecommendation', handleUseAIRecommendation as EventListener);
    };
  }, []);

  // 如果没有选中对话，显示提示
  if (!conversationId) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center text-gray-500">
          <div className="mb-4">
            <svg className="w-16 h-16 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
          </div>
          <p className="text-lg font-medium">请选择一个对话开始聊天</p>
          <p className="text-sm text-gray-400 mt-1">从左侧对话列表中选择一个对话</p>
        </div>
      </div>
    );
  }

  const isLoading = conversationLoading || messagesLoading;
  const hasError = conversationError || messagesError;

  return (
    <div className="flex-1 flex flex-col bg-white h-full">
      {/* 聊天头部 */}
      <div className={`flex-shrink-0 p-4 border-b border-gray-200 flex justify-between items-center bg-white shadow-sm ${isSmallScreen ? 'flex-wrap gap-2' : ''}`}>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
            {conversation?.customer?.avatar ? (
              <img src={conversation.customer.avatar} alt={conversation?.customer?.nickname || '客户'} className="w-full h-full rounded-full object-cover" />
            ) : (
              <span className="text-indigo-600 font-medium">{conversation?.customer?.nickname?.slice(0, 1) || '客'}</span>
            )}
          </div>

          <div>
            <div className="font-medium flex items-center">
              {conversation?.customer?.nickname || '未知客户'}
              {conversation?.customer?.vipLevel && <span className="ml-2 text-xs px-2 py-0.5 bg-yellow-100 text-yellow-800 rounded-full">VIP{conversation.customer.vipLevel}</span>}
              {isLoading && <div className="ml-2 w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>}
            </div>
            <div className="text-xs text-gray-500">{conversation?.platform?.name || '未知平台'}</div>
          </div>
        </div>

        {/* AI状态显示 */}
        <div className={`flex items-center gap-2 ${isSmallScreen ? 'w-full justify-end' : ''}`}>
          <div className={`text-sm px-3 py-1 rounded ${conversation?.autoReplyEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {conversation?.autoReplyEnabled ? 'AI自动回复中' : '手动回复模式'}
          </div>
        </div>
      </div>

      {/* 消息列表区域 */}
      <div className="flex-1 flex flex-col overflow-hidden min-h-0">
        <QueryErrorBoundary
          error={hasError}
          onRetry={() => {
            refetchConversation();
            refetchMessages();
          }}
        >
          {isLoading ? <LoadingState message="加载消息中..." /> : <MessageList ref={messageListRef} messages={messagesData?.data || []} onScrollToBottom={scrollToBottom} />}
        </QueryErrorBoundary>
      </div>

      {/* 消息输入框 */}
      <div className="flex-shrink-0 border-t border-gray-200 p-4 bg-white">
        <MessageInput onSendMessage={handleSendMessage} disabled={sendMessageMutation.isPending} loading={sendMessageMutation.isPending} onInputChange={handleInputChange} ref={messageInputRef} />
      </div>
    </div>
  );
}
