export interface FrontendLogRequest {
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: number;
  url?: string;
  userAgent?: string;
  userId?: string;
  sessionId?: string;
  module: string;
  details?: {
    method?: string;
    requestUrl?: string;
    requestData?: Record<string, unknown> | string | null;
    responseStatus?: number;
    responseData?: Record<string, unknown> | string | null;
    duration?: number;
    stack?: string;
    errorType?: string;
  };
}
