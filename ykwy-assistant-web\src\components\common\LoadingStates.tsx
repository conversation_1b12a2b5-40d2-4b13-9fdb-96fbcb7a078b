import { Loader2 } from 'lucide-react';
import { ReactNode } from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return <Loader2 className={`animate-spin text-gray-500 ${sizeClasses[size]} ${className}`} />;
}

interface LoadingStateProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingState({ message = '加载中...', size = 'md', className = '' }: LoadingStateProps) {
  return (
    <div className={`flex flex-col items-center justify-center py-8 ${className}`}>
      <LoadingSpinner size={size} className="mb-3" />
      <p className="text-sm text-gray-600">{message}</p>
    </div>
  );
}

interface InlineLoadingProps {
  message?: string;
  className?: string;
}

export function InlineLoading({ message = '加载中...', className = '' }: InlineLoadingProps) {
  return (
    <div className={`flex items-center gap-2 text-sm text-gray-600 ${className}`}>
      <LoadingSpinner size="sm" />
      <span>{message}</span>
    </div>
  );
}

interface SkeletonProps {
  className?: string;
  count?: number;
}

export function Skeleton({ className = '', count = 1 }: SkeletonProps) {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className={`animate-pulse bg-gray-200 rounded ${className}`} />
      ))}
    </>
  );
}

// 预定义的骨架屏组件
export function MessageSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className="flex gap-3">
          <Skeleton className="w-8 h-8 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-16 w-full rounded-lg" />
          </div>
        </div>
      ))}
    </div>
  );
}

export function ConversationSkeleton() {
  return (
    <div className="space-y-3">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="flex gap-3 p-3">
          <Skeleton className="w-10 h-10 rounded-full" />
          <div className="flex-1 space-y-2">
            <div className="flex justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-16" />
            </div>
            <Skeleton className="h-4 w-full" />
          </div>
        </div>
      ))}
    </div>
  );
}

export function AIRecommendationSkeleton() {
  return (
    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg space-y-3">
      <div className="flex justify-between items-center">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-12" />
      </div>
      <Skeleton className="h-16 w-full" />
      <div className="flex justify-between items-center">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-6 w-12" />
      </div>
    </div>
  );
}

// 通用的查询状态包装器
interface QueryStateWrapperProps {
  isLoading: boolean;
  error: Error | null;
  data: unknown;
  onRetry?: () => void;
  loadingComponent?: ReactNode;
  errorComponent?: ReactNode;
  emptyComponent?: ReactNode;
  children: ReactNode;
}

export function QueryStateWrapper({ isLoading, error, data, loadingComponent, errorComponent, emptyComponent, children }: QueryStateWrapperProps) {
  if (isLoading) {
    return <>{loadingComponent || <LoadingState />}</>;
  }

  if (error) {
    return <>{errorComponent || <div className="text-red-500">加载失败: {error.message}</div>}</>;
  }

  if (!data || (Array.isArray(data) && data.length === 0)) {
    return <>{emptyComponent || <div className="text-gray-500 text-center py-8">暂无数据</div>}</>;
  }

  return <>{children}</>;
}
