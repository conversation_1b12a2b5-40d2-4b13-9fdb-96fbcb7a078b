import React from 'react';

interface AuthCardProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
}

export default function AuthCard({ children, title, subtitle }: AuthCardProps) {
  return (
    <>
      {/* 标题和副标题 */}
      <div className="text-center mb-6">
        <h2 className="text-3xl font-bold tracking-tight text-gray-900">{title}</h2>
        {subtitle && <p className="mt-2 text-sm text-gray-600">{subtitle}</p>}
      </div>

      {children}
    </>
  );
}
