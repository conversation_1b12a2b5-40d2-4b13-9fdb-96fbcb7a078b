"""
销售工具集基础类 - 提供共享的基础功能
"""
import logging

logger = logging.getLogger(__name__)

class BaseTools:
    """销售工具集基础类 - 提供共享的核心功能"""

    def __init__(self, index=None, agent=None):
        self.index = index
        self.agent = agent  # 引用SalesAgent实例以获取对话历史
        self.current_conversation_id = None  # 当前对话ID
        self.current_connection_id = None    # 当前千牛连接ID
        self.current_customer_id = None      # 当前客户ID
        self.current_customer_nick = None  # 当前客户昵称

    def set_connection_info(self, connection_id: str, customer_id: str, customer_nick: str = None):
        """设置连接信息"""
        self.current_connection_id = connection_id
        self.current_customer_id = customer_id
        self.current_customer_nick = customer_nick
        logger.info(f"🔗 设置连接信息: {connection_id}, {customer_id}, {customer_nick}")