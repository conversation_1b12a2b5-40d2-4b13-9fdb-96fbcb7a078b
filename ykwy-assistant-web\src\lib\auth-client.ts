import { useEffect, useState } from 'react';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002';

// 用户信息接口
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  organizationId?: string;
  teamId?: string;
  organization?: {
    id: string;
    name: string;
  };
  team?: {
    id: string;
    name: string;
  };
}

// 会话信息接口（兼容原有代码）
export interface Session {
  user: User;
}

// 认证状态管理类
class AuthManager {
  private user: User | null = null;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private isLoading = false;
  private listeners: Set<() => void> = new Set();

  constructor() {
    this.loadFromStorage();
  }

  private loadFromStorage() {
    try {
      const stored = localStorage.getItem('auth-storage');
      if (stored) {
        const data = JSON.parse(stored);
        this.user = data.user;
        this.accessToken = data.accessToken;
        this.refreshToken = data.refreshToken;
      }
    } catch (error) {
      console.error('Failed to load auth from storage:', error);
    }
  }

  private saveToStorage() {
    try {
      localStorage.setItem(
        'auth-storage',
        JSON.stringify({
          user: this.user,
          accessToken: this.accessToken,
          refreshToken: this.refreshToken,
        }),
      );
    } catch (error) {
      console.error('Failed to save auth to storage:', error);
    }
  }

  private notify() {
    this.listeners.forEach((listener) => listener());
  }

  subscribe(listener: () => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  getState() {
    return {
      user: this.user,
      accessToken: this.accessToken,
      refreshToken: this.refreshToken,
      isLoading: this.isLoading,
      isAuthenticated: !!this.user,
    };
  }

  async signIn(credentials: { email: string; password: string }) {
    this.isLoading = true;
    this.notify();

    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        const { user, accessToken, refreshToken } = result.data;
        this.user = user;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.saveToStorage();
        this.isLoading = false;
        this.notify();
        return { data: result.data };
      } else {
        this.isLoading = false;
        this.notify();
        return { error: { message: result.error || 'Login failed' } };
      }
    } catch {
      this.isLoading = false;
      this.notify();
      return { error: { message: 'Network error' } };
    }
  }

  async signUp(userData: { name: string; email: string; password: string }) {
    this.isLoading = true;
    this.notify();

    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        const { user, accessToken, refreshToken } = result.data;
        this.user = user;
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.saveToStorage();
        this.isLoading = false;
        this.notify();
        return { data: result.data };
      } else {
        this.isLoading = false;
        this.notify();
        return { error: { message: result.error || 'Registration failed' } };
      }
    } catch {
      this.isLoading = false;
      this.notify();
      return { error: { message: 'Network error' } };
    }
  }

  async signOut() {
    try {
      if (this.accessToken) {
        await fetch(`${API_BASE_URL}/api/auth/logout`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    }

    this.user = null;
    this.accessToken = null;
    this.refreshToken = null;
    this.saveToStorage();
    this.notify();
  }

  async refreshAuth(): Promise<boolean> {
    if (!this.refreshToken) {
      return false;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken: this.refreshToken }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        const { accessToken: newAccessToken, refreshToken: newRefreshToken } = result.data;
        this.accessToken = newAccessToken;
        this.refreshToken = newRefreshToken;
        this.saveToStorage();
        this.notify();
        return true;
      } else {
        // 刷新失败，清除认证状态
        this.user = null;
        this.accessToken = null;
        this.refreshToken = null;
        this.saveToStorage();
        this.notify();
        return false;
      }
    } catch (error) {
      console.error('Refresh token error:', error);
      this.user = null;
      this.accessToken = null;
      this.refreshToken = null;
      this.saveToStorage();
      this.notify();
      return false;
    }
  }
}

// 创建全局认证管理器实例
const authManager = new AuthManager();

// Hook for using auth state
export function useSession() {
  const [state, setState] = useState(authManager.getState());

  useEffect(() => {
    const unsubscribe = authManager.subscribe(() => {
      setState(authManager.getState());
    });
    return () => unsubscribe();
  }, []);

  return {
    data: state.user ? { user: state.user } : null,
    isPending: state.isLoading,
  };
}

// 已废弃：请使用 useOrganization hook 替代
// export function useListOrganizations() { ... }

// 兼容原有 authClient 接口的对象
export const authClient = {
  useSession,
  signIn: {
    email: (credentials: { email: string; password: string }) => authManager.signIn(credentials),
  },
  signUp: {
    email: (userData: { name: string; email: string; password: string }) => authManager.signUp(userData),
  },
  signOut: () => authManager.signOut(),
};
