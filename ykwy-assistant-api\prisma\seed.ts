import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 默认密码
const DEFAULT_PASSWORD = 'Password123'

// 固定的组织和团队ID（UUIDv7格式）
const FIXED_ORGANIZATION_ID = '01984f1a-eaa4-74c2-88a4-82ff40abaea9'
const FIXED_TEAM_ID = '01984f1a-eae7-7491-b9d6-4fa95ec874ff'

// 创建用户和对应账户的辅助函数
async function createUserWithAccount(userData: {
  name: string
  email: string
  role: 'SYSTEM_ADMIN' | 'ORGANIZATION_ADMIN' | 'TEAM_MANAGER' | 'CUSTOMER_SERVICE'
  organizationId?: string
  teamId?: string
  permissionLevel?: number
  canManageTeams?: boolean
  managedTeams?: string[]
}) {
  // 创建用户（使用 JWT 认证系统）
  const hashedPassword = await Bun.password.hash(DEFAULT_PASSWORD)

  const user = await prisma.user.create({
    data: {
      name: userData.name,
      email: userData.email,
      emailVerified: true, // 种子数据直接设为已验证
      role: userData.role,
      teamId: userData.teamId,
      permissionLevel: userData.permissionLevel || 1,
      canManageTeams: userData.canManageTeams || false,
      managedTeams: userData.managedTeams || [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  })

  // 创建账户记录
  await prisma.account.create({
    data: {
      userId: user.id,
      accountId: user.id,
      providerId: 'credential',
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  })

  // 如果用户属于组织，创建 Member 记录
  if (userData.organizationId) {
    await prisma.member.create({
      data: {
        organizationId: userData.organizationId,
        userId: user.id,
        role: userData.role // 使用相同的角色
      }
    })
  }

  return user
}

async function main() {
  console.log('🌱 开始数据库种子数据初始化...')

  // 清理现有数据（总是执行，因为这是种子数据）
  console.log('🧹 清理现有数据...')
  await prisma.connectionInvitation.deleteMany()
  await prisma.qianniuAccount.deleteMany()
  await prisma.qianniuClient.deleteMany()
  await prisma.aiRecommendation.deleteMany() // AI推荐记录
  await prisma.message.deleteMany()
  await prisma.conversation.deleteMany()
  await prisma.customer.deleteMany()
  await prisma.customerServiceTask.deleteMany()
  await prisma.conversationActivity.deleteMany()
  await prisma.dailyStats.deleteMany()
  await prisma.organizationSetting.deleteMany() // 组织设置
  await prisma.brand.deleteMany()
  await prisma.invitation.deleteMany()
  await prisma.member.deleteMany() // Better Auth 成员记录
  await prisma.team.deleteMany()
  await prisma.organization.deleteMany()
  await prisma.session.deleteMany()
  await prisma.account.deleteMany()
  await prisma.user.deleteMany()

  // 1. 创建系统管理员（不属于任何组织）
  console.log('👤 创建系统管理员...')
  await createUserWithAccount({
    name: '系统管理员',
    email: '<EMAIL>',
    role: 'SYSTEM_ADMIN'
  })

  // 2. 创建组织（使用固定ID）
  console.log('🏢 创建组织...')
  const organization = await prisma.organization.create({
    data: {
      id: FIXED_ORGANIZATION_ID,
      name: '易客无忧科技有限公司',
      description: '专业的客服管理平台',
      logoUrl: 'https://example.com/logo.png',
      website: 'https://ykwy.com',
      contactEmail: '<EMAIL>',
      slug: 'ykwy-tech'
    }
  })

  // 3. 创建团队（使用固定ID，只创建一个团队）
  console.log('👨‍👩‍👧‍👦 创建团队...')
  const team1 = await prisma.team.create({
    data: {
      id: FIXED_TEAM_ID,
      name: '客服团队',
      organizationId: organization.id
    }
  })

  // 4. 创建组织用户（直接关联组织和团队）
  console.log('👥 创建组织用户...')
  const orgAdminUser = await createUserWithAccount({
    name: '组织管理员',
    email: '<EMAIL>',
    role: 'ORGANIZATION_ADMIN',
    organizationId: organization.id,
    permissionLevel: 4,
    canManageTeams: true,
    managedTeams: [team1.id]
  })

  await createUserWithAccount({
    name: '团队经理',
    email: '<EMAIL>',
    role: 'TEAM_MANAGER',
    organizationId: organization.id,
    teamId: team1.id,
    permissionLevel: 3,
    canManageTeams: true,
    managedTeams: [team1.id]
  })

  const customerServiceUser1 = await createUserWithAccount({
    name: '客服小张',
    email: '<EMAIL>',
    role: 'CUSTOMER_SERVICE',
    organizationId: organization.id,
    teamId: team1.id,
    permissionLevel: 1
  })

  await createUserWithAccount({
    name: '客服小李',
    email: '<EMAIL>',
    role: 'CUSTOMER_SERVICE',
    organizationId: organization.id,
    teamId: team1.id,
    permissionLevel: 1
  })

  // 5. 创建品牌
  console.log('🏷️ 创建品牌...')
  const brand1 = await prisma.brand.create({
    data: {
      organizationId: organization.id,
      name: '美妆品牌',
      description: '专业美妆护肤品牌',
      logo: 'https://example.com/brand1-logo.png',
      website: 'https://beauty-brand.com',
      contactInfo: {
        phone: '************',
        email: '<EMAIL>'
      }
    }
  })

  const brand2 = await prisma.brand.create({
    data: {
      organizationId: organization.id,
      name: '服装品牌',
      description: '时尚服装品牌',
      logo: 'https://example.com/brand2-logo.png',
      website: 'https://fashion-brand.com',
      contactInfo: {
        phone: '************',
        email: '<EMAIL>'
      }
    }
  })

  // 6. 创建连接邀请（先创建邀请，然后创建对应的千牛客户端）
  console.log('� 创建连接邀请...')
  const invitation1 = await prisma.connectionInvitation.create({
    data: {
      organizationId: organization.id,
      teamId: team1.id,
      name: '客服工作站-01',
      description: '一楼客服区第1台电脑',
      status: 'ACTIVATED', // 已激活状态
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
      activatedAt: new Date()
    }
  })

  const invitation2 = await prisma.connectionInvitation.create({
    data: {
      organizationId: organization.id,
      teamId: team1.id,
      name: '客服工作站-02',
      description: '一楼客服区第2台电脑',
      status: 'ACTIVATED', // 已激活状态
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
      activatedAt: new Date()
    }
  })

  // 创建一个待激活的邀请示例
  await prisma.connectionInvitation.create({
    data: {
      organizationId: organization.id,
      teamId: team1.id,
      name: '客服工作站-03',
      description: '二楼客服区第1台电脑',
      status: 'PENDING',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后过期
    }
  })

  // 7. 创建千牛客户端（使用邀请ID作为connectionId）
  console.log('�💻 创建千牛客户端...')
  const qianniuClient1 = await prisma.qianniuClient.create({
    data: {
      organizationId: organization.id,
      teamId: team1.id,
      name: '客服工作站-01',
      description: '一楼客服区第1台电脑',
      connectionId: invitation1.id, // 使用邀请ID作为连接ID
      isOnline: true,
      lastOnlineAt: new Date(),
      clientInfo: {
        ip: '*************',
        mac: '00:11:22:33:44:55',
        os: 'Windows 11',
        qianniuVersion: '9.50.10'
      }
    }
  })

  const qianniuClient2 = await prisma.qianniuClient.create({
    data: {
      organizationId: organization.id,
      teamId: team1.id,
      name: '客服工作站-02',
      description: '一楼客服区第2台电脑',
      connectionId: invitation2.id, // 使用邀请ID作为连接ID
      isOnline: false,
      lastOnlineAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
      clientInfo: {
        ip: '*************',
        mac: '00:11:22:33:44:66',
        os: 'Windows 10',
        qianniuVersion: '9.50.10'
      }
    }
  })

  // 更新邀请记录，关联到对应的客户端
  await prisma.connectionInvitation.update({
    where: { id: invitation1.id },
    data: { qianniuClientId: qianniuClient1.id }
  })

  await prisma.connectionInvitation.update({
    where: { id: invitation2.id },
    data: { qianniuClientId: qianniuClient2.id }
  })

  // 8. 创建千牛账号
  console.log('📱 创建千牛账号...')
  const qianniuAccount1 = await prisma.qianniuAccount.create({
    data: {
      clientId: qianniuClient1.id,
      brandId: brand1.id,
      accountName: 'beauty_shop_admin',
      accountId: 'taobao_beauty_001',
      shopName: '美妆旗舰店',
      shopId: 'shop_123456',
      platformType: 'TAOBAO',
      isLoggedIn: true,
      lastLoginAt: new Date()
    }
  })

  await prisma.qianniuAccount.create({
    data: {
      clientId: qianniuClient1.id,
      brandId: brand1.id,
      accountName: 'beauty_tmall_admin',
      accountId: 'tmall_beauty_001',
      shopName: '美妆天猫旗舰店',
      shopId: 'tmall_123456',
      platformType: 'TMALL',
      isLoggedIn: true,
      lastLoginAt: new Date()
    }
  })

  await prisma.qianniuAccount.create({
    data: {
      clientId: qianniuClient2.id,
      brandId: brand2.id,
      accountName: 'fashion_shop_admin',
      accountId: 'taobao_fashion_001',
      shopName: '时尚服装店',
      shopId: 'shop_789012',
      platformType: 'TAOBAO',
      isLoggedIn: false,
      lastLoginAt: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4小时前
    }
  })

  // 9. 创建客户
  console.log('👥 创建客户...')
  const customer1 = await prisma.customer.create({
    data: {
      qianniuAccountId: qianniuAccount1.id,
      platformCustomerId: 'taobao_customer_001',
      nickname: '张三',
      avatar: 'https://example.com/avatar1.jpg',
      phone: '***********',
      email: '<EMAIL>',
      realName: '张三',
      location: '北京市朝阳区',
      tags: ['VIP客户', '美妆爱好者'],
      lastActiveAt: new Date(),
      totalOrders: 5,
      totalAmount: 1299.99
    }
  })

  const customer2 = await prisma.customer.create({
    data: {
      qianniuAccountId: qianniuAccount1.id,
      platformCustomerId: 'taobao_customer_002',
      nickname: '李四',
      avatar: 'https://example.com/avatar2.jpg',
      phone: '***********',
      email: '<EMAIL>',
      realName: '李四',
      location: '上海市浦东新区',
      tags: ['新客户'],
      lastActiveAt: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
      totalOrders: 1,
      totalAmount: 299.99
    }
  })

  // 10. 创建对话
  console.log('💬 创建对话...')
  const conversation1 = await prisma.conversation.create({
    data: {
      organizationId: organization.id,
      qianniuAccountId: qianniuAccount1.id,
      customerId: customer1.id,
      assignedUserId: customerServiceUser1.id,
      title: '产品咨询',
      status: 'IN_PROGRESS',
      priority: 'NORMAL',
      tags: ['产品咨询']
    }
  })

  const conversation2 = await prisma.conversation.create({
    data: {
      organizationId: organization.id,
      qianniuAccountId: qianniuAccount1.id,
      customerId: customer2.id,
      assignedUserId: customerServiceUser1.id,
      title: '售后问题',
      status: 'PENDING',
      priority: 'HIGH',
      tags: ['售后', '退货']
    }
  })

  // 11. 创建消息
  console.log('📝 创建消息...')
  await prisma.message.create({
    data: {
      conversationId: conversation1.id,
      senderType: 'CUSTOMER',
      content: '你好，我想了解一下这款面霜的成分',
      messageType: 'TEXT',
      sequenceNumber: 1,
      sentAt: new Date(Date.now() - 10 * 60 * 1000) // 10分钟前
    }
  })

  await prisma.message.create({
    data: {
      conversationId: conversation1.id,
      senderId: customerServiceUser1.id,
      senderType: 'CUSTOMER_SERVICE',
      content:
        '您好！这款面霜主要成分包括透明质酸、维生素E等，具有很好的保湿效果。',
      messageType: 'TEXT',
      sequenceNumber: 2,
      sentAt: new Date(Date.now() - 8 * 60 * 1000) // 8分钟前
    }
  })

  await prisma.message.create({
    data: {
      conversationId: conversation2.id,
      senderType: 'CUSTOMER',
      content: '我收到的产品有质量问题，想要退货',
      messageType: 'TEXT',
      sequenceNumber: 1,
      sentAt: new Date(Date.now() - 15 * 60 * 1000) // 15分钟前
    }
  })

  // 12. 创建组织设置（包括RAGFlow配置）
  console.log('⚙️ 创建组织设置...')
  await prisma.organizationSetting.create({
    data: {
      organizationId: organization.id,
      key: 'ragflow_config',
      value: {
        apiUrl: 'https://demo.ragflow.io',
        apiKey: null, // 需要用户配置
        knowledgeBaseId: null, // 需要用户配置
        model: 'deepseek-chat',
        temperature: 0.7,
        maxTokens: 1000,
        enabled: false
      },
      description: 'RAGFlow AI配置'
    }
  })

  await prisma.organizationSetting.create({
    data: {
      organizationId: organization.id,
      key: 'auto_reply_config',
      value: {
        enabled: true,
        workingHours: {
          start: '09:00',
          end: '18:00',
          timezone: 'Asia/Shanghai'
        },
        responseDelay: 2000, // 2秒延迟
        maxAutoReplies: 3 // 最多自动回复3次
      },
      description: '自动回复配置'
    }
  })

  // 13. 创建AI推荐示例
  console.log('🤖 创建AI推荐示例...')
  await prisma.aiRecommendation.create({
    data: {
      messageId: (await prisma.message.findFirst({
        where: { conversationId: conversation1.id, senderType: 'CUSTOMER' }
      }))!.id,
      conversationId: conversation1.id,
      query: '面霜成分咨询',
      recommendation: '您好！这款面霜主要成分包括透明质酸、维生素E、神经酰胺等，具有很好的保湿和修护效果。透明质酸能够深层补水，维生素E具有抗氧化功效，神经酰胺则能够修护肌肤屏障。适合干性和敏感性肌肤使用。',
      confidence: 0.85,
      modelName: 'deepseek-chat',
      modelVersion: 'v1.0',
      strategyType: 'PRODUCT_INFO',
      metadata: {
        keywords: ['面霜', '成分', '保湿'],
        category: '产品咨询'
      }
    }
  })

  await prisma.aiRecommendation.create({
    data: {
      messageId: (await prisma.message.findFirst({
        where: { conversationId: conversation2.id, senderType: 'CUSTOMER' }
      }))!.id,
      conversationId: conversation2.id,
      query: '产品质量问题退货',
      recommendation: '非常抱歉给您带来不便！我们对产品质量问题非常重视。请您提供订单号和问题照片，我会立即为您处理退货事宜。我们承诺7天无理由退货，质量问题我们承担运费。',
      confidence: 0.92,
      modelName: 'deepseek-chat',
      modelVersion: 'v1.0',
      strategyType: 'COMPLAINT_HANDLE',
      metadata: {
        keywords: ['质量问题', '退货', '售后'],
        category: '售后服务'
      }
    }
  })

  // 14. 创建组织邀请示例
  console.log('📨 创建组织邀请...')
  await prisma.invitation.create({
    data: {
      organizationId: organization.id,
      email: '<EMAIL>',
      role: 'CUSTOMER_SERVICE',
      status: 'PENDING',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
      inviterId: orgAdminUser.id,
      teamId: team1.id,
      token: 'sample-invitation-token-123',
      inviteCode: 'INVITE123'
    }
  })

  console.log('✅ 数据库种子数据初始化完成!')
  console.log(`
📊 创建的数据统计:
- 用户: 6个 (1个系统管理员, 1个组织管理员, 2个团队管理员, 2个客服)
- 组织: 1个 (单一组织模式)
- 团队: 2个 (每个团队都有管理员)
- 品牌: 2个
- 连接邀请: 3个 (2个已激活, 1个待激活)
- 千牛客户端: 2个 (已关联connectionId)
- 千牛账号: 3个
- 客户: 2个
- 对话: 2个
- 消息: 3条
- AI推荐: 2个
- 组织设置: 2个 (RAGFlow配置, 自动回复配置)
- 组织邀请: 1个

🔑 测试账号 (密码统一为: ${DEFAULT_PASSWORD}):
- 系统管理员: <EMAIL> (不属于任何组织)
- 组织管理员: <EMAIL> (管理整个组织)
- 团队管理员1: <EMAIL> (管理团队A)
- 团队管理员2: <EMAIL> (管理团队B)
- 客服1: <EMAIL> (团队A成员)
- 客服2: <EMAIL> (团队B成员)

🏗️ 新的单一组织架构:
- 系统管理员 → 全局权限，不属于组织
- 组织管理员 → 组织内全部权限 (权限级别4)
- 团队管理员 → 团队内权限 (权限级别3)
- 客服人员 → 基础权限 (权限级别1)

🔄 组织邀请注册:
- 邀请邮箱: <EMAIL>
- 邀请码: INVITE123
- 邀请令牌: sample-invitation-token-123
  `)
}

main()
  .catch(e => {
    console.error('❌ 种子数据初始化失败:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
