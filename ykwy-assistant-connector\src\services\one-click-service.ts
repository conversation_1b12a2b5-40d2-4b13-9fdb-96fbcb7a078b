import ProcessManager from '../utils/process-manager.js';

interface QianniuConnector {
  getConfigManager(): ConfigManager;
  getApiService(): ApiService;
  checkIfAlreadyInjected(): Promise<boolean>;
  setupConnection(scriptUrl: string): Promise<boolean>;
  startTcpProxy(backendHost?: string, backendPort?: number): Promise<boolean>;
  startContinuousMonitoring(): Promise<void>;
}

interface ConfigManager {
  isConfigComplete(): boolean;
  configWizard(): Promise<void>;
  getConfig(): {
    tcpProxy: {
      backendHost: string;
      backendPort: number;
    };
  };
}

interface ApiService {
  createConnectionInvitation(): Promise<string>;
}

/**
 * 一键启动服务
 * 负责协调完整的一键启动流程
 */
class OneClickService {
  private connector: QianniuConnector;
  private configManager: ConfigManager;
  private apiService: ApiService;

  constructor(qianniuConnector: QianniuConnector) {
    this.connector = qianniuConnector;
    this.configManager = qianniuConnector.getConfigManager();
    this.apiService = qianniuConnector.getApiService();
  }

  /**
   * 一键启动流程
   * @returns {Promise<boolean>} 是否成功
   */
  async start() {
    console.log('🚀 千牛连接一键启动');
    console.log('='.repeat(50));

    try {
      // 1. 检查配置
      if (!this.configManager.isConfigComplete()) {
        console.log('⚠️  首次运行需要配置');
        await this.configManager.configWizard();
      }

      // 2. 检查千牛是否已被注入
      console.log('\n🔍 检查千牛注入状态...');
      const alreadyInjected = await this.connector.checkIfAlreadyInjected();

      let scriptUrl = null;

      if (alreadyInjected) {
        console.log('✅ 千牛已被注入脚本，跳过API调用和注入步骤');
        console.log('💡 直接启动服务并等待千牛启动');
      } else {
        console.log('📝 千牛尚未注入，需要完整的注入流程');

        // 3. 获取脚本地址
        try {
          scriptUrl = await this.apiService.createConnectionInvitation();
        } catch {
          console.error('❌ 无法获取脚本地址，程序退出');
          return false;
        }

        // 4. 检查千牛状态并处理
        console.log('\n📋 检查千牛客户端状态...');
        const processes = await ProcessManager.checkQianniuProcess();

        if (processes.length > 0) {
          console.log('⚠️  检测到千牛正在运行');
          console.log('🔍 当前运行的千牛进程:');
          ProcessManager.displayProcesses(processes);

          console.log('\n💡 需要先关闭千牛客户端才能注入脚本');
          console.log('请按以下步骤操作：');
          console.log('1. 关闭千牛客户端');
          console.log('2. 等待脚本自动检测到千牛关闭');
          console.log('3. 脚本将自动注入连接脚本');
          console.log('4. 注入完成后，重新启动千牛客户端');
          console.log('5. 脚本将自动启动代理服务\n');

          // 等待千牛关闭
          console.log('⏳ 等待千牛客户端关闭...');
          await ProcessManager.waitForQianniuState(false, 60000); // 等待60秒
          console.log('✅ 千牛客户端已关闭');
        } else {
          console.log('✅ 千牛客户端未运行，可以直接注入脚本');
        }

        // 5. 注入脚本
        console.log('\n💉 开始注入脚本...');
        const setupSuccess = await this.connector.setupConnection(scriptUrl);
        if (!setupSuccess) {
          console.error('❌ 脚本注入失败，程序退出');
          return false;
        }
      }

      // 6. 启动TCP代理服务
      console.log('\n🌐 启动TCP代理服务...');
      const config = this.configManager.getConfig();
      const proxyStarted = await this.connector.startTcpProxy(config.tcpProxy.backendHost, config.tcpProxy.backendPort);
      if (!proxyStarted) {
        console.error('❌ TCP代理服务启动失败');
        return false;
      }

      // 7. 启动持续的千牛进程监听
      console.log('\n🔄 启动千牛进程持续监听...');
      await this.connector.startContinuousMonitoring();

      // 8. 等待至少一个千牛进程启动（可选）
      console.log('\n💡 请启动千牛客户端...');
      console.log('⏳ 等待千牛客户端启动...');
      try {
        await ProcessManager.waitForAnyQianniuProcess(120000); // 等待2分钟
        console.log('✅ 检测到千牛客户端已启动');
      } catch {
        console.log('⚠️  未在2分钟内检测到千牛启动，但监听器仍在运行');
        console.log('💡 启动千牛后，系统将自动处理');
      }

      console.log('\n🎉 千牛连接一键启动完成！');
      console.log('📡 TCP代理服务正在运行，监听端口: 9996');
      console.log('🎯 转发到后端: localhost:9997');
      console.log('🔄 千牛进程监听器正在运行');
      console.log('💡 每当检测到新的千牛进程时，将自动执行QNQtHelp64');
      console.log('💡 按 Ctrl+C 停止所有服务');

      return true;
    } catch (error) {
      console.error('❌ 一键启动失败:', error.message);
      return false;
    }
  }
}

export default OneClickService;
