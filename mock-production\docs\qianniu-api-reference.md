# 千牛API接口参考文档

## 概述

本文档详细描述了千牛API的所有接口，包括输入参数、输出结果和接口间的依赖关系。所有数据结构基于qianniu.md中的真实API返回数据设计。

## 接口依赖关系图

```
1. search_buyer_id (搜索买家) 🔍
   ↓ encrypt_account_id + nick
   ├─→ 2. query_customer_info (查询客户信息) 👤
   ├─→ 3. search_shop_items (搜索商品) 🛍️
   │   ↓ item_id
   │   ├─→ 5. invite_order (邀请下单) 🛒
   │   └─→ 6. send_item_card (发送商品卡片) 📇
   ├─→ 4. query_item_record (查询商品记录) 📦
   ├─→ 7. query_recent_orders (查询近期订单) 📋
   │   ↓ biz_order_id
   │   └─→ 9. query_order_logistics (查询物流) 🚚
   └─→ 8. query_history_orders (查询历史订单) 📚
       ↓ biz_order_id
       └─→ 9. query_order_logistics (查询物流) 🚚
```

## 接口详细说明

### 1. 🔍 搜索买家ID

**方法**: `search_buyer_id`  
**端点**: `/api/v1/qianniu-api/search-buyer-id`  
**依赖**: 无（入口接口）

#### 输入参数
```python
{
    "connection_id": str,      # 千牛连接ID
    "search_query": str        # 搜索关键词（买家昵称，如"tb783904683"）
}
```

#### 输出结果
```python
List[BuyerSearchResult]:
[
    {
        "account_id": "*************",
        "encrypt_account_id": "RAzN8BQaCakmNLV4DBQGxZrh2qsaF",  # 🔑 关键输出
        "nick": "tb2494039180",
        "display_nick": "tb2494039180", 
        "account_type": 3,
        "search_type": "byNick",
        "account_roles": []
    }
]
```

#### 关键输出字段
- `encrypt_account_id`: 后续所有客户相关API的必需参数
- `nick`: 邀请下单时需要的买家昵称

---

### 2. 👤 查询客户信息

**方法**: `query_customer_info`  
**端点**: `/api/v1/qianniu-api/query-customer-info`  
**依赖**: `search_buyer_id` → `encrypt_account_id`

#### 输入参数
```python
{
    "connection_id": str,      # 千牛连接ID
    "customer_id": str         # 加密客户ID（来自搜索买家ID）
}
```

#### 输出结果
```python
CustomerInfo:
{
    "buyer_credit_level": 8,
    "buyer_credit_score": 261,
    "send_good_rate": "100.00%",
    "is_new_customer": False,
    "is_shop_fans": True,
    "has_membership": False,
    "vip_level": 0,
    "corporate_member": False,
    "buyer_credit_icon": null,
    "buyer_credit_pic": null
}
```

#### 业务用途
- 客户画像分析
- 个性化服务策略
- 风险评估

---

### 3. 🛍️ 搜索店铺商品

**方法**: `search_shop_items`  
**端点**: `/api/v1/qianniu-api/search-shop-items`  
**依赖**: `search_buyer_id` → `encrypt_account_id`

#### 输入参数
```python
{
    "connection_id": str,      # 千牛连接ID
    "customer_id": str,        # 加密客户ID
    "page_size": 8,            # 每页数量（默认8）
    "page_no": 1,              # 页码（默认1）
    "keyword": "",             # 搜索关键词（可选）
    "sort_key": "sold",        # 排序字段（默认按销量）
    "desc": True,              # 是否降序（默认True）
    "type": 0,                 # 商品类型
    "query_gift": False        # 是否查询赠品
}
```

#### 输出结果
```python
ShopItemsResult:
{
    "items": [
        {
            "item_id": "************",  # 🔑 关键输出，用于后续操作
            "title": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本",
            "price": "0.20",
            "pic_url": "https://img.alicdn.com/bao/uploaded/...",
            "item_url": "https://item.taobao.com/item.htm?id=************",
            "category_id": *********,
            "quantity": 9,
            "sold_quantity": 1,
            "approve_status": "onsale",
            "have_gift": False,
            "props": null,
            "props_name": null
        }
    ],
    "page": 1,
    "page_size": 8,
    "total_count": 3
}
```

#### 关键输出字段
- `item_id`: 用于邀请下单和发送商品卡片
- `title`: 商品名称
- `price`: 商品价格
- `item_url`: 商品链接

---

### 4. 📦 查询商品记录

**方法**: `query_item_record`  
**端点**: `/api/v1/qianniu-api/query-item-record`  
**依赖**: `search_buyer_id` → `encrypt_account_id`

#### 输入参数
```python
{
    "connection_id": str,      # 千牛连接ID
    "customer_id": str         # 加密客户ID
}
```

#### 输出结果
```python
ItemRecordResult:
{
    "recently_bought_items": [  # 最近购买
        {
            "item_id": "************",
            "title": "AI 智能写作助手｜高效产出各类文案",
            "price": "0.10",
            "pic_url": "https://img.alicdn.com/...",
            "item_url": "https://item.taobao.com/...",
            "buy_time": "今天",
            "quantity": 1
        }
    ],
    "foot_point_items": [       # 浏览足迹
        {
            "item_id": "************",
            "title": "AI 智能写作 朋友圈 / 演讲稿 / 短视频脚本",
            "view_time": "1天前",
            "time_stamp": "*************"
        }
    ],
    "under_inquiry_items": [],  # 咨询中商品
    "show_recently_bought": True,
    "has_more_foot_print": False
}
```

#### 业务用途
- 了解客户购买历史
- 分析客户兴趣偏好
- 推荐相关商品

---

### 5. 🛒 邀请下单

**方法**: `invite_order`  
**端点**: `/api/v1/qianniu-api/invite-order`  
**依赖**: 
- `search_buyer_id` → `encrypt_account_id`, `nick`
- `search_shop_items` → `item_id`

#### 输入参数
```python
{
    "connection_id": str,      # 千牛连接ID
    "customer_id": str,        # 加密客户ID
    "item_props": str,         # 商品属性JSON字符串
    "buyer_nick": str,         # 买家昵称
    "biz_domain": "taobao",    # 业务域（默认taobao）
    "encrypt_type": "internal" # 加密类型（默认internal）
}
```

#### item_props格式示例
```json
[{
    "itemId": ************,
    "skuId": *************,
    "quantity": 1,
    "context": {}
}]
```

#### 输出结果
```python
InviteOrderResult:
{
    "success": True,
    "order_url": "https://trade.taobao.com/...",  # 可选
    "message": "邀请下单成功"
}
```

#### 业务用途
- 主动营销
- 促成交易
- 简化下单流程

---

### 6. 📇 发送商品卡片

**方法**: `send_item_card`  
**端点**: `/api/v1/qianniu-api/send-item-card`  
**依赖**: 
- `search_buyer_id` → `encrypt_account_id`
- `search_shop_items` → `item_id`

#### 输入参数
```python
{
    "connection_id": str,      # 千牛连接ID
    "customer_id": str,        # 加密客户ID
    "batch_item_ids": [str],   # 商品ID数组
    "type": -1                 # 卡片类型（默认-1）
}
```

#### 输出结果
```python
SendItemCardResult:
{
    "send_card": True,
    "send_url": "https://item.taobao.com/item.htm?id=************"
}
```

#### 业务用途
- 商品推荐
- 可视化展示
- 提升转化率

---

### 7. 📋 查询近期订单

**方法**: `query_recent_orders`  
**端点**: `/api/v1/qianniu-api/query-recent-orders`  
**依赖**: `search_buyer_id` → `encrypt_account_id`

#### 输入参数
```python
{
    "connection_id": str,      # 千牛连接ID
    "customer_id": str,        # 加密客户ID
    "order_status": ""         # 订单状态筛选（可选）
}
```

#### 输出结果
```python
OrdersResult:
{
    "orders": [
        {
            "biz_order_id": "*******************",  # 🔑 关键输出，用于物流查询
            "order_price": "0.10",
            "card_type_text": "待发货",
            "category": "虚拟",
            "create_time": "2025-07-22 21:26:33",
            "pay_time": "2025-07-22 21:26:33",
            "receiver_name": "宫**",
            "receiver_address": "上海市浦东新区***",
            "receiver_mobile_phone": "176****1234",
            "buy_amount": 1,
            "post_fee": "0.00",
            "refund_fee": "0.00",
            "after_sale_text": "",
            "consign_time": null,
            "promotion_total_fee": null,
            "items": [
                {
                    "auction_id": "************",
                    "auction_title": "AI 智能写作助手｜高效产出各类文案",
                    "auction_price": "0.10",
                    "auction_url": "https://item.taobao.com/...",
                    "pic_url": "https://img.alicdn.com/...",
                    "buy_amount": 1,
                    "price": "0.10",
                    "old_price": "0.10",
                    "card_type_text": "待发货",
                    "logistics_status": 0,
                    "pay_status": 1,
                    "refund_status": 0,
                    "sub_order_id": "*******************",
                    "create_time": "2025-07-22 21:26:33",
                    "pay_time": "2025-07-22 21:26:33"
                }
            ]
        }
    ],
    "page_num": 1,
    "use_new_change_refund": True,
    "use_new_instead_refund": True
}
```

#### 关键输出字段
- `biz_order_id`: 用于查询物流信息
- `order_price`: 订单总金额
- `card_type_text`: 订单状态
- `items`: 订单商品详情

---

### 8. 📚 查询历史订单

**方法**: `query_history_orders`  
**端点**: `/api/v1/qianniu-api/query-history-orders`  
**依赖**: `search_buyer_id` → `encrypt_account_id`

#### 输入参数
```python
{
    "connection_id": str,      # 千牛连接ID
    "customer_id": str,        # 加密客户ID
    "page_num": 1,             # 页码（默认1）
    "page_size": 10            # 每页数量（默认10）
}
```

#### 输出结果
```python
OrdersResult:  # 与近期订单相同结构
{
    "orders": [],  # 可能为空数组
    "page_num": 1
}
```

#### 业务用途
- 客户历史分析
- 复购行为研究
- 长期客户关系维护

---

### 9. 🚚 查询物流信息

**方法**: `query_order_logistics`  
**端点**: `/api/v1/qianniu-api/query-order-logistics`  
**依赖**: `query_recent_orders` 或 `query_history_orders` → `biz_order_id`

#### 输入参数
```python
{
    "connection_id": str,      # 千牛连接ID
    "biz_order_id": str        # 业务订单ID（来自订单查询结果）
}
```

#### 输出结果
```python
LogisticsInfo:
{
    "order_id": "*******************",
    "logistics_company": "顺丰速运",
    "tracking_number": "SF1234567890",
    "status": "运输中",
    "estimated_delivery": "2025-07-25",
    "events": [
        {
            "time": "2025-07-22 21:26:33",
            "status": "已发货",
            "location": "北京分拣中心"
        },
        {
            "time": "2025-07-23 08:15:20",
            "status": "运输中",
            "location": "北京转运中心"
        }
    ]
}
```

#### 业务用途
- 物流跟踪
- 客户服务
- 配送状态查询

## 核心业务流程

### 完整的客户服务流程

1. **🔍 获取客户ID**
   ```python
   search_buyer_id("tb783904683") → encrypt_account_id
   ```

2. **👤 了解客户**
   ```python
   query_customer_info(encrypt_account_id) → 客户画像
   ```

3. **🛍️ 查看商品**
   ```python
   search_shop_items(encrypt_account_id) → 可推荐商品
   ```

4. **📦 查看历史**
   ```python
   query_item_record(encrypt_account_id) → 购买/浏览记录
   ```

5. **📇 推荐商品**
   ```python
   send_item_card(encrypt_account_id, [item_id]) → 发送商品卡片
   ```

6. **🛒 邀请下单**
   ```python
   invite_order(encrypt_account_id, item_props, nick) → 生成订单
   ```

7. **📋 查询订单**
   ```python
   query_recent_orders(encrypt_account_id) → 订单状态
   ```

8. **🚚 跟踪物流**
   ```python
   query_order_logistics(biz_order_id) → 物流进度
   ```

## 关键参数传递链

- **connection_id**: 所有接口都需要，标识千牛连接
- **encrypt_account_id**: 从搜索买家获得，大部分接口需要
- **item_id**: 从商品搜索获得，用于邀请下单和发送卡片
- **biz_order_id**: 从订单查询获得，用于物流查询
- **nick**: 从搜索买家获得，用于邀请下单

## 错误处理

所有接口都具备优雅降级机制：
- API调用失败时返回空的结构化结果
- 不会抛出异常中断业务流程
- 详细的错误日志记录便于调试
