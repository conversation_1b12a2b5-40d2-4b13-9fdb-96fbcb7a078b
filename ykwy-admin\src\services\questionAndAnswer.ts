import type {
  ApiResponse,
  BulkDeleteResult,
  QuestionAndAnswer,
  QuestionAndAnswerDetail,
  QuestionAndAnswerInput,
  QuestionAndAnswerListResponse,
  QuestionAndAnswerQueryParams,
  QuestionAndAnswerStats,
} from '@/models/questionAndAnswer';
import { request } from '@umijs/max';
import Cookies from 'js-cookie';

const API_BASE_URL =
  (process.env.UMI_APP_API_URL || 'http://localhost:3009') + '/api/v1';

/**
 * 获取问答列表
 */
export async function getQuestionAndAnswerList(
  params?: QuestionAndAnswerQueryParams,
  options?: { [key: string]: any },
) {
  // 转换分页参数
  const queryParams: any = {
    ...params,
  };

  if (params?.current && params?.pageSize) {
    queryParams.skip = (params.current - 1) * params.pageSize;
    queryParams.take = params.pageSize;
    delete queryParams.current;
    delete queryParams.pageSize;
  }

  return request<ApiResponse<QuestionAndAnswerListResponse>>(
    `${API_BASE_URL}/questionAndAnswers`,
    {
      method: 'GET',
      params: queryParams,
      ...(options || {}),
    },
  );
}

/**
 * 获取单个问答详情（包含分类信息）
 */
export async function getQuestionAndAnswerDetail(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswerDetail>>(
    `${API_BASE_URL}/questionAndAnswer/${id}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 创建或更新问答
 */
export async function upsertQuestionAndAnswer(
  data: QuestionAndAnswerInput,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswer>>(
    `${API_BASE_URL}/questionAndAnswer`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
      ...(options || {}),
    },
  );
}

/**
 * 软删除问答
 */
export async function deleteQuestionAndAnswer(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswer>>(
    `${API_BASE_URL}/questionAndAnswer/${id}`,
    {
      method: 'DELETE',
      ...(options || {}),
    },
  );
}

/**
 * 批量软删除问答
 */
export async function bulkDeleteQuestionAndAnswers(
  ids: string[],
  options?: { [key: string]: any },
) {
  return request<ApiResponse<BulkDeleteResult>>(
    `${API_BASE_URL}/questionAndAnswers/bulk-delete`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: { ids },
      ...(options || {}),
    },
  );
}

/**
 * 恢复已删除问答
 */
export async function restoreQuestionAndAnswer(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswer>>(
    `${API_BASE_URL}/questionAndAnswer/${id}/restore`,
    {
      method: 'POST',
      ...(options || {}),
    },
  );
}

/**
 * 更新订单状态
 */
export async function updateOrderStatus(
  id: string,
  orderStatus: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswer>>(
    `${API_BASE_URL}/questionAndAnswer/${id}/status`,
    {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      data: { orderStatus },
      ...(options || {}),
    },
  );
}

/**
 * 根据问题类型查找问答
 */
export async function getQuestionAndAnswersByType(
  questionType: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswer[]>>(
    `${API_BASE_URL}/questionAndAnswers/type/${encodeURIComponent(
      questionType,
    )}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 根据分类编码查找问答
 */
export async function getQuestionAndAnswersByCategoryCode(
  categoryCode: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswer[]>>(
    `${API_BASE_URL}/questionAndAnswers/category/${encodeURIComponent(
      categoryCode,
    )}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 根据订单状态查找问答
 */
export async function getQuestionAndAnswersByOrderStatus(
  orderStatus: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswer[]>>(
    `${API_BASE_URL}/questionAndAnswers/orderStatus/${encodeURIComponent(
      orderStatus,
    )}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 根据样本搜索问答
 */
export async function searchQuestionAndAnswersBySample(
  sample: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswer[]>>(
    `${API_BASE_URL}/questionAndAnswers/search/sample`,
    {
      method: 'GET',
      params: { sample },
      ...(options || {}),
    },
  );
}

/**
 * 获取所有唯一的问题类型
 */
export async function getUniqueQuestionTypes(options?: { [key: string]: any }) {
  return request<ApiResponse<string[]>>(
    `${API_BASE_URL}/questionAndAnswers/types`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 获取问答统计数据
 */
export async function getQuestionAndAnswerStats(
  params?: {
    startDate?: string;
    endDate?: string;
    groupBy?: 'questionType' | 'categoryCode' | 'orderStatus';
    topLimit?: number;
  },
  options?: { [key: string]: any },
) {
  return request<ApiResponse<QuestionAndAnswerStats>>(
    `${API_BASE_URL}/questionAndAnswers/stats`,
    {
      method: 'GET',
      params,
      ...(options || {}),
    },
  );
}

/**
 * 下载问答知识库导入模板（使用原生fetch实现）
 */
export async function downloadQAKnowledgeTemplate(
  format: 'excel' | 'csv' = 'excel',
  options?: { [key: string]: any },
): Promise<{ blob: Blob; filename: string }> {
  const url = `${API_BASE_URL}/questionAndAnswerFile/template?format=${format}`;
  const token = Cookies.get('access_token');
  const fetchOptions: RequestInit = {
    method: 'GET',
    headers: {
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
      ...(options?.headers || {}),
    },
    ...options,
  };
  const response = await fetch(url, fetchOptions);
  if (!response.ok) {
    throw new Error(`下载模板失败: ${response.status} ${response.statusText}`);
  }
  const blob = await response.blob();
  // 从header获取文件名
  let filename =
    format === 'csv' ? '问答知识库导入模板.csv' : '问答知识库导入模板.xlsx';
  const disposition = response.headers.get('Content-Disposition');
  if (disposition) {
    const match = disposition.match(/filename="?([^";]+)"?/);
    if (match) {
      filename = decodeURIComponent(match[1]);
    }
  }
  return { blob, filename };
}

/**
 * 导入问答知识库文件
 */
export async function importQAKnowledgeFile(
  file: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();
  formData.append('file', file);
  return request(`${API_BASE_URL}/questionAndAnswerFile/import`, {
    method: 'POST',
    data: formData,
    requestType: 'form',
    timeout: 120000,
    ...(options || {}),
  });
}

/**
 * 同步到Ragflow知识库
 */
export async function syncToRagflow(options?: { [key: string]: any }) {
  return request(`${API_BASE_URL}/questionAndAnswers/sync-ragflow`, {
    method: 'POST',
    ...(options || {}),
  });
}
