<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>淘宝产品信息智能销售代理</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .chat-container {
        width: 90%;
        max-width: 800px;
        height: 80vh;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .chat-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .chat-header h1 {
        font-size: 24px;
        margin-bottom: 5px;
      }

      .chat-header p {
        opacity: 0.9;
        font-size: 14px;
      }

      .debug-button {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 8px 16px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 12px;
      }

      .debug-button:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .debug-panel {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: none;
        z-index: 1000;
      }

      .debug-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        width: 90%;
        max-width: 1000px;
        height: 80%;
        border-radius: 10px;
        display: flex;
        overflow: hidden;
      }

      .debug-sidebar {
        width: 300px;
        background: #f8f9fa;
        border-right: 1px solid #dee2e6;
        overflow-y: auto;
      }

      .debug-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .debug-header {
        padding: 15px;
        background: #007bff;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .debug-close {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
      }

      .dialogue-item {
        padding: 10px 15px;
        border-bottom: 1px solid #dee2e6;
        cursor: pointer;
      }

      .dialogue-item:hover {
        background: #e9ecef;
      }

      .dialogue-item.active {
        background: #007bff;
        color: white;
      }

      .debug-chat {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 0;
      }

      .debug-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        min-height: 0;
      }

      .debug-messages .message {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
        gap: 10px;
      }

      .debug-messages .message.user {
        justify-content: flex-start;
        flex-direction: row;
      }

      .debug-messages .message-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        flex-shrink: 0;
      }

      .debug-messages .message.user .message-avatar {
        background: #007bff;
        color: white;
      }

      .debug-messages .message.agent .message-avatar {
        background: #28a745;
        color: white;
      }

      .debug-messages .message-content {
        background: #f8f9fa;
        padding: 12px 16px;
        border-radius: 18px;
        max-width: 70%;
        word-wrap: break-word;
        border: 1px solid #e9ecef;
      }

      .debug-messages .message.user .message-content {
        background: #e3f2fd;
        color: #1976d2;
        border: 1px solid #bbdefb;
      }

      .debug-controls {
        padding: 15px;
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .step-info {
        flex: 1;
        font-size: 14px;
        color: #666;
      }

      .step-button {
        padding: 8px 16px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      }

      .step-button:disabled {
        background: #6c757d;
        cursor: not-allowed;
      }

      .original-response {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
      }

      .original-response-label {
        font-weight: bold;
        color: #856404;
        margin-bottom: 5px;
      }

      .chat-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #f8f9fa;
      }

      .message {
        margin-bottom: 15px;
        display: flex;
        align-items: flex-start;
      }

      .message.user {
        justify-content: flex-start;
        flex-direction: row;
      }

      .message-content {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
      }

      .message.user .message-content {
        background: #e3f2fd;
        color: #1976d2;
        border: 1px solid #bbdefb;
      }

      .message.agent .message-content {
        background: white;
        border: 1px solid #e9ecef;
        color: #333;
      }

      .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin: 0 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
      }

      .message.user .message-avatar {
        background: #007bff;
        color: white;
      }

      .message.agent .message-avatar {
        background: #28a745;
        color: white;
      }

      .chat-input {
        padding: 20px;
        background: white;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 10px;
      }

      .chat-input input {
        flex: 1;
        padding: 12px 16px;
        border: 1px solid #ddd;
        border-radius: 25px;
        outline: none;
        font-size: 14px;
      }

      .chat-input input:focus {
        border-color: #007bff;
      }

      .chat-input button {
        padding: 12px 24px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.3s;
      }

      .chat-input button:hover {
        background: #0056b3;
      }

      .chat-input button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      .loading {
        display: none;
        text-align: center;
        padding: 10px;
        color: #666;
      }

      .loading.show {
        display: block;
      }

      .reset-btn {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 15px;
        cursor: pointer;
        font-size: 12px;
      }

      .reset-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .brand-selector {
        position: absolute;
        top: 20px;
        left: 20px;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        padding: 8px 16px;
        color: white;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .brand-selector select {
        background: transparent;
        border: none;
        color: white;
        font-size: 12px;
        outline: none;
        cursor: pointer;
      }

      .brand-selector select option {
        background: #667eea;
        color: white;
      }

      .brand-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 4px;
      }

      .brand-indicator.ykwy {
        background: #007bff;
      }

      .brand-indicator.qinggong {
        background: #28a745;
      }
    </style>
  </head>
  <body>
    <div class="chat-container">
      <div class="chat-header">
        <div class="brand-selector">
          <span class="brand-indicator" id="brandIndicator"></span>
          <select id="brandSelect" onchange="changeBrand()">
            <option value="ykwy">易客无忧</option>
            <option value="qinggong">轻功体育</option>
          </select>
        </div>
        <button class="reset-btn" onclick="resetChat()">重置对话</button>
        <button class="debug-button" onclick="openDebugPanel()">
          调试模式
        </button>
        <h1 id="chatTitle">🤖 易客无忧智能销售代理</h1>
        <p id="chatDescription">专业的AI产品销售顾问，帮助您选择最适合的AI工具</p>
        <p
          id="sessionInfo"
          style="font-size: 12px; opacity: 0.7; margin-top: 5px"
        ></p>
      </div>

      <div class="chat-messages" id="chatMessages">
        <div class="message agent">
          <div class="message-avatar">🤖</div>
          <div class="message-content">
            您好！我是淘宝产品信息智能销售代理，专门为您提供AI产品咨询服务。我们有三款优秀的AI产品：
            <br /><br />
            🎨 <strong>AI PPT制作助手</strong> - 专业演示文稿制作<br />
            🎬 <strong>AI视频创作平台</strong> - 零基础视频制作<br />
            ✍️ <strong>AI写作助手</strong> - 专业内容创作<br />
            <br />
            请告诉我您的需求，我会为您推荐最适合的产品！
          </div>
        </div>
      </div>

      <div class="loading" id="loading">
        <div>🤖 正在思考中...</div>
      </div>

      <div class="chat-input">
        <input
          type="text"
          id="messageInput"
          placeholder="请输入您的问题..."
          onkeypress="handleKeyPress(event)"
        />
        <button onclick="sendMessage()" id="sendBtn">发送</button>
      </div>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel" id="debugPanel">
      <div class="debug-content">
        <div class="debug-sidebar">
          <div class="debug-header">
            <h3>选择对话</h3>
            <button class="debug-close" onclick="closeDebugPanel()">×</button>
          </div>
          <div id="dialogueList">
            <!-- 对话列表将在这里动态加载 -->
          </div>
        </div>
        <div class="debug-main">
          <div class="debug-header">
            <h3 id="debugTitle">单步调试</h3>
          </div>
          <div class="debug-chat">
            <div class="debug-messages" id="debugMessages">
              <div style="text-align: center; color: #666; padding: 50px">
                请从左侧选择一个对话开始调试
              </div>
            </div>
            <div class="debug-controls">
              <div class="step-info" id="stepInfo">准备开始调试...</div>
              <button
                class="step-button"
                id="nextStepBtn"
                onclick="nextStep()"
                disabled
              >
                下一步
              </button>
              <button class="step-button" onclick="resetDebug()">重置</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      const API_BASE = ''

      // 当前选择的品牌
      let currentBrand = localStorage.getItem('currentBrand') || 'ykwy'

      // 生成或获取会话ID
      let conversationId = localStorage.getItem('conversationId')
      if (!conversationId) {
        conversationId =
          'web_session_' +
          Date.now() +
          '_' +
          Math.random().toString(36).substr(2, 9)
        localStorage.setItem('conversationId', conversationId)
      }

      console.log('当前会话ID:', conversationId)
      console.log('当前品牌:', currentBrand)

      // 品牌配置
      const brandConfig = {
        ykwy: {
          name: '易客无忧',
          title: '🤖 易客无忧智能销售代理',
          description: '专业的AI产品销售顾问，帮助您选择最适合的AI工具',
          welcomeMessage: `您好！我是易客无忧智能销售代理，专门为您提供AI产品咨询服务。我们有三款优秀的AI产品：
            <br /><br />
            🎨 <strong>AI PPT制作助手</strong> - 专业演示文稿制作<br />
            🎬 <strong>AI视频创作平台</strong> - 零基础视频制作<br />
            ✍️ <strong>AI写作助手</strong> - 专业内容创作<br />
            <br />
            请告诉我您的需求，我会为您推荐最适合的产品！`,
          avatar: '🤖',
          indicatorClass: 'ykwy'
        },
        qinggong: {
          name: '轻功体育',
          title: '🏃 轻功体育智能销售代理',
          description: '专业的体育用品销售顾问，帮助您选择最适合的运动装备',
          welcomeMessage: `您好！欢迎来到轻功体育！🏃‍♂️
            <br /><br />
            我们是专业的体育用品销售商，主要提供：<br />
            • 运动鞋类：跑鞋、篮球鞋、训练鞋等<br />
            • 运动服装：T恤、运动套装、运动裤等<br />
            • 体育器材：哑铃、杠铃、健身器械等<br />
            • 健身用品：瑜伽垫、拉力带、护具等<br />
            <br />
            有什么需要帮助的吗？`,
          avatar: '🏃',
          indicatorClass: 'qinggong'
        }
      }

      // 切换品牌
      function changeBrand() {
        const brandSelect = document.getElementById('brandSelect')
        const newBrand = brandSelect.value

        if (newBrand === currentBrand) return

        currentBrand = newBrand
        localStorage.setItem('currentBrand', currentBrand)

        // 更新UI
        updateBrandUI()

        // 重置对话
        resetChatForBrand()

        console.log('切换到品牌:', currentBrand)
      }

      // 更新品牌UI
      function updateBrandUI() {
        const config = brandConfig[currentBrand]

        // 更新标题和描述
        document.getElementById('chatTitle').textContent = config.title
        document.getElementById('chatDescription').textContent = config.description

        // 更新品牌指示器
        const indicator = document.getElementById('brandIndicator')
        indicator.className = `brand-indicator ${config.indicatorClass}`

        // 更新选择器
        document.getElementById('brandSelect').value = currentBrand
      }

      // 为品牌重置对话
      function resetChatForBrand() {
        const config = brandConfig[currentBrand]

        // 清空消息并显示欢迎消息
        const messagesContainer = document.getElementById('chatMessages')
        messagesContainer.innerHTML = `
          <div class="message agent">
            <div class="message-avatar">${config.avatar}</div>
            <div class="message-content">${config.welcomeMessage}</div>
          </div>
        `
      }

      async function sendMessage() {
        const input = document.getElementById('messageInput')
        const message = input.value.trim()

        if (!message) return

        // 添加用户消息
        addMessage(message, 'user')
        input.value = ''

        // 显示加载状态
        showLoading(true)
        document.getElementById('sendBtn').disabled = true

        try {
          const response = await fetch('/chat', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              message: message,
              brand_id: currentBrand, // 添加品牌ID
              conversation_id: conversationId // 添加会话ID
            })
          })

          const data = await response.json()

          if (response.ok) {
            addMessage(data.response, 'agent')
          } else {
            addMessage('抱歉，服务暂时不可用，请稍后再试。', 'agent')
          }
        } catch (error) {
          console.error('Error:', error)
          addMessage('网络连接出现问题，请检查网络后重试。', 'agent')
        } finally {
          showLoading(false)
          document.getElementById('sendBtn').disabled = false
          input.focus()
        }
      }

      function addMessage(content, type) {
        const messagesContainer = document.getElementById('chatMessages')
        const messageDiv = document.createElement('div')
        messageDiv.className = `message ${type}`

        const avatar = type === 'user' ? '👤' : brandConfig[currentBrand].avatar

        messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">${content.replace(
                  /\n/g,
                  '<br>'
                )}</div>
            `

        messagesContainer.appendChild(messageDiv)
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }

      function showLoading(show) {
        const loading = document.getElementById('loading')
        loading.className = show ? 'loading show' : 'loading'
      }

      function handleKeyPress(event) {
        if (event.key === 'Enter') {
          sendMessage()
        }
      }

      async function resetChat() {
        try {
          // 重置当前会话的记忆（包含品牌参数）
          await fetch(`/conversation/${conversationId}/reset?brand_id=${currentBrand}`, {
            method: 'POST'
          })

          // 重置对话UI
          resetChatForBrand()

          console.log('对话已重置，会话ID:', conversationId, '品牌:', currentBrand)
        } catch (error) {
          console.error('Reset error:', error)
        }
      }

      // 调试功能相关变量
      let debugDialogues = []
      let currentDialogue = null
      let currentStep = 0

      // 打开调试面板
      async function openDebugPanel() {
        document.getElementById('debugPanel').style.display = 'block'
        await loadDialogues()
      }

      // 关闭调试面板
      function closeDebugPanel() {
        document.getElementById('debugPanel').style.display = 'none'
      }

      // 加载所有对话数据
      async function loadDialogues() {
        const listContainer = document.getElementById('dialogueList')
        listContainer.innerHTML =
          '<div style="padding: 20px; text-align: center;">加载中...</div>'

        try {
          console.log('开始加载对话数据...')
          const response = await fetch('/debug/dialogues')
          console.log('API响应状态:', response.status)

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }

          const data = await response.json()
          console.log('获取到的数据:', data)

          debugDialogues = data.dialogues || []
          listContainer.innerHTML = ''

          if (debugDialogues.length === 0) {
            listContainer.innerHTML =
              '<div style="padding: 20px; text-align: center; color: #666;">没有找到对话数据</div>'
            return
          }

          debugDialogues.forEach((dialogue, index) => {
            const item = document.createElement('div')
            item.className = 'dialogue-item'
            item.innerHTML = `
                        <div style="font-weight: bold;">${dialogue.id}</div>
                        <div style="font-size: 12px; color: #666;">${dialogue.product}</div>
                        <div style="font-size: 12px; color: #666;">${dialogue.customerType}</div>
                    `
            item.onclick = () => selectDialogue(index)
            listContainer.appendChild(item)
          })

          console.log(`成功加载 ${debugDialogues.length} 个对话`)
        } catch (error) {
          console.error('加载对话数据失败:', error)
          listContainer.innerHTML = `<div style="padding: 20px; text-align: center; color: red;">加载失败: ${error.message}</div>`
        }
      }

      // 选择对话
      function selectDialogue(index) {
        // 更新选中状态
        document.querySelectorAll('.dialogue-item').forEach(item => {
          item.classList.remove('active')
        })
        document
          .querySelectorAll('.dialogue-item')
          [index].classList.add('active')

        // 设置当前对话
        currentDialogue = debugDialogues[index]
        currentStep = 0

        // 更新标题
        document.getElementById(
          'debugTitle'
        ).textContent = `调试: ${currentDialogue.id}`

        // 重置调试区域
        resetDebug()
      }

      // 重置调试
      async function resetDebug() {
        if (!currentDialogue) return

        // 重置特定对话的聊天历史
        try {
          const conversationId = `debug_${currentDialogue.id}`
          await fetch('/reset', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              conversation_id: conversationId
            })
          })
          console.log(`聊天历史已重置: ${conversationId}`)
        } catch (error) {
          console.error('重置聊天历史失败:', error)
        }

        currentStep = 0
        const messagesContainer = document.getElementById('debugMessages')
        messagesContainer.innerHTML = ''

        updateStepInfo()
        document.getElementById('nextStepBtn').disabled = false
      }

      // 下一步
      async function nextStep() {
        if (!currentDialogue || currentStep >= currentDialogue.dialogue.length)
          return

        const currentMessage = currentDialogue.dialogue[currentStep]

        if (currentMessage.speaker === 'customer') {
          // 1. 显示客户消息
          addDebugMessage(currentMessage.message, 'user')

          // 2. 立即发送给AI获取回复
          document.getElementById('nextStepBtn').disabled = true
          document.getElementById('stepInfo').textContent = 'AI正在思考...'

          await sendDebugMessage(currentMessage.message)

          // 3. 显示原始客服回复进行对比
          currentStep++
          if (
            currentStep < currentDialogue.dialogue.length &&
            currentDialogue.dialogue[currentStep].speaker === 'service'
          ) {
            const originalResponse =
              currentDialogue.dialogue[currentStep].message
            showOriginalResponse(originalResponse)
            currentStep++ // 跳过service消息，因为我们已经处理了
          }

          document.getElementById('nextStepBtn').disabled = false
          updateStepInfo()
        }

        // 检查是否完成
        if (currentStep >= currentDialogue.dialogue.length) {
          document.getElementById('nextStepBtn').disabled = true
          updateStepInfo('调试完成！')
        }
      }

      // 发送调试消息给AI
      async function sendDebugMessage(message) {
        try {
          const conversationId = currentDialogue
            ? `debug_${currentDialogue.id}`
            : null

          const response = await fetch('/chat', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              message: message,
              conversation_id: conversationId
            })
          })

          const data = await response.json()
          addDebugMessage(data.response, 'agent')
        } catch (error) {
          console.error('发送调试消息失败:', error)
          addDebugMessage('发送消息失败', 'agent')
        }
      }

      // 添加调试消息
      function addDebugMessage(message, type) {
        const messagesContainer = document.getElementById('debugMessages')
        const messageDiv = document.createElement('div')
        messageDiv.className = `message ${type}`

        const avatar = document.createElement('div')
        avatar.className = 'message-avatar'
        avatar.textContent = type === 'user' ? '👤' : '🤖'

        const content = document.createElement('div')
        content.className = 'message-content'
        content.textContent = message

        messageDiv.appendChild(avatar)
        messageDiv.appendChild(content)
        messagesContainer.appendChild(messageDiv)
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }

      // 显示原始客服回复
      function showOriginalResponse(originalResponse) {
        const messagesContainer = document.getElementById('debugMessages')
        const originalDiv = document.createElement('div')
        originalDiv.className = 'original-response'
        originalDiv.innerHTML = `
                <div class="original-response-label">原始客服回复：</div>
                <div>${originalResponse}</div>
            `
        messagesContainer.appendChild(originalDiv)
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }

      // 更新步骤信息
      function updateStepInfo(customText = null) {
        const stepInfoElement = document.getElementById('stepInfo')
        if (customText) {
          stepInfoElement.textContent = customText
        } else if (currentDialogue) {
          stepInfoElement.textContent = `步骤 ${currentStep + 1} / ${
            currentDialogue.dialogue.length
          }`
        } else {
          stepInfoElement.textContent = '请选择对话'
        }
      }

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', function () {
        // 初始化品牌UI
        updateBrandUI()
        resetChatForBrand()

        // 聚焦输入框
        document.getElementById('messageInput').focus()

        // 显示会话ID和品牌信息
        document.getElementById(
          'sessionInfo'
        ).textContent = `会话ID: ${conversationId} | 品牌: ${brandConfig[currentBrand].name}`
      })
    </script>
  </body>
</html>
