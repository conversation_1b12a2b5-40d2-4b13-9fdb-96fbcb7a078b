"""
千牛API数据模型定义
基于qianniu.md中的实际返回数据结构设计
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime


@dataclass
class QianniuApiResponse:
    """千牛API统一响应格式"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    method: Optional[str] = None
    api: Optional[str] = None
    ret: Optional[List[str]] = None
    version: Optional[str] = None


@dataclass
class ShopItem:
    """店铺商品信息"""
    item_id: str
    title: str
    price: str
    pic_url: str
    item_url: str
    category_id: int
    quantity: int
    sold_quantity: int
    approve_status: str
    have_gift: bool = False
    props: Optional[str] = None
    props_name: Optional[str] = None


@dataclass
class ShopItemsResult:
    """店铺商品查询结果"""
    items: List[ShopItem]
    page: int
    page_size: int
    total_count: int


@dataclass
class CustomerInfo:
    """客户信息"""
    buyer_credit_level: int
    buyer_credit_score: int
    send_good_rate: str
    is_new_customer: bool
    is_shop_fans: bool
    has_membership: bool
    vip_level: int
    corporate_member: bool = False
    buyer_credit_icon: Optional[str] = None
    buyer_credit_pic: Optional[str] = None


@dataclass
class OrderItem:
    """订单商品项"""
    auction_id: str
    auction_title: str
    auction_price: str
    auction_url: str
    pic_url: str
    buy_amount: int
    price: str
    old_price: str
    card_type_text: str
    logistics_status: int
    pay_status: int
    refund_status: int
    sub_order_id: str
    create_time: str
    pay_time: Optional[str] = None
    item_code: Optional[str] = None
    sku_code: Optional[str] = None


@dataclass
class Order:
    """订单信息"""
    biz_order_id: str
    order_price: str
    card_type_text: str
    category: str
    create_time: str
    pay_time: str
    receiver_name: str
    receiver_address: str
    receiver_mobile_phone: str
    buy_amount: int
    post_fee: str
    refund_fee: str
    after_sale_text: str
    items: List[OrderItem]
    consign_time: Optional[str] = None
    promotion_total_fee: Optional[str] = None


@dataclass
class OrdersResult:
    """订单查询结果"""
    orders: List[Order]
    page_num: int
    use_new_change_refund: bool = True
    use_new_instead_refund: bool = True


@dataclass
class ItemRecord:
    """商品记录（足迹/购买记录）"""
    item_id: str
    title: str
    price: str
    pic_url: str
    item_url: str
    buy_time: str
    view_time: Optional[str] = None
    time_stamp: Optional[str] = None
    quantity: Optional[int] = None
    sold_quantity: Optional[int] = None


@dataclass
class ItemRecordResult:
    """商品记录查询结果"""
    recently_bought_items: List[ItemRecord]
    foot_point_items: List[ItemRecord]
    under_inquiry_items: List[ShopItem]
    show_recently_bought: bool = True
    has_more_foot_print: bool = False


@dataclass
class BuyerSearchResult:
    """买家搜索结果"""
    account_id: str
    encrypt_account_id: str
    nick: str
    display_nick: str
    account_type: int
    search_type: str
    account_roles: Optional[List[str]] = None


@dataclass
class LogisticsEvent:
    """物流事件"""
    time: str
    status: str
    location: str


@dataclass
class OrderDetailItem:
    """订单详情商品项"""
    actual_pay_amount: str
    biz_order_id: str
    buy_amount: int
    item_id: int
    item_title: str
    pic_url: str
    sub_order_id: str


@dataclass
class OrderDetail:
    """订单详情"""
    item_detail_info_list: List[OrderDetailItem]
    main_biz_order_id: str
    price: float


@dataclass
class ReceiverInfo:
    """收货人信息"""
    address: str
    address_key_word_flag: bool
    area: str
    city: str
    detail_address: str
    full_address: str
    mobile: str
    name: str
    state: str
    town: str


@dataclass
class LogisticsInfo:
    """物流信息"""
    order_id: str
    logistics_info_list: List[Dict] = None
    order_detail_list: List[OrderDetail] = None
    receiver: Optional[ReceiverInfo] = None
    status: Optional[str] = None
    events: List[LogisticsEvent] = None


@dataclass
class CouponInfo:
    """优惠券信息"""
    activity_id: str
    name: str
    description: str
    discount_amount: Optional[str] = None
    min_amount: Optional[str] = None
    expire_time: Optional[str] = None


@dataclass
class CouponsResult:
    """优惠券查询结果"""
    coupons: List[CouponInfo]
    total_count: int


@dataclass
class CustomerService:
    """客服信息"""
    service_id: str
    service_name: str
    online_status: str
    group_id: Optional[str] = None
    group_name: Optional[str] = None


@dataclass
class CustomerServiceResult:
    """客服查询结果"""
    services: List[CustomerService]
    total_count: int


@dataclass
class DispatchGroup:
    """客服分组"""
    group_id: str
    group_name: str
    member_count: int
    online_count: int


@dataclass
class DispatchGroupsResult:
    """客服分组查询结果"""
    groups: List[DispatchGroup]
    total_count: int


@dataclass
class InviteOrderResult:
    """邀请下单结果"""
    success: bool
    order_url: Optional[str] = None
    message: Optional[str] = None


@dataclass
class SendItemCardResult:
    """发送商品卡片结果"""
    send_card: bool
    send_url: str


@dataclass
class SendCouponResult:
    """发送优惠券结果"""
    success: bool
    coupon_id: Optional[str] = None
    message: Optional[str] = None


# 工具函数：将原始API响应转换为结构化数据
class QianniuDataParser:
    """千牛数据解析器"""

    @staticmethod
    def parse_shop_items(raw_data: Dict) -> ShopItemsResult:
        """解析店铺商品数据"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 统一使用两层结构：raw_data['data']['data']
            level1_data = raw_data.get('data', {})
            level2_data = level1_data.get('data', {})

            # 商品数据在data字段中（搜索店铺商品API返回的结构）
            items_data = level2_data.get('data', [])
            logger.info(f"📦 解析商品数据: 找到 {len(items_data)} 个商品")

            items = []
            for item_data in items_data:
                try:
                    item = ShopItem(
                        item_id=str(item_data.get('itemId', '')),
                        title=item_data.get('title', ''),
                        price=str(item_data.get('price', '0')),
                        pic_url=item_data.get('pic', ''),  # 注意：测试结果显示字段名是'pic'
                        item_url=item_data.get('itemUrl', ''),
                        category_id=item_data.get('categoryId', 0),
                        quantity=item_data.get('quantity', 0),
                        sold_quantity=item_data.get('soldQuantity', 0),
                        approve_status=item_data.get('approveStatus', ''),
                        have_gift=item_data.get('haveGift', False),
                        props=item_data.get('props'),
                        props_name=item_data.get('propsName')
                    )
                    items.append(item)
                except Exception as e:
                    logger.error(f"❌ 解析商品失败: {e}")
                    continue

            return ShopItemsResult(
                items=items,
                page=level2_data.get('page', 1),
                page_size=level2_data.get('pageSize', 8),
                total_count=level2_data.get('totalCount', 0)
            )

        except Exception as e:
            logger.error(f"❌ 商品数据解析失败: {e}")
            try:
                from ..utils.logger import log_json_data
                log_json_data(logger, "商品数据", raw_data, "error", 600)
            except ImportError:
                logger.error(f"商品数据: {raw_data}")

            return ShopItemsResult(
                items=[],
                page=1,
                page_size=8,
                total_count=0
            )

    @staticmethod
    def parse_customer_info(raw_data: Dict) -> CustomerInfo:
        """解析客户信息"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 统一使用两层结构：raw_data['data']['data']
            level1_data = raw_data.get('data', {})
            level2_data = level1_data.get('data', {})

            logger.info(f"📋 解析客户信息数据")

            return CustomerInfo(
                buyer_credit_level=level2_data.get('buyerCreditLevel', 0),
                buyer_credit_score=level2_data.get('buyerCreditScore', 0),
                send_good_rate=level2_data.get('sendGoodRate', '0%'),
                is_new_customer=level2_data.get('isNewCustomer', False),
                is_shop_fans=level2_data.get('isShopFans', False),
                has_membership=level2_data.get('hasMembership', False),
                vip_level=level2_data.get('vipLevel', 0),
                corporate_member=level2_data.get('corporateMember', False),
                buyer_credit_icon=level2_data.get('buyerCreditIcon'),
                buyer_credit_pic=level2_data.get('buyerCreditPic')
            )

        except Exception as e:
            logger.error(f"❌ 客户信息解析失败: {e}")
            try:
                from ..utils.logger import log_json_data
                log_json_data(logger, "客户信息数据", raw_data, "error", 600)
            except ImportError:
                logger.error(f"客户信息数据: {raw_data}")

            # 返回默认的客户信息
            return CustomerInfo(
                buyer_credit_level=0,
                buyer_credit_score=0,
                send_good_rate='0%',
                is_new_customer=False,
                is_shop_fans=False,
                has_membership=False,
                vip_level=0,
                corporate_member=False,
                buyer_credit_icon=None,
                buyer_credit_pic=None
            )

    @staticmethod
    def parse_orders(raw_data: Dict) -> OrdersResult:
        """解析订单数据"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 根据日志显示的实际数据结构，逐层解析
            logger.info(f"原始数据键: {list(raw_data.keys())}")

            # 第一层：raw_data['data']
            level1_data = raw_data.get('data', {})
            logger.info(f"第一层数据键: {list(level1_data.keys()) if isinstance(level1_data, dict) else 'not dict'}")

            # 第二层：raw_data['data']['data']
            level2_data = level1_data.get('data', {})
            logger.info(f"第二层数据键: {list(level2_data.keys()) if isinstance(level2_data, dict) else 'not dict'}")

            # 统一使用两层结构，订单数据在第二层
            orders_data = level2_data.get('orders', [])
            data = level2_data
            logger.info(f"📍 订单数据在第二层")

            logger.info(f"📊 解析订单数据: 找到 {len(orders_data)} 个订单")

            # 如果没有找到订单，打印更多调试信息
            if len(orders_data) == 0:
                # 导入格式化函数
                try:
                    from ..utils.logger import log_json_data
                    log_json_data(logger, "⚠️ 未找到订单数据，level2_data内容", level2_data, "warning", 600)
                    log_json_data(logger, "⚠️ level1_data内容", level1_data, "warning", 600)
                except ImportError:
                    # 如果导入失败，使用原来的方式
                    logger.warning(f"⚠️ 未找到订单数据，level2_data内容: {level2_data}")
                    logger.warning(f"⚠️ level1_data内容: {level1_data}")

        except (KeyError, TypeError, AttributeError) as e:
            logger.error(f"❌ 订单数据解析失败: {e}")
            # 格式化输出原始数据结构
            try:
                from ..utils.logger import log_json_data
                log_json_data(logger, "原始数据结构", raw_data, "error", 800)
            except ImportError:
                logger.error(f"原始数据结构: {raw_data}")
            # 如果不是预期格式，返回空结果
            orders_data = []
            data = {}

        orders = []
        for i, order_data in enumerate(orders_data):
            try:
                logger.debug(f"📦 解析第 {i+1} 个订单: {order_data.get('bizOrderId', 'unknown')}")

                # 解析订单商品
                items = []
                for item_data in order_data.get('itemList', []):
                    item = OrderItem(
                        auction_id=str(item_data.get('auctionId', '')),
                        auction_title=item_data.get('auctionTitle', ''),
                        auction_price=item_data.get('auctionPrice', '0'),
                        auction_url=item_data.get('auctionUrl', ''),
                        pic_url=item_data.get('picUrl', ''),
                        buy_amount=item_data.get('buyAmount', 1),
                        price=item_data.get('price', '0'),
                        old_price=item_data.get('oldPrice', '0'),
                        card_type_text=item_data.get('cardTypeText', ''),
                        logistics_status=item_data.get('logisticsStatus', 0),
                        pay_status=item_data.get('payStatus', 0),
                        refund_status=item_data.get('refundStatus', 0),
                        sub_order_id=str(item_data.get('subOrderId', '')),
                        create_time=item_data.get('createTime', ''),
                        pay_time=item_data.get('payTime'),
                        item_code=item_data.get('itemCode'),
                        sku_code=item_data.get('skuCode')
                    )
                items.append(item)

                # 创建订单对象
                order = Order(
                    biz_order_id=str(order_data.get('bizOrderId', '')),
                    order_price=order_data.get('orderPrice', '0'),  # 注意：API返回的字段名就是orderPrice
                    card_type_text=order_data.get('cardTypeText', ''),
                    category=order_data.get('category', ''),
                    create_time=order_data.get('createTime', ''),
                    pay_time=order_data.get('payTime', ''),
                    receiver_name=order_data.get('receiverName', ''),
                    receiver_address=order_data.get('receiverAddress', ''),
                    receiver_mobile_phone=order_data.get('receiverMobilePhone', ''),
                    buy_amount=order_data.get('buyAmount', 1),
                    post_fee=order_data.get('postFee', '0'),
                    refund_fee=order_data.get('refundFee', '0'),
                    after_sale_text=order_data.get('afterSaleText', ''),
                    items=items,
                    consign_time=order_data.get('consignTime'),
                    promotion_total_fee=order_data.get('promotionTotalFee')
                )
                orders.append(order)
                logger.debug(f"✅ 订单解析成功: {order.biz_order_id}")

            except Exception as e:
                logger.error(f"❌ 解析订单失败: {e}")
                # 格式化输出订单数据
                try:
                    from ..utils.logger import log_json_data
                    log_json_data(logger, "订单数据", order_data, "error", 400)
                except ImportError:
                    logger.error(f"订单数据: {order_data}")
                continue

        logger.info(f"📊 订单解析完成: 成功解析 {len(orders)} 个订单")

        return OrdersResult(
            orders=orders,
            page_num=data.get('pageNum', 1),
            use_new_change_refund=data.get('useNewChangeRefund', True),
            use_new_instead_refund=data.get('useNewInsteadRefund', True)
        )

    @staticmethod
    def parse_logistics_info(raw_data: Dict, order_id: str) -> LogisticsInfo:
        """解析物流信息"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 统一使用两层结构：raw_data['data']['data']
            level1_data = raw_data.get('data', {})
            level2_data = level1_data.get('data', {})

            # 解析订单详情列表
            order_detail_list = []
            for order_detail_data in level2_data.get('orderDetailList', []):
                item_detail_list = []
                for item_data in order_detail_data.get('itemDetailInfoList', []):
                    item_detail = OrderDetailItem(
                        actual_pay_amount=item_data.get('actualPayAmount', '0'),
                        biz_order_id=str(item_data.get('bizOrderId', '')),
                        buy_amount=item_data.get('buyAmount', 1),
                        item_id=item_data.get('itemId', 0),
                        item_title=item_data.get('itemTitle', ''),
                        pic_url=item_data.get('picUrl', ''),
                        sub_order_id=str(item_data.get('subOrderId', ''))
                    )
                    item_detail_list.append(item_detail)

                order_detail = OrderDetail(
                    item_detail_info_list=item_detail_list,
                    main_biz_order_id=str(order_detail_data.get('mainBizOrderId', '')),
                    price=order_detail_data.get('price', 0.0)
                )
                order_detail_list.append(order_detail)

            # 解析收货人信息
            receiver_data = level2_data.get('receiver', {})
            receiver = ReceiverInfo(
                address=receiver_data.get('address', ''),
                address_key_word_flag=receiver_data.get('addressKeyWordFlag', False),
                area=receiver_data.get('area', ''),
                city=receiver_data.get('city', ''),
                detail_address=receiver_data.get('detailAddress', ''),
                full_address=receiver_data.get('fullAddress', ''),
                mobile=receiver_data.get('mobile', ''),
                name=receiver_data.get('name', ''),
                state=receiver_data.get('state', ''),
                town=receiver_data.get('town', '')
            ) if receiver_data else None

            # 解析物流信息列表
            logistics_info_list = level2_data.get('logisticsInfoList', [])

            return LogisticsInfo(
                order_id=order_id,
                logistics_info_list=logistics_info_list,
                order_detail_list=order_detail_list,
                receiver=receiver,
                status="已发货" if logistics_info_list else "未发货",
                events=[]  # 当前API没有返回详细的物流轨迹
            )

        except Exception as e:
            logger.error(f"❌ 解析物流信息失败: {e}")
            try:
                from ..utils.logger import log_json_data
                log_json_data(logger, "物流数据", raw_data, "error", 600)
            except ImportError:
                logger.error(f"物流数据: {raw_data}")

            return LogisticsInfo(
                order_id=order_id,
                logistics_info_list=[],
                order_detail_list=[],
                receiver=None,
                status="解析失败",
                events=[]
            )

    @staticmethod
    def parse_buyer_search_result(raw_data: Dict) -> List[BuyerSearchResult]:
        """解析买家搜索结果"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 统一使用两层结构：raw_data['data']['data']
            level1_data = raw_data.get('data', {})
            level2_data = level1_data.get('data', {})

            # 根据API测试结果，买家搜索数据在data字段中
            search_results = level2_data.get('data', [])
            logger.info(f"🔍 解析买家搜索结果: 找到 {len(search_results)} 个结果")

            results = []
            for result_data in search_results:
                try:
                    result = BuyerSearchResult(
                        account_id=str(result_data.get('accountId', '')),
                        encrypt_account_id=str(result_data.get('encryptAccountId', '')),
                        nick=result_data.get('nick', ''),
                        display_nick=result_data.get('displayNick', ''),
                        account_type=result_data.get('accountType', 0),
                        search_type=result_data.get('searchType', ''),
                        account_roles=result_data.get('accountRoles', [])
                    )
                    results.append(result)
                except Exception as e:
                    logger.error(f"❌ 解析买家搜索结果失败: {e}")
                    continue

            return results

        except Exception as e:
            logger.error(f"❌ 买家搜索数据解析失败: {e}")
            try:
                from ..utils.logger import log_json_data
                log_json_data(logger, "买家搜索数据", raw_data, "error", 600)
            except ImportError:
                logger.error(f"买家搜索数据: {raw_data}")

            return []

    @staticmethod
    def parse_shop_coupons(raw_data: Dict) -> CouponsResult:
        """解析店铺优惠券数据"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 统一使用两层结构：raw_data['data']['data']
            level1_data = raw_data.get('data', {})
            level2_data = level1_data.get('data', {})

            # 根据API测试结果，优惠券数据分为公开和私有两类
            public_coupons = level2_data.get('publicCouponList', [])
            private_coupons = level2_data.get('privateCouponList', [])
            coupons_data = public_coupons + private_coupons
            logger.info(f"🎫 解析优惠券数据: 找到 {len(coupons_data)} 个优惠券")

            coupons = []
            for coupon_data in coupons_data:
                try:
                    coupon = CouponInfo(
                        activity_id=str(coupon_data.get('activityId', '')),
                        name=coupon_data.get('name', ''),
                        description=coupon_data.get('description', ''),
                        discount_amount=coupon_data.get('discountAmount'),
                        min_amount=coupon_data.get('minAmount'),
                        expire_time=coupon_data.get('expireTime')
                    )
                    coupons.append(coupon)
                except Exception as e:
                    logger.error(f"❌ 解析优惠券失败: {e}")
                    continue

            return CouponsResult(
                coupons=coupons,
                total_count=level2_data.get('totalCount', len(coupons))
            )

        except Exception as e:
            logger.error(f"❌ 优惠券数据解析失败: {e}")
            try:
                from ..utils.logger import log_json_data
                log_json_data(logger, "优惠券数据", raw_data, "error", 600)
            except ImportError:
                logger.error(f"优惠券数据: {raw_data}")

            return CouponsResult(
                coupons=[],
                total_count=0
            )

    @staticmethod
    def parse_item_record(raw_data: Dict) -> ItemRecordResult:
        """解析商品记录数据"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 统一使用两层结构：raw_data['data']['data']
            level1_data = raw_data.get('data', {})
            level2_data = level1_data.get('data', {})

            # 根据API测试结果，商品记录在footPointItemList字段中
            items_data = level2_data.get('footPointItemList', [])
            logger.info(f"📋 解析商品记录数据: 找到 {len(items_data)} 个记录")

            items = []
            for item_data in items_data:
                try:
                    item = ShopItem(
                        item_id=str(item_data.get('itemId', '')),
                        title=item_data.get('title', ''),
                        price=str(item_data.get('price', '0')),
                        pic_url=item_data.get('pic', ''),
                        item_url=item_data.get('itemUrl', ''),
                        category_id=item_data.get('categoryId', 0),
                        quantity=item_data.get('quantity', 0),
                        sold_quantity=item_data.get('soldQuantity', 0),
                        approve_status=item_data.get('approveStatus', ''),
                        have_gift=item_data.get('haveGift', False),
                        props=item_data.get('props'),
                        props_name=item_data.get('propsName')
                    )
                    items.append(item)
                except Exception as e:
                    logger.error(f"❌ 解析商品记录失败: {e}")
                    continue

            return ItemRecordResult(
                items=items,
                total_count=len(items)
            )

        except Exception as e:
            logger.error(f"❌ 商品记录数据解析失败: {e}")
            try:
                from ..utils.logger import log_json_data
                log_json_data(logger, "商品记录数据", raw_data, "error", 600)
            except ImportError:
                logger.error(f"商品记录数据: {raw_data}")

            return ItemRecordResult(
                items=[],
                total_count=0
            )
