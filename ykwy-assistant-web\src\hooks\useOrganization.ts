import { useSession } from '../lib/auth-client';

/**
 * 组织管理 Hook
 * 直接从用户会话中获取组织信息
 */
export function useOrganization() {
  const { data: session } = useSession();

  const user = session?.user;
  const organizationId = user?.organizationId;
  const organization = user?.organization;

  return {
    // 组织信息
    organizationId,
    organization,

    // 状态
    hasOrganization: !!organizationId,
    isOrganizationAdmin: user?.role === 'ORGANIZATION_ADMIN',

    // 用户在组织中的角色
    userRole: user?.role,

    // 便捷方法
    isAdmin: user?.role === 'ORGANIZATION_ADMIN',
    isTeamManager: user?.role === 'TEAM_MANAGER',
    isCustomerService: user?.role === 'CUSTOMER_SERVICE',
  };
}

/**
 * 团队管理 Hook
 * 直接从用户会话中获取团队信息
 */
export function useTeam() {
  const { data: session } = useSession();

  const user = session?.user;
  const teamId = user?.teamId;
  const team = user?.team;

  return {
    // 团队信息
    teamId,
    team,

    // 状态
    hasTeam: !!teamId,
    isTeamManager: user?.role === 'TEAM_MANAGER',

    // 用户在团队中的角色
    userRole: user?.role,
  };
}
