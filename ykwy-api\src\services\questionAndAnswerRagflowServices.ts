import { getFormattedDateTimeFileName } from '../utils/dateTimeTool.ts';
import { RagflowUploader } from '../utils/ragflowUploader.ts';
import { KnowledgeCategoryService } from './knowledgeCategoryService.ts';
import { QuestionAndAnswerService } from './questionAndAnswerService.ts';

const questionAndAnswerService = new QuestionAndAnswerService();
const knowledgeCategoryService = new KnowledgeCategoryService();
const ragflowUploader = new RagflowUploader();

export class RagflowUploaderService {
  async syncToRagflow() {
    // 1. 查分类
    const allCategories = await knowledgeCategoryService.findAllActive();
    const categoryMap: Record<string, string> = {};
    allCategories.forEach((cat) => {
      categoryMap[cat.code] = cat.name;
    });

    // 2. 查问答
    const allQA = await questionAndAnswerService.findAllFromQuestionAndAnswer({
      includeDeleted: false,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });

    // 3. 构建 TXT 内容（内存字符串）
    const lines = allQA.map((item) => {
      const questionType = sanitizeSimpleText(item.questionType);
      const categoryName = sanitizeSimpleText(categoryMap[item.categoryCode] || item.categoryCode);
      const commonSamples = formatArrayField(item.commonQuestionSamples);
      const answers = formatArrayField(item.answers);
      const orderStatus = formatOrderStatus(item.orderStatus ?? '');

      return [
        `问题类型：${questionType}`,
        `分类：${categoryName}`,
        `常见问法：${commonSamples}`,
        `回答：${answers}`,
        `订单状态：${orderStatus}`,
        '！@#￥', // 使用分隔符作为数据分段
      ].join('\n');
    });

    const fileContent = lines.join('\n');
    const fileName = getFormattedDateTimeFileName('问答知识库');
    const delimiter = '！@#￥';

    // 4. 完整调用 Ragflow 工具方法：删除旧文档 -> 上传 -> 设置分隔符 -> 解析
    await ragflowUploader.clearDocumentsByPrefix('问答知识库');
    const newDoc = await ragflowUploader.uploadFileFromStream(fileContent, fileName);
    await ragflowUploader.updateDocumentParserConfig(newDoc.id, delimiter);
    await ragflowUploader.parseDocuments([newDoc.id]);

    return newDoc.id;
  }
}

function sanitizeSimpleText(field: string | null | undefined): string {
  return typeof field === 'string' ? field.trim() : '';
}

function formatArrayField(arr: string[] | null | undefined): string {
  if (!arr || arr.length === 0) return '';
  return arr.map((s) => s.replace(/\n/g, ' ').trim()).join('|');
}

function formatOrderStatus(status: string): string {
  if (!status || status === '0000') return '无';
  const labels = ['售前', '发货前', '发货后', '售后'];
  const result: string[] = [];
  status.split('').forEach((bit, idx) => {
    if (bit === '1' && labels[idx]) result.push(labels[idx]);
  });
  return result.join('|');
}
