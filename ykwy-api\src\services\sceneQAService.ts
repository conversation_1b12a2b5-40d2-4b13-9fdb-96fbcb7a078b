import { Prisma } from '@prisma/client';

import prisma from '../client/prisma.ts';
import type { BulkDeleteResultDto, SceneQADto, SceneQAListDto } from '../types/dto/sceneQA';
import type { SceneQAInput, SceneQAQuery, SceneQASearchByQuery } from '../types/validators/sceneQAValidator';

/**
 * 场景Q&A服务层，负责与数据库的交互
 */
export class SceneQAService {
  /**
   * 创建或更新场景Q&A
   * @param data 场景Q&A输入参数
   */
  async upsert(data: SceneQAInput): Promise<SceneQADto> {
    const { question, answer, keywords, intent, productUrl, shopId } = data;

    // 构建更新/创建数据对象
    const sceneQAData: Prisma.SceneQACreateInput = {
      question,
      answer,
      keywords,
      intent,
      productUrl,
      shopId,
    };

    // upsert: 使用question和intent的组合唯一键
    return prisma.sceneQA.upsert({
      where: {
        question_intent: {
          question,
          intent,
        },
      },
      update: {
        answer,
        keywords,
        productUrl,
        shopId,
      },
      create: sceneQAData,
    });
  }

  /**
   * 获取单个场景Q&A详情
   * @param id 场景Q&A ID
   */
  async findById(id: string): Promise<SceneQADto | null> {
    return prisma.sceneQA.findUnique({
      where: { id },
    });
  }

  /**
   * 根据查询条件搜索场景Q&A (shopId必须，支持其他字段组合查询)
   * @param params 搜索参数，shopId为必须字段，其他字段为可选组合查询条件
   */
  async searchByQuery(params: SceneQASearchByQuery): Promise<SceneQAListDto> {
    const { shopId, question, answer, keyword, intent, productUrl, query, skip = 0, take = 10 } = params;

    // 基础条件：shopId 必须匹配
    const whereCondition: Prisma.SceneQAWhereInput = {
      shopId,
    };

    // 构建其他查询条件数组
    const conditions: Prisma.SceneQAWhereInput[] = [];

    // 处理具体字段查询（精确匹配）
    if (question) conditions.push({ question });
    if (answer) conditions.push({ answer });
    if (keyword) conditions.push({ keywords: { has: keyword } });
    if (intent) conditions.push({ intent });
    if (productUrl) conditions.push({ productUrl });

    // 如果有其他查询条件，使用AND组合
    if (conditions.length > 0) {
      whereCondition.AND = conditions;
    }

    // 如果有query参数，进行关键词反向匹配
    let sceneQAs: SceneQADto[];
    let total: number;

    if (query && query.trim() !== '') {
      const trimmedQuery = query.trim().toLowerCase();

      // 获取所有符合基础条件的记录
      const allRecords = await prisma.sceneQA.findMany({
        where: whereCondition,
      });

      // 在代码中进行关键词反向匹配
      const filteredRecords = allRecords.filter((record) => {
        // 只检查query是否包含任何关键词
        return record.keywords.some((kw) => trimmedQuery.includes(kw.toLowerCase()));
      });

      total = filteredRecords.length;
      sceneQAs = filteredRecords.slice(skip, skip + take);
    } else {
      // 没有query参数时，使用正常的数据库查询
      const [records, count] = await prisma.$transaction([
        prisma.sceneQA.findMany({
          where: whereCondition,
          skip,
          take,
          orderBy: { createdAt: 'desc' },
        }),
        prisma.sceneQA.count({
          where: whereCondition,
        }),
      ]);

      sceneQAs = records;
      total = count;
    }

    return {
      items: sceneQAs,
      total,
    };
  }

  /**
   * 获取多个场景Q&A（分页）
   * @param params 查询参数
   */
  async findMany(params: SceneQAQuery): Promise<SceneQAListDto> {
    const { question, answer, keyword, intent, productUrl, shopId, skip = 0, take = 10 } = params;
    const where: Prisma.SceneQAWhereInput = {};

    if (question) where.question = { contains: question, mode: 'insensitive' };
    if (answer) where.answer = { contains: answer, mode: 'insensitive' };
    if (keyword) where.keywords = { has: keyword };
    if (intent) where.intent = { contains: intent, mode: 'insensitive' };
    if (productUrl) where.productUrl = { contains: productUrl, mode: 'insensitive' };
    if (shopId) where.shopId = { contains: shopId, mode: 'insensitive' };

    const [sceneQAs, total] = await prisma.$transaction([
      prisma.sceneQA.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: 'desc' },
      }),
      prisma.sceneQA.count({ where }),
    ]);

    return { items: sceneQAs, total };
  }

  /**
   * 删除场景Q&A
   * @param id 场景Q&A ID
   */
  async delete(id: string): Promise<SceneQADto> {
    return prisma.sceneQA.delete({
      where: { id },
    });
  }

  /**
   * 批量删除场景Q&A
   * @param ids 场景Q&A ID数组
   */
  async bulkDelete(ids: string[]): Promise<BulkDeleteResultDto> {
    const result = await prisma.sceneQA.deleteMany({
      where: { id: { in: ids } },
    });

    return { count: result.count };
  }

  /**
   * 批量创建场景Q&A
   * @param dataList 场景Q&A数据数组
   */
  async bulkCreate(dataList: Prisma.SceneQACreateManyInput[]): Promise<BulkDeleteResultDto> {
    const result = await prisma.sceneQA.createMany({
      data: dataList,
      skipDuplicates: true,
    });

    return { count: result.count };
  }

  /**
   * 清空所有场景Q&A（物理删除）
   */
  async truncate(): Promise<BulkDeleteResultDto> {
    const result = await prisma.sceneQA.deleteMany({});
    return { count: result.count };
  }

  /**
   * 循环分页获取全部场景Q&A
   * @param baseQuery 查询参数（不包含skip和take）
   * @param pageSize 每页数量，默认100
   */
  async findAll(baseQuery: Omit<SceneQAQuery, 'skip' | 'take'> = {}, pageSize = 100): Promise<SceneQADto[]> {
    let skip = 0;
    let allItems: SceneQADto[] = [];

    while (true) {
      const { items } = await this.findMany({
        ...baseQuery,
        skip,
        take: pageSize,
      });

      allItems = allItems.concat(items);
      if (items.length < pageSize) {
        break;
      }
      skip += pageSize;
    }

    return allItems;
  }
}
