import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { authClient } from '../../lib/auth-client';
import { type ConnectionInvitationResponse, useCreateConnectionInvitation, useTeamsForSelect } from '../../services';
import { DeploymentInstructionsModal } from './DeploymentInstructionsModal';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

const createInvitationSchema = z.object({
  name: z.string().min(1, '请输入连接名称').max(50, '连接名称不能超过50个字符'),
  description: z.string().optional(),
  teamId: z.string().min(1, '请选择团队'),
  expiresInDays: z.coerce.number().min(1, '过期天数至少为1天').max(30, '过期天数不能超过30天').default(7),
});

type CreateInvitationForm = z.infer<typeof createInvitationSchema>;

interface CreateConnectionInvitationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateConnectionInvitationDialog({ open, onOpenChange }: CreateConnectionInvitationDialogProps) {
  const [createdInvitation, setCreatedInvitation] = useState<ConnectionInvitationResponse | null>(null);
  const [showInstructions, setShowInstructions] = useState(false);

  const form = useForm({
    resolver: zodResolver(createInvitationSchema),
    defaultValues: {
      name: '',
      description: '',
      teamId: '',
      expiresInDays: 7,
    },
  });

  // 获取当前用户信息
  const { data: session } = authClient.useSession();
  const currentUser = session?.user as { id: string; role: string; teamId?: string } | undefined;
  const isTeamManager = currentUser?.role === 'TEAM_MANAGER';
  const isOrgAdmin = currentUser?.role === 'ORGANIZATION_ADMIN' || currentUser?.role === 'SYSTEM_ADMIN';

  // 获取团队列表
  const { data: teams, error: teamsError } = useTeamsForSelect();

  // 调试信息
  console.log('Current user:', currentUser);
  console.log('User role:', currentUser?.role);
  console.log('Is team manager:', isTeamManager);
  console.log('Teams data:', teams);
  console.log('Teams error:', teamsError);

  // 根据用户角色设置默认团队
  useEffect(() => {
    if (teams && isTeamManager) {
      // 团队管理员：找到其管理的团队并设为默认值
      const managedTeam = teams.find((team: { id: string; name: string }) => team.id === currentUser?.teamId);
      if (managedTeam) {
        form.setValue('teamId', managedTeam.id);
      }
    }
  }, [teams, isTeamManager, currentUser, form]);

  // 创建邀请
  const createMutation = useCreateConnectionInvitation();

  const onSubmit = (data: CreateInvitationForm) => {
    createMutation.mutate(data, {
      onSuccess: (response) => {
        toast.success('连接邀请创建成功');

        // 重置表单并关闭对话框
        form.reset();
        onOpenChange(false);

        // 延迟显示部署说明，确保主对话框先关闭
        setTimeout(() => {
          setCreatedInvitation(response);
          setShowInstructions(true);
        }, 100);
      },
      onError: (error: Error & { response?: { data?: { error?: string } } }) => {
        toast.error(error.response?.data?.error || '创建邀请失败');
      },
    });
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>创建连接邀请</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>连接名称 *</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：客服工作站-01" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>描述</FormLabel>
                    <FormControl>
                      <Textarea placeholder="例如：一楼客服区第3台电脑" rows={3} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="teamId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>所属团队 *</FormLabel>
                    <FormControl>
                      {isTeamManager ? (
                        // 团队管理员：显示只读的团队信息
                        <div className="flex items-center space-x-2 p-3 border rounded-md bg-gray-50">
                          <span className="text-sm text-gray-600">团队：</span>
                          <span className="font-medium">{teams?.find((team: { id: string; name: string }) => team.id === field.value)?.name || '加载中...'}</span>
                          <span className="text-xs text-gray-500">(团队管理员默认团队)</span>
                        </div>
                      ) : isOrgAdmin ? (
                        // 组织管理员：可选择团队
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="选择团队" />
                          </SelectTrigger>
                          <SelectContent>
                            {teams?.map((team: { id: string; name: string }) => (
                              <SelectItem key={team.id} value={team.id}>
                                {team.name}
                              </SelectItem>
                            ))}
                            {(!teams || teams.length === 0) && (
                              <SelectItem value="no-teams" disabled>
                                暂无可用团队
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      ) : (
                        // 其他角色：无权限创建邀请
                        <div className="p-3 border rounded-md bg-red-50 text-red-700">
                          <span className="text-sm">您没有权限创建连接邀请</span>
                        </div>
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expiresInDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>有效期（天）</FormLabel>
                    <FormControl>
                      <Input type="number" min={1} max={30} {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-2 pt-4">
                <Button type="button" variant="outline" onClick={handleClose}>
                  取消
                </Button>
                <Button type="submit" disabled={createMutation.isPending}>
                  {createMutation.isPending ? '创建中...' : '创建邀请'}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 部署说明模态框 */}
      {createdInvitation && <DeploymentInstructionsModal invitation={createdInvitation} open={showInstructions} onOpenChange={setShowInstructions} />}
    </>
  );
}
