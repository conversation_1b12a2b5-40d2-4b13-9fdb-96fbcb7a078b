import {
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Drawer,
  Input,
  Select,
  Space,
  Switch,
  Table,
  Tabs,
} from 'antd';
import React, { useState } from 'react';
import RuleSetting from './RuleSetting';

const sizeTableData = [
  {
    key: '1',
    name: '女士 宽松',
    range: '成人体重(84.0斤 ~ 149.9斤),成人身高(150.0厘米 ~ 179.9厘米)',
    value: 'L, M, S, XL, XS',
    bind: 10,
  },
  {
    key: '2',
    name: '男士 宽松',
    range: '成人体重(98.0斤 ~ 186.0斤),成人身高(160.0厘米 ~ 189.9厘米)',
    value: 'L, M, S, XL, XXL',
    bind: 513,
  },
  {
    key: '3',
    name: '男士 修身/合身版',
    range: '成人体重(98.0斤 ~ 186.0斤),成人身高(160.0厘米 ~ 189.9厘米)',
    value: '<PERSON>, <PERSON>, <PERSON>, X<PERSON>, XXL',
    bind: 1030,
  },
  {
    key: '4',
    name: '女士 修身/合身版',
    range: '成人体重(84.0斤 ~ 149.9斤),成人身高(150.0厘米 ~ 179.9厘米)',
    value: 'L, M, S, XL, XS',
    bind: 446,
  },
];

const complexTableData = [
  {
    key: '1',
    name: '男女情侣装合身版',
    type: '情侣身高体重尺码表',
    range:
      '男款:\n成人体重(98.0斤 ~ 186.0斤),成人身高(160.0厘米 ~ 189.9厘米)\n女款:\n成人体重(84.0斤 ~ 149.9斤),成人身高(150.0厘米 ~ 179.9厘米)',
    value: '男款:\nL,M,S,XL,XXL,宽松版映\n女款:\nL,M,S,XL,XS,宽松版映',
    bind: 0,
  },
  {
    key: '2',
    name: '情侣/宽松版',
    type: '情侣身高体重尺码表',
    range:
      '男款:\n成人体重(80.0斤 ~ 159.9斤),成人身高(155.0厘米 ~ 184.9厘米)\n女款:\n成人体重(84.0斤 ~ 149.9斤),成人身高(150.0厘米 ~ 179.9厘米)',
    value: '男款:\nL,M,S,XL,XXL\n女款:\nL,M,S,XL,XS',
    bind: 0,
  },
];

const SizeTable: React.FC = () => {
  const [activeTab, setActiveTab] = useState('1');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [showRulePage, setShowRulePage] = useState(false);

  const sizeColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text: string) => <a>{text}</a>,
    },
    {
      title: '尺码范围',
      dataIndex: 'range',
    },
    {
      title: '尺码值',
      dataIndex: 'value',
    },
    {
      title: '绑定商品',
      dataIndex: 'bind',
      render: (num: number) => <a>共 {num} 件商品</a>,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      render: () => (
        <Space>
          <Button icon={<CopyOutlined />} size="small" />

          <Button icon={<EditOutlined />} size="small" />

          <Button icon={<DeleteOutlined />} size="small" danger />
        </Space>
      ),
    },
  ];

  const complexColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (text: string) => <a>{text}</a>,
    },
    {
      title: '类型',
      dataIndex: 'type',
    },
    {
      title: '尺码范围',
      dataIndex: 'range',
      render: (text: string) => (
        <div style={{ whiteSpace: 'pre-line' }}>{text}</div>
      ),
    },
    {
      title: '尺码值',
      dataIndex: 'value',
      render: (text: string) => (
        <div style={{ whiteSpace: 'pre-line' }}>{text}</div>
      ),
    },
    {
      title: '绑定商品',
      dataIndex: 'bind',
      render: (num: number) => <a>共 {num} 件商品</a>,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      render: () => (
        <Space>
          <Button icon={<CopyOutlined />} size="small" />

          <Button icon={<EditOutlined />} size="small" />

          <Button icon={<DeleteOutlined />} size="small" danger />
        </Space>
      ),
    },
  ];

  if (showRulePage) {
    return <RuleSetting onBack={() => setShowRulePage(false)} />;
  }

  return (
    <div className="bg-[#f7f8fa] min-h-screen p-6">
      <div className="bg-white rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="font-semibold text-lg">尺码表</div>
          <Button className="ml-2" onClick={() => setDrawerOpen(true)}>
            规则设置
          </Button>
        </div>
        <div className="flex justify-between items-center mb-4">
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              { key: '1', label: '尺码表' },
              { key: '2', label: '复合尺码表' },
            ]}
            className="flex-1"
          />

          <div>
            <Button
              className="mr-2"
              type="default"
              onClick={() => setShowRulePage(true)}
            >
              快速绑定
            </Button>
            {activeTab === '1' ? (
              <Button type="primary" icon={<PlusOutlined />}>
                新建尺码表
              </Button>
            ) : (
              <Button type="primary" icon={<PlusOutlined />}>
                新建复合尺码表
              </Button>
            )}
          </div>
        </div>
        <Input.Search placeholder="请输入尺码表名称" className="w-80 mb-4" />

        {activeTab === '1' ? (
          <Table
            columns={sizeColumns}
            dataSource={sizeTableData}
            pagination={false}
            rowSelection={{}}
            bordered={false}
            scroll={{ y: 'calc(100vh - 300px)' }}
          />
        ) : (
          <Table
            columns={complexColumns}
            dataSource={complexTableData}
            pagination={false}
            rowSelection={{}}
            bordered={false}
            scroll={{ y: 'calc(100vh - 300px)' }}
          />
        )}
        <div className="flex justify-between items-center mt-4">
          <span>共 {activeTab === '1' ? 4 : 2} 条相关记录</span>
          <div>
            <Button className="mr-2"></Button>
            <Select defaultValue={10} className="w-20 ml-4">
              <Select.Option value={10}>10 条/页</Select.Option>
              <Select.Option value={20}>20 条/页</Select.Option>
            </Select>
          </div>
        </div>
      </div>
      <Drawer
        title="规则设置"
        width={600}
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
      >
        <div className="mb-6">
          <Switch className="mr-2" />
          <span className="font-medium">仅对售前买家推荐尺码</span>
        </div>
        <div className="text-[#888] text-sm leading-7">
          开启后，当买家进行咨询时，只会在选择的客服或订单状态下推荐尺码。如果您区分了售前和售后客服组，推荐您指定客服或订单状态，否则建议使用订单状态，请谨慎设置哦~
        </div>
        <div className="text-right mt-8">
          <Button className="mr-2">保存</Button>
          <Button type="primary">取消</Button>
        </div>
      </Drawer>
    </div>
  );
};

export default SizeTable;
