import { z<PERSON><PERSON>da<PERSON> } from '@hono/zod-validator';
import { UserRole } from '@prisma/client';
import { Hono } from 'hono';
import { z } from 'zod';

import { prisma } from '../lib/db';
import { extractTokenFromHeader, generateTokenPair, verifyToken } from '../lib/jwt';
import { logger } from '../lib/logger';

const auth = new Hono();

// 登录请求验证
const loginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少6位'),
});

// 注册请求验证
const registerSchema = z.object({
  name: z.string().min(1, '请输入姓名'),
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少6位'),
});

// 刷新令牌验证
const refreshSchema = z.object({
  refreshToken: z.string(),
});

/**
 * 用户登录
 */
auth.post('/login', zValidator('json', loginSchema), async (c) => {
  try {
    const { email, password } = c.req.valid('json');

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        members: {
          include: {
            organization: {
              select: { id: true, name: true },
            },
          },
        },
        team: {
          select: { id: true, name: true },
        },
      },
    });

    if (!user) {
      return c.json({ error: 'Invalid email or password' }, 401);
    }

    // 验证密码
    const account = await prisma.account.findFirst({
      where: {
        userId: user.id,
        providerId: 'credential',
      },
    });

    if (!account?.password) {
      return c.json({ error: 'Invalid email or password' }, 401);
    }

    // 检查密码格式并验证
    let isValidPassword = false;

    try {
      // 尝试使用 Bun 的密码验证（新的 JWT 系统）
      isValidPassword = await Bun.password.verify(password, account.password);
    } catch (error) {
      logger.debug('密码验证失败 - 可能是旧的Better Auth格式');

      // 返回特定错误，提示用户需要重新注册
      return c.json(
        {
          error: 'Your account was created with the old authentication system. Please register again or contact support.',
          code: 'OLD_AUTH_FORMAT',
        },
        401,
      );
    }

    if (!isValidPassword) {
      return c.json({ error: 'Invalid email or password' }, 401);
    }

    // 获取用户的组织信息
    logger.debug('用户成员信息', {
      userMembers: user.members,
      userTeamId: user.teamId,
    });

    const organizationId = user.members[0]?.organization?.id;
    const teamId = user.teamId;

    logger.debug('最终组织和团队ID', { organizationId, teamId });

    // 生成令牌
    const tokens = await generateTokenPair({
      id: user.id,
      email: user.email,
      role: user.role,
      organizationId,
      teamId: teamId || undefined,
    });

    // 创建会话记录
    await prisma.session.create({
      data: {
        userId: user.id,
        token: tokens.accessToken,
        expiresAt: new Date(Date.now() + tokens.expiresIn * 1000),
        ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
        userAgent: c.req.header('user-agent'),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    const responseData = {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        organizationId,
        teamId,
      },
      ...tokens,
    };

    logger.info('用户登录成功', {
      userId: user.id,
      email: user.email,
      organizationId,
      teamId,
    });

    return c.json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    logger.error('用户登录失败', {}, error instanceof Error ? error : new Error(String(error)));
    return c.json({ error: 'Internal server error' }, 500);
  }
});

/**
 * 用户注册
 */
auth.post('/register', zValidator('json', registerSchema), async (c) => {
  try {
    const { name, email, password } = c.req.valid('json');

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return c.json({ error: 'User already exists' }, 409);
    }

    // 加密密码
    const hashedPassword = await Bun.password.hash(password);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name,
        email,
        emailVerified: false,
        role: UserRole.CUSTOMER_SERVICE,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 创建账户记录
    await prisma.account.create({
      data: {
        userId: user.id,
        accountId: user.id,
        providerId: 'credential',
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 生成令牌
    const tokens = await generateTokenPair({
      id: user.id,
      email: user.email,
      role: user.role,
    });

    // 创建会话记录
    await prisma.session.create({
      data: {
        userId: user.id,
        token: tokens.accessToken,
        expiresAt: new Date(Date.now() + tokens.expiresIn * 1000),
        ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
        userAgent: c.req.header('user-agent'),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    return c.json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
        },
        ...tokens,
      },
    });
  } catch (error) {
    logger.error('用户注册失败', {}, error instanceof Error ? error : new Error(String(error)));
    return c.json({ error: 'Internal server error' }, 500);
  }
});

/**
 * 刷新令牌
 */
auth.post('/refresh', zValidator('json', refreshSchema), async (c) => {
  try {
    const { refreshToken } = c.req.valid('json');

    // 验证刷新令牌
    const payload = await verifyToken(refreshToken);
    if (!payload || payload.type !== 'refresh') {
      return c.json({ error: 'Invalid refresh token' }, 401);
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        members: {
          include: {
            organization: {
              select: { id: true, name: true },
            },
          },
        },
        team: {
          select: { id: true, name: true },
        },
      },
    });

    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    // 获取用户的组织信息
    const organizationId = user.members[0]?.organization?.id;
    const teamId = user.teamId;

    // 生成新的令牌对
    const tokens = await generateTokenPair({
      id: user.id,
      email: user.email,
      role: user.role,
      organizationId,
      teamId: teamId || undefined,
    });

    return c.json({
      success: true,
      data: tokens,
    });
  } catch (error) {
    logger.error('刷新令牌失败', {}, error instanceof Error ? error : new Error(String(error)));
    return c.json({ error: 'Internal server error' }, 500);
  }
});

/**
 * 用户登出
 */
auth.post('/logout', async (c) => {
  try {
    const authHeader = c.req.header('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (token) {
      // 删除会话记录
      await prisma.session.deleteMany({
        where: { token },
      });
    }

    return c.json({ success: true, message: 'Logged out successfully' });
  } catch (error) {
    logger.error('用户登出失败', {}, error instanceof Error ? error : new Error(String(error)));
    return c.json({ error: 'Internal server error' }, 500);
  }
});

/**
 * 获取当前用户信息
 */
auth.get('/me', async (c) => {
  try {
    const authHeader = c.req.header('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return c.json({ error: 'No token provided' }, 401);
    }

    const payload = await verifyToken(token);
    if (!payload || payload.type !== 'access') {
      return c.json({ error: 'Invalid token' }, 401);
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        members: {
          include: {
            organization: {
              select: { id: true, name: true },
            },
          },
        },
        team: {
          select: { id: true, name: true },
        },
      },
    });

    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    return c.json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          organizationId: user.members[0]?.organization?.id,
          teamId: user.teamId,
          organization: user.members[0]?.organization,
          team: user.team,
        },
      },
    });
  } catch (error) {
    logger.error('获取用户信息失败', {}, error instanceof Error ? error : new Error(String(error)));
    return c.json({ error: 'Internal server error' }, 500);
  }
});

export default auth;
