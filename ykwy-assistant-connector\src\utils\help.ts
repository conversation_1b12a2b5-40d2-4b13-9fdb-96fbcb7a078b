/**
 * 帮助工具
 * 提供帮助信息显示功能
 */
export class Help {
  /**
   * 显示帮助信息
   */
  static show(): void {
    console.log(`
🛠️  千牛连接一键启动脚本

🚀 主要功能:
  双击运行或: bun run index.ts             # 一键启动（推荐）

✨ 一键启动功能:
  ✅ 智能检测千牛注入状态，避免重复操作
  ✅ 自动登录系统获取认证（仅在需要时）
  ✅ 自动调用后端API获取脚本地址（仅在需要时）
  ✅ 智能检测千牛状态并引导操作
  ✅ 自动注入连接脚本到千牛客户端（仅在需要时）
  ✅ 自动启动TCP代理服务
  ✅ 持续监听千牛进程，自动处理新启动的千牛实例

📋 其他命令:
  bun run index.ts config                  # 重新配置API服务器和团队信息
  bun run index.ts help                    # 显示此帮助信息

🔄 操作流程:
  1. 首次运行会提示配置API服务器、登录信息和团队信息
  2. 智能检测千牛注入状态：
     - 如果已注入：跳过API调用和注入步骤，直接启动服务
     - 如果未注入：执行完整注入流程
  3. 未注入时的完整流程：
     - 自动使用邮箱密码登录系统获取认证
     - 自动调用后端API创建连接邀请获取脚本地址
     - 智能检测千牛状态并注入脚本
  4. 启动TCP代理服务
  5. 启动千牛进程持续监听器
  6. 每当检测到新的千牛进程启动时，自动执行QNQtHelp64
  7. 完成！系统现在会自动处理所有千牛实例

📁 配置文件: config.json
  - 首次运行会自动创建
  - 可手动编辑修改配置
  - 运行 'bun run index.ts config' 重新配置

⚠️  注意事项:
  - Windows系统建议以管理员权限运行
  - 确保API服务器正常运行
  - 确保网络连接正常
  - 确保有正确的团队权限
    `);
  }
}

// ES Module export - 已在类定义时使用 export
