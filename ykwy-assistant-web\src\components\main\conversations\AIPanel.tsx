import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Bo<PERSON>, RefreshCw, ShoppingBag, TrendingUp, User, Zap } from 'lucide-react';
import React from 'react';

import { conversationMessagesQueryOptions, conversationQueryOptions, customerQueryOptions } from '../../../services';
import { useAIRecommendations } from '../../../services/hooks/useSalesAgentRecommendations';

import { Button } from '@/components/ui/button';

interface AIRecommendation {
  id: string;
  type: 'quick_reply';
  title: string;
  content: string;
  confidence: number;
  category: string;
}

interface AIPanelProps {
  conversationId?: string;
}

// AI推荐骨架屏 - 更大气的设计
function AIRecommendationSkeleton() {
  return (
    <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg animate-pulse">
      {/* 头部标签和分类图标 */}
      <div className="flex items-center justify-between mb-3">
        <div className="h-4 w-20 bg-gray-300 rounded"></div>
        <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
      </div>

      {/* 推荐内容 - 多行模拟 */}
      <div className="space-y-2 mb-4">
        <div className="h-4 w-full bg-gray-300 rounded"></div>
        <div className="h-4 w-11/12 bg-gray-300 rounded"></div>
        <div className="h-4 w-4/5 bg-gray-300 rounded"></div>
      </div>

      {/* 底部置信度和按钮 */}
      <div className="flex items-center justify-between">
        <div className="h-3 w-16 bg-gray-300 rounded"></div>
        <div className="h-6 w-12 bg-gray-300 rounded"></div>
      </div>
    </div>
  );
}

// 客户信息骨架屏 - 让高度由文字行高自然撑开
function CustomerInfoSkeleton() {
  return (
    <div className="flex-shrink-0 p-4 bg-gray-50 border-t border-gray-200 animate-pulse">
      {/* 标题部分 - 完全复制实际结构 */}
      <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
        <div className="w-4 h-4 bg-gray-300 rounded"></div>
        <div className="bg-gray-300 rounded w-16">&nbsp;</div>
      </h4>

      {/* 客户信息条目 - 让文字行高自然撑开 */}
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">
            <span className="bg-gray-300 rounded inline-block w-8">&nbsp;</span>
          </span>
          <span className="font-medium">
            <span className="bg-gray-300 rounded inline-block w-12">&nbsp;</span>
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600">
            <span className="bg-gray-300 rounded inline-block w-12">&nbsp;</span>
          </span>
          <span className="font-medium">
            <span className="bg-gray-300 rounded inline-block w-16">&nbsp;</span>
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600">
            <span className="bg-gray-300 rounded inline-block w-12">&nbsp;</span>
          </span>
          <span className="font-medium">
            <span className="bg-gray-300 rounded inline-block w-8">&nbsp;</span>
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600">
            <span className="bg-gray-300 rounded inline-block w-12">&nbsp;</span>
          </span>
          <span className="font-medium">
            <span className="bg-gray-300 rounded inline-block w-20">&nbsp;</span>
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600">
            <span className="bg-gray-300 rounded inline-block w-12">&nbsp;</span>
          </span>
          <span className="font-medium">
            <span className="bg-gray-300 rounded inline-block w-14">&nbsp;</span>
          </span>
        </div>
      </div>

      {/* 客户标签 */}
      <div className="mt-3">
        <div className="flex flex-wrap gap-1">
          <span className="px-2 py-1 text-xs bg-gray-300 rounded-full w-12">&nbsp;</span>
          <span className="px-2 py-1 text-xs bg-gray-300 rounded-full w-16">&nbsp;</span>
          <span className="px-2 py-1 text-xs bg-gray-300 rounded-full w-14">&nbsp;</span>
        </div>
      </div>
    </div>
  );
}

export default function AIPanel({ conversationId }: AIPanelProps) {
  const queryClient = useQueryClient();

  const { data: conversation } = useQuery({
    ...conversationQueryOptions(conversationId!),
    enabled: !!conversationId,
  });

  const { data: customer } = useQuery({
    ...customerQueryOptions(conversation?.customerId || ''),
    enabled: !!conversation?.customerId,
  });

  // 获取消息列表以便生成AI推荐
  const { data: messagesData } = useQuery({
    ...conversationMessagesQueryOptions(conversationId!),
    enabled: !!conversationId,
  });

  const messages = messagesData?.data || [];

  // 使用销售智能体获取推荐（只有在有消息时才获取推荐）
  const {
    data: salesAgentRecommendations,
    isLoading: recommendationsLoading,
    isFetching: recommendationsFetching,
    error: recommendationsError,
  } = useAIRecommendations(
    conversationId!,
    messages.length > 0, // 只有在有消息时才启用推荐
  );

  // 添加调试日志
  console.log('AIPanel Debug:', {
    conversationId,
    messagesLength: messages.length,
    salesAgentRecommendations,
    recommendationsLoading,
    recommendationsFetching,
    recommendationsError: recommendationsError
      ? {
        name: recommendationsError.name,
        message: recommendationsError.message,
      }
      : null,
    enabled: messages.length > 0,
  });

  // 移除useEffect中的invalidateQueries，避免重复请求
  // React Query会根据queryKey自动管理缓存，不需要手动失效

  // 转换为AIRecommendation格式 - 使用稳定的ID避免重复渲染
  const recommendations: AIRecommendation[] = (salesAgentRecommendations || []).map(
    (content: string, index: number): AIRecommendation => ({
      id: `${conversationId}-sales-agent-${index}`, // 移除时间戳，使用稳定ID
      type: 'quick_reply' as const,
      title: `AI 推荐 ${index + 1}`,
      content,
      confidence: 90 - index * 5,
      category: '销售智能体推荐',
    }),
  );

  const handleUseRecommendation = (content: string) => {
    const event = new CustomEvent('useAIRecommendation', { detail: { content } });
    window.dispatchEvent(event);
  };

  const handleRetryRecommendations = () => {
    if (conversationId) {
      queryClient.invalidateQueries({
        queryKey: ['aiRecommendations', conversationId],
      });
    }
  };

  const formatMoney = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount);
  };

  const formatTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`;
    } else if (diffInMinutes < 24 * 60) {
      return `${Math.floor(diffInMinutes / 60)}小时前`;
    } else {
      return `${Math.floor(diffInMinutes / (24 * 60))}天前`;
    }
  };

  return (
    <div className="w-full bg-white border-l border-gray-200 flex flex-col h-full">
      {/* AI推荐头部 - 固定 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Bot className="w-5 h-5 text-indigo-600" />
          <h3 className="font-semibold text-gray-900">AI推荐回复</h3>
          {recommendationsLoading && <div className="w-4 h-4 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>}
        </div>
      </div>

      {/* AI推荐列表 - 可滚动区域 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {recommendationsLoading || recommendationsFetching ? (
            <>
              <AIRecommendationSkeleton />
              <AIRecommendationSkeleton />
              <AIRecommendationSkeleton />
            </>
          ) : recommendations && recommendations.length > 0 ? (
            recommendations.map((rec) => (
              <div
                key={rec.id}
                className="p-4 bg-gray-50 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 cursor-pointer transition-colors"
                onClick={() => handleUseRecommendation(rec.content)}
              >
                <div className="flex items-center justify-between mb-3">
                  <span className="text-xs text-gray-600 font-medium">{rec.category || rec.type}</span>
                  <div className="flex items-center gap-1">
                    {rec.category === 'order-status' && <ShoppingBag className="w-3 h-3 text-blue-500" />}
                    {rec.category === 'quick-reply' && <Zap className="w-3 h-3 text-green-500" />}
                    {rec.category === 'detailed-answer' && <TrendingUp className="w-3 h-3 text-purple-500" />}
                    {rec.category === 'complaint-handle' && <span className="text-red-500">🛠️</span>}
                    {rec.category === 'sales-promotion' && <span className="text-orange-500">🎯</span>}
                  </div>
                </div>

                <p className="text-sm text-gray-900 mb-4 leading-relaxed">{rec.content}</p>

                <div className="flex items-center justify-between">
                  <span className={`text-xs font-medium ${rec.confidence >= 90 ? 'text-green-600' : rec.confidence >= 80 ? 'text-yellow-600' : 'text-gray-600'}`}>置信度: {rec.confidence}%</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="text-xs h-6 px-2 text-indigo-600 hover:text-indigo-700"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      handleUseRecommendation(rec.content);
                    }}
                  >
                    使用
                  </Button>
                </div>
              </div>
            ))
          ) : recommendationsError && (!salesAgentRecommendations || salesAgentRecommendations.length === 0) ? (
            // 只有在没有推荐数据时才显示错误状态
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Bot className="w-12 h-12 text-orange-300 mb-4" />
              <h4 className="text-sm font-medium text-orange-600 mb-2">AI推荐暂时不可用</h4>
              <p className="text-xs text-orange-500 max-w-48 mb-4">正在使用默认推荐，请稍后重试获取智能推荐</p>
              <Button size="sm" variant="outline" onClick={handleRetryRecommendations} className="text-orange-600 border-orange-300 hover:bg-orange-50">
                <RefreshCw className="w-3 h-3 mr-1" />
                重试
              </Button>
            </div>
          ) : (
            // 没有推荐时的提示
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Bot className="w-12 h-12 text-gray-300 mb-4" />
              <h4 className="text-sm font-medium text-gray-600 mb-2">暂无AI推荐</h4>
              <p className="text-xs text-gray-500 max-w-48">{messages.length === 0 ? '开始对话后，AI将为您提供智能回复建议' : '等待客户发送消息，AI将基于消息内容生成推荐回复'}</p>
            </div>
          )}
        </div>
      </div>

      {/* 客户详情 - 固定在底部，立即渲染骨架屏 */}
      {conversationId ? (
        // 只要有conversationId就立即显示，优先骨架屏
        !customer ? (
          <CustomerInfoSkeleton />
        ) : (
          <div className="flex-shrink-0 p-4 bg-gray-50 border-t border-gray-200">
            <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <User className="w-4 h-4" />
              客户信息
            </h4>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">昵称:</span>
                <span className="font-medium">{customer.name}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">VIP等级:</span>
                <span className="font-medium text-indigo-600">{customer.vipLevel}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">历史订单:</span>
                <span className="font-medium">{customer.totalOrders}单</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">消费金额:</span>
                <span className="font-medium text-green-600">{formatMoney(customer.totalSpent)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">最后活跃:</span>
                <span className="font-medium">{formatTime(customer.lastActive)}</span>
              </div>
            </div>

            {/* 客户标签 */}
            {customer.tags.length > 0 && (
              <div className="mt-3">
                <div className="flex flex-wrap gap-1">
                  {customer.tags.map((tag) => (
                    <span key={tag} className="px-2 py-1 text-xs bg-indigo-100 text-indigo-600 rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )
      ) : (
        // 没有conversationId时显示空状态
        <div className="flex-shrink-0 h-32 bg-gray-50 border-t border-gray-200 flex items-center justify-center">
          <span className="text-gray-400 text-sm">请选择对话</span>
        </div>
      )}
    </div>
  );
}
