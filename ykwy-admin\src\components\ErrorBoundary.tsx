import { But<PERSON>, Result } from 'antd';
import { Component, ErrorInfo, ReactNode } from 'react';
import logger from '../utils/logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误到日志系统
    logger.error(`React组件错误: ${error.message}`, 'component', {
      stack: error.stack,
      errorType: 'ComponentError',
      componentStack: errorInfo.componentStack,
    });

    // 调用全局错误处理器（如果存在）
    if ((window as any).__REACT_ERROR_HANDLER__) {
      (window as any).__REACT_ERROR_HANDLER__(error, errorInfo);
    }

    console.error('React Error Boundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
    logger.info('用户点击重试', 'component');
  };

  render() {
    if (this.state.hasError) {
      // 自定义错误显示
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误显示
      return (
        <Result
          status="error"
          title="页面出现异常"
          subTitle="抱歉，页面遇到了一些问题，请尝试刷新页面或联系技术支持。"
          extra={[
            <Button type="primary" key="retry" onClick={this.handleRetry}>
              重试
            </Button>,
            <Button key="refresh" onClick={() => window.location.reload()}>
              刷新页面
            </Button>,
          ]}
        >
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <div style={{ textAlign: 'left', marginTop: 16 }}>
              <details>
                <summary>错误详情（开发模式）</summary>
                <pre style={{ whiteSpace: 'pre-wrap', fontSize: 12 }}>
                  {this.state.error.stack}
                </pre>
              </details>
            </div>
          )}
        </Result>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
