import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { wsManager } from '../lib/websocket';

/**
 * 基于角色权限的消息通知服务
 * 权限规则：
 * - 组织管理员：接收组织内所有消息
 * - 团队管理员：接收所属团队的所有消息
 * - 客服人员：只接收绑定连接的消息
 */
export class ConnectionBasedNotificationService {
  private onlineUserCache = new Map<string, Set<string>>(); // organizationId -> Set<userId>
  private userConnectionCache = new Map<string, string>(); // userId -> connectionId
  private cacheExpiry = new Map<string, number>(); // userId -> expiry timestamp

  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存过期时间

  /**
   * 用户上线时更新缓存
   */
  async userOnline(userId: string, organizationId: string) {
    try {
      // 添加到在线用户缓存
      if (!this.onlineUserCache.has(organizationId)) {
        this.onlineUserCache.set(organizationId, new Set());
      }
      this.onlineUserCache.get(organizationId)!.add(userId);

      // 设置缓存过期时间
      this.cacheExpiry.set(userId, Date.now() + this.CACHE_TTL);

      // 查询用户绑定的连接
      const boundConnection = await prisma.connectionInvitation.findFirst({
        where: {
          boundUserId: userId,
          status: 'ACTIVATED',
          organizationId: organizationId,
        },
        select: {
          id: true,
          qianniuClientId: true,
        },
      });

      if (boundConnection?.qianniuClientId) {
        // 查询连接ID
        const client = await prisma.qianniuClient.findUnique({
          where: { id: boundConnection.qianniuClientId },
          select: { connectionId: true },
        });

        if (client?.connectionId) {
          this.userConnectionCache.set(userId, client.connectionId);
          logger.debug('用户连接绑定缓存更新', {
            userId,
            connectionId: client.connectionId,
            organizationId,
          });
        }
      }

      logger.debug('用户上线状态更新', {
        userId,
        organizationId,
        onlineUsersCount: this.onlineUserCache.get(organizationId)?.size || 0,
      });
    } catch (error) {
      logger.error('更新用户在线状态失败', { userId, organizationId }, error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 用户下线时清理缓存
   */
  userOffline(userId: string, organizationId: string) {
    // 从在线用户缓存中移除
    const orgUsers = this.onlineUserCache.get(organizationId);
    if (orgUsers) {
      orgUsers.delete(userId);
      if (orgUsers.size === 0) {
        this.onlineUserCache.delete(organizationId);
      }
    }

    // 清理用户连接缓存
    this.userConnectionCache.delete(userId);
    this.cacheExpiry.delete(userId);

    logger.debug('用户下线状态更新', {
      userId,
      organizationId,
      onlineUsersCount: orgUsers?.size || 0,
    });
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache() {
    const now = Date.now();
    const expiredUsers: string[] = [];

    for (const [userId, expiry] of this.cacheExpiry.entries()) {
      if (now > expiry) {
        expiredUsers.push(userId);
      }
    }

    for (const userId of expiredUsers) {
      this.userConnectionCache.delete(userId);
      this.cacheExpiry.delete(userId);

      // 从在线用户缓存中移除
      for (const [orgId, users] of this.onlineUserCache.entries()) {
        if (users.has(userId)) {
          users.delete(userId);
          if (users.size === 0) {
            this.onlineUserCache.delete(orgId);
          }
          break;
        }
      }
    }

    if (expiredUsers.length > 0) {
      logger.debug('清理过期用户缓存', { expiredCount: expiredUsers.length });
    }
  }

  /**
   * 根据连接ID获取应该接收消息的在线用户（基于角色权限）
   */
  async getBoundOnlineUsers(connectionId: string, organizationId: string): Promise<string[]> {
    try {
      // 清理过期缓存
      this.cleanExpiredCache();

      // 查找连接信息和绑定的用户
      const boundConnection = await prisma.connectionInvitation.findFirst({
        where: {
          qianniuClient: {
            connectionId: connectionId,
          },
          status: 'ACTIVATED',
          organizationId: organizationId,
        },
        select: {
          boundUserId: true,
          teamId: true,
          team: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!boundConnection) {
        logger.debug('连接未找到或未激活', { connectionId, organizationId });
        return [];
      }

      const orgUsers = this.onlineUserCache.get(organizationId);
      if (!orgUsers || orgUsers.size === 0) {
        logger.debug('组织内无在线用户', { connectionId, organizationId });
        return [];
      }

      // 获取组织内所有在线用户的详细信息
      const onlineUserIds = Array.from(orgUsers);
      const onlineUsers = await prisma.user.findMany({
        where: {
          id: { in: onlineUserIds },
          members: {
            some: {
              organizationId: organizationId,
            },
          },
        },
        select: {
          id: true,
          role: true,
          teamId: true,
          members: {
            where: {
              organizationId: organizationId,
            },
            select: {
              role: true,
            },
          },
        },
      });

      const targetUsers: string[] = [];

      for (const user of onlineUsers) {
        const member = user.members[0];
        if (!member) {
          continue;
        }
        const memberRole = member.role || user.role;

        // 1. 组织管理员：接收所有消息
        if (memberRole === 'ORGANIZATION_ADMIN') {
          targetUsers.push(user.id);
          continue;
        }

        // 2. 团队管理员：接收所属团队的消息
        if (memberRole === 'TEAM_MANAGER' && user.teamId === boundConnection.teamId) {
          targetUsers.push(user.id);
          continue;
        }

        // 3. 客服人员：只接收绑定连接的消息
        if (memberRole === 'CUSTOMER_SERVICE' && user.id === boundConnection.boundUserId) {
          targetUsers.push(user.id);
          continue;
        }
      }

      logger.debug('找到应接收消息的在线用户', {
        connectionId,
        organizationId,
        teamId: boundConnection.teamId,
        boundUserId: boundConnection.boundUserId,
        targetUsers,
        totalOnlineUsers: onlineUsers.length,
      });

      return targetUsers;
    } catch (error) {
      logger.error('获取目标在线用户失败', { connectionId, organizationId }, error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }

  /**
   * 向有权限接收特定连接消息的在线用户发送消息
   * 权限规则：
   * - 组织管理员：接收组织内所有消息
   * - 团队管理员：接收所属团队的所有消息
   * - 客服人员：只接收绑定连接的消息
   */
  async notifyBoundUsers(connectionId: string, organizationId: string, message: string) {
    try {
      const targetUsers = await this.getBoundOnlineUsers(connectionId, organizationId);

      if (targetUsers.length === 0) {
        logger.debug('没有有权限的在线用户，跳过通知', { connectionId, organizationId });
        return;
      }

      // 向有权限的在线用户发送消息
      wsManager.broadcast(organizationId, message, { to: targetUsers });

      logger.info('消息已发送给有权限的用户', {
        connectionId,
        organizationId,
        targetUsersCount: targetUsers.length,
        targetUsers,
      });
    } catch (error) {
      logger.error('向目标用户发送消息失败', { connectionId, organizationId }, error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 获取在线用户统计
   */
  getOnlineStats() {
    const stats = {
      totalOrganizations: this.onlineUserCache.size,
      totalOnlineUsers: 0,
      organizationStats: {} as Record<string, number>,
    };

    for (const [orgId, users] of this.onlineUserCache.entries()) {
      const userCount = users.size;
      stats.totalOnlineUsers += userCount;
      stats.organizationStats[orgId] = userCount;
    }

    return stats;
  }

  /**
   * 刷新用户连接绑定缓存
   */
  async refreshUserConnectionCache(userId: string, organizationId: string) {
    try {
      // 查询用户绑定的连接
      const boundConnection = await prisma.connectionInvitation.findFirst({
        where: {
          boundUserId: userId,
          status: 'ACTIVATED',
          organizationId: organizationId,
        },
        include: {
          qianniuClient: {
            select: {
              connectionId: true,
            },
          },
        },
      });

      if (boundConnection?.qianniuClient?.connectionId) {
        this.userConnectionCache.set(userId, boundConnection.qianniuClient.connectionId);
        this.cacheExpiry.set(userId, Date.now() + this.CACHE_TTL);

        logger.debug('用户连接绑定缓存已刷新', {
          userId,
          connectionId: boundConnection.qianniuClient.connectionId,
          organizationId,
        });
      } else {
        // 用户没有绑定连接，清理缓存
        this.userConnectionCache.delete(userId);
        this.cacheExpiry.delete(userId);

        logger.debug('用户无绑定连接，已清理缓存', { userId, organizationId });
      }
    } catch (error) {
      logger.error('刷新用户连接绑定缓存失败', { userId, organizationId }, error instanceof Error ? error : new Error(String(error)));
    }
  }
}

// 创建并导出单例
export const connectionBasedNotificationService = new ConnectionBasedNotificationService();

// 定期清理过期缓存
setInterval(() => {
  connectionBasedNotificationService['cleanExpiredCache']();
}, 60 * 1000); // 每分钟清理一次
