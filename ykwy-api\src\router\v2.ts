import { Hono } from 'hono';

import { QuestionAndAnswerController } from '../controller/questionAndAnswer';
import { SceneQAController } from '../controller/sceneQA';
import { TempProductController } from '../controller/tempProduct';
import { checkAccess } from '../middlewares/access';

const v2Router = new Hono();

// ================================
// TempProduct V2 接口
// ================================
const tempProduct = new TempProductController();

// 根据查询字符串搜索临时商品（使用accesstoken认证）
// GET /api/v2/temp-product/search
v2Router.get('/temp-product/search', checkAccess, tempProduct.searchByQuery);

// 插入临时商品（使用accesstoken认证）
// POST /api/v2/temp-product/insert
v2Router.post('/temp-product/insert', checkAccess, tempProduct.upsert);

// ================================
// QuestionAndAnswer V2 接口
// ================================
const questionAndAnswer = new QuestionAndAnswerController();

// 根据查询字符串搜索问答知识库（使用accesstoken认证）
// GET /api/v2/question-answer/search
v2Router.get('/question-answer/search', checkAccess, questionAndAnswer.searchByQuery);

// ================================
// SceneQA V2 接口
// ================================
const sceneQA = new SceneQAController();

// 创建/更新场景Q&A（使用accesstoken认证）
// POST /api/v2/scene-qa
v2Router.post('/scene-qa', checkAccess, sceneQA.upsert);

// 搜索场景Q&A（使用accesstoken认证）
// GET /api/v2/scene-qa/search
v2Router.get('/scene-qa/search', checkAccess, sceneQA.searchByQuery);

// 获取单个场景Q&A详情（使用accesstoken认证）
// GET /api/v2/scene-qa/:id
v2Router.get('/scene-qa/:id', checkAccess, sceneQA.findById);

// 获取场景Q&A列表（使用accesstoken认证）
// GET /api/v2/scene-qa
v2Router.get('/scene-qa', checkAccess, sceneQA.findMany);

// 删除单个场景Q&A（使用accesstoken认证）
// DELETE /api/v2/scene-qa/:id
v2Router.delete('/scene-qa/:id', checkAccess, sceneQA.delete);

// 批量删除场景Q&A（使用accesstoken认证）
// POST /api/v2/scene-qa/bulk-delete
v2Router.post('/scene-qa/bulk-delete', checkAccess, sceneQA.bulkDelete);

// 批量创建场景Q&A（使用accesstoken认证）
// POST /api/v2/scene-qa/bulk-create
v2Router.post('/scene-qa/bulk-create', checkAccess, sceneQA.bulkCreate);

// 清空所有场景Q&A（使用accesstoken认证）
// DELETE /api/v2/scene-qa/truncate
v2Router.delete('/scene-qa/truncate', checkAccess, sceneQA.truncate);

export default v2Router;
